"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/leagues/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/leagues/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaguesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _lib_hooks_useLeagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useLeagues */ \"(app-pages-browser)/./src/lib/hooks/useLeagues.ts\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays,Edit,Eye,Filter,Globe,Plus,Search,Trash2,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays,Edit,Eye,Filter,Globe,Plus,Search,Trash2,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays,Edit,Eye,Filter,Globe,Plus,Search,Trash2,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays,Edit,Eye,Filter,Globe,Plus,Search,Trash2,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays,Edit,Eye,Filter,Globe,Plus,Search,Trash2,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays,Edit,Eye,Filter,Globe,Plus,Search,Trash2,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays,Edit,Eye,Filter,Globe,Plus,Search,Trash2,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays,Edit,Eye,Filter,Globe,Plus,Search,Trash2,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays,Edit,Eye,Filter,Globe,Plus,Search,Trash2,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LeaguesPage() {\n    var _leaguesMeta_totalItems;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions)();\n    // State for filtering\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20\n    });\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Fetch leagues data\n    const { leagues, leaguesMeta, isLoading, error } = (0,_lib_hooks_useLeagues__WEBPACK_IMPORTED_MODULE_9__.useLeagues)(filters);\n    // Handle search\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n        setFilters((prev)=>({\n                ...prev,\n                search: query || undefined,\n                page: 1\n            }));\n    };\n    // Handle pagination\n    const handlePageChange = (page)=>{\n        setFilters((prev)=>({\n                ...prev,\n                page\n            }));\n    };\n    // Handle filters\n    const handleCountryFilter = (country)=>{\n        setFilters((prev)=>({\n                ...prev,\n                country: country || undefined,\n                page: 1\n            }));\n    };\n    const handleActiveFilter = (active)=>{\n        setFilters((prev)=>({\n                ...prev,\n                active,\n                page: 1\n            }));\n    };\n    // Table columns\n    const columns = [\n        {\n            accessorKey: \"logo\",\n            header: \"\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 flex items-center justify-center\",\n                    children: row.original.logo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: row.original.logo,\n                        alt: row.original.name,\n                        className: \"w-6 h-6 object-contain\",\n                        onError: (e)=>{\n                            const target = e.target;\n                            target.style.display = \"none\";\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-5 h-5 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false\n        },\n        {\n            accessorKey: \"name\",\n            header: \"League Name\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium\",\n                            children: row.original.name\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        row.original.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 capitalize\",\n                            children: row.original.type\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"country\",\n            header: \"Country\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        row.original.countryFlag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: row.original.countryFlag,\n                            alt: row.original.country,\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: row.original.country\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"season_detail\",\n            header: \"Season\",\n            cell: (param)=>{\n                let { row } = param;\n                const seasonDetail = row.original.season_detail;\n                if (!seasonDetail) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-400\",\n                    children: \"-\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 35\n                }, this);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium\",\n                            children: seasonDetail.year\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                seasonDetail.start,\n                                \" - \",\n                                seasonDetail.end\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        seasonDetail.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"secondary\",\n                            className: \"text-xs\",\n                            children: \"Current\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"isHot\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        row.original.isHot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"destructive\",\n                            className: \"text-xs\",\n                            children: \"Hot\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: row.original.active ? \"default\" : \"secondary\",\n                            className: \"text-xs\",\n                            children: row.original.active ? \"Active\" : \"Inactive\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            header: \"Actions\",\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/leagues/\".concat(row.original.externalId)),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        isEditor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/leagues/\".concat(row.original.externalId, \"/edit\")),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            className: \"text-red-600 hover:text-red-700\",\n                            onClick: ()=>{\n                                // TODO: Implement delete functionality\n                                console.log(\"Delete league:\", row.original.externalId);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold tracking-tight\",\n                                children: \"Leagues Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage football leagues, seasons, and configurations\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    isEditor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push(\"/dashboard/leagues/create\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            \"Add League\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Leagues\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: (leaguesMeta === null || leaguesMeta === void 0 ? void 0 : (_leaguesMeta_totalItems = leaguesMeta.totalItems) === null || _leaguesMeta_totalItems === void 0 ? void 0 : _leaguesMeta_totalItems.toLocaleString()) || \"Loading...\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Across all countries\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Active Leagues\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: (leagues === null || leagues === void 0 ? void 0 : leagues.filter((l)=>l.active).length) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Currently running seasons\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: new Set(leagues === null || leagues === void 0 ? void 0 : leagues.map((l)=>l.country)).size || 0\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Unique countries represented\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Hot Leagues\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: (leagues === null || leagues === void 0 ? void 0 : leagues.filter((l)=>l.isHot).length) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Popular leagues\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Filters & Search\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_Edit_Eye_Filter_Globe_Plus_Search_Trash2_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            placeholder: \"Search leagues...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>handleSearch(e.target.value),\n                                            className: \"pl-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    className: \"border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                    onChange: (e)=>handleCountryFilter(e.target.value),\n                                    value: filters.country || \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"All Countries\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        Array.from(new Set(leagues === null || leagues === void 0 ? void 0 : leagues.map((l)=>l.country))).sort().map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: country,\n                                                children: country\n                                            }, country, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    className: \"border border-gray-300 rounded-md px-3 py-2 text-sm\",\n                                    onChange: (e)=>handleActiveFilter(e.target.value === \"\" ? undefined : e.target.value === \"true\"),\n                                    value: filters.active === undefined ? \"\" : filters.active.toString(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"All Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"true\",\n                                            children: \"Active Only\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"false\",\n                                            children: \"Inactive Only\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setSearchQuery(\"\");\n                                        setFilters({\n                                            page: 1,\n                                            limit: 20\n                                        });\n                                    },\n                                    children: \"Clear Filters\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"Leagues List\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                            columns: columns,\n                            data: leagues || [],\n                            isLoading: isLoading,\n                            pagination: {\n                                currentPage: (leaguesMeta === null || leaguesMeta === void 0 ? void 0 : leaguesMeta.currentPage) || 1,\n                                totalPages: (leaguesMeta === null || leaguesMeta === void 0 ? void 0 : leaguesMeta.totalPages) || 1,\n                                totalItems: (leaguesMeta === null || leaguesMeta === void 0 ? void 0 : leaguesMeta.totalItems) || 0,\n                                itemsPerPage: (leaguesMeta === null || leaguesMeta === void 0 ? void 0 : leaguesMeta.itemsPerPage) || 20,\n                                onPageChange: handlePageChange\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaguesPage, \"SifH00vH62aKKxWQXNECuZRYOYM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions,\n        _lib_hooks_useLeagues__WEBPACK_IMPORTED_MODULE_9__.useLeagues\n    ];\n});\n_c = LeaguesPage;\nvar _c;\n$RefreshReg$(_c, \"LeaguesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/leagues/page.tsx\n"));

/***/ })

});