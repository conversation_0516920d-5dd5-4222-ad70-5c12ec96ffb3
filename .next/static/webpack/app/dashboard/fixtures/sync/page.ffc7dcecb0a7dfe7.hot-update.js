"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/sync/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/sync/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/fixtures/sync/page.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesSyncPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Calendar,CheckCircle,Clock,Database,RefreshCw,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Calendar,CheckCircle,Clock,Database,RefreshCw,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Calendar,CheckCircle,Clock,Database,RefreshCw,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Calendar,CheckCircle,Clock,Database,RefreshCw,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Calendar,CheckCircle,Clock,Database,RefreshCw,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Calendar,CheckCircle,Clock,Database,RefreshCw,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Calendar,CheckCircle,Clock,Database,RefreshCw,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Calendar,CheckCircle,Clock,Database,RefreshCw,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Calendar,CheckCircle,Clock,Database,RefreshCw,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_fixtures_FixtureNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/fixtures/FixtureNavigation */ \"(app-pages-browser)/./src/components/fixtures/FixtureNavigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction FixturesSyncPage() {\n    var _syncStatus_fixtures, _syncStatus_errors;\n    _s();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQueryClient)();\n    const [syncHistory, setSyncHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isAutoRefresh, setIsAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch current sync status\n    const { data: syncStatus, isLoading: statusLoading, refetch: refetchStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQuery)({\n        queryKey: [\n            \"fixtures-sync-status\"\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getSyncStatus(),\n        refetchInterval: isAutoRefresh ? 5000 : false\n    });\n    // Daily sync mutation\n    const dailySyncMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useMutation)({\n        mutationFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.triggerDailySync(),\n        onSuccess: (result)=>{\n            var _result_details;\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Daily sync started successfully!\");\n            addToSyncHistory({\n                id: Date.now().toString(),\n                type: \"daily\",\n                status: \"success\",\n                startTime: new Date().toISOString(),\n                endTime: new Date().toISOString(),\n                duration: ((_result_details = result.details) === null || _result_details === void 0 ? void 0 : _result_details.duration) || \"N/A\",\n                fixturesProcessed: result.fixturesUpserted || 0,\n                errors: [],\n                details: result.details\n            });\n            refetchStatus();\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Daily sync failed: \".concat(error.message));\n            addToSyncHistory({\n                id: Date.now().toString(),\n                type: \"daily\",\n                status: \"failed\",\n                startTime: new Date().toISOString(),\n                endTime: new Date().toISOString(),\n                fixturesProcessed: 0,\n                errors: [\n                    error.message\n                ]\n            });\n        }\n    });\n    // Season sync mutation\n    const seasonSyncMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useMutation)({\n        mutationFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.triggerSeasonSync(),\n        onSuccess: (result)=>{\n            var _result_details;\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Season sync started successfully!\");\n            addToSyncHistory({\n                id: Date.now().toString(),\n                type: \"season\",\n                status: \"success\",\n                startTime: new Date().toISOString(),\n                endTime: new Date().toISOString(),\n                duration: ((_result_details = result.details) === null || _result_details === void 0 ? void 0 : _result_details.duration) || \"N/A\",\n                fixturesProcessed: result.fixturesUpserted || 0,\n                errors: [],\n                details: result.details\n            });\n            refetchStatus();\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Season sync failed: \".concat(error.message));\n            addToSyncHistory({\n                id: Date.now().toString(),\n                type: \"season\",\n                status: \"failed\",\n                startTime: new Date().toISOString(),\n                endTime: new Date().toISOString(),\n                fixturesProcessed: 0,\n                errors: [\n                    error.message\n                ]\n            });\n        }\n    });\n    const addToSyncHistory = (operation)=>{\n        setSyncHistory((prev)=>[\n                operation,\n                ...prev.slice(0, 9)\n            ]); // Keep last 10 operations\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleString();\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, this);\n            case \"running\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const variants = {\n            success: \"default\",\n            failed: \"destructive\",\n            warning: \"secondary\",\n            running: \"outline\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n            variant: variants[status] || \"outline\",\n            children: [\n                getStatusIcon(status),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-1 capitalize\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this);\n    };\n    const isAnySyncRunning = dailySyncMutation.isLoading || seasonSyncMutation.isLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.AuthGuard, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fixtures_FixtureNavigation__WEBPACK_IMPORTED_MODULE_9__.FixtureNavigation, {\n                                    variant: \"detail\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Fixtures Sync Management\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Synchronize fixture data from API Football service\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setIsAutoRefresh(!isAutoRefresh),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(isAutoRefresh ? \"text-green-600\" : \"text-gray-400\")\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Auto Refresh \",\n                                        isAutoRefresh ? \"ON\" : \"OFF\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>refetchStatus(),\n                                    disabled: statusLoading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(statusLoading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Last Sync\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this),\n                                                statusLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                    className: \"h-4 w-20 mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold\",\n                                                    children: (syncStatus === null || syncStatus === void 0 ? void 0 : syncStatus.lastSync) ? formatDate(syncStatus.lastSync) : \"Never\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Fixtures\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this),\n                                                statusLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                    className: \"h-4 w-16 mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: (syncStatus === null || syncStatus === void 0 ? void 0 : (_syncStatus_fixtures = syncStatus.fixtures) === null || _syncStatus_fixtures === void 0 ? void 0 : _syncStatus_fixtures.toLocaleString()) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Sync Errors\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                statusLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                    className: \"h-4 w-8 mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-red-600\",\n                                                    children: (syncStatus === null || syncStatus === void 0 ? void 0 : (_syncStatus_errors = syncStatus.errors) === null || _syncStatus_errors === void 0 ? void 0 : _syncStatus_errors.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-5 w-5 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-1\",\n                                                    children: isAnySyncRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600 animate-spin mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold text-blue-600\",\n                                                                children: \"Syncing...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-600 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold text-green-600\",\n                                                                children: \"Ready\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this),\n                (syncStatus === null || syncStatus === void 0 ? void 0 : syncStatus.errors) && syncStatus.errors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-red-800 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Recent Sync Errors\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    syncStatus.errors.slice(0, 3).map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-red-700 bg-red-100 p-2 rounded\",\n                                            children: typeof error === \"string\" ? error : JSON.stringify(error)\n                                        }, index, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this)),\n                                    syncStatus.errors.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: [\n                                            \"... and \",\n                                            syncStatus.errors.length - 3,\n                                            \" more errors\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Quick Sync Actions\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"Trigger manual sync operations\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>dailySyncMutation.mutate(),\n                                            disabled: isAnySyncRunning,\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dailySyncMutation.isLoading ? \"Running Daily Sync...\" : \"Daily Sync\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>seasonSyncMutation.mutate(),\n                                            disabled: isAnySyncRunning,\n                                            variant: \"outline\",\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                seasonSyncMutation.isLoading ? \"Running Season Sync...\" : \"Season Sync\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>refetchStatus(),\n                                            variant: \"outline\",\n                                            disabled: statusLoading,\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 \".concat(statusLoading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Refresh Status\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                isAnySyncRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-600 animate-spin mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-800 font-medium\",\n                                                            children: [\n                                                                dailySyncMutation.isLoading ? \"Daily Sync\" : \"Season Sync\",\n                                                                \" in progress...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-blue-600\",\n                                                    children: new Date().toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-blue-200 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-600 h-2 rounded-full animate-pulse\",\n                                                style: {\n                                                    width: \"45%\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-700 mt-2\",\n                                            children: \"Please do not close this page while sync is running.\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-xs text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"\\uD83D\\uDCA1 \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Tips:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside mt-1 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Daily Sync:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Updates recent fixtures (last 7 days)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Season Sync:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Full synchronization of current season data\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Auto Refresh:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Page updates every 5 seconds when enabled\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Recent Sync Operations\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"History of sync operations and their results\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: syncHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Calendar_CheckCircle_Clock_Database_RefreshCw_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No sync operations yet. Start your first sync above.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: syncHistory.map((operation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    getStatusIcon(operation.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium capitalize\",\n                                                                children: [\n                                                                    operation.type,\n                                                                    \" Sync\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: formatDate(operation.startTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: [\n                                                                    operation.fixturesProcessed,\n                                                                    \" fixtures\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            operation.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: operation.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    getStatusBadge(operation.status)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, operation.id, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/sync/page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesSyncPage, \"WEReYqKZu6F0X0nysb/cyvAkhz4=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useMutation\n    ];\n});\n_c = FixturesSyncPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesSyncPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/sync/page.tsx\n"));

/***/ })

});