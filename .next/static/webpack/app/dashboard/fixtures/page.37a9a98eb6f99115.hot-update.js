"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/date-picker */ \"(app-pages-browser)/./src/components/ui/date-picker.tsx\");\n/* harmony import */ var _components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/date-time-display */ \"(app-pages-browser)/./src/components/ui/date-time-display.tsx\");\n/* harmony import */ var _components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/date-filter-modal */ \"(app-pages-browser)/./src/components/ui/date-filter-modal.tsx\");\n/* harmony import */ var _lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils/date-time */ \"(app-pages-browser)/./src/lib/utils/date-time.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// import { BroadcastLinksModal } from '@/components/fixtures/BroadcastLinksModal';\n\n\n\n\nfunction FixturesPage() {\n    var _data_meta_totalItems, _data_meta, _data_data, _data_data1, _data_data2, _fixturesData_meta;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [limit, setLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Input value\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Actual search query for API\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leagueFilter, setLeagueFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixture, setSelectedFixture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [dateFilterModalOpen, setDateFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAdmin, isEditor } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient)();\n    // Mock data for testing when API is down\n    const mockFixtures = {\n        data: [\n            {\n                id: 1,\n                homeTeamName: \"Manchester United\",\n                awayTeamName: \"Liverpool\",\n                homeTeamLogo: \"/images/teams/1.png\",\n                awayTeamLogo: \"/images/teams/2.png\",\n                date: \"2024-12-19T14:30:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Old Trafford\"\n            },\n            {\n                id: 2,\n                homeTeamName: \"Arsenal\",\n                awayTeamName: \"Chelsea\",\n                homeTeamLogo: \"/images/teams/3.png\",\n                awayTeamLogo: \"/images/teams/4.png\",\n                date: \"2024-12-20T16:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Emirates Stadium\"\n            },\n            {\n                id: 3,\n                homeTeamName: \"Barcelona\",\n                awayTeamName: \"Real Madrid\",\n                homeTeamLogo: \"/images/teams/5.png\",\n                awayTeamLogo: \"/images/teams/6.png\",\n                date: \"2024-12-21T20:00:00Z\",\n                status: \"LIVE\",\n                leagueName: \"La Liga\",\n                venue: \"Camp Nou\"\n            },\n            {\n                id: 4,\n                homeTeamName: \"Bayern Munich\",\n                awayTeamName: \"Borussia Dortmund\",\n                homeTeamLogo: \"/images/teams/7.png\",\n                awayTeamLogo: \"/images/teams/8.png\",\n                date: \"2024-12-18T18:30:00Z\",\n                status: \"FT\",\n                leagueName: \"Bundesliga\",\n                venue: \"Allianz Arena\"\n            },\n            {\n                id: 5,\n                homeTeamName: \"PSG\",\n                awayTeamName: \"Marseille\",\n                homeTeamLogo: \"/images/teams/9.png\",\n                awayTeamLogo: \"/images/teams/10.png\",\n                date: \"2024-12-22T21:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Ligue 1\",\n                venue: \"Parc des Princes\"\n            }\n        ],\n        totalItems: 5,\n        totalPages: 1,\n        currentPage: 1,\n        limit: 25\n    };\n    // Fetch fixtures data with fallback to mock data\n    const { data, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\",\n            page,\n            limit,\n            searchQuery,\n            statusFilter,\n            leagueFilter,\n            selectedDate\n        ],\n        queryFn: ()=>{\n            const filters = {\n                page,\n                limit\n            };\n            // Add search query if provided\n            if (searchQuery && searchQuery.trim()) {\n                // For API, we might need to search by team names or other fields\n                // Since API doesn't have a generic search, we'll filter client-side for now\n                filters.search = searchQuery.trim();\n            }\n            if (statusFilter) filters.status = statusFilter;\n            if (leagueFilter) filters.league = leagueFilter;\n            if (selectedDate) filters.date = (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__.convertLocalDateToUTC)(selectedDate);\n            return _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixtures(filters);\n        },\n        staleTime: 30000,\n        retry: false,\n        onError: (error)=>{\n            console.log(\"API is down, using mock data:\", (error === null || error === void 0 ? void 0 : error.message) || \"Unknown error\");\n        }\n    });\n    // Use mock data if API fails or no data\n    const rawData = data || mockFixtures;\n    const isUsingMockData = !data;\n    // Apply client-side filtering for mock data when search is active\n    const fixturesData = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        if (!isUsingMockData || !searchQuery.trim()) {\n            return rawData;\n        }\n        // Filter mock data based on search query\n        const filteredData = rawData.data.filter((fixture)=>{\n            var _fixture_homeTeamName, _fixture_awayTeamName, _fixture_leagueName, _fixture_venue, _fixture_status;\n            const searchLower = searchQuery.toLowerCase();\n            return ((_fixture_homeTeamName = fixture.homeTeamName) === null || _fixture_homeTeamName === void 0 ? void 0 : _fixture_homeTeamName.toLowerCase().includes(searchLower)) || ((_fixture_awayTeamName = fixture.awayTeamName) === null || _fixture_awayTeamName === void 0 ? void 0 : _fixture_awayTeamName.toLowerCase().includes(searchLower)) || ((_fixture_leagueName = fixture.leagueName) === null || _fixture_leagueName === void 0 ? void 0 : _fixture_leagueName.toLowerCase().includes(searchLower)) || ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.toLowerCase().includes(searchLower)) || ((_fixture_status = fixture.status) === null || _fixture_status === void 0 ? void 0 : _fixture_status.toLowerCase().includes(searchLower));\n        });\n        return {\n            ...rawData,\n            data: filteredData,\n            meta: {\n                ...rawData.meta,\n                totalItems: filteredData.length,\n                totalPages: Math.ceil(filteredData.length / limit)\n            },\n            // For backward compatibility with mock structure\n            totalItems: filteredData.length,\n            totalPages: Math.ceil(filteredData.length / limit)\n        };\n    }, [\n        rawData,\n        searchQuery,\n        isUsingMockData,\n        limit\n    ]);\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation)({\n        mutationFn: (fixture)=>{\n            const fixtureId = fixture.externalId || fixture.id;\n            return _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.deleteFixture(fixtureId);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            console.log(\"Fixture deleted successfully\");\n            setDeleteModalOpen(false);\n            setSelectedFixture(null);\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete fixture:\", error.message);\n        }\n    });\n    // Define table columns\n    const columns = [\n        {\n            key: \"date\",\n            title: \"Date & Time\",\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__.DateTimeDisplay, {\n                        dateTime: value,\n                        showDate: true,\n                        showTime: true,\n                        isClickable: true,\n                        onClick: ()=>{\n                            const clickedDate = new Date(value);\n                            setSelectedDate(clickedDate);\n                            setDateFilterModalOpen(true);\n                        },\n                        className: \"min-w-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"match\",\n            title: \"Match\",\n            sortable: false,\n            headerClassName: \"text-center\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-4 py-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-2 min-w-[80px]\",\n                            children: [\n                                row.homeTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"\".concat(\"http://172.31.213.61\", \"/\").concat(row.homeTeamLogo),\n                                    alt: row.homeTeamName,\n                                    className: \"w-8 h-8 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-center leading-tight max-w-[80px] break-words\",\n                                    children: row.homeTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 font-bold text-sm\",\n                                children: \"VS\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-2 min-w-[80px]\",\n                            children: [\n                                row.awayTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"\".concat(\"http://172.31.213.61\", \"/\").concat(row.awayTeamLogo),\n                                    alt: row.awayTeamName,\n                                    className: \"w-8 h-8 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-center leading-tight max-w-[80px] break-words\",\n                                    children: row.awayTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            title: \"Score\",\n            align: \"center\",\n            render: (_, row)=>/*#__PURE__*/ {\n                var _row_goalsHome, _row_goalsAway;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                (_row_goalsHome = row.goalsHome) !== null && _row_goalsHome !== void 0 ? _row_goalsHome : \"-\",\n                                \" - \",\n                                (_row_goalsAway = row.goalsAway) !== null && _row_goalsAway !== void 0 ? _row_goalsAway : \"-\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"HT: \",\n                                row.scoreHalftimeHome,\n                                \" - \",\n                                row.scoreHalftimeAway\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            filterable: true,\n            render: (value, row)=>{\n                const getStatusColor = (status)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                        case \"HT\":\n                            return \"bg-green-100 text-green-800\";\n                        case \"FT\":\n                            return \"bg-gray-100 text-gray-800\";\n                        case \"NS\":\n                            return \"bg-blue-100 text-blue-800\";\n                        case \"CANC\":\n                        case \"PST\":\n                            return \"bg-red-100 text-red-800\";\n                        default:\n                            return \"bg-yellow-100 text-yellow-800\";\n                    }\n                };\n                const getStatusText = (status, elapsed)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                            return \"\".concat(elapsed, \"'\");\n                        case \"HT\":\n                            return \"Half Time\";\n                        case \"FT\":\n                            return \"Full Time\";\n                        case \"NS\":\n                            return \"Not Started\";\n                        case \"CANC\":\n                            return \"Cancelled\";\n                        case \"PST\":\n                            return \"Postponed\";\n                        default:\n                            return status;\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: getStatusColor(value),\n                    children: getStatusText(value, row.elapsed)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"leagueName\",\n            title: \"League\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Broadcast Links\",\n                            onClick: ()=>handleBroadcastLinksModal(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit\",\n                            onClick: ()=>handleEditFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete\",\n                            onClick: ()=>handleDeleteFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Handler functions\n    const handleSearch = ()=>{\n        setSearchQuery(searchInput.trim());\n        setPage(1); // Reset to first page when searching\n    };\n    const handleSearchKeyPress = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearch();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setSearchInput(\"\");\n        setSearchQuery(\"\");\n        setPage(1);\n    };\n    const handleViewFixture = (fixture)=>{\n        // Navigate to fixture detail page using externalId\n        const fixtureId = fixture.externalId || fixture.id;\n        window.open(\"/dashboard/fixtures/\".concat(fixtureId), \"_blank\");\n    };\n    const handleEditFixture = (fixture)=>{\n        // Navigate to edit page using externalId\n        const fixtureId = fixture.externalId || fixture.id;\n        window.open(\"/dashboard/fixtures/\".concat(fixtureId, \"/edit\"), \"_blank\");\n    };\n    const handleDeleteFixture = (fixture)=>{\n        setSelectedFixture(fixture);\n        setDeleteModalOpen(true);\n    };\n    const handleBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const handleViewBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        if (selectedFixture) {\n            deleteMutation.mutate(selectedFixture);\n        }\n    };\n    const handleBulkSync = async ()=>{\n        try {\n            console.log(\"Starting fixtures sync...\");\n            // This would call the sync API\n            // await fixturesApi.syncFixtures();\n            console.log(\"Fixtures sync completed\");\n            refetch();\n        } catch (error) {\n            console.error(\"Sync failed:\", error.message);\n        }\n    };\n    // Date filter handlers\n    const handleApplyDateFilter = (date)=>{\n        setSelectedDate(date);\n        setPage(1); // Reset to first page when filtering\n    };\n    const handleResetDateFilter = ()=>{\n        setSelectedDate(undefined);\n        setPage(1); // Reset to first page when clearing filter\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Fixtures Management\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage football fixtures and match data\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n            lineNumber: 455,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fixtures Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage football fixtures and match data\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkSync,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Export functionality\n                                    console.log(\"Export feature coming soon\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>window.open(\"/dashboard/fixtures/create\", \"_blank\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Fixture\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 479,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_meta = data.meta) === null || _data_meta === void 0 ? void 0 : (_data_meta_totalItems = _data_meta.totalItems) === null || _data_meta_totalItems === void 0 ? void 0 : _data_meta_totalItems.toLocaleString()) || \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 528,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.filter((f)=>[\n                                            \"1H\",\n                                            \"2H\",\n                                            \"HT\"\n                                        ].includes(f.status)).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Live Matches\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.filter((f)=>f.status === \"NS\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Upcoming\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.filter((f)=>f.status === \"FT\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 527,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"All Fixtures\",\n                                                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-normal\",\n                                                    children: \"Demo Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: isUsingMockData ? \"Showing demo data - API backend is not available\" : \"Complete list of football fixtures with real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__.DatePicker, {\n                                            date: selectedDate,\n                                            onDateChange: setSelectedDate,\n                                            placeholder: \"Filter by date\",\n                                            className: \"w-[200px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedDate(undefined),\n                                            className: \"px-2\",\n                                            title: \"Clear date filter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                            rows: 10,\n                            columns: 7\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n                            data: (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.data) || [],\n                            columns: columns,\n                            loading: isLoading && !isUsingMockData,\n                            searchable: true,\n                            searchPlaceholder: \"Search fixtures...\",\n                            showSearchButton: true,\n                            searchValue: searchInput,\n                            onSearchChange: setSearchInput,\n                            onSearchSubmit: handleSearch,\n                            onSearchClear: handleClearSearch,\n                            pagination: {\n                                page,\n                                limit,\n                                total: (fixturesData === null || fixturesData === void 0 ? void 0 : (_fixturesData_meta = fixturesData.meta) === null || _fixturesData_meta === void 0 ? void 0 : _fixturesData_meta.totalItems) || (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.totalItems) || 0,\n                                onPageChange: setPage,\n                                onLimitChange: (newLimit)=>{\n                                    setLimit(newLimit);\n                                    setPage(1); // Reset to first page when changing limit\n                                }\n                            },\n                            emptyMessage: \"No fixtures found\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 566,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>{\n                    setDeleteModalOpen(false);\n                    setSelectedFixture(null);\n                },\n                onConfirm: confirmDelete,\n                title: \"Delete Fixture\",\n                message: selectedFixture ? 'Are you sure you want to delete the fixture \"'.concat(selectedFixture.homeTeamName, \" vs \").concat(selectedFixture.awayTeamName, '\"? This action cannot be undone.') : \"Are you sure you want to delete this fixture?\",\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                variant: \"destructive\",\n                loading: deleteMutation.isLoading\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 639,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__.DateFilterModal, {\n                isOpen: dateFilterModalOpen,\n                onClose: ()=>setDateFilterModalOpen(false),\n                selectedDate: selectedDate,\n                onDateSelect: setSelectedDate,\n                onApplyFilter: handleApplyDateFilter,\n                onResetFilter: handleResetDateFilter\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 671,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 477,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesPage, \"I3nI8t96W3QSaTr1m7GNDLV7lbA=\", false, function() {\n    return [\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation\n    ];\n});\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ZpeHR1cmVzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFDc0M7QUFDbUI7QUFDakQ7QUFDRjtBQUNpQjtBQUNWO0FBQ0o7QUFFNkU7QUFDckU7QUFDSTtBQUM3RCxtRkFBbUY7QUFDMUI7QUFDVztBQUNBO0FBQ007QUFFM0QsU0FBUytCO1FBOGZUQyx1QkFBQUEsWUFTQUEsWUFTQUEsYUFTQUEsYUFtRVE7O0lBM2xCckIsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUNqQyxNQUFNLENBQUNrQyxPQUFPQyxTQUFTLEdBQUduQywrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNvQyxhQUFhQyxlQUFlLEdBQUdyQywrQ0FBUUEsQ0FBQyxLQUFLLGNBQWM7SUFDbEUsTUFBTSxDQUFDc0MsYUFBYUMsZUFBZSxHQUFHdkMsK0NBQVFBLENBQUMsS0FBSyw4QkFBOEI7SUFDbEYsTUFBTSxDQUFDd0MsY0FBY0MsZ0JBQWdCLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMwQyxjQUFjQyxnQkFBZ0IsR0FBRzNDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzRDLGlCQUFpQkMsbUJBQW1CLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUM4QyxpQkFBaUJDLG1CQUFtQixHQUFHL0MsK0NBQVFBLENBQWlCO0lBQ3ZFLE1BQU0sQ0FBQ2dELHlCQUF5QkMsMkJBQTJCLEdBQUdqRCwrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUNrRCw2QkFBNkJDLCtCQUErQixHQUFHbkQsK0NBQVFBLENBQWlCO0lBQy9GLE1BQU0sQ0FBQ29ELGNBQWNDLGdCQUFnQixHQUFHckQsK0NBQVFBLENBQW1Cc0Q7SUFDbkUsTUFBTSxDQUFDQyxxQkFBcUJDLHVCQUF1QixHQUFHeEQsK0NBQVFBLENBQUM7SUFFL0QsTUFBTSxFQUFFeUQsT0FBTyxFQUFFQyxRQUFRLEVBQUUsR0FBR2pDLDBFQUFjQTtJQUM1QyxNQUFNa0MsY0FBY3hELHNFQUFjQTtJQUVsQyx5Q0FBeUM7SUFDekMsTUFBTXlELGVBQWU7UUFDbkI3QixNQUFNO1lBQ0o7Z0JBQ0U4QixJQUFJO2dCQUNKQyxjQUFjO2dCQUNkQyxjQUFjO2dCQUNkQyxjQUFjO2dCQUNkQyxjQUFjO2dCQUNkQyxNQUFNO2dCQUNOQyxRQUFRO2dCQUNSQyxZQUFZO2dCQUNaQyxPQUFPO1lBQ1Q7WUFDQTtnQkFDRVIsSUFBSTtnQkFDSkMsY0FBYztnQkFDZEMsY0FBYztnQkFDZEMsY0FBYztnQkFDZEMsY0FBYztnQkFDZEMsTUFBTTtnQkFDTkMsUUFBUTtnQkFDUkMsWUFBWTtnQkFDWkMsT0FBTztZQUNUO1lBQ0E7Z0JBQ0VSLElBQUk7Z0JBQ0pDLGNBQWM7Z0JBQ2RDLGNBQWM7Z0JBQ2RDLGNBQWM7Z0JBQ2RDLGNBQWM7Z0JBQ2RDLE1BQU07Z0JBQ05DLFFBQVE7Z0JBQ1JDLFlBQVk7Z0JBQ1pDLE9BQU87WUFDVDtZQUNBO2dCQUNFUixJQUFJO2dCQUNKQyxjQUFjO2dCQUNkQyxjQUFjO2dCQUNkQyxjQUFjO2dCQUNkQyxjQUFjO2dCQUNkQyxNQUFNO2dCQUNOQyxRQUFRO2dCQUNSQyxZQUFZO2dCQUNaQyxPQUFPO1lBQ1Q7WUFDQTtnQkFDRVIsSUFBSTtnQkFDSkMsY0FBYztnQkFDZEMsY0FBYztnQkFDZEMsY0FBYztnQkFDZEMsY0FBYztnQkFDZEMsTUFBTTtnQkFDTkMsUUFBUTtnQkFDUkMsWUFBWTtnQkFDWkMsT0FBTztZQUNUO1NBQ0Q7UUFDREMsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLGFBQWE7UUFDYnRDLE9BQU87SUFDVDtJQUVBLGlEQUFpRDtJQUNqRCxNQUFNLEVBQUVILElBQUksRUFBRTBDLFNBQVMsRUFBRUMsS0FBSyxFQUFFQyxPQUFPLEVBQUUsR0FBRzFFLGdFQUFRQSxDQUFDO1FBQ25EMkUsVUFBVTtZQUFDO1lBQVk7WUFBTzVDO1lBQU1FO1lBQU9JO1lBQWFFO1lBQWNFO1lBQWNVO1NBQWE7UUFDakd5QixTQUFTO1lBQ1AsTUFBTUMsVUFBZTtnQkFDbkI5QztnQkFDQUU7WUFDRjtZQUVBLCtCQUErQjtZQUMvQixJQUFJSSxlQUFlQSxZQUFZeUMsSUFBSSxJQUFJO2dCQUNyQyxpRUFBaUU7Z0JBQ2pFLDRFQUE0RTtnQkFDNUVELFFBQVFFLE1BQU0sR0FBRzFDLFlBQVl5QyxJQUFJO1lBQ25DO1lBRUEsSUFBSXZDLGNBQWNzQyxRQUFRWCxNQUFNLEdBQUczQjtZQUNuQyxJQUFJRSxjQUFjb0MsUUFBUUcsTUFBTSxHQUFHdkM7WUFDbkMsSUFBSVUsY0FBYzBCLFFBQVFaLElBQUksR0FBR3JDLDRFQUFxQkEsQ0FBQ3VCO1lBRXZELE9BQU92QywwREFBV0EsQ0FBQ3FFLFdBQVcsQ0FBQ0o7UUFDakM7UUFDQUssV0FBVztRQUNYQyxPQUFPO1FBQ1BDLFNBQVMsQ0FBQ1g7WUFDUlksUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ2IsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPYyxPQUFPLEtBQUk7UUFDakU7SUFDRjtJQUVBLHdDQUF3QztJQUN4QyxNQUFNQyxVQUFVMUQsUUFBUTZCO0lBQ3hCLE1BQU04QixrQkFBa0IsQ0FBQzNEO0lBRXpCLGtFQUFrRTtJQUNsRSxNQUFNNEQsZUFBZTVGLG9EQUFhLENBQUM7UUFDakMsSUFBSSxDQUFDMkYsbUJBQW1CLENBQUNwRCxZQUFZeUMsSUFBSSxJQUFJO1lBQzNDLE9BQU9VO1FBQ1Q7UUFFQSx5Q0FBeUM7UUFDekMsTUFBTUksZUFBZUosUUFBUTFELElBQUksQ0FBQytELE1BQU0sQ0FBQyxDQUFDQztnQkFHdENBLHVCQUNBQSx1QkFDQUEscUJBQ0FBLGdCQUNBQTtZQU5GLE1BQU1DLGNBQWMxRCxZQUFZMkQsV0FBVztZQUMzQyxPQUNFRixFQUFBQSx3QkFBQUEsUUFBUWpDLFlBQVksY0FBcEJpQyw0Q0FBQUEsc0JBQXNCRSxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0YsbUJBQzdDRCx3QkFBQUEsUUFBUWhDLFlBQVksY0FBcEJnQyw0Q0FBQUEsc0JBQXNCRSxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0YsbUJBQzdDRCxzQkFBQUEsUUFBUTNCLFVBQVUsY0FBbEIyQiwwQ0FBQUEsb0JBQW9CRSxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0YsbUJBQzNDRCxpQkFBQUEsUUFBUTFCLEtBQUssY0FBYjBCLHFDQUFBQSxlQUFlRSxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0YsbUJBQ3RDRCxrQkFBQUEsUUFBUTVCLE1BQU0sY0FBZDRCLHNDQUFBQSxnQkFBZ0JFLFdBQVcsR0FBR0MsUUFBUSxDQUFDRjtRQUUzQztRQUVBLE9BQU87WUFDTCxHQUFHUCxPQUFPO1lBQ1YxRCxNQUFNOEQ7WUFDTk0sTUFBTTtnQkFDSixHQUFHLFFBQWlCQSxJQUFJO2dCQUN4QjdCLFlBQVl1QixhQUFhTyxNQUFNO2dCQUMvQjdCLFlBQVk4QixLQUFLQyxJQUFJLENBQUNULGFBQWFPLE1BQU0sR0FBR2xFO1lBQzlDO1lBQ0EsaURBQWlEO1lBQ2pEb0MsWUFBWXVCLGFBQWFPLE1BQU07WUFDL0I3QixZQUFZOEIsS0FBS0MsSUFBSSxDQUFDVCxhQUFhTyxNQUFNLEdBQUdsRTtRQUM5QztJQUNGLEdBQUc7UUFBQ3VEO1FBQVNuRDtRQUFhb0Q7UUFBaUJ4RDtLQUFNO0lBRWpELGtCQUFrQjtJQUNsQixNQUFNcUUsaUJBQWlCckcsbUVBQVdBLENBQUM7UUFDakNzRyxZQUFZLENBQUNUO1lBQ1gsTUFBTVUsWUFBWVYsUUFBUVcsVUFBVSxJQUFJWCxRQUFRbEMsRUFBRTtZQUNsRCxPQUFPaEQsMERBQVdBLENBQUM4RixhQUFhLENBQUNGO1FBQ25DO1FBQ0FHLFdBQVc7WUFDVGpELFlBQVlrRCxpQkFBaUIsQ0FBQztnQkFBRWpDLFVBQVU7b0JBQUM7aUJBQVc7WUFBQztZQUN2RFUsUUFBUUMsR0FBRyxDQUFDO1lBQ1oxQyxtQkFBbUI7WUFDbkJFLG1CQUFtQjtRQUNyQjtRQUNBc0MsU0FBUyxDQUFDWDtZQUNSWSxRQUFRWixLQUFLLENBQUMsNkJBQTZCQSxNQUFNYyxPQUFPO1FBQzFEO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTXNCLFVBQTZCO1FBQ2pDO1lBQ0VDLEtBQUs7WUFDTEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLFFBQVEsQ0FBQ0Msc0JBQ1AsOERBQUNDO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDMUYsOEVBQWVBO3dCQUNkMkYsVUFBVUg7d0JBQ1ZJLFVBQVU7d0JBQ1ZDLFVBQVU7d0JBQ1ZDLGFBQWE7d0JBQ2JDLFNBQVM7NEJBQ1AsTUFBTUMsY0FBYyxJQUFJQyxLQUFLVDs0QkFDN0I5RCxnQkFBZ0JzRTs0QkFDaEJuRSx1QkFBdUI7d0JBQ3pCO3dCQUNBNkQsV0FBVTs7Ozs7Ozs7Ozs7UUFJbEI7UUFDQTtZQUNFTixLQUFLO1lBQ0xDLE9BQU87WUFDUEMsVUFBVTtZQUNWWSxpQkFBaUI7WUFDakJYLFFBQVEsQ0FBQ1ksR0FBR0Msb0JBQ1YsOERBQUNYO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7O2dDQUNaVSxJQUFJL0QsWUFBWSxrQkFDZiw4REFBQ2dFO29DQUNDQyxLQUFLLEdBQWlERixPQUE5Q0csc0JBQTBDLEVBQUMsS0FBb0IsT0FBakJILElBQUkvRCxZQUFZO29DQUN0RXFFLEtBQUtOLElBQUlqRSxZQUFZO29DQUNyQnVELFdBQVU7b0NBQ1ZoQyxTQUFTLENBQUNpRDt3Q0FDUkEsRUFBRUMsYUFBYSxDQUFDQyxLQUFLLENBQUNDLE9BQU8sR0FBRztvQ0FDbEM7Ozs7Ozs4Q0FHSiw4REFBQ0M7b0NBQUtyQixXQUFVOzhDQUNiVSxJQUFJakUsWUFBWTs7Ozs7Ozs7Ozs7O3NDQUtyQiw4REFBQ3NEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDcUI7Z0NBQUtyQixXQUFVOzBDQUFrQzs7Ozs7Ozs7Ozs7c0NBSXBELDhEQUFDRDs0QkFBSUMsV0FBVTs7Z0NBQ1pVLElBQUk5RCxZQUFZLGtCQUNmLDhEQUFDK0Q7b0NBQ0NDLEtBQUssR0FBaURGLE9BQTlDRyxzQkFBMEMsRUFBQyxLQUFvQixPQUFqQkgsSUFBSTlELFlBQVk7b0NBQ3RFb0UsS0FBS04sSUFBSWhFLFlBQVk7b0NBQ3JCc0QsV0FBVTtvQ0FDVmhDLFNBQVMsQ0FBQ2lEO3dDQUNSQSxFQUFFQyxhQUFhLENBQUNDLEtBQUssQ0FBQ0MsT0FBTyxHQUFHO29DQUNsQzs7Ozs7OzhDQUdKLDhEQUFDQztvQ0FBS3JCLFdBQVU7OENBQ2JVLElBQUloRSxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFLM0I7UUFDQTtZQUNFZ0QsS0FBSztZQUNMQyxPQUFPO1lBQ1AyQixPQUFPO1lBQ1B6QixRQUFRLENBQUNZLEdBQUdDO29CQUdMQSxnQkFBeUJBO3VCQUY5Qiw4REFBQ1g7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7Z0NBQ1pVLENBQUFBLGlCQUFBQSxJQUFJYSxTQUFTLGNBQWJiLDRCQUFBQSxpQkFBaUI7Z0NBQUk7Z0NBQUlBLENBQUFBLGlCQUFBQSxJQUFJYyxTQUFTLGNBQWJkLDRCQUFBQSxpQkFBaUI7Ozs7Ozs7d0JBRTNDQSxJQUFJZSxpQkFBaUIsS0FBSyxRQUFRZixJQUFJZ0IsaUJBQWlCLEtBQUssc0JBQzVELDhEQUFDM0I7NEJBQUlDLFdBQVU7O2dDQUF3QjtnQ0FDaENVLElBQUllLGlCQUFpQjtnQ0FBQztnQ0FBSWYsSUFBSWdCLGlCQUFpQjs7Ozs7Ozs7Ozs7OztZQUdyRDtRQUVUO1FBQ0E7WUFDRWhDLEtBQUs7WUFDTEMsT0FBTztZQUNQQyxVQUFVO1lBQ1YrQixZQUFZO1lBQ1o5QixRQUFRLENBQUNDLE9BQU9ZO2dCQUNkLE1BQU1rQixpQkFBaUIsQ0FBQzlFO29CQUN0QixPQUFRQTt3QkFDTixLQUFLO3dCQUNMLEtBQUs7d0JBQ0wsS0FBSzs0QkFDSCxPQUFPO3dCQUNULEtBQUs7NEJBQ0gsT0FBTzt3QkFDVCxLQUFLOzRCQUNILE9BQU87d0JBQ1QsS0FBSzt3QkFDTCxLQUFLOzRCQUNILE9BQU87d0JBQ1Q7NEJBQ0UsT0FBTztvQkFDWDtnQkFDRjtnQkFFQSxNQUFNK0UsZ0JBQWdCLENBQUMvRSxRQUFnQmdGO29CQUNyQyxPQUFRaEY7d0JBQ04sS0FBSzt3QkFDTCxLQUFLOzRCQUNILE9BQU8sR0FBVyxPQUFSZ0YsU0FBUTt3QkFDcEIsS0FBSzs0QkFDSCxPQUFPO3dCQUNULEtBQUs7NEJBQ0gsT0FBTzt3QkFDVCxLQUFLOzRCQUNILE9BQU87d0JBQ1QsS0FBSzs0QkFDSCxPQUFPO3dCQUNULEtBQUs7NEJBQ0gsT0FBTzt3QkFDVDs0QkFDRSxPQUFPaEY7b0JBQ1g7Z0JBQ0Y7Z0JBRUEscUJBQ0UsOERBQUN6RCx1REFBS0E7b0JBQUMyRyxXQUFXNEIsZUFBZTlCOzhCQUM5QitCLGNBQWMvQixPQUFPWSxJQUFJb0IsT0FBTzs7Ozs7O1lBR3ZDO1FBQ0Y7UUFDQTtZQUNFcEMsS0FBSztZQUNMQyxPQUFPO1lBQ1BDLFVBQVU7WUFDVitCLFlBQVk7WUFDWjlCLFFBQVEsQ0FBQ0Msc0JBQ1AsOERBQUN1QjtvQkFBS3JCLFdBQVU7OEJBQXlCRjs7Ozs7O1FBRTdDO1FBQ0E7WUFDRUosS0FBSztZQUNMQyxPQUFPO1lBQ1BFLFFBQVEsQ0FBQ1ksR0FBR0Msb0JBQ1YsOERBQUNYO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQzVHLHlEQUFNQTs0QkFDTDJJLE1BQUs7NEJBQ0xDLFNBQVE7NEJBQ1JyQyxPQUFNOzRCQUNOVSxTQUFTLElBQU00QixrQkFBa0J2QjtzQ0FFakMsNEVBQUNqSCw0SUFBR0E7Z0NBQUN1RyxXQUFVOzs7Ozs7Ozs7OztzQ0FFakIsOERBQUM1Ryx5REFBTUE7NEJBQ0wySSxNQUFLOzRCQUNMQyxTQUFROzRCQUNSckMsT0FBTTs0QkFDTlUsU0FBUyxJQUFNNkIsMEJBQTBCeEI7c0NBRXpDLDRFQUFDekcsNElBQUtBO2dDQUFDK0YsV0FBVTs7Ozs7Ozs7Ozs7d0JBRWxCM0QsNEJBQ0MsOERBQUNqRCx5REFBTUE7NEJBQ0wySSxNQUFLOzRCQUNMQyxTQUFROzRCQUNSckMsT0FBTTs0QkFDTlUsU0FBUyxJQUFNOEIsa0JBQWtCekI7c0NBRWpDLDRFQUFDaEgsNElBQUlBO2dDQUFDc0csV0FBVTs7Ozs7Ozs7Ozs7d0JBR25CNUQsMkJBQ0MsOERBQUNoRCx5REFBTUE7NEJBQ0wySSxNQUFLOzRCQUNMQyxTQUFROzRCQUNSckMsT0FBTTs0QkFDTlUsU0FBUyxJQUFNK0Isb0JBQW9CMUI7c0NBRW5DLDRFQUFDL0csNElBQU1BO2dDQUFDcUcsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFLNUI7S0FDRDtJQUVELG9CQUFvQjtJQUNwQixNQUFNcUMsZUFBZTtRQUNuQm5ILGVBQWVILFlBQVkyQyxJQUFJO1FBQy9COUMsUUFBUSxJQUFJLHFDQUFxQztJQUNuRDtJQUVBLE1BQU0wSCx1QkFBdUIsQ0FBQ3JCO1FBQzVCLElBQUlBLEVBQUV2QixHQUFHLEtBQUssU0FBUztZQUNyQjJDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1FLG9CQUFvQjtRQUN4QnZILGVBQWU7UUFDZkUsZUFBZTtRQUNmTixRQUFRO0lBQ1Y7SUFFQSxNQUFNcUgsb0JBQW9CLENBQUN2RDtRQUN6QixtREFBbUQ7UUFDbkQsTUFBTVUsWUFBWVYsUUFBUVcsVUFBVSxJQUFJWCxRQUFRbEMsRUFBRTtRQUNsRGdHLE9BQU9DLElBQUksQ0FBQyx1QkFBaUMsT0FBVnJELFlBQWE7SUFDbEQ7SUFFQSxNQUFNK0Msb0JBQW9CLENBQUN6RDtRQUN6Qix5Q0FBeUM7UUFDekMsTUFBTVUsWUFBWVYsUUFBUVcsVUFBVSxJQUFJWCxRQUFRbEMsRUFBRTtRQUNsRGdHLE9BQU9DLElBQUksQ0FBQyx1QkFBaUMsT0FBVnJELFdBQVUsVUFBUTtJQUN2RDtJQUVBLE1BQU1nRCxzQkFBc0IsQ0FBQzFEO1FBQzNCaEQsbUJBQW1CZ0Q7UUFDbkJsRCxtQkFBbUI7SUFDckI7SUFFQSxNQUFNa0gsdUJBQXVCLENBQUNoRTtRQUM1QjVDLCtCQUErQjRDO1FBQy9COUMsMkJBQTJCO0lBQzdCO0lBRUEsTUFBTStHLDJCQUEyQixDQUFDakU7UUFDaEM1QywrQkFBK0I0QztRQUMvQjlDLDJCQUEyQjtJQUM3QjtJQUVBLE1BQU1nSCxnQkFBZ0I7UUFDcEIsSUFBSW5ILGlCQUFpQjtZQUNuQnlELGVBQWUyRCxNQUFNLENBQUNwSDtRQUN4QjtJQUNGO0lBRUEsTUFBTXFILGlCQUFpQjtRQUNyQixJQUFJO1lBQ0Y3RSxRQUFRQyxHQUFHLENBQUM7WUFDWiwrQkFBK0I7WUFDL0Isb0NBQW9DO1lBQ3BDRCxRQUFRQyxHQUFHLENBQUM7WUFDWlo7UUFDRixFQUFFLE9BQU9ELE9BQVk7WUFDbkJZLFFBQVFaLEtBQUssQ0FBQyxnQkFBZ0JBLE1BQU1jLE9BQU87UUFDN0M7SUFDRjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNNEUsd0JBQXdCLENBQUNsRztRQUM3QmIsZ0JBQWdCYTtRQUNoQmpDLFFBQVEsSUFBSSxxQ0FBcUM7SUFDbkQ7SUFFQSxNQUFNb0ksd0JBQXdCO1FBQzVCaEgsZ0JBQWdCQztRQUNoQnJCLFFBQVEsSUFBSSwyQ0FBMkM7SUFDekQ7SUFFQSxJQUFJeUMsT0FBTztRQUNULHFCQUNFLDhEQUFDMEM7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEOztzQ0FDQyw4REFBQ2tEOzRCQUFHakQsV0FBVTtzQ0FBbUM7Ozs7OztzQ0FDakQsOERBQUNrRDs0QkFBRWxELFdBQVU7c0NBQXFCOzs7Ozs7Ozs7Ozs7OEJBR3BDLDhEQUFDakgscURBQUlBOzhCQUNILDRFQUFDQyw0REFBV0E7d0JBQUNnSCxXQUFVO2tDQUNyQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDa0Q7b0NBQUVsRCxXQUFVOzhDQUFvQjs7Ozs7OzhDQUNqQyw4REFBQzVHLHlEQUFNQTtvQ0FBQ2lILFNBQVMsSUFBTS9DOztzREFDckIsOERBQUN6RCw0SUFBU0E7NENBQUNtRyxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVFwRDtJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDs7MENBQ0MsOERBQUNrRDtnQ0FBR2pELFdBQVU7MENBQW1DOzs7Ozs7MENBQ2pELDhEQUFDa0Q7Z0NBQUVsRCxXQUFVOzBDQUFxQjs7Ozs7Ozs7Ozs7O2tDQUdwQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDNUcseURBQU1BO2dDQUNMNEksU0FBUTtnQ0FDUjNCLFNBQVMsSUFBTS9DO2dDQUNmNkYsVUFBVS9GOztrREFFViw4REFBQ3ZELDRJQUFTQTt3Q0FBQ21HLFdBQVcsZ0JBQWdELE9BQWhDNUMsWUFBWSxpQkFBaUI7Ozs7OztvQ0FBUTs7Ozs7Ozs0QkFJNUVoQiwyQkFDQyw4REFBQ2hELHlEQUFNQTtnQ0FDTDRJLFNBQVE7Z0NBQ1IzQixTQUFTeUM7Z0NBQ1RLLFVBQVUvRjs7a0RBRVYsOERBQUNwRCw0SUFBTUE7d0NBQUNnRyxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7OzBDQUt2Qyw4REFBQzVHLHlEQUFNQTtnQ0FDTDRJLFNBQVE7Z0NBQ1IzQixTQUFTO29DQUNQLHVCQUF1QjtvQ0FDdkJwQyxRQUFRQyxHQUFHLENBQUM7Z0NBQ2Q7O2tEQUVBLDhEQUFDbkUsNElBQVFBO3dDQUFDaUcsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs0QkFJdEMzRCw0QkFDQyw4REFBQ2pELHlEQUFNQTtnQ0FBQ2lILFNBQVMsSUFBTW1DLE9BQU9DLElBQUksQ0FBQyw4QkFBOEI7O2tEQUMvRCw4REFBQzdJLDRJQUFJQTt3Q0FBQ29HLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXpDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNqSCxxREFBSUE7a0NBQ0gsNEVBQUNDLDREQUFXQTs0QkFBQ2dILFdBQVU7OzhDQUNyQiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1p0RixDQUFBQSxpQkFBQUEsNEJBQUFBLGFBQUFBLEtBQU1vRSxJQUFJLGNBQVZwRSxrQ0FBQUEsd0JBQUFBLFdBQVl1QyxVQUFVLGNBQXRCdkMsNENBQUFBLHNCQUF3QjBJLGNBQWMsT0FBTTs7Ozs7OzhDQUUvQyw4REFBQ0Y7b0NBQUVsRCxXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSXpDLDhEQUFDakgscURBQUlBO2tDQUNILDRFQUFDQyw0REFBV0E7NEJBQUNnSCxXQUFVOzs4Q0FDckIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNadEYsQ0FBQUEsaUJBQUFBLDRCQUFBQSxhQUFBQSxLQUFNQSxJQUFJLGNBQVZBLGlDQUFBQSxXQUFZK0QsTUFBTSxDQUFDNEUsQ0FBQUEsSUFBSzs0Q0FBQzs0Q0FBTTs0Q0FBTTt5Q0FBSyxDQUFDeEUsUUFBUSxDQUFDd0UsRUFBRXZHLE1BQU0sR0FBR2lDLE1BQU0sS0FBSTs7Ozs7OzhDQUU1RSw4REFBQ21FO29DQUFFbEQsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUl6Qyw4REFBQ2pILHFEQUFJQTtrQ0FDSCw0RUFBQ0MsNERBQVdBOzRCQUFDZ0gsV0FBVTs7OENBQ3JCLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWnRGLENBQUFBLGlCQUFBQSw0QkFBQUEsY0FBQUEsS0FBTUEsSUFBSSxjQUFWQSxrQ0FBQUEsWUFBWStELE1BQU0sQ0FBQzRFLENBQUFBLElBQUtBLEVBQUV2RyxNQUFNLEtBQUssTUFBTWlDLE1BQU0sS0FBSTs7Ozs7OzhDQUV4RCw4REFBQ21FO29DQUFFbEQsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUl6Qyw4REFBQ2pILHFEQUFJQTtrQ0FDSCw0RUFBQ0MsNERBQVdBOzRCQUFDZ0gsV0FBVTs7OENBQ3JCLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWnRGLENBQUFBLGlCQUFBQSw0QkFBQUEsY0FBQUEsS0FBTUEsSUFBSSxjQUFWQSxrQ0FBQUEsWUFBWStELE1BQU0sQ0FBQzRFLENBQUFBLElBQUtBLEVBQUV2RyxNQUFNLEtBQUssTUFBTWlDLE1BQU0sS0FBSTs7Ozs7OzhDQUV4RCw4REFBQ21FO29DQUFFbEQsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU0zQyw4REFBQ2pILHFEQUFJQTs7a0NBQ0gsOERBQUNHLDJEQUFVQTtrQ0FDVCw0RUFBQzZHOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7O3NEQUNDLDhEQUFDNUcsMERBQVNBOzRDQUFDNkcsV0FBVTs7OERBQ25CLDhEQUFDbEcsNElBQVFBO29EQUFDa0csV0FBVTs7Ozs7O2dEQUFpQjtnREFFcEMzQixpQ0FDQyw4REFBQ2dEO29EQUFLckIsV0FBVTs4REFBMkU7Ozs7Ozs7Ozs7OztzREFLL0YsOERBQUMvRyxnRUFBZUE7c0RBQ2JvRixrQkFDRyxxREFDQTs7Ozs7Ozs7Ozs7OzhDQUlSLDhEQUFDMEI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDM0YsbUVBQVVBOzRDQUNUd0MsTUFBTWQ7NENBQ051SCxjQUFjdEg7NENBQ2R1SCxhQUFZOzRDQUNadkQsV0FBVTs7Ozs7O3dDQUVYakUsOEJBQ0MsOERBQUMzQyx5REFBTUE7NENBQ0w0SSxTQUFROzRDQUNSRCxNQUFLOzRDQUNMMUIsU0FBUyxJQUFNckUsZ0JBQWdCQzs0Q0FDL0IrRCxXQUFVOzRDQUNWTCxPQUFNO3NEQUVOLDRFQUFDekYsNElBQUNBO2dEQUFDOEYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNdkIsOERBQUNoSCw0REFBV0E7a0NBQ1RvRSwwQkFDQyw4REFBQ2pELGtFQUFhQTs0QkFBQ3FKLE1BQU07NEJBQUkvRCxTQUFTOzs7OztpREFFbEMsOERBQUNuRyxnRUFBU0E7NEJBQ1JvQixNQUFPNEQsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjNUQsSUFBSSxLQUFJLEVBQUU7NEJBQy9CK0UsU0FBU0E7NEJBQ1RnRSxTQUFTckcsYUFBYSxDQUFDaUI7NEJBQ3ZCcUYsWUFBWTs0QkFDWkMsbUJBQWtCOzRCQUNsQkMsa0JBQWtCOzRCQUNsQkMsYUFBYTlJOzRCQUNiK0ksZ0JBQWdCOUk7NEJBQ2hCK0ksZ0JBQWdCMUI7NEJBQ2hCMkIsZUFBZXpCOzRCQUNmMEIsWUFBWTtnQ0FDVnRKO2dDQUNBRTtnQ0FDQXFKLE9BQU8sQ0FBQzVGLHlCQUFBQSxvQ0FBRCxrQ0FBdUJRLElBQUksY0FBM0IsNERBQTZCN0IsVUFBVSxNQUFLcUIseUJBQUFBLG1DQUFELGFBQXVCckIsVUFBVSxLQUFJO2dDQUN2RmtILGNBQWN2SjtnQ0FDZHdKLGVBQWUsQ0FBQ0M7b0NBQ2R2SixTQUFTdUo7b0NBQ1R6SixRQUFRLElBQUksMENBQTBDO2dDQUN4RDs0QkFDRjs0QkFDQTBKLGNBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9yQiw4REFBQy9LLDhEQUFZQTtnQkFDWGdMLFFBQVFoSjtnQkFDUmlKLFNBQVM7b0JBQ1BoSixtQkFBbUI7b0JBQ25CRSxtQkFBbUI7Z0JBQ3JCO2dCQUNBK0ksV0FBVzdCO2dCQUNYakQsT0FBTTtnQkFDTnhCLFNBQ0UxQyxrQkFDSSxnREFBbUZBLE9BQW5DQSxnQkFBZ0JnQixZQUFZLEVBQUMsUUFBbUMsT0FBN0JoQixnQkFBZ0JpQixZQUFZLEVBQUMsc0NBQ2hIO2dCQUVOZ0ksYUFBWTtnQkFDWkMsWUFBVztnQkFDWDNDLFNBQVE7Z0JBQ1J5QixTQUFTdkUsZUFBZTlCLFNBQVM7Ozs7OzswQkFnQm5DLDhEQUFDN0MsOEVBQWVBO2dCQUNkZ0ssUUFBUXJJO2dCQUNSc0ksU0FBUyxJQUFNckksdUJBQXVCO2dCQUN0Q0osY0FBY0E7Z0JBQ2Q2SSxjQUFjNUk7Z0JBQ2Q2SSxlQUFlOUI7Z0JBQ2YrQixlQUFlOUI7Ozs7Ozs7Ozs7OztBQUl2QjtHQXBwQndCdkk7O1FBY1FMLHNFQUFjQTtRQUN4QnRCLGtFQUFjQTtRQW9FVUYsNERBQVFBO1FBaUU3QkMsK0RBQVdBOzs7S0FwSlo0QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2Rhc2hib2FyZC9maXh0dXJlcy9wYWdlLnRzeD8wNTMzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUXVlcnksIHVzZU11dGF0aW9uLCB1c2VRdWVyeUNsaWVudCB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJztcbmltcG9ydCB7IERhdGFUYWJsZSwgQ29sdW1uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2RhdGEtdGFibGUnO1xuaW1wb3J0IHsgQ29uZmlybU1vZGFsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL21vZGFsJztcbmltcG9ydCB7IGZpeHR1cmVzQXBpIH0gZnJvbSAnQC9saWIvYXBpL2ZpeHR1cmVzJztcbmltcG9ydCB7IEZpeHR1cmUgfSBmcm9tICdAL2xpYi90eXBlcy9hcGknO1xuaW1wb3J0IHsgRXllLCBFZGl0LCBUcmFzaDIsIFBsdXMsIFJlZnJlc2hDdywgQ2FsZW5kYXIsIEZpbHRlciwgRG93bmxvYWQsIFVwbG9hZCwgUmFkaW8sIExpc3QsIFgsIFNlYXJjaCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBUYWJsZVNrZWxldG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NrZWxldG9uJztcbmltcG9ydCB7IHVzZVBlcm1pc3Npb25zIH0gZnJvbSAnQC9saWIvbWlkZGxld2FyZS9hdXRoLWd1YXJkJztcbi8vIGltcG9ydCB7IEJyb2FkY2FzdExpbmtzTW9kYWwgfSBmcm9tICdAL2NvbXBvbmVudHMvZml4dHVyZXMvQnJvYWRjYXN0TGlua3NNb2RhbCc7XG5pbXBvcnQgeyBEYXRlUGlja2VyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2RhdGUtcGlja2VyJztcbmltcG9ydCB7IERhdGVUaW1lRGlzcGxheSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9kYXRlLXRpbWUtZGlzcGxheSc7XG5pbXBvcnQgeyBEYXRlRmlsdGVyTW9kYWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvZGF0ZS1maWx0ZXItbW9kYWwnO1xuaW1wb3J0IHsgaXNTYW1lRGF0ZSwgY29udmVydExvY2FsRGF0ZVRvVVRDIH0gZnJvbSAnQC9saWIvdXRpbHMvZGF0ZS10aW1lJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRml4dHVyZXNQYWdlKCkge1xuICBjb25zdCBbcGFnZSwgc2V0UGFnZV0gPSB1c2VTdGF0ZSgxKTtcbiAgY29uc3QgW2xpbWl0LCBzZXRMaW1pdF0gPSB1c2VTdGF0ZSgyNSk7XG4gIGNvbnN0IFtzZWFyY2hJbnB1dCwgc2V0U2VhcmNoSW5wdXRdID0gdXNlU3RhdGUoJycpOyAvLyBJbnB1dCB2YWx1ZVxuICBjb25zdCBbc2VhcmNoUXVlcnksIHNldFNlYXJjaFF1ZXJ5XSA9IHVzZVN0YXRlKCcnKTsgLy8gQWN0dWFsIHNlYXJjaCBxdWVyeSBmb3IgQVBJXG4gIGNvbnN0IFtzdGF0dXNGaWx0ZXIsIHNldFN0YXR1c0ZpbHRlcl0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtsZWFndWVGaWx0ZXIsIHNldExlYWd1ZUZpbHRlcl0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtkZWxldGVNb2RhbE9wZW4sIHNldERlbGV0ZU1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzZWxlY3RlZEZpeHR1cmUsIHNldFNlbGVjdGVkRml4dHVyZV0gPSB1c2VTdGF0ZTxGaXh0dXJlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFticm9hZGNhc3RMaW5rc01vZGFsT3Blbiwgc2V0QnJvYWRjYXN0TGlua3NNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2VsZWN0ZWRGaXh0dXJlRm9yQnJvYWRjYXN0LCBzZXRTZWxlY3RlZEZpeHR1cmVGb3JCcm9hZGNhc3RdID0gdXNlU3RhdGU8Rml4dHVyZSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2VsZWN0ZWREYXRlLCBzZXRTZWxlY3RlZERhdGVdID0gdXNlU3RhdGU8RGF0ZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcbiAgY29uc3QgW2RhdGVGaWx0ZXJNb2RhbE9wZW4sIHNldERhdGVGaWx0ZXJNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IHsgaXNBZG1pbiwgaXNFZGl0b3IgfSA9IHVzZVBlcm1pc3Npb25zKCk7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKTtcblxuICAvLyBNb2NrIGRhdGEgZm9yIHRlc3Rpbmcgd2hlbiBBUEkgaXMgZG93blxuICBjb25zdCBtb2NrRml4dHVyZXMgPSB7XG4gICAgZGF0YTogW1xuICAgICAge1xuICAgICAgICBpZDogMSxcbiAgICAgICAgaG9tZVRlYW1OYW1lOiAnTWFuY2hlc3RlciBVbml0ZWQnLFxuICAgICAgICBhd2F5VGVhbU5hbWU6ICdMaXZlcnBvb2wnLFxuICAgICAgICBob21lVGVhbUxvZ286ICcvaW1hZ2VzL3RlYW1zLzEucG5nJyxcbiAgICAgICAgYXdheVRlYW1Mb2dvOiAnL2ltYWdlcy90ZWFtcy8yLnBuZycsXG4gICAgICAgIGRhdGU6ICcyMDI0LTEyLTE5VDE0OjMwOjAwWicsXG4gICAgICAgIHN0YXR1czogJ05TJyxcbiAgICAgICAgbGVhZ3VlTmFtZTogJ1ByZW1pZXIgTGVhZ3VlJyxcbiAgICAgICAgdmVudWU6ICdPbGQgVHJhZmZvcmQnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogMixcbiAgICAgICAgaG9tZVRlYW1OYW1lOiAnQXJzZW5hbCcsXG4gICAgICAgIGF3YXlUZWFtTmFtZTogJ0NoZWxzZWEnLFxuICAgICAgICBob21lVGVhbUxvZ286ICcvaW1hZ2VzL3RlYW1zLzMucG5nJyxcbiAgICAgICAgYXdheVRlYW1Mb2dvOiAnL2ltYWdlcy90ZWFtcy80LnBuZycsXG4gICAgICAgIGRhdGU6ICcyMDI0LTEyLTIwVDE2OjAwOjAwWicsXG4gICAgICAgIHN0YXR1czogJ05TJyxcbiAgICAgICAgbGVhZ3VlTmFtZTogJ1ByZW1pZXIgTGVhZ3VlJyxcbiAgICAgICAgdmVudWU6ICdFbWlyYXRlcyBTdGFkaXVtJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IDMsXG4gICAgICAgIGhvbWVUZWFtTmFtZTogJ0JhcmNlbG9uYScsXG4gICAgICAgIGF3YXlUZWFtTmFtZTogJ1JlYWwgTWFkcmlkJyxcbiAgICAgICAgaG9tZVRlYW1Mb2dvOiAnL2ltYWdlcy90ZWFtcy81LnBuZycsXG4gICAgICAgIGF3YXlUZWFtTG9nbzogJy9pbWFnZXMvdGVhbXMvNi5wbmcnLFxuICAgICAgICBkYXRlOiAnMjAyNC0xMi0yMVQyMDowMDowMFonLFxuICAgICAgICBzdGF0dXM6ICdMSVZFJyxcbiAgICAgICAgbGVhZ3VlTmFtZTogJ0xhIExpZ2EnLFxuICAgICAgICB2ZW51ZTogJ0NhbXAgTm91J1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IDQsXG4gICAgICAgIGhvbWVUZWFtTmFtZTogJ0JheWVybiBNdW5pY2gnLFxuICAgICAgICBhd2F5VGVhbU5hbWU6ICdCb3J1c3NpYSBEb3J0bXVuZCcsXG4gICAgICAgIGhvbWVUZWFtTG9nbzogJy9pbWFnZXMvdGVhbXMvNy5wbmcnLFxuICAgICAgICBhd2F5VGVhbUxvZ286ICcvaW1hZ2VzL3RlYW1zLzgucG5nJyxcbiAgICAgICAgZGF0ZTogJzIwMjQtMTItMThUMTg6MzA6MDBaJyxcbiAgICAgICAgc3RhdHVzOiAnRlQnLFxuICAgICAgICBsZWFndWVOYW1lOiAnQnVuZGVzbGlnYScsXG4gICAgICAgIHZlbnVlOiAnQWxsaWFueiBBcmVuYSdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiA1LFxuICAgICAgICBob21lVGVhbU5hbWU6ICdQU0cnLFxuICAgICAgICBhd2F5VGVhbU5hbWU6ICdNYXJzZWlsbGUnLFxuICAgICAgICBob21lVGVhbUxvZ286ICcvaW1hZ2VzL3RlYW1zLzkucG5nJyxcbiAgICAgICAgYXdheVRlYW1Mb2dvOiAnL2ltYWdlcy90ZWFtcy8xMC5wbmcnLFxuICAgICAgICBkYXRlOiAnMjAyNC0xMi0yMlQyMTowMDowMFonLFxuICAgICAgICBzdGF0dXM6ICdOUycsXG4gICAgICAgIGxlYWd1ZU5hbWU6ICdMaWd1ZSAxJyxcbiAgICAgICAgdmVudWU6ICdQYXJjIGRlcyBQcmluY2VzJ1xuICAgICAgfVxuICAgIF0sXG4gICAgdG90YWxJdGVtczogNSxcbiAgICB0b3RhbFBhZ2VzOiAxLFxuICAgIGN1cnJlbnRQYWdlOiAxLFxuICAgIGxpbWl0OiAyNVxuICB9O1xuXG4gIC8vIEZldGNoIGZpeHR1cmVzIGRhdGEgd2l0aCBmYWxsYmFjayB0byBtb2NrIGRhdGFcbiAgY29uc3QgeyBkYXRhLCBpc0xvYWRpbmcsIGVycm9yLCByZWZldGNoIH0gPSB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsnZml4dHVyZXMnLCAnYWxsJywgcGFnZSwgbGltaXQsIHNlYXJjaFF1ZXJ5LCBzdGF0dXNGaWx0ZXIsIGxlYWd1ZUZpbHRlciwgc2VsZWN0ZWREYXRlXSxcbiAgICBxdWVyeUZuOiAoKSA9PiB7XG4gICAgICBjb25zdCBmaWx0ZXJzOiBhbnkgPSB7XG4gICAgICAgIHBhZ2UsXG4gICAgICAgIGxpbWl0LFxuICAgICAgfTtcblxuICAgICAgLy8gQWRkIHNlYXJjaCBxdWVyeSBpZiBwcm92aWRlZFxuICAgICAgaWYgKHNlYXJjaFF1ZXJ5ICYmIHNlYXJjaFF1ZXJ5LnRyaW0oKSkge1xuICAgICAgICAvLyBGb3IgQVBJLCB3ZSBtaWdodCBuZWVkIHRvIHNlYXJjaCBieSB0ZWFtIG5hbWVzIG9yIG90aGVyIGZpZWxkc1xuICAgICAgICAvLyBTaW5jZSBBUEkgZG9lc24ndCBoYXZlIGEgZ2VuZXJpYyBzZWFyY2gsIHdlJ2xsIGZpbHRlciBjbGllbnQtc2lkZSBmb3Igbm93XG4gICAgICAgIGZpbHRlcnMuc2VhcmNoID0gc2VhcmNoUXVlcnkudHJpbSgpO1xuICAgICAgfVxuXG4gICAgICBpZiAoc3RhdHVzRmlsdGVyKSBmaWx0ZXJzLnN0YXR1cyA9IHN0YXR1c0ZpbHRlcjtcbiAgICAgIGlmIChsZWFndWVGaWx0ZXIpIGZpbHRlcnMubGVhZ3VlID0gbGVhZ3VlRmlsdGVyO1xuICAgICAgaWYgKHNlbGVjdGVkRGF0ZSkgZmlsdGVycy5kYXRlID0gY29udmVydExvY2FsRGF0ZVRvVVRDKHNlbGVjdGVkRGF0ZSk7XG5cbiAgICAgIHJldHVybiBmaXh0dXJlc0FwaS5nZXRGaXh0dXJlcyhmaWx0ZXJzKTtcbiAgICB9LFxuICAgIHN0YWxlVGltZTogMzAwMDAsIC8vIDMwIHNlY29uZHNcbiAgICByZXRyeTogZmFsc2UsIC8vIERvbid0IHJldHJ5IHdoZW4gQVBJIGlzIGRvd25cbiAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ0FQSSBpcyBkb3duLCB1c2luZyBtb2NrIGRhdGE6JywgZXJyb3I/Lm1lc3NhZ2UgfHwgJ1Vua25vd24gZXJyb3InKTtcbiAgICB9XG4gIH0pO1xuXG4gIC8vIFVzZSBtb2NrIGRhdGEgaWYgQVBJIGZhaWxzIG9yIG5vIGRhdGFcbiAgY29uc3QgcmF3RGF0YSA9IGRhdGEgfHwgbW9ja0ZpeHR1cmVzO1xuICBjb25zdCBpc1VzaW5nTW9ja0RhdGEgPSAhZGF0YTtcblxuICAvLyBBcHBseSBjbGllbnQtc2lkZSBmaWx0ZXJpbmcgZm9yIG1vY2sgZGF0YSB3aGVuIHNlYXJjaCBpcyBhY3RpdmVcbiAgY29uc3QgZml4dHVyZXNEYXRhID0gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgaWYgKCFpc1VzaW5nTW9ja0RhdGEgfHwgIXNlYXJjaFF1ZXJ5LnRyaW0oKSkge1xuICAgICAgcmV0dXJuIHJhd0RhdGE7XG4gICAgfVxuXG4gICAgLy8gRmlsdGVyIG1vY2sgZGF0YSBiYXNlZCBvbiBzZWFyY2ggcXVlcnlcbiAgICBjb25zdCBmaWx0ZXJlZERhdGEgPSByYXdEYXRhLmRhdGEuZmlsdGVyKChmaXh0dXJlOiBhbnkpID0+IHtcbiAgICAgIGNvbnN0IHNlYXJjaExvd2VyID0gc2VhcmNoUXVlcnkudG9Mb3dlckNhc2UoKTtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIGZpeHR1cmUuaG9tZVRlYW1OYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKSB8fFxuICAgICAgICBmaXh0dXJlLmF3YXlUZWFtTmFtZT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hMb3dlcikgfHxcbiAgICAgICAgZml4dHVyZS5sZWFndWVOYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKSB8fFxuICAgICAgICBmaXh0dXJlLnZlbnVlPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKSB8fFxuICAgICAgICBmaXh0dXJlLnN0YXR1cz8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hMb3dlcilcbiAgICAgICk7XG4gICAgfSk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgLi4ucmF3RGF0YSxcbiAgICAgIGRhdGE6IGZpbHRlcmVkRGF0YSxcbiAgICAgIG1ldGE6IHtcbiAgICAgICAgLi4uKHJhd0RhdGEgYXMgYW55KS5tZXRhLFxuICAgICAgICB0b3RhbEl0ZW1zOiBmaWx0ZXJlZERhdGEubGVuZ3RoLFxuICAgICAgICB0b3RhbFBhZ2VzOiBNYXRoLmNlaWwoZmlsdGVyZWREYXRhLmxlbmd0aCAvIGxpbWl0KSxcbiAgICAgIH0sXG4gICAgICAvLyBGb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eSB3aXRoIG1vY2sgc3RydWN0dXJlXG4gICAgICB0b3RhbEl0ZW1zOiBmaWx0ZXJlZERhdGEubGVuZ3RoLFxuICAgICAgdG90YWxQYWdlczogTWF0aC5jZWlsKGZpbHRlcmVkRGF0YS5sZW5ndGggLyBsaW1pdCksXG4gICAgfTtcbiAgfSwgW3Jhd0RhdGEsIHNlYXJjaFF1ZXJ5LCBpc1VzaW5nTW9ja0RhdGEsIGxpbWl0XSk7XG5cbiAgLy8gRGVsZXRlIG11dGF0aW9uXG4gIGNvbnN0IGRlbGV0ZU11dGF0aW9uID0gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46IChmaXh0dXJlOiBGaXh0dXJlKSA9PiB7XG4gICAgICBjb25zdCBmaXh0dXJlSWQgPSBmaXh0dXJlLmV4dGVybmFsSWQgfHwgZml4dHVyZS5pZDtcbiAgICAgIHJldHVybiBmaXh0dXJlc0FwaS5kZWxldGVGaXh0dXJlKGZpeHR1cmVJZCk7XG4gICAgfSxcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnZml4dHVyZXMnXSB9KTtcbiAgICAgIGNvbnNvbGUubG9nKCdGaXh0dXJlIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgICBzZXREZWxldGVNb2RhbE9wZW4oZmFsc2UpO1xuICAgICAgc2V0U2VsZWN0ZWRGaXh0dXJlKG51bGwpO1xuICAgIH0sXG4gICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBkZWxldGUgZml4dHVyZTonLCBlcnJvci5tZXNzYWdlKTtcbiAgICB9LFxuICB9KTtcblxuICAvLyBEZWZpbmUgdGFibGUgY29sdW1uc1xuICBjb25zdCBjb2x1bW5zOiBDb2x1bW48Rml4dHVyZT5bXSA9IFtcbiAgICB7XG4gICAgICBrZXk6ICdkYXRlJyxcbiAgICAgIHRpdGxlOiAnRGF0ZSAmIFRpbWUnLFxuICAgICAgc29ydGFibGU6IHRydWUsXG4gICAgICByZW5kZXI6ICh2YWx1ZSkgPT4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICA8RGF0ZVRpbWVEaXNwbGF5XG4gICAgICAgICAgICBkYXRlVGltZT17dmFsdWV9XG4gICAgICAgICAgICBzaG93RGF0ZT17dHJ1ZX1cbiAgICAgICAgICAgIHNob3dUaW1lPXt0cnVlfVxuICAgICAgICAgICAgaXNDbGlja2FibGU9e3RydWV9XG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IGNsaWNrZWREYXRlID0gbmV3IERhdGUodmFsdWUpO1xuICAgICAgICAgICAgICBzZXRTZWxlY3RlZERhdGUoY2xpY2tlZERhdGUpO1xuICAgICAgICAgICAgICBzZXREYXRlRmlsdGVyTW9kYWxPcGVuKHRydWUpO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1pbi13LVsxMDBweF1cIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKSxcbiAgICB9LFxuICAgIHtcbiAgICAgIGtleTogJ21hdGNoJyxcbiAgICAgIHRpdGxlOiAnTWF0Y2gnLFxuICAgICAgc29ydGFibGU6IGZhbHNlLFxuICAgICAgaGVhZGVyQ2xhc3NOYW1lOiAndGV4dC1jZW50ZXInLFxuICAgICAgcmVuZGVyOiAoXywgcm93KSA9PiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC00IHB5LTNcIj5cbiAgICAgICAgICB7LyogSG9tZSBUZWFtICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgc3BhY2UteS0yIG1pbi13LVs4MHB4XVwiPlxuICAgICAgICAgICAge3Jvdy5ob21lVGVhbUxvZ28gJiYgKFxuICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgc3JjPXtgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19ET01BSU5fQ0ROX1BJQ1RVUkV9LyR7cm93LmhvbWVUZWFtTG9nb31gfVxuICAgICAgICAgICAgICAgIGFsdD17cm93LmhvbWVUZWFtTmFtZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTggaC04IG9iamVjdC1jb250YWluXCJcbiAgICAgICAgICAgICAgICBvbkVycm9yPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmRpc3BsYXkgPSAnbm9uZSc7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtY2VudGVyIGxlYWRpbmctdGlnaHQgbWF4LXctWzgwcHhdIGJyZWFrLXdvcmRzXCI+XG4gICAgICAgICAgICAgIHtyb3cuaG9tZVRlYW1OYW1lfVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFZTICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMlwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBmb250LWJvbGQgdGV4dC1zbVwiPlZTPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEF3YXkgVGVhbSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIHNwYWNlLXktMiBtaW4tdy1bODBweF1cIj5cbiAgICAgICAgICAgIHtyb3cuYXdheVRlYW1Mb2dvICYmIChcbiAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgIHNyYz17YCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRE9NQUlOX0NETl9QSUNUVVJFfS8ke3Jvdy5hd2F5VGVhbUxvZ299YH1cbiAgICAgICAgICAgICAgICBhbHQ9e3Jvdy5hd2F5VGVhbU5hbWV9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy04IGgtOCBvYmplY3QtY29udGFpblwiXG4gICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWNlbnRlciBsZWFkaW5nLXRpZ2h0IG1heC13LVs4MHB4XSBicmVhay13b3Jkc1wiPlxuICAgICAgICAgICAgICB7cm93LmF3YXlUZWFtTmFtZX1cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApLFxuICAgIH0sXG4gICAge1xuICAgICAga2V5OiAnc2NvcmUnLFxuICAgICAgdGl0bGU6ICdTY29yZScsXG4gICAgICBhbGlnbjogJ2NlbnRlcicsXG4gICAgICByZW5kZXI6IChfLCByb3cpID0+IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQtbGdcIj5cbiAgICAgICAgICAgIHtyb3cuZ29hbHNIb21lID8/ICctJ30gLSB7cm93LmdvYWxzQXdheSA/PyAnLSd9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgeyhyb3cuc2NvcmVIYWxmdGltZUhvbWUgIT09IG51bGwgJiYgcm93LnNjb3JlSGFsZnRpbWVBd2F5ICE9PSBudWxsKSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICBIVDoge3Jvdy5zY29yZUhhbGZ0aW1lSG9tZX0gLSB7cm93LnNjb3JlSGFsZnRpbWVBd2F5fVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApLFxuICAgIH0sXG4gICAge1xuICAgICAga2V5OiAnc3RhdHVzJyxcbiAgICAgIHRpdGxlOiAnU3RhdHVzJyxcbiAgICAgIHNvcnRhYmxlOiB0cnVlLFxuICAgICAgZmlsdGVyYWJsZTogdHJ1ZSxcbiAgICAgIHJlbmRlcjogKHZhbHVlLCByb3cpID0+IHtcbiAgICAgICAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgICAgICAgY2FzZSAnMUgnOlxuICAgICAgICAgICAgY2FzZSAnMkgnOlxuICAgICAgICAgICAgY2FzZSAnSFQnOlxuICAgICAgICAgICAgICByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCc7XG4gICAgICAgICAgICBjYXNlICdGVCc6XG4gICAgICAgICAgICAgIHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCc7XG4gICAgICAgICAgICBjYXNlICdOUyc6XG4gICAgICAgICAgICAgIHJldHVybiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCc7XG4gICAgICAgICAgICBjYXNlICdDQU5DJzpcbiAgICAgICAgICAgIGNhc2UgJ1BTVCc6XG4gICAgICAgICAgICAgIHJldHVybiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgcmV0dXJuICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCc7XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnN0IGdldFN0YXR1c1RleHQgPSAoc3RhdHVzOiBzdHJpbmcsIGVsYXBzZWQ/OiBudW1iZXIpID0+IHtcbiAgICAgICAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgICAgICAgY2FzZSAnMUgnOlxuICAgICAgICAgICAgY2FzZSAnMkgnOlxuICAgICAgICAgICAgICByZXR1cm4gYCR7ZWxhcHNlZH0nYDtcbiAgICAgICAgICAgIGNhc2UgJ0hUJzpcbiAgICAgICAgICAgICAgcmV0dXJuICdIYWxmIFRpbWUnO1xuICAgICAgICAgICAgY2FzZSAnRlQnOlxuICAgICAgICAgICAgICByZXR1cm4gJ0Z1bGwgVGltZSc7XG4gICAgICAgICAgICBjYXNlICdOUyc6XG4gICAgICAgICAgICAgIHJldHVybiAnTm90IFN0YXJ0ZWQnO1xuICAgICAgICAgICAgY2FzZSAnQ0FOQyc6XG4gICAgICAgICAgICAgIHJldHVybiAnQ2FuY2VsbGVkJztcbiAgICAgICAgICAgIGNhc2UgJ1BTVCc6XG4gICAgICAgICAgICAgIHJldHVybiAnUG9zdHBvbmVkJztcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgIHJldHVybiBzdGF0dXM7XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuXG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT17Z2V0U3RhdHVzQ29sb3IodmFsdWUpfT5cbiAgICAgICAgICAgIHtnZXRTdGF0dXNUZXh0KHZhbHVlLCByb3cuZWxhcHNlZCl9XG4gICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgKTtcbiAgICAgIH0sXG4gICAgfSxcbiAgICB7XG4gICAgICBrZXk6ICdsZWFndWVOYW1lJyxcbiAgICAgIHRpdGxlOiAnTGVhZ3VlJyxcbiAgICAgIHNvcnRhYmxlOiB0cnVlLFxuICAgICAgZmlsdGVyYWJsZTogdHJ1ZSxcbiAgICAgIHJlbmRlcjogKHZhbHVlKSA9PiAoXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPnt2YWx1ZX08L3NwYW4+XG4gICAgICApLFxuICAgIH0sXG4gICAge1xuICAgICAga2V5OiAnYWN0aW9ucycsXG4gICAgICB0aXRsZTogJ0FjdGlvbnMnLFxuICAgICAgcmVuZGVyOiAoXywgcm93KSA9PiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTFcIj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgdGl0bGU9XCJWaWV3IERldGFpbHNcIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld0ZpeHR1cmUocm93KX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICB0aXRsZT1cIkJyb2FkY2FzdCBMaW5rc1wiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVCcm9hZGNhc3RMaW5rc01vZGFsKHJvdyl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFJhZGlvIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIHtpc0VkaXRvcigpICYmIChcbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICB0aXRsZT1cIkVkaXRcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVFZGl0Rml4dHVyZShyb3cpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8RWRpdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgICAge2lzQWRtaW4oKSAmJiAoXG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVGaXh0dXJlKHJvdyl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICksXG4gICAgfSxcbiAgXTtcblxuICAvLyBIYW5kbGVyIGZ1bmN0aW9uc1xuICBjb25zdCBoYW5kbGVTZWFyY2ggPSAoKSA9PiB7XG4gICAgc2V0U2VhcmNoUXVlcnkoc2VhcmNoSW5wdXQudHJpbSgpKTtcbiAgICBzZXRQYWdlKDEpOyAvLyBSZXNldCB0byBmaXJzdCBwYWdlIHdoZW4gc2VhcmNoaW5nXG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VhcmNoS2V5UHJlc3MgPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJykge1xuICAgICAgaGFuZGxlU2VhcmNoKCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNsZWFyU2VhcmNoID0gKCkgPT4ge1xuICAgIHNldFNlYXJjaElucHV0KCcnKTtcbiAgICBzZXRTZWFyY2hRdWVyeSgnJyk7XG4gICAgc2V0UGFnZSgxKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVWaWV3Rml4dHVyZSA9IChmaXh0dXJlOiBGaXh0dXJlKSA9PiB7XG4gICAgLy8gTmF2aWdhdGUgdG8gZml4dHVyZSBkZXRhaWwgcGFnZSB1c2luZyBleHRlcm5hbElkXG4gICAgY29uc3QgZml4dHVyZUlkID0gZml4dHVyZS5leHRlcm5hbElkIHx8IGZpeHR1cmUuaWQ7XG4gICAgd2luZG93Lm9wZW4oYC9kYXNoYm9hcmQvZml4dHVyZXMvJHtmaXh0dXJlSWR9YCwgJ19ibGFuaycpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUVkaXRGaXh0dXJlID0gKGZpeHR1cmU6IEZpeHR1cmUpID0+IHtcbiAgICAvLyBOYXZpZ2F0ZSB0byBlZGl0IHBhZ2UgdXNpbmcgZXh0ZXJuYWxJZFxuICAgIGNvbnN0IGZpeHR1cmVJZCA9IGZpeHR1cmUuZXh0ZXJuYWxJZCB8fCBmaXh0dXJlLmlkO1xuICAgIHdpbmRvdy5vcGVuKGAvZGFzaGJvYXJkL2ZpeHR1cmVzLyR7Zml4dHVyZUlkfS9lZGl0YCwgJ19ibGFuaycpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUZpeHR1cmUgPSAoZml4dHVyZTogRml4dHVyZSkgPT4ge1xuICAgIHNldFNlbGVjdGVkRml4dHVyZShmaXh0dXJlKTtcbiAgICBzZXREZWxldGVNb2RhbE9wZW4odHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQnJvYWRjYXN0TGlua3MgPSAoZml4dHVyZTogRml4dHVyZSkgPT4ge1xuICAgIHNldFNlbGVjdGVkRml4dHVyZUZvckJyb2FkY2FzdChmaXh0dXJlKTtcbiAgICBzZXRCcm9hZGNhc3RMaW5rc01vZGFsT3Blbih0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVWaWV3QnJvYWRjYXN0TGlua3MgPSAoZml4dHVyZTogRml4dHVyZSkgPT4ge1xuICAgIHNldFNlbGVjdGVkRml4dHVyZUZvckJyb2FkY2FzdChmaXh0dXJlKTtcbiAgICBzZXRCcm9hZGNhc3RMaW5rc01vZGFsT3Blbih0cnVlKTtcbiAgfTtcblxuICBjb25zdCBjb25maXJtRGVsZXRlID0gKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZEZpeHR1cmUpIHtcbiAgICAgIGRlbGV0ZU11dGF0aW9uLm11dGF0ZShzZWxlY3RlZEZpeHR1cmUpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVCdWxrU3luYyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ1N0YXJ0aW5nIGZpeHR1cmVzIHN5bmMuLi4nKTtcbiAgICAgIC8vIFRoaXMgd291bGQgY2FsbCB0aGUgc3luYyBBUElcbiAgICAgIC8vIGF3YWl0IGZpeHR1cmVzQXBpLnN5bmNGaXh0dXJlcygpO1xuICAgICAgY29uc29sZS5sb2coJ0ZpeHR1cmVzIHN5bmMgY29tcGxldGVkJyk7XG4gICAgICByZWZldGNoKCk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignU3luYyBmYWlsZWQ6JywgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIERhdGUgZmlsdGVyIGhhbmRsZXJzXG4gIGNvbnN0IGhhbmRsZUFwcGx5RGF0ZUZpbHRlciA9IChkYXRlOiBEYXRlIHwgdW5kZWZpbmVkKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWREYXRlKGRhdGUpO1xuICAgIHNldFBhZ2UoMSk7IC8vIFJlc2V0IHRvIGZpcnN0IHBhZ2Ugd2hlbiBmaWx0ZXJpbmdcbiAgfTtcblxuICBjb25zdCBoYW5kbGVSZXNldERhdGVGaWx0ZXIgPSAoKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWREYXRlKHVuZGVmaW5lZCk7XG4gICAgc2V0UGFnZSgxKTsgLy8gUmVzZXQgdG8gZmlyc3QgcGFnZSB3aGVuIGNsZWFyaW5nIGZpbHRlclxuICB9O1xuXG4gIGlmIChlcnJvcikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPkZpeHR1cmVzIE1hbmFnZW1lbnQ8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMVwiPk1hbmFnZSBmb290YmFsbCBmaXh0dXJlcyBhbmQgbWF0Y2ggZGF0YTwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbWItNFwiPkZhaWxlZCB0byBsb2FkIGZpeHR1cmVzPC9wPlxuICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHJlZmV0Y2goKX0+XG4gICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgIFRyeSBBZ2FpblxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5GaXh0dXJlcyBNYW5hZ2VtZW50PC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTFcIj5NYW5hZ2UgZm9vdGJhbGwgZml4dHVyZXMgYW5kIG1hdGNoIGRhdGE8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVmZXRjaCgpfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT17YG1yLTIgaC00IHctNCAke2lzTG9hZGluZyA/ICdhbmltYXRlLXNwaW4nIDogJyd9YH0gLz5cbiAgICAgICAgICAgIFJlZnJlc2hcbiAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgIHtpc0FkbWluKCkgJiYgKFxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJ1bGtTeW5jfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8VXBsb2FkIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgIFN5bmMgRGF0YVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgLy8gRXhwb3J0IGZ1bmN0aW9uYWxpdHlcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0V4cG9ydCBmZWF0dXJlIGNvbWluZyBzb29uJyk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgRXhwb3J0XG4gICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICB7aXNFZGl0b3IoKSAmJiAoXG4gICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5vcGVuKCcvZGFzaGJvYXJkL2ZpeHR1cmVzL2NyZWF0ZScsICdfYmxhbmsnKX0+XG4gICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgIEFkZCBGaXh0dXJlXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogU3RhdHMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPlxuICAgICAgICAgICAgICB7ZGF0YT8ubWV0YT8udG90YWxJdGVtcz8udG9Mb2NhbGVTdHJpbmcoKSB8fCAnTG9hZGluZy4uLid9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlRvdGFsIEZpeHR1cmVzPC9wPlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICB7ZGF0YT8uZGF0YT8uZmlsdGVyKGYgPT4gWycxSCcsICcySCcsICdIVCddLmluY2x1ZGVzKGYuc3RhdHVzKSkubGVuZ3RoIHx8IDB9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkxpdmUgTWF0Y2hlczwvcD5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC15ZWxsb3ctNjAwXCI+XG4gICAgICAgICAgICAgIHtkYXRhPy5kYXRhPy5maWx0ZXIoZiA9PiBmLnN0YXR1cyA9PT0gJ05TJykubGVuZ3RoIHx8IDB9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlVwY29taW5nPC9wPlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIHtkYXRhPy5kYXRhPy5maWx0ZXIoZiA9PiBmLnN0YXR1cyA9PT0gJ0ZUJykubGVuZ3RoIHx8IDB9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkNvbXBsZXRlZDwvcD5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIERhdGEgVGFibGUgKi99XG4gICAgICA8Q2FyZD5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IHNtOml0ZW1zLWNlbnRlciBzbTpqdXN0aWZ5LWJldHdlZW4gZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwibXItMiBoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgICBBbGwgRml4dHVyZXNcbiAgICAgICAgICAgICAgICB7aXNVc2luZ01vY2tEYXRhICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCBmb250LW5vcm1hbFwiPlxuICAgICAgICAgICAgICAgICAgICBEZW1vIE1vZGVcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICB7aXNVc2luZ01vY2tEYXRhXG4gICAgICAgICAgICAgICAgICA/IFwiU2hvd2luZyBkZW1vIGRhdGEgLSBBUEkgYmFja2VuZCBpcyBub3QgYXZhaWxhYmxlXCJcbiAgICAgICAgICAgICAgICAgIDogXCJDb21wbGV0ZSBsaXN0IG9mIGZvb3RiYWxsIGZpeHR1cmVzIHdpdGggcmVhbC10aW1lIHVwZGF0ZXNcIlxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPERhdGVQaWNrZXJcbiAgICAgICAgICAgICAgICBkYXRlPXtzZWxlY3RlZERhdGV9XG4gICAgICAgICAgICAgICAgb25EYXRlQ2hhbmdlPXtzZXRTZWxlY3RlZERhdGV9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJGaWx0ZXIgYnkgZGF0ZVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1bMjAwcHhdXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAge3NlbGVjdGVkRGF0ZSAmJiAoXG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkRGF0ZSh1bmRlZmluZWQpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMlwiXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIkNsZWFyIGRhdGUgZmlsdGVyXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgPFRhYmxlU2tlbGV0b24gcm93cz17MTB9IGNvbHVtbnM9ezd9IC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxEYXRhVGFibGVcbiAgICAgICAgICAgICAgZGF0YT17KGZpeHR1cmVzRGF0YT8uZGF0YSB8fCBbXSkgYXMgRml4dHVyZVtdfVxuICAgICAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxuICAgICAgICAgICAgICBsb2FkaW5nPXtpc0xvYWRpbmcgJiYgIWlzVXNpbmdNb2NrRGF0YX1cbiAgICAgICAgICAgICAgc2VhcmNoYWJsZT17dHJ1ZX1cbiAgICAgICAgICAgICAgc2VhcmNoUGxhY2Vob2xkZXI9XCJTZWFyY2ggZml4dHVyZXMuLi5cIlxuICAgICAgICAgICAgICBzaG93U2VhcmNoQnV0dG9uPXt0cnVlfVxuICAgICAgICAgICAgICBzZWFyY2hWYWx1ZT17c2VhcmNoSW5wdXR9XG4gICAgICAgICAgICAgIG9uU2VhcmNoQ2hhbmdlPXtzZXRTZWFyY2hJbnB1dH1cbiAgICAgICAgICAgICAgb25TZWFyY2hTdWJtaXQ9e2hhbmRsZVNlYXJjaH1cbiAgICAgICAgICAgICAgb25TZWFyY2hDbGVhcj17aGFuZGxlQ2xlYXJTZWFyY2h9XG4gICAgICAgICAgICAgIHBhZ2luYXRpb249e3tcbiAgICAgICAgICAgICAgICBwYWdlLFxuICAgICAgICAgICAgICAgIGxpbWl0LFxuICAgICAgICAgICAgICAgIHRvdGFsOiAoZml4dHVyZXNEYXRhIGFzIGFueSk/Lm1ldGE/LnRvdGFsSXRlbXMgfHwgKGZpeHR1cmVzRGF0YSBhcyBhbnkpPy50b3RhbEl0ZW1zIHx8IDAsXG4gICAgICAgICAgICAgICAgb25QYWdlQ2hhbmdlOiBzZXRQYWdlLFxuICAgICAgICAgICAgICAgIG9uTGltaXRDaGFuZ2U6IChuZXdMaW1pdCkgPT4ge1xuICAgICAgICAgICAgICAgICAgc2V0TGltaXQobmV3TGltaXQpO1xuICAgICAgICAgICAgICAgICAgc2V0UGFnZSgxKTsgLy8gUmVzZXQgdG8gZmlyc3QgcGFnZSB3aGVuIGNoYW5naW5nIGxpbWl0XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgZW1wdHlNZXNzYWdlPVwiTm8gZml4dHVyZXMgZm91bmRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuXG4gICAgICB7LyogRGVsZXRlIENvbmZpcm1hdGlvbiBNb2RhbCAqL31cbiAgICAgIDxDb25maXJtTW9kYWxcbiAgICAgICAgaXNPcGVuPXtkZWxldGVNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHtcbiAgICAgICAgICBzZXREZWxldGVNb2RhbE9wZW4oZmFsc2UpO1xuICAgICAgICAgIHNldFNlbGVjdGVkRml4dHVyZShudWxsKTtcbiAgICAgICAgfX1cbiAgICAgICAgb25Db25maXJtPXtjb25maXJtRGVsZXRlfVxuICAgICAgICB0aXRsZT1cIkRlbGV0ZSBGaXh0dXJlXCJcbiAgICAgICAgbWVzc2FnZT17XG4gICAgICAgICAgc2VsZWN0ZWRGaXh0dXJlXG4gICAgICAgICAgICA/IGBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHRoZSBmaXh0dXJlIFwiJHtzZWxlY3RlZEZpeHR1cmUuaG9tZVRlYW1OYW1lfSB2cyAke3NlbGVjdGVkRml4dHVyZS5hd2F5VGVhbU5hbWV9XCI/IFRoaXMgYWN0aW9uIGNhbm5vdCBiZSB1bmRvbmUuYFxuICAgICAgICAgICAgOiAnQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIGZpeHR1cmU/J1xuICAgICAgICB9XG4gICAgICAgIGNvbmZpcm1UZXh0PVwiRGVsZXRlXCJcbiAgICAgICAgY2FuY2VsVGV4dD1cIkNhbmNlbFwiXG4gICAgICAgIHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiXG4gICAgICAgIGxvYWRpbmc9e2RlbGV0ZU11dGF0aW9uLmlzTG9hZGluZ31cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBCcm9hZGNhc3QgTGlua3MgTW9kYWwgKi99XG4gICAgICB7Lyoge3NlbGVjdGVkRml4dHVyZUZvckJyb2FkY2FzdCAmJiAoXG4gICAgICAgIDxCcm9hZGNhc3RMaW5rc01vZGFsXG4gICAgICAgICAgaXNPcGVuPXticm9hZGNhc3RMaW5rc01vZGFsT3Blbn1cbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XG4gICAgICAgICAgICBzZXRCcm9hZGNhc3RMaW5rc01vZGFsT3BlbihmYWxzZSk7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZEZpeHR1cmVGb3JCcm9hZGNhc3QobnVsbCk7XG4gICAgICAgICAgfX1cbiAgICAgICAgICBmaXh0dXJlPXtzZWxlY3RlZEZpeHR1cmVGb3JCcm9hZGNhc3R9XG4gICAgICAgIC8+XG4gICAgICApfSAqL31cblxuICAgICAgey8qIERhdGUgRmlsdGVyIE1vZGFsICovfVxuICAgICAgPERhdGVGaWx0ZXJNb2RhbFxuICAgICAgICBpc09wZW49e2RhdGVGaWx0ZXJNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldERhdGVGaWx0ZXJNb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICBzZWxlY3RlZERhdGU9e3NlbGVjdGVkRGF0ZX1cbiAgICAgICAgb25EYXRlU2VsZWN0PXtzZXRTZWxlY3RlZERhdGV9XG4gICAgICAgIG9uQXBwbHlGaWx0ZXI9e2hhbmRsZUFwcGx5RGF0ZUZpbHRlcn1cbiAgICAgICAgb25SZXNldEZpbHRlcj17aGFuZGxlUmVzZXREYXRlRmlsdGVyfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUXVlcnkiLCJ1c2VNdXRhdGlvbiIsInVzZVF1ZXJ5Q2xpZW50IiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIkJhZGdlIiwiRGF0YVRhYmxlIiwiQ29uZmlybU1vZGFsIiwiZml4dHVyZXNBcGkiLCJFeWUiLCJFZGl0IiwiVHJhc2gyIiwiUGx1cyIsIlJlZnJlc2hDdyIsIkNhbGVuZGFyIiwiRG93bmxvYWQiLCJVcGxvYWQiLCJSYWRpbyIsIlgiLCJUYWJsZVNrZWxldG9uIiwidXNlUGVybWlzc2lvbnMiLCJEYXRlUGlja2VyIiwiRGF0ZVRpbWVEaXNwbGF5IiwiRGF0ZUZpbHRlck1vZGFsIiwiY29udmVydExvY2FsRGF0ZVRvVVRDIiwiRml4dHVyZXNQYWdlIiwiZGF0YSIsInBhZ2UiLCJzZXRQYWdlIiwibGltaXQiLCJzZXRMaW1pdCIsInNlYXJjaElucHV0Iiwic2V0U2VhcmNoSW5wdXQiLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5Iiwic3RhdHVzRmlsdGVyIiwic2V0U3RhdHVzRmlsdGVyIiwibGVhZ3VlRmlsdGVyIiwic2V0TGVhZ3VlRmlsdGVyIiwiZGVsZXRlTW9kYWxPcGVuIiwic2V0RGVsZXRlTW9kYWxPcGVuIiwic2VsZWN0ZWRGaXh0dXJlIiwic2V0U2VsZWN0ZWRGaXh0dXJlIiwiYnJvYWRjYXN0TGlua3NNb2RhbE9wZW4iLCJzZXRCcm9hZGNhc3RMaW5rc01vZGFsT3BlbiIsInNlbGVjdGVkRml4dHVyZUZvckJyb2FkY2FzdCIsInNldFNlbGVjdGVkRml4dHVyZUZvckJyb2FkY2FzdCIsInNlbGVjdGVkRGF0ZSIsInNldFNlbGVjdGVkRGF0ZSIsInVuZGVmaW5lZCIsImRhdGVGaWx0ZXJNb2RhbE9wZW4iLCJzZXREYXRlRmlsdGVyTW9kYWxPcGVuIiwiaXNBZG1pbiIsImlzRWRpdG9yIiwicXVlcnlDbGllbnQiLCJtb2NrRml4dHVyZXMiLCJpZCIsImhvbWVUZWFtTmFtZSIsImF3YXlUZWFtTmFtZSIsImhvbWVUZWFtTG9nbyIsImF3YXlUZWFtTG9nbyIsImRhdGUiLCJzdGF0dXMiLCJsZWFndWVOYW1lIiwidmVudWUiLCJ0b3RhbEl0ZW1zIiwidG90YWxQYWdlcyIsImN1cnJlbnRQYWdlIiwiaXNMb2FkaW5nIiwiZXJyb3IiLCJyZWZldGNoIiwicXVlcnlLZXkiLCJxdWVyeUZuIiwiZmlsdGVycyIsInRyaW0iLCJzZWFyY2giLCJsZWFndWUiLCJnZXRGaXh0dXJlcyIsInN0YWxlVGltZSIsInJldHJ5Iiwib25FcnJvciIsImNvbnNvbGUiLCJsb2ciLCJtZXNzYWdlIiwicmF3RGF0YSIsImlzVXNpbmdNb2NrRGF0YSIsImZpeHR1cmVzRGF0YSIsInVzZU1lbW8iLCJmaWx0ZXJlZERhdGEiLCJmaWx0ZXIiLCJmaXh0dXJlIiwic2VhcmNoTG93ZXIiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwibWV0YSIsImxlbmd0aCIsIk1hdGgiLCJjZWlsIiwiZGVsZXRlTXV0YXRpb24iLCJtdXRhdGlvbkZuIiwiZml4dHVyZUlkIiwiZXh0ZXJuYWxJZCIsImRlbGV0ZUZpeHR1cmUiLCJvblN1Y2Nlc3MiLCJpbnZhbGlkYXRlUXVlcmllcyIsImNvbHVtbnMiLCJrZXkiLCJ0aXRsZSIsInNvcnRhYmxlIiwicmVuZGVyIiwidmFsdWUiLCJkaXYiLCJjbGFzc05hbWUiLCJkYXRlVGltZSIsInNob3dEYXRlIiwic2hvd1RpbWUiLCJpc0NsaWNrYWJsZSIsIm9uQ2xpY2siLCJjbGlja2VkRGF0ZSIsIkRhdGUiLCJoZWFkZXJDbGFzc05hbWUiLCJfIiwicm93IiwiaW1nIiwic3JjIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0RPTUFJTl9DRE5fUElDVFVSRSIsImFsdCIsImUiLCJjdXJyZW50VGFyZ2V0Iiwic3R5bGUiLCJkaXNwbGF5Iiwic3BhbiIsImFsaWduIiwiZ29hbHNIb21lIiwiZ29hbHNBd2F5Iiwic2NvcmVIYWxmdGltZUhvbWUiLCJzY29yZUhhbGZ0aW1lQXdheSIsImZpbHRlcmFibGUiLCJnZXRTdGF0dXNDb2xvciIsImdldFN0YXR1c1RleHQiLCJlbGFwc2VkIiwic2l6ZSIsInZhcmlhbnQiLCJoYW5kbGVWaWV3Rml4dHVyZSIsImhhbmRsZUJyb2FkY2FzdExpbmtzTW9kYWwiLCJoYW5kbGVFZGl0Rml4dHVyZSIsImhhbmRsZURlbGV0ZUZpeHR1cmUiLCJoYW5kbGVTZWFyY2giLCJoYW5kbGVTZWFyY2hLZXlQcmVzcyIsImhhbmRsZUNsZWFyU2VhcmNoIiwid2luZG93Iiwib3BlbiIsImhhbmRsZUJyb2FkY2FzdExpbmtzIiwiaGFuZGxlVmlld0Jyb2FkY2FzdExpbmtzIiwiY29uZmlybURlbGV0ZSIsIm11dGF0ZSIsImhhbmRsZUJ1bGtTeW5jIiwiaGFuZGxlQXBwbHlEYXRlRmlsdGVyIiwiaGFuZGxlUmVzZXREYXRlRmlsdGVyIiwiaDEiLCJwIiwiZGlzYWJsZWQiLCJ0b0xvY2FsZVN0cmluZyIsImYiLCJvbkRhdGVDaGFuZ2UiLCJwbGFjZWhvbGRlciIsInJvd3MiLCJsb2FkaW5nIiwic2VhcmNoYWJsZSIsInNlYXJjaFBsYWNlaG9sZGVyIiwic2hvd1NlYXJjaEJ1dHRvbiIsInNlYXJjaFZhbHVlIiwib25TZWFyY2hDaGFuZ2UiLCJvblNlYXJjaFN1Ym1pdCIsIm9uU2VhcmNoQ2xlYXIiLCJwYWdpbmF0aW9uIiwidG90YWwiLCJvblBhZ2VDaGFuZ2UiLCJvbkxpbWl0Q2hhbmdlIiwibmV3TGltaXQiLCJlbXB0eU1lc3NhZ2UiLCJpc09wZW4iLCJvbkNsb3NlIiwib25Db25maXJtIiwiY29uZmlybVRleHQiLCJjYW5jZWxUZXh0Iiwib25EYXRlU2VsZWN0Iiwib25BcHBseUZpbHRlciIsIm9uUmVzZXRGaWx0ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ })

});