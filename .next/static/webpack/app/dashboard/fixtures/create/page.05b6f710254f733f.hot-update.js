"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/create/page",{

/***/ "(app-pages-browser)/./src/lib/api/fixtures.ts":
/*!*********************************!*\
  !*** ./src/lib/api/fixtures.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fixturesApi: function() { return /* binding */ fixturesApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst fixturesApi = {\n    // Public endpoints - Using Next.js API proxy\n    getFixtures: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        // Use Next.js API proxy instead of direct API call\n        const response = await fetch(\"/api/fixtures?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch fixtures: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    getFixtureById: async (externalId)=>{\n        // Use Next.js API proxy instead of direct API call\n        const response = await fetch(\"/api/fixtures/\".concat(externalId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch fixture: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Upcoming and Live fixtures (Public) - Using Next.js API proxy\n    getUpcomingAndLive: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        // Use Next.js API proxy instead of direct API call\n        const response = await fetch(\"/api/fixtures/live?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch live fixtures: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Team schedule (Requires auth)\n    getTeamSchedule: async function(teamId) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/schedules/\".concat(teamId, \"?\").concat(params.toString()));\n        return response;\n    },\n    // Fixture statistics (Requires auth)\n    getFixtureStatistics: async (externalId)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/statistics/\".concat(externalId));\n        return response;\n    },\n    // Admin only - Sync operations\n    triggerSeasonSync: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/sync/fixtures\");\n        return response;\n    },\n    triggerDailySync: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/sync/daily\");\n        return response;\n    },\n    // Editor+ - Sync status\n    getSyncStatus: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/sync/status\");\n        return response;\n    },\n    // CRUD operations - Using Next.js API proxy\n    createFixture: async (data)=>{\n        const response = await fetch(\"/api/fixtures\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to create fixture: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    updateFixture: async (externalId, data)=>{\n        // Get token from auth store (same pattern as delete)\n        const getAuthHeaders = ()=>{\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            if (true) {\n                // Try to get from Zustand store first\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            console.log(\"\\uD83D\\uDD11 Update fixture - Using token from auth store:\", token.substring(0, 20) + \"...\");\n                            headers.Authorization = \"Bearer \".concat(token);\n                            return headers;\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    console.log(\"\\uD83D\\uDD11 Update fixture - Using fallback token from localStorage\");\n                    headers.Authorization = \"Bearer \".concat(fallbackToken);\n                    return headers;\n                }\n            }\n            console.warn(\"❌ Update fixture - No token found!\");\n            return headers;\n        };\n        const headers = getAuthHeaders();\n        console.log(\"\\uD83D\\uDD04 Update fixture request:\", {\n            externalId,\n            hasAuth: !!headers.Authorization,\n            data\n        });\n        const response = await fetch(\"/api/fixtures/\".concat(externalId), {\n            method: \"PUT\",\n            headers,\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"❌ Update fixture failed:\", response.status, response.statusText, errorData);\n            throw new Error(errorData.message || \"Failed to update fixture: \".concat(response.statusText));\n        }\n        const result = await response.json();\n        console.log(\"✅ Update fixture successful:\", externalId);\n        return result.data || result;\n    },\n    deleteFixture: async (externalId)=>{\n        // Get token from auth store (same pattern as broadcast-links.ts)\n        const getAuthHeaders = ()=>{\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            if (true) {\n                // Try to get from Zustand store first\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            console.log(\"\\uD83D\\uDD11 Delete fixture - Using token from auth store:\", token.substring(0, 20) + \"...\");\n                            headers.Authorization = \"Bearer \".concat(token);\n                            return headers;\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    console.log(\"\\uD83D\\uDD11 Delete fixture - Using fallback token from localStorage\");\n                    headers.Authorization = \"Bearer \".concat(fallbackToken);\n                    return headers;\n                }\n            }\n            console.warn(\"❌ Delete fixture - No token found!\");\n            return headers;\n        };\n        const headers = getAuthHeaders();\n        console.log(\"\\uD83D\\uDD04 Delete fixture request:\", {\n            externalId,\n            hasAuth: !!headers.Authorization\n        });\n        const response = await fetch(\"/api/fixtures/\".concat(externalId), {\n            method: \"DELETE\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"❌ Delete fixture failed:\", response.status, response.statusText, errorData);\n            throw new Error(errorData.message || \"Failed to delete fixture: \".concat(response.statusText));\n        }\n        console.log(\"✅ Delete fixture successful:\", externalId);\n    },\n    // Aliases for consistency\n    getFixture: async (externalId)=>{\n        const response = await fixturesApi.getFixtureById(externalId);\n        return response.data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/fixtures.ts\n"));

/***/ })

});