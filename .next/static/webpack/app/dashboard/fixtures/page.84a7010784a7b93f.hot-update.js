"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/external-link.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ExternalLink; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\"path\", { d: \"M15 3h6v6\", key: \"1q9fwt\" }],\n  [\"path\", { d: \"M10 14 21 3\", key: \"gplh6r\" }],\n  [\"path\", { d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\", key: \"a6xqqp\" }]\n];\nconst ExternalLink = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"external-link\", __iconNode);\n\n\n//# sourceMappingURL=external-link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZXh0ZXJuYWwtbGluay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RDtBQUNBLGFBQWEsK0JBQStCO0FBQzVDLGFBQWEsaUNBQWlDO0FBQzlDLGFBQWEsOEVBQThFO0FBQzNGO0FBQ0EscUJBQXFCLGdFQUFnQjs7QUFFVTtBQUMvQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2V4dGVybmFsLWxpbmsuanM/OWE3NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC41MTEuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IF9faWNvbk5vZGUgPSBbXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xNSAzaDZ2NlwiLCBrZXk6IFwiMXE5Znd0XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xMCAxNCAyMSAzXCIsIGtleTogXCJncGxoNnJcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTE4IDEzdjZhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOGEyIDIgMCAwIDEgMi0yaDZcIiwga2V5OiBcImE2eHFxcFwiIH1dXG5dO1xuY29uc3QgRXh0ZXJuYWxMaW5rID0gY3JlYXRlTHVjaWRlSWNvbihcImV4dGVybmFsLWxpbmtcIiwgX19pY29uTm9kZSk7XG5cbmV4cG9ydCB7IF9faWNvbk5vZGUsIEV4dGVybmFsTGluayBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1leHRlcm5hbC1saW5rLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/message-circle.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ MessageCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\"path\", { d: \"M7.9 20A9 9 0 1 0 4 16.1L2 22Z\", key: \"vv11sd\" }]\n];\nconst MessageCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"message-circle\", __iconNode);\n\n\n//# sourceMappingURL=message-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbWVzc2FnZS1jaXJjbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQ7QUFDQSxhQUFhLG9EQUFvRDtBQUNqRTtBQUNBLHNCQUFzQixnRUFBZ0I7O0FBRVU7QUFDaEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9tZXNzYWdlLWNpcmNsZS5qcz9hM2Q5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjUxMS4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgX19pY29uTm9kZSA9IFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTcuOSAyMEE5IDkgMCAxIDAgNCAxNi4xTDIgMjJaXCIsIGtleTogXCJ2djExc2RcIiB9XVxuXTtcbmNvbnN0IE1lc3NhZ2VDaXJjbGUgPSBjcmVhdGVMdWNpZGVJY29uKFwibWVzc2FnZS1jaXJjbGVcIiwgX19pY29uTm9kZSk7XG5cbmV4cG9ydCB7IF9faWNvbk5vZGUsIE1lc3NhZ2VDaXJjbGUgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWVzc2FnZS1jaXJjbGUuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/save.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Save; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z\",\n      key: \"1c8476\"\n    }\n  ],\n  [\"path\", { d: \"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7\", key: \"1ydtos\" }],\n  [\"path\", { d: \"M7 3v4a1 1 0 0 0 1 1h7\", key: \"t51u73\" }]\n];\nconst Save = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"save\", __iconNode);\n\n\n//# sourceMappingURL=save.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2F2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSwrREFBK0Q7QUFDNUUsYUFBYSw0Q0FBNEM7QUFDekQ7QUFDQSxhQUFhLGdFQUFnQjs7QUFFVTtBQUN2QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3NhdmUuanM/YmQ5NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC41MTEuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IF9faWNvbk5vZGUgPSBbXG4gIFtcbiAgICBcInBhdGhcIixcbiAgICB7XG4gICAgICBkOiBcIk0xNS4yIDNhMiAyIDAgMCAxIDEuNC42bDMuOCAzLjhhMiAyIDAgMCAxIC42IDEuNFYxOWEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMlY1YTIgMiAwIDAgMSAyLTJ6XCIsXG4gICAgICBrZXk6IFwiMWM4NDc2XCJcbiAgICB9XG4gIF0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xNyAyMXYtN2ExIDEgMCAwIDAtMS0xSDhhMSAxIDAgMCAwLTEgMXY3XCIsIGtleTogXCIxeWR0b3NcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTcgM3Y0YTEgMSAwIDAgMCAxIDFoN1wiLCBrZXk6IFwidDUxdTczXCIgfV1cbl07XG5jb25zdCBTYXZlID0gY3JlYXRlTHVjaWRlSWNvbihcInNhdmVcIiwgX19pY29uTm9kZSk7XG5cbmV4cG9ydCB7IF9faWNvbk5vZGUsIFNhdmUgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2F2ZS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Zap; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\",\n      key: \"1xq2db\"\n    }\n  ]\n];\nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"zap\", __iconNode);\n\n\n//# sourceMappingURL=zap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvemFwLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXREO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksZ0VBQWdCOztBQUVVO0FBQ3RDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvemFwLmpzPzM2MjciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNTExLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJNNCAxNGExIDEgMCAwIDEtLjc4LTEuNjNsOS45LTEwLjJhLjUuNSAwIDAgMSAuODYuNDZsLTEuOTIgNi4wMkExIDEgMCAwIDAgMTMgMTBoN2ExIDEgMCAwIDEgLjc4IDEuNjNsLTkuOSAxMC4yYS41LjUgMCAwIDEtLjg2LS40NmwxLjkyLTYuMDJBMSAxIDAgMCAwIDExIDE0elwiLFxuICAgICAga2V5OiBcIjF4cTJkYlwiXG4gICAgfVxuICBdXG5dO1xuY29uc3QgWmFwID0gY3JlYXRlTHVjaWRlSWNvbihcInphcFwiLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IHsgX19pY29uTm9kZSwgWmFwIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXphcC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_fixtures_BroadcastLinksModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/fixtures/BroadcastLinksModal */ \"(app-pages-browser)/./src/components/fixtures/BroadcastLinksModal.tsx\");\n/* harmony import */ var _components_ui_date_picker__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/date-picker */ \"(app-pages-browser)/./src/components/ui/date-picker.tsx\");\n/* harmony import */ var _components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/date-time-display */ \"(app-pages-browser)/./src/components/ui/date-time-display.tsx\");\n/* harmony import */ var _components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/date-filter-modal */ \"(app-pages-browser)/./src/components/ui/date-filter-modal.tsx\");\n/* harmony import */ var _lib_utils_date_time__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils/date-time */ \"(app-pages-browser)/./src/lib/utils/date-time.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction FixturesPage() {\n    var _data_meta_totalItems, _data_meta, _data_data, _data_data1, _data_data2, _fixturesData_meta;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [limit, setLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Input value\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Actual search query for API\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leagueFilter, setLeagueFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixture, setSelectedFixture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [dateFilterModalOpen, setDateFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAdmin, isEditor } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQueryClient)();\n    // Mock data for testing when API is down\n    const mockFixtures = {\n        data: [\n            {\n                id: 1,\n                homeTeamName: \"Manchester United\",\n                awayTeamName: \"Liverpool\",\n                homeTeamLogo: \"/images/teams/1.png\",\n                awayTeamLogo: \"/images/teams/2.png\",\n                date: \"2024-12-19T14:30:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Old Trafford\"\n            },\n            {\n                id: 2,\n                homeTeamName: \"Arsenal\",\n                awayTeamName: \"Chelsea\",\n                homeTeamLogo: \"/images/teams/3.png\",\n                awayTeamLogo: \"/images/teams/4.png\",\n                date: \"2024-12-20T16:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Emirates Stadium\"\n            },\n            {\n                id: 3,\n                homeTeamName: \"Barcelona\",\n                awayTeamName: \"Real Madrid\",\n                homeTeamLogo: \"/images/teams/5.png\",\n                awayTeamLogo: \"/images/teams/6.png\",\n                date: \"2024-12-21T20:00:00Z\",\n                status: \"LIVE\",\n                leagueName: \"La Liga\",\n                venue: \"Camp Nou\"\n            },\n            {\n                id: 4,\n                homeTeamName: \"Bayern Munich\",\n                awayTeamName: \"Borussia Dortmund\",\n                homeTeamLogo: \"/images/teams/7.png\",\n                awayTeamLogo: \"/images/teams/8.png\",\n                date: \"2024-12-18T18:30:00Z\",\n                status: \"FT\",\n                leagueName: \"Bundesliga\",\n                venue: \"Allianz Arena\"\n            },\n            {\n                id: 5,\n                homeTeamName: \"PSG\",\n                awayTeamName: \"Marseille\",\n                homeTeamLogo: \"/images/teams/9.png\",\n                awayTeamLogo: \"/images/teams/10.png\",\n                date: \"2024-12-22T21:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Ligue 1\",\n                venue: \"Parc des Princes\"\n            }\n        ],\n        totalItems: 5,\n        totalPages: 1,\n        currentPage: 1,\n        limit: 25\n    };\n    // Fetch fixtures data with fallback to mock data\n    const { data, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\",\n            page,\n            limit,\n            searchQuery,\n            statusFilter,\n            leagueFilter,\n            selectedDate\n        ],\n        queryFn: ()=>{\n            const filters = {\n                page,\n                limit\n            };\n            // Add search query if provided\n            if (searchQuery && searchQuery.trim()) {\n                // For API, we might need to search by team names or other fields\n                // Since API doesn't have a generic search, we'll filter client-side for now\n                filters.search = searchQuery.trim();\n            }\n            if (statusFilter) filters.status = statusFilter;\n            if (leagueFilter) filters.league = leagueFilter;\n            if (selectedDate) filters.date = (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_14__.convertLocalDateToUTC)(selectedDate);\n            return _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixtures(filters);\n        },\n        staleTime: 30000,\n        retry: false,\n        onError: (error)=>{\n            console.log(\"API is down, using mock data:\", (error === null || error === void 0 ? void 0 : error.message) || \"Unknown error\");\n        }\n    });\n    // Use mock data if API fails or no data\n    const rawData = data || mockFixtures;\n    const isUsingMockData = !data;\n    // Apply client-side filtering for mock data when search is active\n    const fixturesData = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        if (!isUsingMockData || !searchQuery.trim()) {\n            return rawData;\n        }\n        // Filter mock data based on search query\n        const filteredData = rawData.data.filter((fixture)=>{\n            var _fixture_homeTeamName, _fixture_awayTeamName, _fixture_leagueName, _fixture_venue, _fixture_status;\n            const searchLower = searchQuery.toLowerCase();\n            return ((_fixture_homeTeamName = fixture.homeTeamName) === null || _fixture_homeTeamName === void 0 ? void 0 : _fixture_homeTeamName.toLowerCase().includes(searchLower)) || ((_fixture_awayTeamName = fixture.awayTeamName) === null || _fixture_awayTeamName === void 0 ? void 0 : _fixture_awayTeamName.toLowerCase().includes(searchLower)) || ((_fixture_leagueName = fixture.leagueName) === null || _fixture_leagueName === void 0 ? void 0 : _fixture_leagueName.toLowerCase().includes(searchLower)) || ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.toLowerCase().includes(searchLower)) || ((_fixture_status = fixture.status) === null || _fixture_status === void 0 ? void 0 : _fixture_status.toLowerCase().includes(searchLower));\n        });\n        return {\n            ...rawData,\n            data: filteredData,\n            meta: {\n                ...rawData.meta,\n                totalItems: filteredData.length,\n                totalPages: Math.ceil(filteredData.length / limit)\n            },\n            // For backward compatibility with mock structure\n            totalItems: filteredData.length,\n            totalPages: Math.ceil(filteredData.length / limit)\n        };\n    }, [\n        rawData,\n        searchQuery,\n        isUsingMockData,\n        limit\n    ]);\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_17__.useMutation)({\n        mutationFn: (fixture)=>{\n            const fixtureId = fixture.externalId || fixture.id;\n            return _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.deleteFixture(fixtureId);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            console.log(\"Fixture deleted successfully\");\n            setDeleteModalOpen(false);\n            setSelectedFixture(null);\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete fixture:\", error.message);\n        }\n    });\n    // Define table columns\n    const columns = [\n        {\n            key: \"date\",\n            title: \"Date & Time\",\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_12__.DateTimeDisplay, {\n                        dateTime: value,\n                        showDate: true,\n                        showTime: true,\n                        isClickable: true,\n                        onClick: ()=>{\n                            const clickedDate = new Date(value);\n                            setSelectedDate(clickedDate);\n                            setDateFilterModalOpen(true);\n                        },\n                        className: \"min-w-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"match\",\n            title: \"Match\",\n            sortable: false,\n            headerClassName: \"text-center\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-4 py-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-2 min-w-[80px]\",\n                            children: [\n                                row.homeTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"\".concat(\"http://172.31.213.61\", \"/\").concat(row.homeTeamLogo),\n                                    alt: row.homeTeamName,\n                                    className: \"w-8 h-8 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-center leading-tight max-w-[80px] break-words\",\n                                    children: row.homeTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 font-bold text-sm\",\n                                children: \"VS\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-2 min-w-[80px]\",\n                            children: [\n                                row.awayTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"\".concat(\"http://172.31.213.61\", \"/\").concat(row.awayTeamLogo),\n                                    alt: row.awayTeamName,\n                                    className: \"w-8 h-8 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-center leading-tight max-w-[80px] break-words\",\n                                    children: row.awayTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            title: \"Score\",\n            align: \"center\",\n            render: (_, row)=>/*#__PURE__*/ {\n                var _row_goalsHome, _row_goalsAway;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                (_row_goalsHome = row.goalsHome) !== null && _row_goalsHome !== void 0 ? _row_goalsHome : \"-\",\n                                \" - \",\n                                (_row_goalsAway = row.goalsAway) !== null && _row_goalsAway !== void 0 ? _row_goalsAway : \"-\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"HT: \",\n                                row.scoreHalftimeHome,\n                                \" - \",\n                                row.scoreHalftimeAway\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            filterable: true,\n            render: (value, row)=>{\n                const getStatusColor = (status)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                        case \"HT\":\n                            return \"bg-green-100 text-green-800\";\n                        case \"FT\":\n                            return \"bg-gray-100 text-gray-800\";\n                        case \"NS\":\n                            return \"bg-blue-100 text-blue-800\";\n                        case \"CANC\":\n                        case \"PST\":\n                            return \"bg-red-100 text-red-800\";\n                        default:\n                            return \"bg-yellow-100 text-yellow-800\";\n                    }\n                };\n                const getStatusText = (status, elapsed)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                            return \"\".concat(elapsed, \"'\");\n                        case \"HT\":\n                            return \"Half Time\";\n                        case \"FT\":\n                            return \"Full Time\";\n                        case \"NS\":\n                            return \"Not Started\";\n                        case \"CANC\":\n                            return \"Cancelled\";\n                        case \"PST\":\n                            return \"Postponed\";\n                        default:\n                            return status;\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: getStatusColor(value),\n                    children: getStatusText(value, row.elapsed)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"leagueName\",\n            title: \"League\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Broadcast Links\",\n                            onClick: ()=>handleBroadcastLinksModal(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit\",\n                            onClick: ()=>handleEditFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete\",\n                            onClick: ()=>handleDeleteFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Handler functions\n    const handleSearch = ()=>{\n        setSearchQuery(searchInput.trim());\n        setPage(1); // Reset to first page when searching\n    };\n    const handleSearchKeyPress = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearch();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setSearchInput(\"\");\n        setSearchQuery(\"\");\n        setPage(1);\n    };\n    const handleViewFixture = (fixture)=>{\n        // Navigate to fixture detail page using externalId\n        const fixtureId = fixture.externalId || fixture.id;\n        window.open(\"/dashboard/fixtures/\".concat(fixtureId), \"_blank\");\n    };\n    const handleEditFixture = (fixture)=>{\n        // Navigate to edit page using externalId\n        const fixtureId = fixture.externalId || fixture.id;\n        window.open(\"/dashboard/fixtures/\".concat(fixtureId, \"/edit\"), \"_blank\");\n    };\n    const handleDeleteFixture = (fixture)=>{\n        setSelectedFixture(fixture);\n        setDeleteModalOpen(true);\n    };\n    const handleBroadcastLinksModal = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        if (selectedFixture) {\n            deleteMutation.mutate(selectedFixture);\n        }\n    };\n    const handleBulkSync = async ()=>{\n        try {\n            console.log(\"Starting fixtures sync...\");\n            // This would call the sync API\n            // await fixturesApi.syncFixtures();\n            console.log(\"Fixtures sync completed\");\n            refetch();\n        } catch (error) {\n            console.error(\"Sync failed:\", error.message);\n        }\n    };\n    // Date filter handlers\n    const handleApplyDateFilter = (date)=>{\n        setSelectedDate(date);\n        setPage(1); // Reset to first page when filtering\n    };\n    const handleResetDateFilter = ()=>{\n        setSelectedDate(undefined);\n        setPage(1); // Reset to first page when clearing filter\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Fixtures Management\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage football fixtures and match data\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n            lineNumber: 450,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fixtures Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage football fixtures and match data\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 11\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkSync,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Export functionality\n                                    console.log(\"Export feature coming soon\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>window.open(\"/dashboard/fixtures/create\", \"_blank\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Fixture\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 474,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_meta = data.meta) === null || _data_meta === void 0 ? void 0 : (_data_meta_totalItems = _data_meta.totalItems) === null || _data_meta_totalItems === void 0 ? void 0 : _data_meta_totalItems.toLocaleString()) || \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.filter((f)=>[\n                                            \"1H\",\n                                            \"2H\",\n                                            \"HT\"\n                                        ].includes(f.status)).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Live Matches\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.filter((f)=>f.status === \"NS\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Upcoming\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.filter((f)=>f.status === \"FT\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 522,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"All Fixtures\",\n                                                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-normal\",\n                                                    children: \"Demo Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: isUsingMockData ? \"Showing demo data - API backend is not available\" : \"Complete list of football fixtures with real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_picker__WEBPACK_IMPORTED_MODULE_11__.DatePicker, {\n                                            date: selectedDate,\n                                            onDateChange: setSelectedDate,\n                                            placeholder: \"Filter by date\",\n                                            className: \"w-[200px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedDate(undefined),\n                                            className: \"px-2\",\n                                            title: \"Clear date filter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 562,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                            rows: 10,\n                            columns: 7\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n                            data: (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.data) || [],\n                            columns: columns,\n                            loading: isLoading && !isUsingMockData,\n                            searchable: true,\n                            searchPlaceholder: \"Search fixtures...\",\n                            showSearchButton: true,\n                            searchValue: searchInput,\n                            onSearchChange: setSearchInput,\n                            onSearchSubmit: handleSearch,\n                            onSearchClear: handleClearSearch,\n                            pagination: {\n                                page,\n                                limit,\n                                total: (fixturesData === null || fixturesData === void 0 ? void 0 : (_fixturesData_meta = fixturesData.meta) === null || _fixturesData_meta === void 0 ? void 0 : _fixturesData_meta.totalItems) || (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.totalItems) || 0,\n                                onPageChange: setPage,\n                                onLimitChange: (newLimit)=>{\n                                    setLimit(newLimit);\n                                    setPage(1); // Reset to first page when changing limit\n                                }\n                            },\n                            emptyMessage: \"No fixtures found\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 561,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>{\n                    setDeleteModalOpen(false);\n                    setSelectedFixture(null);\n                },\n                onConfirm: confirmDelete,\n                title: \"Delete Fixture\",\n                message: selectedFixture ? 'Are you sure you want to delete the fixture \"'.concat(selectedFixture.homeTeamName, \" vs \").concat(selectedFixture.awayTeamName, '\"? This action cannot be undone.') : \"Are you sure you want to delete this fixture?\",\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                variant: \"destructive\",\n                loading: deleteMutation.isLoading\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 634,\n                columnNumber: 7\n            }, this),\n            selectedFixtureForBroadcast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fixtures_BroadcastLinksModal__WEBPACK_IMPORTED_MODULE_10__.BroadcastLinksModal, {\n                isOpen: broadcastLinksModalOpen,\n                onClose: ()=>{\n                    setBroadcastLinksModalOpen(false);\n                    setSelectedFixtureForBroadcast(null);\n                },\n                fixture: selectedFixtureForBroadcast\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 655,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_13__.DateFilterModal, {\n                isOpen: dateFilterModalOpen,\n                onClose: ()=>setDateFilterModalOpen(false),\n                selectedDate: selectedDate,\n                onDateSelect: setSelectedDate,\n                onApplyFilter: handleApplyDateFilter,\n                onResetFilter: handleResetDateFilter\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 666,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 472,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesPage, \"I3nI8t96W3QSaTr1m7GNDLV7lbA=\", false, function() {\n    return [\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_17__.useMutation\n    ];\n});\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/fixtures/BroadcastLinksModal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/fixtures/BroadcastLinksModal.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BroadcastLinksModal: function() { return /* binding */ BroadcastLinksModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_api_broadcast_links__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/broadcast-links */ \"(app-pages-browser)/./src/lib/api/broadcast-links.ts\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ BroadcastLinksModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst BroadcastLinksModal = (param)=>{\n    let { isOpen, onClose, fixture } = param;\n    _s();\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingLink, setEditingLink] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        url: \"\",\n        comment: \"\",\n        language: \"English\",\n        quality: \"HD\"\n    });\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.useQueryClient)();\n    // Fetch broadcast links for this fixture\n    const { data: linksData, isLoading, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQuery)({\n        queryKey: [\n            \"broadcast-links\",\n            fixture.externalId || fixture.id\n        ],\n        queryFn: ()=>_lib_api_broadcast_links__WEBPACK_IMPORTED_MODULE_7__.broadcastLinksApi.getBroadcastLinksByFixture(fixture.externalId || fixture.id),\n        enabled: isOpen\n    });\n    const links = (linksData === null || linksData === void 0 ? void 0 : linksData.data) || [];\n    // Create mutation\n    const createMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutation)({\n        mutationFn: (data)=>_lib_api_broadcast_links__WEBPACK_IMPORTED_MODULE_7__.broadcastLinksApi.createBroadcastLink(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"broadcast-links\"\n                ]\n            });\n            setShowAddForm(false);\n            resetForm();\n        },\n        onError: (error)=>{\n            console.error(\"Failed to create broadcast link:\", error.message);\n        }\n    });\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _lib_api_broadcast_links__WEBPACK_IMPORTED_MODULE_7__.broadcastLinksApi.updateBroadcastLink(id, data);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"broadcast-links\"\n                ]\n            });\n            setEditingLink(null);\n            resetForm();\n        },\n        onError: (error)=>{\n            console.error(\"Failed to update broadcast link:\", error.message);\n        }\n    });\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutation)({\n        mutationFn: (id)=>_lib_api_broadcast_links__WEBPACK_IMPORTED_MODULE_7__.broadcastLinksApi.deleteBroadcastLink(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"broadcast-links\"\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete broadcast link:\", error.message);\n        }\n    });\n    const resetForm = ()=>{\n        setFormData({\n            title: \"\",\n            url: \"\",\n            comment: \"\",\n            language: \"English\",\n            quality: \"HD\"\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!formData.title.trim() || !formData.url.trim() || !formData.comment.trim()) {\n            return;\n        }\n        const submitData = {\n            fixtureId: fixture.externalId || fixture.id,\n            linkName: formData.title.trim(),\n            linkUrl: formData.url.trim(),\n            linkComment: formData.comment.trim(),\n            language: formData.language,\n            quality: formData.quality\n        };\n        if (editingLink) {\n            updateMutation.mutate({\n                id: editingLink.id,\n                data: submitData\n            });\n        } else {\n            createMutation.mutate(submitData);\n        }\n    };\n    const handleEdit = (link)=>{\n        setEditingLink(link);\n        setFormData({\n            title: link.linkName,\n            url: link.linkUrl,\n            comment: link.linkComment || \"\",\n            language: link.language || \"English\",\n            quality: link.quality || \"HD\"\n        });\n        setShowAddForm(true);\n    };\n    const handleDelete = (link)=>{\n        if (confirm('Are you sure you want to delete \"'.concat(link.linkName, '\"?'))) {\n            deleteMutation.mutate(link.id);\n        }\n    };\n    const handleCancel = ()=>{\n        setShowAddForm(false);\n        setEditingLink(null);\n        resetForm();\n    };\n    const getQualityColor = (quality)=>{\n        switch(quality.toLowerCase()){\n            case \"4k\":\n            case \"uhd\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"hd\":\n            case \"1080p\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"sd\":\n            case \"720p\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getLanguageFlag = (language)=>{\n        const flags = {\n            en: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            es: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n            fr: \"\\uD83C\\uDDEB\\uD83C\\uDDF7\",\n            de: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            it: \"\\uD83C\\uDDEE\\uD83C\\uDDF9\",\n            pt: \"\\uD83C\\uDDF5\\uD83C\\uDDF9\",\n            ar: \"\\uD83C\\uDDF8\\uD83C\\uDDE6\"\n        };\n        return flags[language] || \"\\uD83C\\uDF10\";\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 49\n                                            }, undefined),\n                                            \"Broadcast Links - \",\n                                            fixture.homeTeamName,\n                                            \" vs \",\n                                            fixture.awayTeamName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Manage streaming links for this fixture\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 43\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 37\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 43\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 37\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 31\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        !showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        links.length,\n                                        \" broadcast link\",\n                                        links.length !== 1 ? \"s\" : \"\",\n                                        \" available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setShowAddForm(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        \"Add Link\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 37\n                        }, undefined),\n                        showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: editingLink ? \"Edit Broadcast Link\" : \"Add New Broadcast Link\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 55\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleCancel,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 55\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 49\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"title\",\n                                                                children: \"Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"title\",\n                                                                value: formData.title,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        title: e.target.value\n                                                                    }),\n                                                                placeholder: \"e.g., ESPN HD Stream\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"url\",\n                                                                children: \"URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"url\",\n                                                                type: \"url\",\n                                                                value: formData.url,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        url: e.target.value\n                                                                    }),\n                                                                placeholder: \"https://...\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"language\",\n                                                                children: \"Language\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"language\",\n                                                                value: formData.language,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        language: e.target.value\n                                                                    }),\n                                                                className: \"w-full p-2 border rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"en\",\n                                                                        children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 English\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"es\",\n                                                                        children: \"\\uD83C\\uDDEA\\uD83C\\uDDF8 Spanish\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"fr\",\n                                                                        children: \"\\uD83C\\uDDEB\\uD83C\\uDDF7 French\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"de\",\n                                                                        children: \"\\uD83C\\uDDE9\\uD83C\\uDDEA German\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"it\",\n                                                                        children: \"\\uD83C\\uDDEE\\uD83C\\uDDF9 Italian\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"pt\",\n                                                                        children: \"\\uD83C\\uDDF5\\uD83C\\uDDF9 Portuguese\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ar\",\n                                                                        children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6 Arabic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 73\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"quality\",\n                                                                children: \"Quality\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"quality\",\n                                                                value: formData.quality,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        quality: e.target.value\n                                                                    }),\n                                                                className: \"w-full p-2 border rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"4K\",\n                                                                        children: \"4K Ultra HD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"HD\",\n                                                                        children: \"HD (1080p)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"720p\",\n                                                                        children: \"HD (720p)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SD\",\n                                                                        children: \"SD (480p)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 73\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 61\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 55\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"comment\",\n                                                        children: \"Comment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"comment\",\n                                                        value: formData.comment,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                comment: e.target.value\n                                                            }),\n                                                        placeholder: \"e.g., Official HD stream with English commentary\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 61\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 55\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end space-x-2 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        onClick: handleCancel,\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: createMutation.isLoading || updateMutation.isLoading,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            editingLink ? \"Update\" : \"Add\",\n                                                            \" Link\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 61\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 55\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 49\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 37\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                                rows: 3,\n                                columns: 1\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 43\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 mb-4\",\n                                        children: \"Failed to load broadcast links\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>console.log(\"Mock: Retry loading\"),\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 49\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 43\n                            }, undefined) : links.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"No broadcast links added yet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Add a link to get started\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    !showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>setShowAddForm(true),\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 61\n                                            }, undefined),\n                                            \"Add First Link\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 55\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 43\n                            }, undefined) : links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        link.linkName.toLowerCase().includes(\"comment\") || link.linkName.toLowerCase().includes(\"chat\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 91\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 91\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: link.linkName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 85\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 79\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: getQualityColor(link.quality || \"HD\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"mr-1 h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 85\n                                                                        }, undefined),\n                                                                        link.quality || \"HD\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 79\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: [\n                                                                        getLanguageFlag(link.language || \"English\"),\n                                                                        \" \",\n                                                                        link.language || \"English\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 79\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-green-50 text-green-700\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 79\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 73\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 flex items-center space-x-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: link.linkUrl,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-blue-600 hover:text-blue-800 flex items-center space-x-1 truncate max-w-[300px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: link.linkUrl\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 85\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 85\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 79\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 73\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 67\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleEdit(link),\n                                                            disabled: (editingLink === null || editingLink === void 0 ? void 0 : editingLink.id) === link.id,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 79\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 73\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleDelete(link),\n                                                            disabled: deleteMutation.isLoading,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 79\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 73\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 67\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 61\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 55\n                                    }, undefined)\n                                }, link.id, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 49\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 25\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n            lineNumber: 175,\n            columnNumber: 19\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n        lineNumber: 174,\n        columnNumber: 13\n    }, undefined);\n};\n_s(BroadcastLinksModal, \"dhmRdZt8nnrJSbbSahBtjATqsf0=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = BroadcastLinksModal;\nvar _c;\n$RefreshReg$(_c, \"BroadcastLinksModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/fixtures/BroadcastLinksModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: function() { return /* binding */ Label; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/ui/label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Label;\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUU4QjtBQUN5QjtBQUNVO0FBRWpDO0FBRWhDLE1BQU1JLGdCQUFnQkYsNkRBQUdBLENBQ3ZCO0FBR0YsTUFBTUcsc0JBQVFMLDZDQUFnQixNQUk1QixRQUEwQk87UUFBekIsRUFBRUMsU0FBUyxFQUFFLEdBQUdDLE9BQU87eUJBQ3hCLDhEQUFDUix1REFBbUI7UUFDbEJNLEtBQUtBO1FBQ0xDLFdBQVdMLDhDQUFFQSxDQUFDQyxpQkFBaUJJO1FBQzlCLEdBQUdDLEtBQUs7Ozs7Ozs7O0FBR2JKLE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4PzEzZWIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXG4gIFwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCJcbilcblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PiAmXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8TGFiZWxQcmltaXRpdmUuUm9vdFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24obGFiZWxWYXJpYW50cygpLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5MYWJlbC5kaXNwbGF5TmFtZSA9IExhYmVsUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWVcblxuZXhwb3J0IHsgTGFiZWwgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjdmEiLCJjbiIsImxhYmVsVmFyaWFudHMiLCJMYWJlbCIsImZvcndhcmRSZWYiLCJyZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsIlJvb3QiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/label.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/broadcast-links.ts":
/*!****************************************!*\
  !*** ./src/lib/api/broadcast-links.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   broadcastLinksApi: function() { return /* binding */ broadcastLinksApi; }\n/* harmony export */ });\n/* harmony import */ var _lib_stores_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/stores/auth */ \"(app-pages-browser)/./src/lib/stores/auth.ts\");\n\n// Helper function to get auth headers\nconst getAuthHeaders = ()=>{\n    const authState = _lib_stores_auth__WEBPACK_IMPORTED_MODULE_0__.useAuthStore.getState();\n    const token = authState.accessToken;\n    console.log(\"\\uD83D\\uDD11 Auth Debug:\", {\n        isAuthenticated: authState.isAuthenticated,\n        hasToken: !!token,\n        tokenLength: (token === null || token === void 0 ? void 0 : token.length) || 0,\n        tokenPreview: (token === null || token === void 0 ? void 0 : token.substring(0, 20)) + \"...\" || 0\n    });\n    if (!token) {\n        console.error(\"❌ No access token found in auth store!\");\n        // Try to get from localStorage as fallback\n        const fallbackToken = localStorage.getItem(\"accessToken\");\n        if (fallbackToken) {\n            console.log(\"\\uD83D\\uDD04 Using fallback token from localStorage\");\n            return {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(fallbackToken)\n            };\n        }\n    }\n    return {\n        \"Content-Type\": \"application/json\",\n        ...token && {\n            \"Authorization\": \"Bearer \".concat(token)\n        }\n    };\n};\nconst broadcastLinksApi = {\n    // Get all broadcast links with filters\n    getBroadcastLinks: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await fetch(\"/api/broadcast-links?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch broadcast links: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Get broadcast links for a specific fixture\n    getBroadcastLinksByFixture: async (fixtureId)=>{\n        const response = await fetch(\"/api/broadcast-links/fixture/\".concat(fixtureId), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch broadcast links for fixture: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Get single broadcast link\n    getBroadcastLinkById: async (id)=>{\n        const response = await fetch(\"/api/broadcast-links/\".concat(id), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch broadcast link: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Create new broadcast link\n    createBroadcastLink: async (data)=>{\n        const response = await fetch(\"/api/broadcast-links\", {\n            method: \"POST\",\n            headers: getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to create broadcast link: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Update broadcast link\n    updateBroadcastLink: async (id, data)=>{\n        const response = await fetch(\"/api/broadcast-links/\".concat(id), {\n            method: \"PUT\",\n            headers: getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to update broadcast link: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Delete broadcast link\n    deleteBroadcastLink: async (id)=>{\n        const response = await fetch(\"/api/broadcast-links/\".concat(id), {\n            method: \"DELETE\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to delete broadcast link: \".concat(response.statusText));\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/broadcast-links.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: function() { return /* binding */ Label; },\n/* harmony export */   Root: function() { return /* binding */ Root; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // src/label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = (props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            var _props_onMouseDown;\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            (_props_onMouseDown = props.onMouseDown) === null || _props_onMouseDown === void 0 ? void 0 : _props_onMouseDown.call(props, event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\n_c1 = Label;\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs\n"));

/***/ })

});