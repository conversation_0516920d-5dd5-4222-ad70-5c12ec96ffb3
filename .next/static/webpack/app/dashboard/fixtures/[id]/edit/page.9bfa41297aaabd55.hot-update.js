"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _leagues_data, _teams_data, _homeTeams, _awayTeams;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Smart loading with search and pagination\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teamSearch, setTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Fetch leagues with search only\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"search\",\n            leagueSearch\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100,\n                ...leagueSearch && {\n                    search: leagueSearch\n                }\n            })\n    });\n    // Fetch teams with search and pagination\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"search\",\n            teamSearch\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                ...teamSearch && {\n                    search: teamSearch\n                }\n            })\n    });\n    // No data accumulation needed - direct use of API data\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            // 🔍 DEBUG: Check if fixture uses internal or external IDs\n            console.log(\"\\uD83D\\uDD0D FIXTURE ID ANALYSIS:\", {\n                \"fixture.homeTeamId\": fixture.homeTeamId,\n                \"fixture.awayTeamId\": fixture.awayTeamId,\n                \"fixture.leagueId\": fixture.leagueId,\n                \"fixture object\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search handlers only\n    const handleLeagueSearch = (query)=>{\n        console.log(\"\\uD83C\\uDFC6 League search:\", query);\n        setLeagueSearch(query);\n    };\n    const handleTeamSearch = (query)=>{\n        console.log(\"\\uD83C\\uDFE0⚽ Team search:\", query);\n        setTeamSearch(query);\n    };\n    const statusOptions = [\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        {\n            value: \"FT\",\n            label: \"Match Finished\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Use direct API data for options\n    const leagueOptions = (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league, index)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season,\n            uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n        }))) || [];\n    const teamOptions = (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }))) || [];\n    // Use same team options for both home and away\n    const homeTeamOptions = teamOptions;\n    const awayTeamOptions = teamOptions;\n    // Debug options count\n    console.log(\"\\uD83D\\uDCCA OPTIONS DEBUG:\", {\n        leagueOptionsCount: leagueOptions.length,\n        homeTeamOptionsCount: homeTeamOptions.length,\n        awayTeamOptionsCount: awayTeamOptions.length,\n        leagueMeta: leagues === null || leagues === void 0 ? void 0 : leagues.meta,\n        homeTeamMeta: (_homeTeams = homeTeams) === null || _homeTeams === void 0 ? void 0 : _homeTeams.meta,\n        awayTeamMeta: (_awayTeams = awayTeams) === null || _awayTeams === void 0 ? void 0 : _awayTeams.meta\n    });\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            homeTeamsLoading,\n            awayTeamsLoading,\n            leaguesLoading,\n            homeTeamOptionsCount: homeTeamOptions.length,\n            awayTeamOptionsCount: awayTeamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        homeTeamsLoading,\n        awayTeamsLoading,\n        leaguesLoading,\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = homeTeamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = awayTeamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        homeTeams: homeTeamOptions.length,\n        awayTeams: awayTeamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (homeTeamOptions.length > 0 && awayTeamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // No need to force dropdown re-render - stable keys prevent issues\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (homeTeamOptions.length > 0) {\n        console.log(\"Sample Home Team Options:\", homeTeamOptions.slice(0, 3));\n        console.log(\"Home Team Option Types:\", homeTeamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const homeTeamInOptions = homeTeamOptions.find((t)=>t.value === \"4450\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC HOME TEAM CHECK:\", {\n            \"homeTeam (4450)\": homeTeamInOptions,\n            \"totalHomeTeams\": homeTeamOptions.length\n        });\n    }\n    if (awayTeamOptions.length > 0) {\n        console.log(\"Sample Away Team Options:\", awayTeamOptions.slice(0, 3));\n        console.log(\"Away Team Option Types:\", awayTeamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const awayTeamInOptions = awayTeamOptions.find((t)=>t.value === \"20022\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC AWAY TEAM CHECK:\", {\n            \"awayTeam (20022)\": awayTeamInOptions,\n            \"totalAwayTeams\": awayTeamOptions.length\n        });\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n        // 🔍 Check if our specific league exists\n        const leagueInOptions = leagueOptions.find((l)=>l.value === \"570\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC LEAGUE CHECK:\", {\n            \"league (570)\": leagueInOptions,\n            \"totalLeagues\": leagueOptions.length\n        });\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: homeTeamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: awayTeamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 410,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            isLoading: leaguesLoading\n                        }, \"league-search\", false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 448,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || homeTeamsLoading || awayTeamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 504,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 503,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || homeTeamsError || awayTeamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 32\n                                }, this),\n                                homeTeamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load home teams: \",\n                                        homeTeamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 34\n                                }, this),\n                                awayTeamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load away teams: \",\n                                        awayTeamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 554,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 553,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 545,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 573,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: homeTeamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: homeTeamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: homeTeamsLoading,\n                                                            onSearch: handleHomeTeamSearch,\n                                                            isLoading: homeTeamsLoading\n                                                        }, \"home-team-search\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: awayTeamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: awayTeamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: awayTeamsLoading,\n                                                            onSearch: handleAwayTeamSearch,\n                                                            isLoading: awayTeamsLoading\n                                                        }, \"away-team-search\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isLoading ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 795,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 587,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 571,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"mu0Q8T2Z2S48Hp1SSbEbfNF3m5w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});