"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Smart loading with search and pagination - SEPARATED for Home/Away\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [awayTeamSearch, setAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leaguePage, setLeaguePage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [homeTeamPage, setHomeTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [awayTeamPage, setAwayTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [allLeagues, setAllLeagues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allHomeTeams, setAllHomeTeams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAwayTeams, setAllAwayTeams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues with search and pagination\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"search\",\n            leagueSearch,\n            leaguePage\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100,\n                page: leaguePage,\n                ...leagueSearch && {\n                    search: leagueSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch home teams with search and pagination\n    const { data: homeTeams, isLoading: homeTeamsLoading, error: homeTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"home-teams\",\n            \"search\",\n            homeTeamSearch,\n            homeTeamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: homeTeamPage,\n                ...homeTeamSearch && {\n                    search: homeTeamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch away teams with search and pagination\n    const { data: awayTeams, isLoading: awayTeamsLoading, error: awayTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"away-teams\",\n            \"search\",\n            awayTeamSearch,\n            awayTeamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: awayTeamPage,\n                ...awayTeamSearch && {\n                    search: awayTeamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Track previous search to detect search changes\n    const [prevLeagueSearch, setPrevLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [prevHomeTeamSearch, setPrevHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [prevAwayTeamSearch, setPrevAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Accumulate leagues data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (leagues === null || leagues === void 0 ? void 0 : leagues.data) {\n            // Check if search changed\n            const searchChanged = leagueSearch !== prevLeagueSearch;\n            if (leaguePage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"\\uD83D\\uDD04 Replacing leagues data:\", {\n                    page: leaguePage,\n                    searchChanged,\n                    search: leagueSearch\n                });\n                setAllLeagues(leagues.data);\n                setPrevLeagueSearch(leagueSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"\\uD83D\\uDD04 Appending leagues data:\", {\n                    page: leaguePage,\n                    existing: allLeagues.length,\n                    new: leagues.data.length\n                });\n                setAllLeagues((prev)=>{\n                    const existingIds = new Set(prev.map((l)=>l.externalId));\n                    const newLeagues = leagues.data.filter((l)=>!existingIds.has(l.externalId));\n                    return [\n                        ...prev,\n                        ...newLeagues\n                    ];\n                });\n            }\n        }\n    }, [\n        leagues,\n        leaguePage,\n        leagueSearch,\n        prevLeagueSearch,\n        allLeagues.length\n    ]);\n    // Accumulate HOME teams data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (homeTeams === null || homeTeams === void 0 ? void 0 : homeTeams.data) {\n            // Check if search changed\n            const searchChanged = homeTeamSearch !== prevHomeTeamSearch;\n            if (homeTeamPage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"\\uD83C\\uDFE0 Replacing HOME teams data:\", {\n                    page: homeTeamPage,\n                    searchChanged,\n                    search: homeTeamSearch\n                });\n                setAllHomeTeams(homeTeams.data);\n                setPrevHomeTeamSearch(homeTeamSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"\\uD83C\\uDFE0 Appending HOME teams data:\", {\n                    page: homeTeamPage,\n                    existing: allHomeTeams.length,\n                    new: homeTeams.data.length\n                });\n                setAllHomeTeams((prev)=>{\n                    const existingIds = new Set(prev.map((t)=>t.id || t.externalId));\n                    const newTeams = homeTeams.data.filter((t)=>!existingIds.has(t.id || t.externalId));\n                    console.log(\"\\uD83C\\uDFE0 Adding new HOME teams:\", {\n                        existing: prev.length,\n                        new: newTeams.length,\n                        total: prev.length + newTeams.length\n                    });\n                    return [\n                        ...prev,\n                        ...newTeams\n                    ];\n                });\n            }\n        }\n    }, [\n        homeTeams,\n        homeTeamPage,\n        homeTeamSearch,\n        prevHomeTeamSearch\n    ]);\n    // Accumulate AWAY teams data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (awayTeams === null || awayTeams === void 0 ? void 0 : awayTeams.data) {\n            // Check if search changed\n            const searchChanged = awayTeamSearch !== prevAwayTeamSearch;\n            if (awayTeamPage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"✈️ Replacing AWAY teams data:\", {\n                    page: awayTeamPage,\n                    searchChanged,\n                    search: awayTeamSearch\n                });\n                setAllAwayTeams(awayTeams.data);\n                setPrevAwayTeamSearch(awayTeamSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"✈️ Appending AWAY teams data:\", {\n                    page: awayTeamPage,\n                    existing: allAwayTeams.length,\n                    new: awayTeams.data.length\n                });\n                setAllAwayTeams((prev)=>{\n                    const existingIds = new Set(prev.map((t)=>t.id || t.externalId));\n                    const newTeams = awayTeams.data.filter((t)=>!existingIds.has(t.id || t.externalId));\n                    console.log(\"✈️ Adding new AWAY teams:\", {\n                        existing: prev.length,\n                        new: newTeams.length,\n                        total: prev.length + newTeams.length\n                    });\n                    return [\n                        ...prev,\n                        ...newTeams\n                    ];\n                });\n            }\n        }\n    }, [\n        awayTeams,\n        awayTeamPage,\n        awayTeamSearch,\n        prevAwayTeamSearch\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            // 🔍 DEBUG: Check if fixture uses internal or external IDs\n            console.log(\"\\uD83D\\uDD0D FIXTURE ID ANALYSIS:\", {\n                \"fixture.homeTeamId\": fixture.homeTeamId,\n                \"fixture.awayTeamId\": fixture.awayTeamId,\n                \"fixture.leagueId\": fixture.leagueId,\n                \"fixture object\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search and pagination handlers\n    const handleLeagueSearch = (query)=>{\n        console.log(\"\\uD83C\\uDFC6 League search:\", query);\n        setLeagueSearch(query);\n        setLeaguePage(1); // Reset to first page on new search\n    };\n    const handleHomeTeamSearch = (query)=>{\n        console.log(\"\\uD83C\\uDFE0 Home team search:\", query);\n        setHomeTeamSearch(query);\n        setHomeTeamPage(1); // Reset to first page on new search\n    };\n    const handleAwayTeamSearch = (query)=>{\n        console.log(\"✈️ Away team search:\", query);\n        setAwayTeamSearch(query);\n        setAwayTeamPage(1); // Reset to first page on new search\n    };\n    const handleLeagueLoadMore = ()=>{\n        var _leagues_meta, _leagues_meta1, _leagues_meta2;\n        console.log(\"\\uD83D\\uDD04 League Load More:\", {\n            currentPage: leaguePage,\n            totalPages: leagues === null || leagues === void 0 ? void 0 : (_leagues_meta = leagues.meta) === null || _leagues_meta === void 0 ? void 0 : _leagues_meta.totalPages,\n            hasMore: (leagues === null || leagues === void 0 ? void 0 : (_leagues_meta1 = leagues.meta) === null || _leagues_meta1 === void 0 ? void 0 : _leagues_meta1.totalPages) && leaguePage < leagues.meta.totalPages,\n            allLeaguesCount: allLeagues.length\n        });\n        if ((leagues === null || leagues === void 0 ? void 0 : (_leagues_meta2 = leagues.meta) === null || _leagues_meta2 === void 0 ? void 0 : _leagues_meta2.totalPages) && leaguePage < leagues.meta.totalPages) {\n            console.log(\"✅ Loading more leagues, page:\", leaguePage + 1);\n            setLeaguePage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more leagues\");\n        }\n    };\n    const handleHomeTeamLoadMore = ()=>{\n        var _homeTeams_meta, _homeTeams_meta1, _homeTeams_meta2;\n        console.log(\"\\uD83C\\uDFE0 Home Team Load More:\", {\n            currentPage: homeTeamPage,\n            totalPages: homeTeams === null || homeTeams === void 0 ? void 0 : (_homeTeams_meta = homeTeams.meta) === null || _homeTeams_meta === void 0 ? void 0 : _homeTeams_meta.totalPages,\n            hasMore: (homeTeams === null || homeTeams === void 0 ? void 0 : (_homeTeams_meta1 = homeTeams.meta) === null || _homeTeams_meta1 === void 0 ? void 0 : _homeTeams_meta1.totalPages) && homeTeamPage < homeTeams.meta.totalPages,\n            allHomeTeamsCount: allHomeTeams.length\n        });\n        if ((homeTeams === null || homeTeams === void 0 ? void 0 : (_homeTeams_meta2 = homeTeams.meta) === null || _homeTeams_meta2 === void 0 ? void 0 : _homeTeams_meta2.totalPages) && homeTeamPage < homeTeams.meta.totalPages) {\n            console.log(\"✅ Loading more HOME teams, page:\", homeTeamPage + 1);\n            setHomeTeamPage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more HOME teams\");\n        }\n    };\n    const handleAwayTeamLoadMore = ()=>{\n        var _awayTeams_meta, _awayTeams_meta1, _awayTeams_meta2;\n        console.log(\"✈️ Away Team Load More:\", {\n            currentPage: awayTeamPage,\n            totalPages: awayTeams === null || awayTeams === void 0 ? void 0 : (_awayTeams_meta = awayTeams.meta) === null || _awayTeams_meta === void 0 ? void 0 : _awayTeams_meta.totalPages,\n            hasMore: (awayTeams === null || awayTeams === void 0 ? void 0 : (_awayTeams_meta1 = awayTeams.meta) === null || _awayTeams_meta1 === void 0 ? void 0 : _awayTeams_meta1.totalPages) && awayTeamPage < awayTeams.meta.totalPages,\n            allAwayTeamsCount: allAwayTeams.length\n        });\n        if ((awayTeams === null || awayTeams === void 0 ? void 0 : (_awayTeams_meta2 = awayTeams.meta) === null || _awayTeams_meta2 === void 0 ? void 0 : _awayTeams_meta2.totalPages) && awayTeamPage < awayTeams.meta.totalPages) {\n            console.log(\"✅ Loading more AWAY teams, page:\", awayTeamPage + 1);\n            setAwayTeamPage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more AWAY teams\");\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        {\n            value: \"FT\",\n            label: \"Match Finished\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Use accumulated data for options with unique keys\n    const leagueOptions = allLeagues.map((league, index)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season,\n            uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n        }));\n    const homeTeamOptions = allHomeTeams.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"home-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }));\n    const awayTeamOptions = allAwayTeams.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"away-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }));\n    // Debug options count\n    console.log(\"\\uD83D\\uDCCA OPTIONS DEBUG:\", {\n        allLeaguesCount: allLeagues.length,\n        allHomeTeamsCount: allHomeTeams.length,\n        allAwayTeamsCount: allAwayTeams.length,\n        leagueOptionsCount: leagueOptions.length,\n        homeTeamOptionsCount: homeTeamOptions.length,\n        awayTeamOptionsCount: awayTeamOptions.length,\n        leagueMeta: leagues === null || leagues === void 0 ? void 0 : leagues.meta,\n        homeTeamMeta: homeTeams === null || homeTeams === void 0 ? void 0 : homeTeams.meta,\n        awayTeamMeta: awayTeams === null || awayTeams === void 0 ? void 0 : awayTeams.meta\n    });\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            teamOptionsCount: teamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        teamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = homeTeamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = awayTeamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        teams: teamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dropdownKey, setDropdownKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        teamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Force dropdown re-render when options change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDropdownKey((prev)=>prev + 1);\n    }, [\n        teamOptions.length,\n        leagueOptions.length\n    ]);\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (teamOptions.length > 0) {\n        console.log(\"Sample Team Options:\", teamOptions.slice(0, 3));\n        console.log(\"Team Option Types:\", teamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const homeTeamInOptions = teamOptions.find((t)=>t.value === \"4450\");\n        const awayTeamInOptions = teamOptions.find((t)=>t.value === \"20022\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC TEAMS CHECK:\", {\n            \"homeTeam (4450)\": homeTeamInOptions,\n            \"awayTeam (20022)\": awayTeamInOptions,\n            \"totalTeams\": teamOptions.length\n        });\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n        // 🔍 Check if our specific league exists\n        const leagueInOptions = leagueOptions.find((l)=>l.value === \"570\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC LEAGUE CHECK:\", {\n            \"league (570)\": leagueInOptions,\n            \"totalLeagues\": leagueOptions.length\n        });\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: teamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: teamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 561,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            onLoadMore: handleLeagueLoadMore,\n                            hasMore: (leagues === null || leagues === void 0 ? void 0 : leagues.meta) ? leaguePage < leagues.meta.totalPages : false,\n                            isLoading: leaguesLoading\n                        }, \"league-\".concat(dropdownKey), false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 599,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || homeTeamsLoading || awayTeamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 657,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 664,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 656,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 699,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 706,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 698,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 727,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 725,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 740,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: homeTeamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: homeTeamsLoading,\n                                                            onSearch: handleHomeTeamSearch,\n                                                            onLoadMore: handleHomeTeamLoadMore,\n                                                            hasMore: (homeTeams === null || homeTeams === void 0 ? void 0 : homeTeams.meta) ? homeTeamPage < homeTeams.meta.totalPages : false,\n                                                            isLoading: homeTeamsLoading\n                                                        }, \"home-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: awayTeamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: awayTeamsLoading,\n                                                            onSearch: handleAwayTeamSearch,\n                                                            onLoadMore: handleAwayTeamLoadMore,\n                                                            hasMore: (awayTeams === null || awayTeams === void 0 ? void 0 : awayTeams.meta) ? awayTeamPage < awayTeams.meta.totalPages : false,\n                                                            isLoading: awayTeamsLoading\n                                                        }, \"away-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 811,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 844,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 900,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 917,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 955,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 942,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 749,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 739,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 723,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"gltr02cSr/PiTe0zfM3HmS1vq24=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});