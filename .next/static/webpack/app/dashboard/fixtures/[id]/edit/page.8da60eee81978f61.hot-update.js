"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _leagues_data, _teams_data;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Search states\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearchResults, setHomeTeamSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues without search initially\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams without search initially\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"teams\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Home Team Search with 3s debounce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!homeTeamSearch.trim()) {\n            setHomeTeamSearchResults([]);\n            return;\n        }\n        const timer = setTimeout(async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD0D Searching home teams for:\", homeTeamSearch);\n                const searchResults = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                    limit: 100,\n                    search: homeTeamSearch\n                });\n                if (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data) {\n                    console.log(\"✅ Home team search results:\", searchResults.data.length);\n                    console.log(\"\\uD83D\\uDCCB Search results data:\", searchResults.data);\n                    // Append to existing results (avoid duplicates)\n                    setHomeTeamSearchResults((prev)=>{\n                        const existingIds = new Set(prev.map((t)=>t.externalId));\n                        const newTeams = searchResults.data.filter((t)=>!existingIds.has(t.externalId));\n                        console.log(\"\\uD83D\\uDD04 Updating search results:\", {\n                            previous: prev.length,\n                            newTeams: newTeams.length,\n                            total: prev.length + newTeams.length\n                        });\n                        return [\n                            ...prev,\n                            ...newTeams\n                        ];\n                    });\n                }\n            } catch (error) {\n                console.error(\"❌ Home team search error:\", error);\n            }\n        }, 3000); // 3 second debounce\n        return ()=>clearTimeout(timer);\n    }, [\n        homeTeamSearch\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            // 🔍 DEBUG: Check if fixture uses internal or external IDs\n            console.log(\"\\uD83D\\uDD0D FIXTURE ID ANALYSIS:\", {\n                \"fixture.homeTeamId\": fixture.homeTeamId,\n                \"fixture.awayTeamId\": fixture.awayTeamId,\n                \"fixture.leagueId\": fixture.leagueId,\n                \"fixture object\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search handlers with useCallback to prevent re-renders\n    const handleLeagueSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        console.log(\"\\uD83C\\uDFC6 League search:\", query);\n        setLeagueSearch(query);\n    }, []);\n    const handleHomeTeamSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        console.log(\"\\uD83C\\uDFE0 Home team search:\", query);\n        setHomeTeamSearch(query);\n    }, []);\n    const statusOptions = [\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        {\n            value: \"FT\",\n            label: \"Match Finished\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Use direct API data for options\n    const leagueOptions = (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league, index)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season,\n            uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n        }))) || [];\n    const teamOptions = (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }))) || [];\n    // Home team options: combine initial teams + search results\n    const homeTeamOptions = [\n        ...teamOptions,\n        ...homeTeamSearchResults.map((team, index)=>({\n                value: team.externalId.toString(),\n                label: team.name,\n                logo: team.logo,\n                uniqueKey: \"search-home-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n            }))\n    ];\n    // Away team options: use basic team options for now\n    const awayTeamOptions = teamOptions;\n    // Debug options count\n    console.log(\"\\uD83D\\uDCCA OPTIONS DEBUG:\", {\n        leagueOptionsCount: leagueOptions.length,\n        homeTeamOptionsCount: homeTeamOptions.length,\n        awayTeamOptionsCount: awayTeamOptions.length,\n        homeTeamSearchResultsCount: homeTeamSearchResults.length,\n        basicTeamOptionsCount: teamOptions.length,\n        leagueMeta: leagues === null || leagues === void 0 ? void 0 : leagues.meta,\n        teamsMeta: teams === null || teams === void 0 ? void 0 : teams.meta\n    });\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            homeTeamOptionsCount: homeTeamOptions.length,\n            awayTeamOptionsCount: awayTeamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = homeTeamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = awayTeamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        homeTeams: homeTeamOptions.length,\n        awayTeams: awayTeamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (homeTeamOptions.length > 0 && awayTeamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // No need to force dropdown re-render - stable keys prevent issues\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (homeTeamOptions.length > 0) {\n        console.log(\"Sample Home Team Options:\", homeTeamOptions.slice(0, 3));\n        console.log(\"Home Team Option Types:\", homeTeamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const homeTeamInOptions = homeTeamOptions.find((t)=>t.value === \"4450\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC HOME TEAM CHECK:\", {\n            \"homeTeam (4450)\": homeTeamInOptions,\n            \"totalHomeTeams\": homeTeamOptions.length\n        });\n    }\n    if (awayTeamOptions.length > 0) {\n        console.log(\"Sample Away Team Options:\", awayTeamOptions.slice(0, 3));\n        console.log(\"Away Team Option Types:\", awayTeamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const awayTeamInOptions = awayTeamOptions.find((t)=>t.value === \"20022\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC AWAY TEAM CHECK:\", {\n            \"awayTeam (20022)\": awayTeamInOptions,\n            \"totalAwayTeams\": awayTeamOptions.length\n        });\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n        // 🔍 Check if our specific league exists\n        const leagueInOptions = leagueOptions.find((l)=>l.value === \"570\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC LEAGUE CHECK:\", {\n            \"league (570)\": leagueInOptions,\n            \"totalLeagues\": leagueOptions.length\n        });\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: homeTeamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: awayTeamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 454,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 451,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            isLoading: leaguesLoading\n                        }, \"league-search\", false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 489,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 545,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 544,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 32\n                                }, this),\n                                homeTeamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load home teams: \",\n                                        homeTeamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 34\n                                }, this),\n                                awayTeamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load away teams: \",\n                                        awayTeamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 586,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 614,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams... (3s delay)\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: homeTeamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleHomeTeamSearch,\n                                                            isLoading: teamsLoading\n                                                        }, \"home-team-search-\".concat(homeTeamOptions.length), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: awayTeamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            isLoading: teamsLoading\n                                                        }, \"away-team-search\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 839,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isLoading ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 628,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 612,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"yvrfggofF4FbzZ7si0O/O22oM14=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});