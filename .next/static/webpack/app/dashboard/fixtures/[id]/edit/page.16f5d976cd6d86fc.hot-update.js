"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _teams;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Smart loading with search and pagination - SEPARATED for Home/Away\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [awayTeamSearch, setAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leaguePage, setLeaguePage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [homeTeamPage, setHomeTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [awayTeamPage, setAwayTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [allLeagues, setAllLeagues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allHomeTeams, setAllHomeTeams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAwayTeams, setAllAwayTeams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues with search and pagination\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"search\",\n            leagueSearch,\n            leaguePage\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100,\n                page: leaguePage,\n                ...leagueSearch && {\n                    search: leagueSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch home teams with search and pagination\n    const { data: homeTeams, isLoading: homeTeamsLoading, error: homeTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"home-teams\",\n            \"search\",\n            homeTeamSearch,\n            homeTeamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: homeTeamPage,\n                ...homeTeamSearch && {\n                    search: homeTeamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch away teams with search and pagination\n    const { data: awayTeams, isLoading: awayTeamsLoading, error: awayTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"away-teams\",\n            \"search\",\n            awayTeamSearch,\n            awayTeamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: awayTeamPage,\n                ...awayTeamSearch && {\n                    search: awayTeamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Track previous search to detect search changes\n    const [prevLeagueSearch, setPrevLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [prevHomeTeamSearch, setPrevHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [prevAwayTeamSearch, setPrevAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Accumulate leagues data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (leagues === null || leagues === void 0 ? void 0 : leagues.data) {\n            // Check if search changed\n            const searchChanged = leagueSearch !== prevLeagueSearch;\n            if (leaguePage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"\\uD83D\\uDD04 Replacing leagues data:\", {\n                    page: leaguePage,\n                    searchChanged,\n                    search: leagueSearch\n                });\n                setAllLeagues(leagues.data);\n                setPrevLeagueSearch(leagueSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"\\uD83D\\uDD04 Appending leagues data:\", {\n                    page: leaguePage,\n                    existing: allLeagues.length,\n                    new: leagues.data.length\n                });\n                setAllLeagues((prev)=>{\n                    const existingIds = new Set(prev.map((l)=>l.externalId));\n                    const newLeagues = leagues.data.filter((l)=>!existingIds.has(l.externalId));\n                    return [\n                        ...prev,\n                        ...newLeagues\n                    ];\n                });\n            }\n        }\n    }, [\n        leagues,\n        leaguePage,\n        leagueSearch,\n        prevLeagueSearch,\n        allLeagues.length\n    ]);\n    // Accumulate HOME teams data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (homeTeams === null || homeTeams === void 0 ? void 0 : homeTeams.data) {\n            // Check if search changed\n            const searchChanged = homeTeamSearch !== prevHomeTeamSearch;\n            if (homeTeamPage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"\\uD83C\\uDFE0 Replacing HOME teams data:\", {\n                    page: homeTeamPage,\n                    searchChanged,\n                    search: homeTeamSearch\n                });\n                setAllHomeTeams(homeTeams.data);\n                setPrevHomeTeamSearch(homeTeamSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"\\uD83C\\uDFE0 Appending HOME teams data:\", {\n                    page: homeTeamPage,\n                    existing: allHomeTeams.length,\n                    new: homeTeams.data.length\n                });\n                setAllHomeTeams((prev)=>{\n                    const existingIds = new Set(prev.map((t)=>t.id || t.externalId));\n                    const newTeams = homeTeams.data.filter((t)=>!existingIds.has(t.id || t.externalId));\n                    console.log(\"\\uD83C\\uDFE0 Adding new HOME teams:\", {\n                        existing: prev.length,\n                        new: newTeams.length,\n                        total: prev.length + newTeams.length\n                    });\n                    return [\n                        ...prev,\n                        ...newTeams\n                    ];\n                });\n            }\n        }\n    }, [\n        homeTeams,\n        homeTeamPage,\n        homeTeamSearch,\n        prevHomeTeamSearch\n    ]);\n    // Accumulate AWAY teams data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (awayTeams === null || awayTeams === void 0 ? void 0 : awayTeams.data) {\n            // Check if search changed\n            const searchChanged = awayTeamSearch !== prevAwayTeamSearch;\n            if (awayTeamPage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"✈️ Replacing AWAY teams data:\", {\n                    page: awayTeamPage,\n                    searchChanged,\n                    search: awayTeamSearch\n                });\n                setAllAwayTeams(awayTeams.data);\n                setPrevAwayTeamSearch(awayTeamSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"✈️ Appending AWAY teams data:\", {\n                    page: awayTeamPage,\n                    existing: allAwayTeams.length,\n                    new: awayTeams.data.length\n                });\n                setAllAwayTeams((prev)=>{\n                    const existingIds = new Set(prev.map((t)=>t.id || t.externalId));\n                    const newTeams = awayTeams.data.filter((t)=>!existingIds.has(t.id || t.externalId));\n                    console.log(\"✈️ Adding new AWAY teams:\", {\n                        existing: prev.length,\n                        new: newTeams.length,\n                        total: prev.length + newTeams.length\n                    });\n                    return [\n                        ...prev,\n                        ...newTeams\n                    ];\n                });\n            }\n        }\n    }, [\n        awayTeams,\n        awayTeamPage,\n        awayTeamSearch,\n        prevAwayTeamSearch\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            // 🔍 DEBUG: Check if fixture uses internal or external IDs\n            console.log(\"\\uD83D\\uDD0D FIXTURE ID ANALYSIS:\", {\n                \"fixture.homeTeamId\": fixture.homeTeamId,\n                \"fixture.awayTeamId\": fixture.awayTeamId,\n                \"fixture.leagueId\": fixture.leagueId,\n                \"fixture object\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search and pagination handlers\n    const handleLeagueSearch = (query)=>{\n        console.log(\"\\uD83C\\uDFC6 League search:\", query);\n        setLeagueSearch(query);\n        setLeaguePage(1); // Reset to first page on new search\n    };\n    const handleHomeTeamSearch = (query)=>{\n        console.log(\"\\uD83C\\uDFE0 Home team search:\", query);\n        setHomeTeamSearch(query);\n        setHomeTeamPage(1); // Reset to first page on new search\n    };\n    const handleAwayTeamSearch = (query)=>{\n        console.log(\"✈️ Away team search:\", query);\n        setAwayTeamSearch(query);\n        setAwayTeamPage(1); // Reset to first page on new search\n    };\n    const handleLeagueLoadMore = ()=>{\n        var _leagues_meta, _leagues_meta1, _leagues_meta2;\n        console.log(\"\\uD83D\\uDD04 League Load More:\", {\n            currentPage: leaguePage,\n            totalPages: leagues === null || leagues === void 0 ? void 0 : (_leagues_meta = leagues.meta) === null || _leagues_meta === void 0 ? void 0 : _leagues_meta.totalPages,\n            hasMore: (leagues === null || leagues === void 0 ? void 0 : (_leagues_meta1 = leagues.meta) === null || _leagues_meta1 === void 0 ? void 0 : _leagues_meta1.totalPages) && leaguePage < leagues.meta.totalPages,\n            allLeaguesCount: allLeagues.length\n        });\n        if ((leagues === null || leagues === void 0 ? void 0 : (_leagues_meta2 = leagues.meta) === null || _leagues_meta2 === void 0 ? void 0 : _leagues_meta2.totalPages) && leaguePage < leagues.meta.totalPages) {\n            console.log(\"✅ Loading more leagues, page:\", leaguePage + 1);\n            setLeaguePage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more leagues\");\n        }\n    };\n    const handleHomeTeamLoadMore = ()=>{\n        var _homeTeams_meta, _homeTeams_meta1, _homeTeams_meta2;\n        console.log(\"\\uD83C\\uDFE0 Home Team Load More:\", {\n            currentPage: homeTeamPage,\n            totalPages: homeTeams === null || homeTeams === void 0 ? void 0 : (_homeTeams_meta = homeTeams.meta) === null || _homeTeams_meta === void 0 ? void 0 : _homeTeams_meta.totalPages,\n            hasMore: (homeTeams === null || homeTeams === void 0 ? void 0 : (_homeTeams_meta1 = homeTeams.meta) === null || _homeTeams_meta1 === void 0 ? void 0 : _homeTeams_meta1.totalPages) && homeTeamPage < homeTeams.meta.totalPages,\n            allHomeTeamsCount: allHomeTeams.length\n        });\n        if ((homeTeams === null || homeTeams === void 0 ? void 0 : (_homeTeams_meta2 = homeTeams.meta) === null || _homeTeams_meta2 === void 0 ? void 0 : _homeTeams_meta2.totalPages) && homeTeamPage < homeTeams.meta.totalPages) {\n            console.log(\"✅ Loading more HOME teams, page:\", homeTeamPage + 1);\n            setHomeTeamPage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more HOME teams\");\n        }\n    };\n    const handleAwayTeamLoadMore = ()=>{\n        var _awayTeams_meta, _awayTeams_meta1, _awayTeams_meta2;\n        console.log(\"✈️ Away Team Load More:\", {\n            currentPage: awayTeamPage,\n            totalPages: awayTeams === null || awayTeams === void 0 ? void 0 : (_awayTeams_meta = awayTeams.meta) === null || _awayTeams_meta === void 0 ? void 0 : _awayTeams_meta.totalPages,\n            hasMore: (awayTeams === null || awayTeams === void 0 ? void 0 : (_awayTeams_meta1 = awayTeams.meta) === null || _awayTeams_meta1 === void 0 ? void 0 : _awayTeams_meta1.totalPages) && awayTeamPage < awayTeams.meta.totalPages,\n            allAwayTeamsCount: allAwayTeams.length\n        });\n        if ((awayTeams === null || awayTeams === void 0 ? void 0 : (_awayTeams_meta2 = awayTeams.meta) === null || _awayTeams_meta2 === void 0 ? void 0 : _awayTeams_meta2.totalPages) && awayTeamPage < awayTeams.meta.totalPages) {\n            console.log(\"✅ Loading more AWAY teams, page:\", awayTeamPage + 1);\n            setAwayTeamPage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more AWAY teams\");\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        {\n            value: \"FT\",\n            label: \"Match Finished\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Use accumulated data for options with unique keys\n    const leagueOptions = allLeagues.map((league, index)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season,\n            uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n        }));\n    const homeTeamOptions = allHomeTeams.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"home-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }));\n    const awayTeamOptions = allAwayTeams.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"away-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }));\n    // Debug options count\n    console.log(\"\\uD83D\\uDCCA OPTIONS DEBUG:\", {\n        allLeaguesCount: allLeagues.length,\n        allHomeTeamsCount: allHomeTeams.length,\n        allAwayTeamsCount: allAwayTeams.length,\n        leagueOptionsCount: leagueOptions.length,\n        homeTeamOptionsCount: homeTeamOptions.length,\n        awayTeamOptionsCount: awayTeamOptions.length,\n        leagueMeta: leagues === null || leagues === void 0 ? void 0 : leagues.meta,\n        homeTeamMeta: homeTeams === null || homeTeams === void 0 ? void 0 : homeTeams.meta,\n        awayTeamMeta: awayTeams === null || awayTeams === void 0 ? void 0 : awayTeams.meta\n    });\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            teamOptionsCount: teamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        teamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = homeTeamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = awayTeamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        teams: teamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dropdownKey, setDropdownKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        teamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Force dropdown re-render when options change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDropdownKey((prev)=>prev + 1);\n    }, [\n        teamOptions.length,\n        leagueOptions.length\n    ]);\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (teamOptions.length > 0) {\n        console.log(\"Sample Team Options:\", teamOptions.slice(0, 3));\n        console.log(\"Team Option Types:\", teamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const homeTeamInOptions = teamOptions.find((t)=>t.value === \"4450\");\n        const awayTeamInOptions = teamOptions.find((t)=>t.value === \"20022\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC TEAMS CHECK:\", {\n            \"homeTeam (4450)\": homeTeamInOptions,\n            \"awayTeam (20022)\": awayTeamInOptions,\n            \"totalTeams\": teamOptions.length\n        });\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n        // 🔍 Check if our specific league exists\n        const leagueInOptions = leagueOptions.find((l)=>l.value === \"570\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC LEAGUE CHECK:\", {\n            \"league (570)\": leagueInOptions,\n            \"totalLeagues\": leagueOptions.length\n        });\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: teamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: teamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 561,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            onLoadMore: handleLeagueLoadMore,\n                            hasMore: (leagues === null || leagues === void 0 ? void 0 : leagues.meta) ? leaguePage < leagues.meta.totalPages : false,\n                            isLoading: leaguesLoading\n                        }, \"league-\".concat(dropdownKey), false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 599,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 657,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 664,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 656,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 699,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 706,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 698,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 727,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 725,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 740,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: homeTeamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: homeTeamsLoading,\n                                                            onSearch: handleHomeTeamSearch,\n                                                            onLoadMore: handleHomeTeamLoadMore,\n                                                            hasMore: (homeTeams === null || homeTeams === void 0 ? void 0 : homeTeams.meta) ? homeTeamPage < homeTeams.meta.totalPages : false,\n                                                            isLoading: homeTeamsLoading\n                                                        }, \"home-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleTeamSearch,\n                                                            onLoadMore: handleTeamLoadMore,\n                                                            hasMore: ((_teams = teams) === null || _teams === void 0 ? void 0 : _teams.meta) ? teamPage < teams.meta.totalPages : false,\n                                                            isLoading: teamsLoading\n                                                        }, \"away-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 811,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 844,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 900,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 917,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 955,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 942,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 749,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 739,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 723,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"gltr02cSr/PiTe0zfM3HmS1vq24=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});