"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ RefreshCw; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\"path\", { d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\", key: \"v9h5vc\" }],\n  [\"path\", { d: \"M21 3v5h-5\", key: \"1q7to0\" }],\n  [\"path\", { d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\", key: \"3uifl3\" }],\n  [\"path\", { d: \"M8 16H3v5\", key: \"1cv678\" }]\n];\nconst RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"refresh-cw\", __iconNode);\n\n\n//# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcmVmcmVzaC1jdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RDtBQUNBLGFBQWEsd0VBQXdFO0FBQ3JGLGFBQWEsZ0NBQWdDO0FBQzdDLGFBQWEseUVBQXlFO0FBQ3RGLGFBQWEsK0JBQStCO0FBQzVDO0FBQ0Esa0JBQWtCLGdFQUFnQjs7QUFFVTtBQUM1QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3JlZnJlc2gtY3cuanM/MmFmNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC41MTEuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IF9faWNvbk5vZGUgPSBbXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0zIDEyYTkgOSAwIDAgMSA5LTkgOS43NSA5Ljc1IDAgMCAxIDYuNzQgMi43NEwyMSA4XCIsIGtleTogXCJ2OWg1dmNcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTIxIDN2NWgtNVwiLCBrZXk6IFwiMXE3dG8wXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNlwiLCBrZXk6IFwiM3VpZmwzXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk04IDE2SDN2NVwiLCBrZXk6IFwiMWN2Njc4XCIgfV1cbl07XG5jb25zdCBSZWZyZXNoQ3cgPSBjcmVhdGVMdWNpZGVJY29uKFwicmVmcmVzaC1jd1wiLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IHsgX19pY29uTm9kZSwgUmVmcmVzaEN3IGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZnJlc2gtY3cuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_fixtures_FixtureNavigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/fixtures/FixtureNavigation */ \"(app-pages-browser)/./src/components/fixtures/FixtureNavigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Search states\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearchResults, setHomeTeamSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [awayTeamSearch, setAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [awayTeamSearchResults, setAwayTeamSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues without search initially\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQuery)({\n        queryKey: [\n            \"leagues\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams without search initially\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQuery)({\n        queryKey: [\n            \"teams\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Home Team Search with 3s debounce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!homeTeamSearch.trim()) {\n            setHomeTeamSearchResults([]);\n            return;\n        }\n        const timer = setTimeout(async ()=>{\n            try {\n                const searchResults = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                    limit: 100,\n                    search: homeTeamSearch\n                });\n                if (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data) {\n                    setHomeTeamSearchResults(searchResults.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Home team search error:\", error);\n                setHomeTeamSearchResults([]);\n            }\n        }, 3000); // 3 second debounce\n        return ()=>clearTimeout(timer);\n    }, [\n        homeTeamSearch\n    ]);\n    // Away Team Search with 3s debounce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!awayTeamSearch.trim()) {\n            setAwayTeamSearchResults([]);\n            return;\n        }\n        const timer = setTimeout(async ()=>{\n            try {\n                const searchResults = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                    limit: 100,\n                    search: awayTeamSearch\n                });\n                if (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data) {\n                    setAwayTeamSearchResults(searchResults.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Away team search error:\", error);\n                setAwayTeamSearchResults([]);\n            }\n        }, 3000); // 3 second debounce\n        return ()=>clearTimeout(timer);\n    }, [\n        awayTeamSearch\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Force status update when fixture loads (separate useEffect to ensure it works)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture && fixture.status && !formData.status) {\n            setFormData((prev)=>({\n                    ...prev,\n                    status: fixture.status\n                }));\n        }\n    }, [\n        fixture,\n        formData.status\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search handlers with useCallback to prevent re-renders\n    const handleLeagueSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setLeagueSearch(query);\n    }, []);\n    const handleHomeTeamSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setHomeTeamSearch(query);\n    }, []);\n    const handleAwayTeamSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setAwayTeamSearch(query);\n    }, []);\n    // Status options - API Football Official Documentation + Additional API Values\n    // Source: https://www.api-football.com/documentation-v3#tag/Fixtures/operation/get-fixtures\n    const statusOptions = [\n        // Time Status\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"ST\",\n            label: \"Scheduled\"\n        },\n        // Match Status - Live\n        {\n            value: \"1H\",\n            label: \"First Half, Kick Off\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half, 2nd Half Started\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time (in Extra Time)\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"LIVE\",\n            label: \"In Progress\"\n        },\n        // Match Finished\n        {\n            value: \"FT\",\n            label: \"Match Finished (Regular Time)\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        // Match Suspended\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        // Match Postponed\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        // Match Cancelled\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        // Match Abandoned\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        // Technical Loss\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        // Walk Over\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Memoize options to prevent re-renders\n    const leagueOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _leagues_data;\n        return (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league, index)=>({\n                value: league.externalId.toString(),\n                label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n                logo: league.logo,\n                season: league.season,\n                uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n            }))) || [];\n    }, [\n        leagues === null || leagues === void 0 ? void 0 : leagues.data\n    ]);\n    const teamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _teams_data;\n        return (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team, index)=>({\n                value: team.externalId.toString(),\n                label: team.name,\n                logo: team.logo,\n                uniqueKey: \"team-\".concat(team.id || team.externalId, \"-\").concat(index)\n            }))) || [];\n    }, [\n        teams === null || teams === void 0 ? void 0 : teams.data\n    ]);\n    // Home team options: replace with search results when searching, otherwise use initial teams\n    const homeTeamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // If we have search results, use ONLY search results (replace mode)\n        if (homeTeamSearchResults.length > 0) {\n            return homeTeamSearchResults.map((team, index)=>({\n                    value: team.externalId.toString(),\n                    label: team.name,\n                    logo: team.logo,\n                    uniqueKey: \"search-home-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n                }));\n        }\n        // If no search results, use initial teams (default mode)\n        return teamOptions;\n    }, [\n        teamOptions,\n        homeTeamSearchResults\n    ]);\n    // Away team options: replace with search results when searching, otherwise use initial teams\n    const awayTeamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // If we have search results, use ONLY search results (replace mode)\n        if (awayTeamSearchResults.length > 0) {\n            return awayTeamSearchResults.map((team, index)=>({\n                    value: team.externalId.toString(),\n                    label: team.name,\n                    logo: team.logo,\n                    uniqueKey: \"search-away-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n                }));\n        }\n        // If no search results, use initial teams (default mode)\n        return teamOptions;\n    }, [\n        teamOptions,\n        awayTeamSearchResults\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = homeTeamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = awayTeamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (homeTeamOptions.length > 0 && awayTeamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 418,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            isLoading: leaguesLoading\n                        }, \"league-search-stable\", false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 453,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 508,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 28\n                                }, this),\n                                !!leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load leagues\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 34\n                                }, this),\n                                !!teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load teams\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 32\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 558,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 550,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fixtures_FixtureNavigation__WEBPACK_IMPORTED_MODULE_12__.FixtureNavigation, {\n                        variant: \"edit\",\n                        fixtureId: fixtureId,\n                        isLoading: updateMutation.isLoading\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 583,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 577,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams... (3s delay)\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: homeTeamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleHomeTeamSearch,\n                                                            isLoading: teamsLoading\n                                                        }, \"home-team-search-stable\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams... (3s delay)\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: awayTeamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleAwayTeamSearch,\n                                                            isLoading: teamsLoading\n                                                        }, \"away-team-search-stable\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 804,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isLoading ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 575,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"fff1PSYkFqddDK7bN1VKE5DSfRs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/fixtures/FixtureNavigation.tsx":
/*!*******************************************************!*\
  !*** ./src/components/fixtures/FixtureNavigation.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FixtureNavigation: function() { return /* binding */ FixtureNavigation; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ FixtureNavigation auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FixtureNavigation = (param)=>{\n    let { variant, fixtureId, onRefresh, isLoading = false, className = \"\" } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleBack = ()=>{\n        switch(variant){\n            case \"detail\":\n                // From detail page -> back to fixtures list\n                router.push(\"/dashboard/fixtures\");\n                break;\n            case \"edit\":\n                // From edit page -> back to detail page\n                if (fixtureId) {\n                    router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n                } else {\n                    router.back();\n                }\n                break;\n            case \"create\":\n                // From create page -> back to fixtures list\n                router.push(\"/dashboard/fixtures\");\n                break;\n            default:\n                router.back();\n        }\n    };\n    const getBackButtonText = ()=>{\n        switch(variant){\n            case \"detail\":\n                return \"Back to Fixtures\";\n            case \"edit\":\n                return \"Back to Detail\";\n            case \"create\":\n                return \"Back to Fixtures\";\n            default:\n                return \"Back\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-4 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: \"outline\",\n                onClick: handleBack,\n                disabled: isLoading,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"mr-2 h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureNavigation.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    getBackButtonText()\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureNavigation.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            variant === \"detail\" && onRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: \"outline\",\n                onClick: onRefresh,\n                disabled: isLoading,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureNavigation.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined),\n                    \"Refresh\"\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureNavigation.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/FixtureNavigation.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FixtureNavigation, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = FixtureNavigation;\nvar _c;\n$RefreshReg$(_c, \"FixtureNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/fixtures/FixtureNavigation.tsx\n"));

/***/ })

});