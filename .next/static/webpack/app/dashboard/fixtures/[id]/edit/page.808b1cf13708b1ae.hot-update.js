"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/lib/api/leagues.ts":
/*!********************************!*\
  !*** ./src/lib/api/leagues.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   leaguesApi: function() { return /* binding */ leaguesApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst leaguesApi = {\n    // Public endpoint via proxy\n    getLeagues: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await fetch(\"/api/leagues?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch leagues\");\n        }\n        return await response.json();\n    },\n    // Requires authentication\n    getLeagueById: async (externalId, season)=>{\n        const params = season ? \"?season=\".concat(season) : \"\";\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/leagues/\".concat(externalId).concat(params));\n        return response;\n    },\n    // Editor+ access required\n    createLeague: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/football/leagues\", data);\n        return response;\n    },\n    // Editor+ access required\n    updateLeague: async (id, data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(\"/football/leagues/\".concat(id), data);\n        return response;\n    },\n    // Helper methods for common operations\n    getActiveLeagues: async ()=>{\n        return leaguesApi.getLeagues({\n            active: true\n        });\n    },\n    getLeaguesByCountry: async (country)=>{\n        return leaguesApi.getLeagues({\n            country\n        });\n    },\n    toggleLeagueStatus: async (id, active)=>{\n        return leaguesApi.updateLeague(id, {\n            active\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/leagues.ts\n"));

/***/ })

});