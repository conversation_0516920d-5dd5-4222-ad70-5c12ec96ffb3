"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _leagues_data, _teams_data;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Fetch leagues for dropdown\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams for dropdown\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_9__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Half Time\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"FT\",\n            label: \"Full Time\"\n        },\n        {\n            value: \"PST\",\n            label: \"Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Cancelled\"\n        }\n    ];\n    const leagueOptions = (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league)=>({\n            value: league.id.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season\n        }))) || [];\n    const teamOptions = (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team)=>({\n            value: team.id.toString(),\n            label: team.name,\n            logo: team.logo\n        }))) || [];\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            teamOptionsCount: teamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        teamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Get selected options for preview - with type conversion fix and detailed debugging\n    const selectedLeague = leagueOptions.find((l)=>l.value === Number(formData.leagueId));\n    const selectedHomeTeam = teamOptions.find((t)=>t.value === Number(formData.homeTeamId));\n    const selectedAwayTeam = teamOptions.find((t)=>t.value === Number(formData.awayTeamId));\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        teams: teamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        teamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (teamOptions.length > 0) {\n        console.log(\"Sample Team Options:\", teamOptions.slice(0, 3));\n        console.log(\"Team Option Types:\", teamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n    }\n    console.log(\"ID Exists Check:\", {\n        homeExists: teamOptions.some((t)=>t.value === Number(formData.homeTeamId)),\n        awayExists: teamOptions.some((t)=>t.value === Number(formData.awayTeamId)),\n        leagueExists: leagueOptions.some((l)=>l.value === Number(formData.leagueId))\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 329,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 380,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 422,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 p-2 bg-yellow-100 border border-yellow-300 rounded\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"DEBUG:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" Home Team - \",\n                                                                selectedHomeTeam ? \"Found: \".concat(selectedHomeTeam.label) : \"Not found\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: teamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 p-2 bg-yellow-100 border border-yellow-300 rounded\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"DEBUG:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" Away Team - \",\n                                                                selectedAwayTeam ? \"Found: \".concat(selectedAwayTeam.label) : \"Not found\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 p-2 bg-blue-100 border border-blue-300 rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"DEBUG:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" League - \",\n                                                        selectedLeague ? \"Found: \".concat(selectedLeague.label) : \"Not found\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 463,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 447,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"GSHj46XZnVF9G5WIuoIXRmSrd1Q=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ZpeHR1cmVzL1tpZF0vZWRpdC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDVztBQUN1QjtBQUNtQjtBQUNqRDtBQU1aO0FBQ2dCO0FBQ0g7QUFDRjtBQUNKO0FBQ2M7QUFDMUI7QUFxQmhCLFNBQVN5QjtRQWlLQUMsZUFPRkM7O0lBdktwQixNQUFNQyxTQUFTMUIsMERBQVNBO0lBQ3hCLE1BQU0yQixTQUFTMUIsMERBQVNBO0lBQ3hCLE1BQU0yQixjQUFjeEIsc0VBQWNBO0lBQ2xDLE1BQU15QixZQUFZQyxTQUFTSixPQUFPSyxFQUFFO0lBRXBDLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHbkMsK0NBQVFBLENBQWtCO1FBQ3hEb0MsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLFNBQVM7SUFDWDtJQUVBLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHakQsK0NBQVFBLENBQTJCLENBQUM7SUFFaEUsd0JBQXdCO0lBQ3hCLE1BQU0sRUFBRWtELE1BQU1DLE9BQU8sRUFBRUMsV0FBV0MsY0FBYyxFQUFFLEdBQUdoRCxnRUFBUUEsQ0FBQztRQUM1RGlELFVBQVU7WUFBQztZQUFXdkI7U0FBVTtRQUNoQ3dCLFNBQVMsSUFBTXJDLDBEQUFXQSxDQUFDc0MsVUFBVSxDQUFDekI7UUFDdEMwQixTQUFTLENBQUMsQ0FBQzFCO0lBQ2I7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTSxFQUFFbUIsTUFBTXhCLE9BQU8sRUFBRTBCLFdBQVdNLGNBQWMsRUFBRUMsT0FBT0MsWUFBWSxFQUFFLEdBQUd2RCxnRUFBUUEsQ0FBQztRQUNqRmlELFVBQVU7WUFBQztZQUFXO1NBQU07UUFDNUJDLFNBQVMsSUFBTXBDLHdEQUFVQSxDQUFDMEMsVUFBVSxDQUFDO2dCQUFFQyxPQUFPO1lBQUk7SUFDcEQ7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTSxFQUFFWixNQUFNdkIsS0FBSyxFQUFFeUIsV0FBV1csWUFBWSxFQUFFSixPQUFPSyxVQUFVLEVBQUUsR0FBRzNELGdFQUFRQSxDQUFDO1FBQzNFaUQsVUFBVTtZQUFDO1lBQVM7U0FBTTtRQUMxQkMsU0FBUyxJQUFNbkMsb0RBQVFBLENBQUM2QyxRQUFRLENBQUM7Z0JBQUVILE9BQU87WUFBSTtJQUNoRDtJQUVBLGtCQUFrQjtJQUNsQixNQUFNSSxpQkFBaUI5RCxtRUFBV0EsQ0FBQztRQUNqQytELFlBQVksQ0FBQ2pCLE9BQWNoQywwREFBV0EsQ0FBQ2tELGFBQWEsQ0FBQ3JDLFdBQVdtQjtRQUNoRW1CLFdBQVc7WUFDVHZDLFlBQVl3QyxpQkFBaUIsQ0FBQztnQkFBRWhCLFVBQVU7b0JBQUM7b0JBQVd2QjtpQkFBVTtZQUFDO1lBQ2pFRCxZQUFZd0MsaUJBQWlCLENBQUM7Z0JBQUVoQixVQUFVO29CQUFDO2lCQUFXO1lBQUM7WUFDdkQ5QiwwQ0FBS0EsQ0FBQytDLE9BQU8sQ0FBQztZQUNkMUMsT0FBTzJDLElBQUksQ0FBQyx1QkFBaUMsT0FBVnpDO1FBQ3JDO1FBQ0EwQyxTQUFTLENBQUNkO1lBQ1JuQywwQ0FBS0EsQ0FBQ21DLEtBQUssQ0FBQ0EsTUFBTWUsT0FBTyxJQUFJO1FBQy9CO0lBQ0Y7SUFFQSx3Q0FBd0M7SUFDeEN6RSxnREFBU0EsQ0FBQztRQUNSLElBQUlrRCxTQUFTO2dCQVlHQSxxQkFDQUEscUJBQ0ZBLG1CQUlDQSxnQkFDQUEsaUJBR0FBLG9CQUNBQSxvQkFDRkEsa0JBRUlBLHNCQUVEQTtZQTNCZCxNQUFNd0IsY0FBYyxJQUFJQyxLQUFLekIsUUFBUVosSUFBSTtZQUN6Q3NDLFFBQVFDLEdBQUcsQ0FBQyxtREFBeUM7Z0JBQ25EMUMsWUFBWWUsUUFBUWYsVUFBVTtnQkFDOUJDLFlBQVljLFFBQVFkLFVBQVU7Z0JBQzlCQyxVQUFVYSxRQUFRYixRQUFRO2dCQUMxQnlDLE9BQU81QixRQUFRNEIsS0FBSztnQkFDcEJ0QyxXQUFXVSxRQUFRVixTQUFTO2dCQUM1QkMsV0FBV1MsUUFBUVQsU0FBUztZQUM5QjtZQUVBUCxZQUFZO2dCQUNWQyxZQUFZZSxFQUFBQSxzQkFBQUEsUUFBUWYsVUFBVSxjQUFsQmUsMENBQUFBLG9CQUFvQjZCLFFBQVEsT0FBTTtnQkFDOUMzQyxZQUFZYyxFQUFBQSxzQkFBQUEsUUFBUWQsVUFBVSxjQUFsQmMsMENBQUFBLG9CQUFvQjZCLFFBQVEsT0FBTTtnQkFDOUMxQyxVQUFVYSxFQUFBQSxvQkFBQUEsUUFBUWIsUUFBUSxjQUFoQmEsd0NBQUFBLGtCQUFrQjZCLFFBQVEsT0FBTTtnQkFDMUN6QyxNQUFNb0MsWUFBWU0sV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7Z0JBQzdDMUMsTUFBTW1DLFlBQVlRLFlBQVksR0FBR0MsS0FBSyxDQUFDLEdBQUc7Z0JBQzFDLDREQUE0RDtnQkFDNUQzQyxXQUFXVSxFQUFBQSxpQkFBQUEsUUFBUTRCLEtBQUssY0FBYjVCLHFDQUFBQSxlQUFla0MsSUFBSSxLQUFJbEMsUUFBUVYsU0FBUyxJQUFJO2dCQUN2REMsV0FBV1MsRUFBQUEsa0JBQUFBLFFBQVE0QixLQUFLLGNBQWI1QixzQ0FBQUEsZ0JBQWVtQyxJQUFJLEtBQUluQyxRQUFRVCxTQUFTLElBQUk7Z0JBQ3ZEQyxPQUFPUSxRQUFRUixLQUFLLElBQUk7Z0JBQ3hCQyxRQUFRTyxRQUFRUCxNQUFNLElBQUk7Z0JBQzFCQyxXQUFXTSxFQUFBQSxxQkFBQUEsUUFBUU4sU0FBUyxjQUFqQk0seUNBQUFBLG1CQUFtQjZCLFFBQVEsT0FBTTtnQkFDNUNsQyxXQUFXSyxFQUFBQSxxQkFBQUEsUUFBUUwsU0FBUyxjQUFqQksseUNBQUFBLG1CQUFtQjZCLFFBQVEsT0FBTTtnQkFDNUNqQyxTQUFTSSxFQUFBQSxtQkFBQUEsUUFBUUosT0FBTyxjQUFmSSx1Q0FBQUEsaUJBQWlCNkIsUUFBUSxPQUFNO2dCQUN4Q08sU0FBU3BDLFFBQVFvQyxPQUFPLElBQUk7Z0JBQzVCQyxhQUFhckMsRUFBQUEsdUJBQUFBLFFBQVFxQyxXQUFXLGNBQW5CckMsMkNBQUFBLHFCQUFxQjZCLFFBQVEsT0FBTTtnQkFDaERTLFNBQVN0QyxRQUFRc0MsT0FBTyxJQUFJO2dCQUM1QkMsWUFBWXZDLEVBQUFBLHNCQUFBQSxRQUFRdUMsVUFBVSxjQUFsQnZDLDBDQUFBQSxvQkFBb0I2QixRQUFRLE9BQU07WUFDaEQ7UUFDRjtJQUNGLEdBQUc7UUFBQzdCO0tBQVE7SUFFWixzRUFBc0U7SUFFdEUsTUFBTXdDLGVBQWU7UUFDbkIsTUFBTUMsWUFBc0MsQ0FBQztRQUU3QyxJQUFJLENBQUMxRCxTQUFTRSxVQUFVLEVBQUV3RCxVQUFVeEQsVUFBVSxHQUFHO1FBQ2pELElBQUksQ0FBQ0YsU0FBU0csVUFBVSxFQUFFdUQsVUFBVXZELFVBQVUsR0FBRztRQUNqRCxJQUFJLENBQUNILFNBQVNJLFFBQVEsRUFBRXNELFVBQVV0RCxRQUFRLEdBQUc7UUFDN0MsSUFBSSxDQUFDSixTQUFTSyxJQUFJLEVBQUVxRCxVQUFVckQsSUFBSSxHQUFHO1FBQ3JDLElBQUksQ0FBQ0wsU0FBU00sSUFBSSxFQUFFb0QsVUFBVXBELElBQUksR0FBRztRQUNyQyxJQUFJLENBQUNOLFNBQVNVLE1BQU0sRUFBRWdELFVBQVVoRCxNQUFNLEdBQUc7UUFFekMsSUFBSVYsU0FBU0UsVUFBVSxLQUFLRixTQUFTRyxVQUFVLEVBQUU7WUFDL0N1RCxVQUFVdkQsVUFBVSxHQUFHO1FBQ3pCO1FBRUFZLFVBQVUyQztRQUNWLE9BQU9DLE9BQU9DLElBQUksQ0FBQ0YsV0FBV0csTUFBTSxLQUFLO0lBQzNDO0lBRUEsTUFBTUMsZUFBZSxDQUFDQztRQUNwQkEsRUFBRUMsY0FBYztRQUVoQixJQUFJLENBQUNQLGdCQUFnQjtZQUNuQm5FLDBDQUFLQSxDQUFDbUMsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBLHdCQUF3QjtRQUN4QixNQUFNd0MsV0FBVyxJQUFJdkIsS0FBSyxHQUFvQjFDLE9BQWpCQSxTQUFTSyxJQUFJLEVBQUMsS0FBaUIsT0FBZEwsU0FBU00sSUFBSTtRQUUzRCxNQUFNNEQsYUFBYTtZQUNqQmhFLFlBQVlKLFNBQVNFLFNBQVNFLFVBQVU7WUFDeENDLFlBQVlMLFNBQVNFLFNBQVNHLFVBQVU7WUFDeENDLFVBQVVOLFNBQVNFLFNBQVNJLFFBQVE7WUFDcENDLE1BQU00RCxTQUFTbEIsV0FBVztZQUMxQnhDLFdBQVdQLFNBQVNPLFNBQVMsSUFBSTtZQUNqQ0MsV0FBV1IsU0FBU1EsU0FBUyxJQUFJO1lBQ2pDQyxPQUFPVCxTQUFTUyxLQUFLLElBQUk7WUFDekJDLFFBQVFWLFNBQVNVLE1BQU07WUFDdkJDLFdBQVdYLFNBQVNXLFNBQVMsR0FBR2IsU0FBU0UsU0FBU1csU0FBUyxJQUFJO1lBQy9EQyxXQUFXWixTQUFTWSxTQUFTLEdBQUdkLFNBQVNFLFNBQVNZLFNBQVMsSUFBSTtZQUMvREMsU0FBU2IsU0FBU2EsT0FBTyxHQUFHZixTQUFTRSxTQUFTYSxPQUFPLElBQUk7WUFDekR3QyxTQUFTckQsU0FBU3FELE9BQU8sSUFBSTtZQUM3QkMsYUFBYXRELFNBQVNzRCxXQUFXLEdBQUd4RCxTQUFTRSxTQUFTc0QsV0FBVyxJQUFJO1lBQ3JFQyxTQUFTdkQsU0FBU3VELE9BQU8sSUFBSTtZQUM3QkMsWUFBWXhELFNBQVN3RCxVQUFVLEdBQUcxRCxTQUFTRSxTQUFTd0QsVUFBVSxJQUFJO1FBQ3BFO1FBRUF4QixlQUFlbUMsTUFBTSxDQUFDRDtJQUN4QjtJQUVBLE1BQU1FLGlCQUFpQixDQUFDQyxPQUE4QkM7UUFDcERyRSxZQUFZc0UsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFLENBQUNGLE1BQU0sRUFBRUM7WUFBTTtRQUMvQyxzQ0FBc0M7UUFDdEMsSUFBSXhELE1BQU0sQ0FBQ3VELE1BQU0sRUFBRTtZQUNqQnRELFVBQVV3RCxDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUUsQ0FBQ0YsTUFBTSxFQUFFRztnQkFBVTtRQUNuRDtJQUNGO0lBRUEsTUFBTUMsZ0JBQWdCO1FBQ3BCO1lBQUVILE9BQU87WUFBTUksT0FBTztRQUFjO1FBQ3BDO1lBQUVKLE9BQU87WUFBTUksT0FBTztRQUFhO1FBQ25DO1lBQUVKLE9BQU87WUFBTUksT0FBTztRQUFZO1FBQ2xDO1lBQUVKLE9BQU87WUFBTUksT0FBTztRQUFjO1FBQ3BDO1lBQUVKLE9BQU87WUFBTUksT0FBTztRQUFZO1FBQ2xDO1lBQUVKLE9BQU87WUFBT0ksT0FBTztRQUFZO1FBQ25DO1lBQUVKLE9BQU87WUFBUUksT0FBTztRQUFZO0tBQ3JDO0lBRUQsTUFBTUMsZ0JBQWdCbkYsQ0FBQUEsb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU3dCLElBQUksY0FBYnhCLG9DQUFBQSxjQUFlb0YsR0FBRyxDQUFDQyxDQUFBQSxTQUFXO1lBQ2xEUCxPQUFPTyxPQUFPOUUsRUFBRSxDQUFDK0MsUUFBUTtZQUN6QjRCLE9BQU8sR0FBaUJHLE9BQWRBLE9BQU8xQixJQUFJLEVBQThDLE9BQTNDMEIsT0FBT0MsTUFBTSxHQUFHLEtBQW1CLE9BQWRELE9BQU9DLE1BQU0sRUFBQyxPQUFLO1lBQ2hFQyxNQUFNRixPQUFPRSxJQUFJO1lBQ2pCRCxRQUFRRCxPQUFPQyxNQUFNO1FBQ3ZCLFFBQU8sRUFBRTtJQUVULE1BQU1FLGNBQWN2RixDQUFBQSxrQkFBQUEsNkJBQUFBLGNBQUFBLE1BQU91QixJQUFJLGNBQVh2QixrQ0FBQUEsWUFBYW1GLEdBQUcsQ0FBQ0ssQ0FBQUEsT0FBUztZQUM1Q1gsT0FBT1csS0FBS2xGLEVBQUUsQ0FBQytDLFFBQVE7WUFDdkI0QixPQUFPTyxLQUFLOUIsSUFBSTtZQUNoQjRCLE1BQU1FLEtBQUtGLElBQUk7UUFDakIsUUFBTyxFQUFFO0lBRVQsd0ZBQXdGO0lBQ3hGaEgsZ0RBQVNBLENBQUM7UUFDUjRFLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEI7WUFDeENmO1lBQ0FMO1lBQ0EwRCxrQkFBa0JGLFlBQVluQixNQUFNO1lBQ3BDc0Isb0JBQW9CUixjQUFjZCxNQUFNO1lBQ3hDdUIsZUFBZSxDQUFDLENBQUVwRixDQUFBQSxTQUFTRSxVQUFVLElBQUlGLFNBQVNHLFVBQVUsSUFBSUgsU0FBU0ksUUFBUTtRQUNuRjtJQUNGLEdBQUc7UUFBQ3lCO1FBQWNMO1FBQWdCd0QsWUFBWW5CLE1BQU07UUFBRWMsY0FBY2QsTUFBTTtRQUFFN0Q7S0FBUztJQUVyRixxRkFBcUY7SUFDckYsTUFBTXFGLGlCQUFpQlYsY0FBY1csSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFakIsS0FBSyxLQUFLa0IsT0FBT3hGLFNBQVNJLFFBQVE7SUFDbkYsTUFBTXFGLG1CQUFtQlQsWUFBWU0sSUFBSSxDQUFDSSxDQUFBQSxJQUFLQSxFQUFFcEIsS0FBSyxLQUFLa0IsT0FBT3hGLFNBQVNFLFVBQVU7SUFDckYsTUFBTXlGLG1CQUFtQlgsWUFBWU0sSUFBSSxDQUFDSSxDQUFBQSxJQUFLQSxFQUFFcEIsS0FBSyxLQUFLa0IsT0FBT3hGLFNBQVNHLFVBQVU7SUFFckYsd0JBQXdCO0lBQ3hCd0MsUUFBUUMsR0FBRyxDQUFDO0lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJ5QztJQUMvQjFDLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUI2QztJQUNqQzlDLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUIrQztJQUVqQyxrQkFBa0I7SUFDbEJoRCxRQUFRQyxHQUFHLENBQUMsMkJBQWlCNUM7SUFDN0IyQyxRQUFRQyxHQUFHLENBQUMsK0JBQXFCO1FBQUVuRCxPQUFPdUYsWUFBWW5CLE1BQU07UUFBRXJFLFNBQVNtRixjQUFjZCxNQUFNO0lBQUM7SUFFNUYsZ0VBQWdFO0lBQ2hFLE1BQU0sQ0FBQytCLFlBQVlDLGNBQWMsR0FBRy9ILCtDQUFRQSxDQUFDO0lBRTdDQyxnREFBU0EsQ0FBQztRQUNSLG1EQUFtRDtRQUNuRCxJQUFJaUgsWUFBWW5CLE1BQU0sR0FBRyxLQUFLYyxjQUFjZCxNQUFNLEdBQUcsS0FBSzdELFNBQVNFLFVBQVUsRUFBRTtZQUM3RTJGLGNBQWN0QixDQUFBQSxPQUFRQSxPQUFPO1FBQy9CO0lBQ0YsR0FBRztRQUFDUyxZQUFZbkIsTUFBTTtRQUFFYyxjQUFjZCxNQUFNO1FBQUU3RCxTQUFTRSxVQUFVO1FBQUVGLFNBQVNHLFVBQVU7UUFBRUgsU0FBU0ksUUFBUTtLQUFDO0lBRTFHLCtCQUErQjtJQUMvQnVDLFFBQVFDLEdBQUcsQ0FBQztJQUNaRCxRQUFRQyxHQUFHLENBQUMsYUFBYTtRQUFFa0QsTUFBTTlGLFNBQVNFLFVBQVU7UUFBRTZGLE1BQU0vRixTQUFTRyxVQUFVO1FBQUUwRSxRQUFRN0UsU0FBU0ksUUFBUTtJQUFDO0lBQzNHdUMsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQjtRQUM1QmtELE1BQU0sT0FBTzlGLFNBQVNFLFVBQVU7UUFDaEM2RixNQUFNLE9BQU8vRixTQUFTRyxVQUFVO1FBQ2hDMEUsUUFBUSxPQUFPN0UsU0FBU0ksUUFBUTtJQUNsQztJQUVBLElBQUk0RSxZQUFZbkIsTUFBTSxHQUFHLEdBQUc7UUFDMUJsQixRQUFRQyxHQUFHLENBQUMsd0JBQXdCb0MsWUFBWTlCLEtBQUssQ0FBQyxHQUFHO1FBQ3pEUCxRQUFRQyxHQUFHLENBQUMsc0JBQXNCb0MsWUFBWTlCLEtBQUssQ0FBQyxHQUFHLEdBQUcwQixHQUFHLENBQUNjLENBQUFBLElBQU07Z0JBQUVwQixPQUFPb0IsRUFBRXBCLEtBQUs7Z0JBQUUwQixNQUFNLE9BQU9OLEVBQUVwQixLQUFLO1lBQUM7SUFDN0c7SUFFQSxJQUFJSyxjQUFjZCxNQUFNLEdBQUcsR0FBRztRQUM1QmxCLFFBQVFDLEdBQUcsQ0FBQywwQkFBMEIrQixjQUFjekIsS0FBSyxDQUFDLEdBQUc7UUFDN0RQLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0IrQixjQUFjekIsS0FBSyxDQUFDLEdBQUcsR0FBRzBCLEdBQUcsQ0FBQ1csQ0FBQUEsSUFBTTtnQkFBRWpCLE9BQU9pQixFQUFFakIsS0FBSztnQkFBRTBCLE1BQU0sT0FBT1QsRUFBRWpCLEtBQUs7WUFBQztJQUNqSDtJQUVBM0IsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQjtRQUM5QnFELFlBQVlqQixZQUFZa0IsSUFBSSxDQUFDUixDQUFBQSxJQUFLQSxFQUFFcEIsS0FBSyxLQUFLa0IsT0FBT3hGLFNBQVNFLFVBQVU7UUFDeEVpRyxZQUFZbkIsWUFBWWtCLElBQUksQ0FBQ1IsQ0FBQUEsSUFBS0EsRUFBRXBCLEtBQUssS0FBS2tCLE9BQU94RixTQUFTRyxVQUFVO1FBQ3hFaUcsY0FBY3pCLGNBQWN1QixJQUFJLENBQUNYLENBQUFBLElBQUtBLEVBQUVqQixLQUFLLEtBQUtrQixPQUFPeEYsU0FBU0ksUUFBUTtJQUM1RTtJQUVBLHdDQUF3QztJQUN4QyxNQUFNaUcsdUJBQXVCO1lBQUMsRUFDNUIzQixLQUFLLEVBQ0w0QixjQUFjLEVBQ2RDLGNBQWMsY0FBYyxFQUs3QjtRQUNDLE1BQU1DLFVBQVVDLHNCQUEwQyxJQUFJO1FBRTlELDBCQUEwQjtRQUMxQjlELFFBQVFDLEdBQUcsQ0FBQyxnQkFBWSxPQUFOOEIsT0FBTSxjQUFZO1lBQUU0QjtZQUFnQkM7WUFBYUssV0FBVyxDQUFDLENBQUNOO1FBQWU7UUFFL0YscUJBQ0UsOERBQUNPO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFBMENwQzs7Ozs7O2dCQUN4RDRCLCtCQUNDLDhEQUFDTztvQkFBSUMsV0FBVTs7d0JBQ1pSLGVBQWV2QixJQUFJLGtCQUNsQiw4REFBQ2dDOzRCQUNDQyxLQUFLLEdBQWNWLE9BQVhFLFNBQVEsS0FBdUIsT0FBcEJGLGVBQWV2QixJQUFJOzRCQUN0Q2tDLEtBQUtYLGVBQWU1QixLQUFLOzRCQUN6Qm9DLFdBQVU7NEJBQ1Z2RSxTQUFTLENBQUN3QjtnQ0FDUkEsRUFBRW1ELGFBQWEsQ0FBQ0MsS0FBSyxDQUFDQyxPQUFPLEdBQUc7NEJBQ2xDOzs7Ozs7c0NBR0osOERBQUNDOzRCQUFLUCxXQUFVO3NDQUNiUixlQUFlNUIsS0FBSzs7Ozs7Ozs7Ozs7eUNBSXpCLDhEQUFDbUM7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ087Z0NBQUtQLFdBQVU7MENBQXdCOzs7Ozs7Ozs7OztzQ0FFMUMsOERBQUNPOzRCQUFLUCxXQUFVO3NDQUF3QlA7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUtsRDtJQUVBLG9FQUFvRTtJQUNwRSxNQUFNZSx1QkFBdUI7UUFDM0IsTUFBTWQsVUFBVUMsc0JBQTBDLElBQUk7UUFFOUQsd0JBQXdCO1FBQ3hCOUQsUUFBUUMsR0FBRyxDQUFDLHdDQUE4QjtZQUFFeUM7WUFBZ0JrQyxXQUFXLENBQUMsQ0FBQ2xDO1FBQWU7UUFFeEYscUJBQ0UsOERBQUN3QjtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1p6QiwrQkFDQzs7NEJBQ0dBLGVBQWVOLElBQUksa0JBQ2xCLDhEQUFDZ0M7Z0NBQ0NDLEtBQUssR0FBYzNCLE9BQVhtQixTQUFRLEtBQXVCLE9BQXBCbkIsZUFBZU4sSUFBSTtnQ0FDdENrQyxLQUFLNUIsZUFBZVgsS0FBSztnQ0FDekJvQyxXQUFVO2dDQUNWdkUsU0FBUyxDQUFDd0I7b0NBQ1JBLEVBQUVtRCxhQUFhLENBQUNDLEtBQUssQ0FBQ0MsT0FBTyxHQUFHO2dDQUNsQzs7Ozs7OzBDQUdKLDhEQUFDQztnQ0FBS1AsV0FBVTswQ0FDYnpCLGVBQWVYLEtBQUs7Ozs7Ozs7cURBSXpCOzswQ0FDRSw4REFBQ21DO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDTztvQ0FBS1AsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OzBDQUUxQyw4REFBQ087Z0NBQUtQLFdBQVU7MENBQXVCOzs7Ozs7Ozs7Ozs7OzhCQU03Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FBeUM7Ozs7OztzQ0FDeEQsOERBQUNsSSxrRUFBV0E7NEJBQ1YySCxhQUFhL0UsaUJBQWlCLHVCQUF1Qjs0QkFDckRnRyxRQUFROzRCQUNSbEQsT0FBT3RFLFNBQVNJLFFBQVE7NEJBQ3hCcUgsZUFBZSxDQUFDbkQsUUFBVUYsZUFBZSxZQUFZRTs0QkFDckRvRCxTQUFTL0M7NEJBQ1RsRCxPQUFPWCxPQUFPVixRQUFROzRCQUN0QnVILFVBQVVuRzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3BCO0lBRUEscURBQXFEO0lBQ3JELE1BQU1vRyxnQkFBZ0J6RyxrQkFBa0JLLGtCQUFrQks7SUFFMUQsSUFBSStGLGVBQWU7UUFDakIscUJBQ0UsOERBQUNmO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUMvSCw2REFBUUE7NEJBQUMrSCxXQUFVOzs7Ozs7c0NBQ3BCLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUMvSCw2REFBUUE7b0NBQUMrSCxXQUFVOzs7Ozs7OENBQ3BCLDhEQUFDL0gsNkRBQVFBO29DQUFDK0gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUd4Qiw4REFBQ3pJLHFEQUFJQTs7c0NBQ0gsOERBQUNHLDJEQUFVQTs7OENBQ1QsOERBQUNPLDZEQUFRQTtvQ0FBQytILFdBQVU7Ozs7Ozs4Q0FDcEIsOERBQUMvSCw2REFBUUE7b0NBQUMrSCxXQUFVOzs7Ozs7Ozs7Ozs7c0NBRXRCLDhEQUFDeEksNERBQVdBOzRCQUFDd0ksV0FBVTs7OENBQ3JCLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMvSCw2REFBUUE7NENBQUMrSCxXQUFVOzs7Ozs7c0RBQ3BCLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUMvSCw2REFBUUE7b0RBQUMrSCxXQUFVOzs7Ozs7OERBQ3BCLDhEQUFDL0gsNkRBQVFBO29EQUFDK0gsV0FBVTs7Ozs7Ozs7Ozs7O3NEQUV0Qiw4REFBQy9ILDZEQUFRQTs0Q0FBQytILFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FFdEIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQy9ILDZEQUFRQTs0Q0FBQytILFdBQVU7Ozs7OztzREFDcEIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQy9ILDZEQUFRQTtvREFBQytILFdBQVU7Ozs7Ozs4REFDcEIsOERBQUMvSCw2REFBUUE7b0RBQUMrSCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR3hCLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMvSCw2REFBUUE7NENBQUMrSCxXQUFVOzs7Ozs7c0RBQ3BCLDhEQUFDL0gsNkRBQVFBOzRDQUFDK0gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTWhDO0lBRUEsdURBQXVEO0lBQ3ZELElBQUksQ0FBQzdGLFdBQVdTLGdCQUFnQkksWUFBWTtRQUMxQyxxQkFDRSw4REFBQytFO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ3BJLHlEQUFNQTt3QkFBQ21KLFNBQVE7d0JBQVVDLFNBQVMsSUFBTW5JLE9BQU9vSSxJQUFJOzswQ0FDbEQsOERBQUM1SSxvR0FBU0E7Z0NBQUMySCxXQUFVOzs7Ozs7NEJBQWlCOzs7Ozs7Ozs7Ozs7OEJBSzFDLDhEQUFDekkscURBQUlBOzhCQUNILDRFQUFDQyw0REFBV0E7d0JBQUN3SSxXQUFVO2tDQUNyQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7O2dDQUNaLENBQUM3Rix5QkFBVyw4REFBQytHO29DQUFFbEIsV0FBVTs4Q0FBb0I7Ozs7OztnQ0FDN0NwRiw4QkFBZ0IsOERBQUNzRztvQ0FBRWxCLFdBQVU7O3dDQUFvQjt3Q0FBeUJwRixhQUFhYyxPQUFPOzs7Ozs7O2dDQUM5RlYsNEJBQWMsOERBQUNrRztvQ0FBRWxCLFdBQVU7O3dDQUFvQjt3Q0FBdUJoRixXQUFXVSxPQUFPOzs7Ozs7OzhDQUN6Riw4REFBQzlELHlEQUFNQTtvQ0FBQ29KLFNBQVMsSUFBTW5JLE9BQU8yQyxJQUFJLENBQUM7OENBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBUXZFO0lBRUEscUJBQ0UsOERBQUN1RTtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDcEkseURBQU1BO3dCQUFDbUosU0FBUTt3QkFBVUMsU0FBUyxJQUFNbkksT0FBT29JLElBQUk7OzBDQUNsRCw4REFBQzVJLG9HQUFTQTtnQ0FBQzJILFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7a0NBR3hDLDhEQUFDRDs7MENBQ0MsOERBQUNvQjtnQ0FBR25CLFdBQVU7O29DQUFtQztvQ0FDaEM3RixRQUFRaUgsWUFBWTtvQ0FBQztvQ0FBS2pILFFBQVFrSCxZQUFZOzs7Ozs7OzBDQUUvRCw4REFBQ0g7Z0NBQUVsQixXQUFVOzBDQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUt0Qyw4REFBQ3pJLHFEQUFJQTs7a0NBQ0gsOERBQUNHLDJEQUFVQTs7MENBQ1QsOERBQUNDLDBEQUFTQTtnQ0FBQ3FJLFdBQVU7O2tEQUNuQiw4REFBQ3pILG9HQUFRQTt3Q0FBQ3lILFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7MENBR3ZDLDhEQUFDdkksZ0VBQWVBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBSW5CLDhEQUFDRCw0REFBV0E7a0NBQ1YsNEVBQUM4Sjs0QkFBS0MsVUFBVXZFOzRCQUFjZ0QsV0FBVTs7OENBQ3RDLDhEQUFDakksa0VBQVdBO29DQUFDeUosT0FBTTtvQ0FBc0JDLGFBQVk7O3NEQUVuRCw4REFBQzFCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDQTs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUMwQjs4RUFBTzs7Ozs7O2dFQUFlO2dFQUFjL0MsbUJBQW1CLFVBQWlDLE9BQXZCQSxpQkFBaUJmLEtBQUssSUFBSzs7Ozs7OztzRUFFL0YsOERBQUMyQjs0REFFQzNCLE9BQU07NERBQ040QixnQkFBZ0JiOzREQUNoQmMsYUFBWTsyREFIUCxRQUFtQixPQUFYWDs7Ozs7c0VBS2YsOERBQUNoSCxrRUFBV0E7NERBQ1Y4RixPQUFNOzREQUNONkIsYUFBYTFFLGVBQWUscUJBQXFCOzREQUNqRDJGLFFBQVE7NERBQ1JsRCxPQUFPdEUsU0FBU0UsVUFBVTs0REFDMUJ1SCxlQUFlLENBQUNuRCxRQUFVRixlQUFlLGNBQWNFOzREQUN2RG9ELFNBQVMxQzs0REFDVHZELE9BQU9YLE9BQU9aLFVBQVU7NERBQ3hCeUgsVUFBVTlGOzs7Ozs7Ozs7Ozs7OERBSWQsOERBQUNnRjs7c0VBQ0MsOERBQUNBOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQzBCOzhFQUFPOzs7Ozs7Z0VBQWU7Z0VBQWM3QyxtQkFBbUIsVUFBaUMsT0FBdkJBLGlCQUFpQmpCLEtBQUssSUFBSzs7Ozs7OztzRUFFL0YsOERBQUMyQjs0REFFQzNCLE9BQU07NERBQ040QixnQkFBZ0JYOzREQUNoQlksYUFBWTsyREFIUCxRQUFtQixPQUFYWDs7Ozs7c0VBS2YsOERBQUNoSCxrRUFBV0E7NERBQ1Y4RixPQUFNOzREQUNONkIsYUFBYTFFLGVBQWUscUJBQXFCOzREQUNqRDJGLFFBQVE7NERBQ1JsRCxPQUFPdEUsU0FBU0csVUFBVTs0REFDMUJzSCxlQUFlLENBQUNuRCxRQUFVRixlQUFlLGNBQWNFOzREQUN2RG9ELFNBQVMxQyxZQUFZeUQsTUFBTSxDQUFDeEQsQ0FBQUEsT0FBUUEsS0FBS1gsS0FBSyxLQUFLdEUsU0FBU0UsVUFBVTs0REFDdEV1QixPQUFPWCxPQUFPWCxVQUFVOzREQUN4QndILFVBQVU5Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU1oQiw4REFBQ2dGOzs4REFDQyw4REFBQ0E7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDMEI7c0VBQU87Ozs7Ozt3REFBZTt3REFBV25ELGlCQUFpQixVQUErQixPQUFyQkEsZUFBZVgsS0FBSyxJQUFLOzs7Ozs7OzhEQUV4Riw4REFBQzRDOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJTCw4REFBQ3pJLGtFQUFXQTtvQ0FBQ3lKLE9BQU07b0NBQVdDLGFBQVk7O3NEQUN4Qyw4REFBQzFCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ25JLGlFQUFVQTtvREFDVCtGLE9BQU07b0RBQ05zQixNQUFLO29EQUNMd0IsUUFBUTtvREFDUmxELE9BQU90RSxTQUFTSyxJQUFJO29EQUNwQnFJLFVBQVUsQ0FBQzNFLElBQU1LLGVBQWUsUUFBUUwsRUFBRTRFLE1BQU0sQ0FBQ3JFLEtBQUs7b0RBQ3REN0MsT0FBT1gsT0FBT1QsSUFBSTtvREFDbEJrSSxhQUFZOzs7Ozs7OERBR2QsOERBQUM1SixpRUFBVUE7b0RBQ1QrRixPQUFNO29EQUNOc0IsTUFBSztvREFDTHdCLFFBQVE7b0RBQ1JsRCxPQUFPdEUsU0FBU00sSUFBSTtvREFDcEJvSSxVQUFVLENBQUMzRSxJQUFNSyxlQUFlLFFBQVFMLEVBQUU0RSxNQUFNLENBQUNyRSxLQUFLO29EQUN0RDdDLE9BQU9YLE9BQU9SLElBQUk7b0RBQ2xCaUksYUFBYSxlQUFnRSxPQUFqREssS0FBS0MsY0FBYyxHQUFHQyxlQUFlLEdBQUdDLFFBQVEsRUFBQzs7Ozs7Ozs7Ozs7O3NEQUlqRiw4REFBQ2xDOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDa0I7Z0RBQUVsQixXQUFVOztrRUFDWCw4REFBQ087d0RBQUtQLFdBQVU7a0VBQXFCOzs7Ozs7a0VBQ3JDLDhEQUFDMEI7a0VBQU87Ozs7OztvREFBdUI7b0RBQThDSSxLQUFLQyxjQUFjLEdBQUdDLGVBQWUsR0FBR0MsUUFBUTtvREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU1wSSw4REFBQ2xLLGtFQUFXQTtvQ0FBQ3lKLE9BQU07b0NBQWVDLGFBQVk7O3NEQUM1Qyw4REFBQzFCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2xJLGtFQUFXQTtvREFDVjhGLE9BQU07b0RBQ042QixhQUFZO29EQUNaaUIsUUFBUTtvREFDUmxELE9BQU90RSxTQUFTVSxNQUFNO29EQUN0QitHLGVBQWUsQ0FBQ25ELFFBQVVGLGVBQWUsVUFBVUU7b0RBQ25Eb0QsU0FBU2pEO29EQUNUaEQsT0FBT1gsT0FBT0osTUFBTTs7Ozs7OzhEQUd0Qiw4REFBQy9CLGlFQUFVQTtvREFDVCtGLE9BQU07b0RBQ05zQixNQUFLO29EQUNMZ0QsS0FBSTtvREFDSjFFLE9BQU90RSxTQUFTVyxTQUFTO29EQUN6QitILFVBQVUsQ0FBQzNFLElBQU1LLGVBQWUsYUFBYUwsRUFBRTRFLE1BQU0sQ0FBQ3JFLEtBQUs7Ozs7Ozs4REFHN0QsOERBQUMzRixpRUFBVUE7b0RBQ1QrRixPQUFNO29EQUNOc0IsTUFBSztvREFDTGdELEtBQUk7b0RBQ0oxRSxPQUFPdEUsU0FBU1ksU0FBUztvREFDekI4SCxVQUFVLENBQUMzRSxJQUFNSyxlQUFlLGFBQWFMLEVBQUU0RSxNQUFNLENBQUNyRSxLQUFLOzs7Ozs7Ozs7Ozs7c0RBSS9ELDhEQUFDM0YsaUVBQVVBOzRDQUNUK0YsT0FBTTs0Q0FDTnNCLE1BQUs7NENBQ0xnRCxLQUFJOzRDQUNKQyxLQUFJOzRDQUNKM0UsT0FBT3RFLFNBQVNhLE9BQU87NENBQ3ZCNkgsVUFBVSxDQUFDM0UsSUFBTUssZUFBZSxXQUFXTCxFQUFFNEUsTUFBTSxDQUFDckUsS0FBSzs0Q0FDekRpRSxhQUFZOzs7Ozs7Ozs7Ozs7OENBSWhCLDhEQUFDMUosa0VBQVdBO29DQUFDeUosT0FBTTtvQ0FBNEJDLGFBQVk7O3NEQUN6RCw4REFBQzFCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ25JLGlFQUFVQTtvREFDVCtGLE9BQU07b0RBQ042QixhQUFZO29EQUNaakMsT0FBT3RFLFNBQVNPLFNBQVM7b0RBQ3pCbUksVUFBVSxDQUFDM0UsSUFBTUssZUFBZSxhQUFhTCxFQUFFNEUsTUFBTSxDQUFDckUsS0FBSzs7Ozs7OzhEQUc3RCw4REFBQzNGLGlFQUFVQTtvREFDVCtGLE9BQU07b0RBQ042QixhQUFZO29EQUNaakMsT0FBT3RFLFNBQVNRLFNBQVM7b0RBQ3pCa0ksVUFBVSxDQUFDM0UsSUFBTUssZUFBZSxhQUFhTCxFQUFFNEUsTUFBTSxDQUFDckUsS0FBSzs7Ozs7Ozs7Ozs7O3NEQUkvRCw4REFBQ3VDOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ25JLGlFQUFVQTtvREFDVCtGLE9BQU07b0RBQ042QixhQUFZO29EQUNaakMsT0FBT3RFLFNBQVNTLEtBQUs7b0RBQ3JCaUksVUFBVSxDQUFDM0UsSUFBTUssZUFBZSxTQUFTTCxFQUFFNEUsTUFBTSxDQUFDckUsS0FBSzs7Ozs7OzhEQUd6RCw4REFBQzNGLGlFQUFVQTtvREFDVCtGLE9BQU07b0RBQ042QixhQUFZO29EQUNaakMsT0FBT3RFLFNBQVNxRCxPQUFPLElBQUk7b0RBQzNCcUYsVUFBVSxDQUFDM0UsSUFBTUssZUFBZSxXQUFXTCxFQUFFNEUsTUFBTSxDQUFDckUsS0FBSzs7Ozs7Ozs7Ozs7O3NEQUk3RCw4REFBQ3VDOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ25JLGlFQUFVQTtvREFDVCtGLE9BQU07b0RBQ05zQixNQUFLO29EQUNMTyxhQUFZO29EQUNaakMsT0FBT3RFLFNBQVNzRCxXQUFXLElBQUk7b0RBQy9Cb0YsVUFBVSxDQUFDM0UsSUFBTUssZUFBZSxlQUFlTCxFQUFFNEUsTUFBTSxDQUFDckUsS0FBSzs7Ozs7OzhEQUcvRCw4REFBQzNGLGlFQUFVQTtvREFDVCtGLE9BQU07b0RBQ042QixhQUFZO29EQUNaakMsT0FBT3RFLFNBQVN1RCxPQUFPLElBQUk7b0RBQzNCbUYsVUFBVSxDQUFDM0UsSUFBTUssZUFBZSxXQUFXTCxFQUFFNEUsTUFBTSxDQUFDckUsS0FBSzs7Ozs7OzhEQUczRCw4REFBQzNGLGlFQUFVQTtvREFDVCtGLE9BQU07b0RBQ05zQixNQUFLO29EQUNMTyxhQUFZO29EQUNaakMsT0FBT3RFLFNBQVN3RCxVQUFVLElBQUk7b0RBQzlCa0YsVUFBVSxDQUFDM0UsSUFBTUssZUFBZSxjQUFjTCxFQUFFNEUsTUFBTSxDQUFDckUsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtsRSw4REFBQ3hGLGtFQUFXQTs7c0RBQ1YsOERBQUNKLHlEQUFNQTs0Q0FDTHNILE1BQUs7NENBQ0w2QixTQUFROzRDQUNSQyxTQUFTLElBQU1uSSxPQUFPb0ksSUFBSTs0Q0FDMUJKLFVBQVUzRixlQUFla0gsU0FBUztzREFDbkM7Ozs7OztzREFHRCw4REFBQ3hLLHlEQUFNQTs0Q0FDTHNILE1BQUs7NENBQ0wyQixVQUFVM0YsZUFBZWtILFNBQVM7OzhEQUVsQyw4REFBQzlKLG9HQUFJQTtvREFBQzBILFdBQVU7Ozs7OztnREFDZjlFLGVBQWVrSCxTQUFTLEdBQUcsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRNUQ7R0Fyb0J3QjNKOztRQUNQdkIsc0RBQVNBO1FBQ1RDLHNEQUFTQTtRQUNKRyxrRUFBY0E7UUFxQm1CRCw0REFBUUE7UUFPYUEsNERBQVFBO1FBTWRBLDREQUFRQTtRQU1yREQsK0RBQVdBOzs7S0EzQ1pxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2Rhc2hib2FyZC9maXh0dXJlcy9baWRdL2VkaXQvcGFnZS50c3g/YmQxYiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VQYXJhbXMsIHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VNdXRhdGlvbiwgdXNlUXVlcnksIHVzZVF1ZXJ5Q2xpZW50IH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xuaW1wb3J0IHtcbiAgSW5wdXRGaWVsZCxcbiAgU2VsZWN0RmllbGQsXG4gIEZvcm1TZWN0aW9uLFxuICBGb3JtQWN0aW9uc1xufSBmcm9tICdAL2NvbXBvbmVudHMvdWkvZm9ybS1maWVsZCc7XG5pbXBvcnQgeyBTa2VsZXRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9za2VsZXRvbic7XG5pbXBvcnQgeyBmaXh0dXJlc0FwaSB9IGZyb20gJ0AvbGliL2FwaS9maXh0dXJlcyc7XG5pbXBvcnQgeyBsZWFndWVzQXBpIH0gZnJvbSAnQC9saWIvYXBpL2xlYWd1ZXMnO1xuaW1wb3J0IHsgdGVhbXNBcGkgfSBmcm9tICdAL2xpYi9hcGkvdGVhbXMnO1xuaW1wb3J0IHsgQXJyb3dMZWZ0LCBTYXZlLCBDYWxlbmRhciB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3Nvbm5lcic7XG5cbmludGVyZmFjZSBGaXh0dXJlRm9ybURhdGEge1xuICBob21lVGVhbUlkOiBzdHJpbmc7XG4gIGF3YXlUZWFtSWQ6IHN0cmluZztcbiAgbGVhZ3VlSWQ6IHN0cmluZztcbiAgZGF0ZTogc3RyaW5nO1xuICB0aW1lOiBzdHJpbmc7XG4gIHZlbnVlTmFtZTogc3RyaW5nO1xuICB2ZW51ZUNpdHk6IHN0cmluZztcbiAgcm91bmQ6IHN0cmluZztcbiAgc3RhdHVzOiBzdHJpbmc7XG4gIGdvYWxzSG9tZTogc3RyaW5nO1xuICBnb2Fsc0F3YXk6IHN0cmluZztcbiAgZWxhcHNlZDogc3RyaW5nO1xuICByZWZlcmVlPzogc3RyaW5nO1xuICB0ZW1wZXJhdHVyZT86IHN0cmluZztcbiAgd2VhdGhlcj86IHN0cmluZztcbiAgYXR0ZW5kYW5jZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRWRpdEZpeHR1cmVQYWdlKCkge1xuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKTtcbiAgY29uc3QgZml4dHVyZUlkID0gcGFyc2VJbnQocGFyYW1zLmlkIGFzIHN0cmluZyk7XG5cbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZTxGaXh0dXJlRm9ybURhdGE+KHtcbiAgICBob21lVGVhbUlkOiAnJyxcbiAgICBhd2F5VGVhbUlkOiAnJyxcbiAgICBsZWFndWVJZDogJycsXG4gICAgZGF0ZTogJycsXG4gICAgdGltZTogJycsXG4gICAgdmVudWVOYW1lOiAnJyxcbiAgICB2ZW51ZUNpdHk6ICcnLFxuICAgIHJvdW5kOiAnJyxcbiAgICBzdGF0dXM6ICcnLFxuICAgIGdvYWxzSG9tZTogJycsXG4gICAgZ29hbHNBd2F5OiAnJyxcbiAgICBlbGFwc2VkOiAnJyxcbiAgfSk7XG5cbiAgY29uc3QgW2Vycm9ycywgc2V0RXJyb3JzXSA9IHVzZVN0YXRlPFBhcnRpYWw8Rml4dHVyZUZvcm1EYXRhPj4oe30pO1xuXG4gIC8vIEZldGNoIGZpeHR1cmUgZGV0YWlsc1xuICBjb25zdCB7IGRhdGE6IGZpeHR1cmUsIGlzTG9hZGluZzogZml4dHVyZUxvYWRpbmcgfSA9IHVzZVF1ZXJ5KHtcbiAgICBxdWVyeUtleTogWydmaXh0dXJlJywgZml4dHVyZUlkXSxcbiAgICBxdWVyeUZuOiAoKSA9PiBmaXh0dXJlc0FwaS5nZXRGaXh0dXJlKGZpeHR1cmVJZCksXG4gICAgZW5hYmxlZDogISFmaXh0dXJlSWQsXG4gIH0pO1xuXG4gIC8vIEZldGNoIGxlYWd1ZXMgZm9yIGRyb3Bkb3duXG4gIGNvbnN0IHsgZGF0YTogbGVhZ3VlcywgaXNMb2FkaW5nOiBsZWFndWVzTG9hZGluZywgZXJyb3I6IGxlYWd1ZXNFcnJvciB9ID0gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ2xlYWd1ZXMnLCAnYWxsJ10sXG4gICAgcXVlcnlGbjogKCkgPT4gbGVhZ3Vlc0FwaS5nZXRMZWFndWVzKHsgbGltaXQ6IDEwMCB9KSxcbiAgfSk7XG5cbiAgLy8gRmV0Y2ggdGVhbXMgZm9yIGRyb3Bkb3duXG4gIGNvbnN0IHsgZGF0YTogdGVhbXMsIGlzTG9hZGluZzogdGVhbXNMb2FkaW5nLCBlcnJvcjogdGVhbXNFcnJvciB9ID0gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ3RlYW1zJywgJ2FsbCddLFxuICAgIHF1ZXJ5Rm46ICgpID0+IHRlYW1zQXBpLmdldFRlYW1zKHsgbGltaXQ6IDEwMCB9KSxcbiAgfSk7XG5cbiAgLy8gVXBkYXRlIG11dGF0aW9uXG4gIGNvbnN0IHVwZGF0ZU11dGF0aW9uID0gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46IChkYXRhOiBhbnkpID0+IGZpeHR1cmVzQXBpLnVwZGF0ZUZpeHR1cmUoZml4dHVyZUlkLCBkYXRhKSxcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnZml4dHVyZScsIGZpeHR1cmVJZF0gfSk7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2ZpeHR1cmVzJ10gfSk7XG4gICAgICB0b2FzdC5zdWNjZXNzKCdGaXh0dXJlIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgICByb3V0ZXIucHVzaChgL2Rhc2hib2FyZC9maXh0dXJlcy8ke2ZpeHR1cmVJZH1gKTtcbiAgICB9LFxuICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XG4gICAgICB0b2FzdC5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gdXBkYXRlIGZpeHR1cmUnKTtcbiAgICB9LFxuICB9KTtcblxuICAvLyBQb3B1bGF0ZSBmb3JtIHdoZW4gZml4dHVyZSBkYXRhIGxvYWRzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGZpeHR1cmUpIHtcbiAgICAgIGNvbnN0IGZpeHR1cmVEYXRlID0gbmV3IERhdGUoZml4dHVyZS5kYXRlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFBvcHVsYXRpbmcgZm9ybSB3aXRoIGZpeHR1cmUgZGF0YTonLCB7XG4gICAgICAgIGhvbWVUZWFtSWQ6IGZpeHR1cmUuaG9tZVRlYW1JZCxcbiAgICAgICAgYXdheVRlYW1JZDogZml4dHVyZS5hd2F5VGVhbUlkLFxuICAgICAgICBsZWFndWVJZDogZml4dHVyZS5sZWFndWVJZCxcbiAgICAgICAgdmVudWU6IGZpeHR1cmUudmVudWUsXG4gICAgICAgIHZlbnVlTmFtZTogZml4dHVyZS52ZW51ZU5hbWUsXG4gICAgICAgIHZlbnVlQ2l0eTogZml4dHVyZS52ZW51ZUNpdHlcbiAgICAgIH0pO1xuXG4gICAgICBzZXRGb3JtRGF0YSh7XG4gICAgICAgIGhvbWVUZWFtSWQ6IGZpeHR1cmUuaG9tZVRlYW1JZD8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgYXdheVRlYW1JZDogZml4dHVyZS5hd2F5VGVhbUlkPy50b1N0cmluZygpIHx8ICcnLFxuICAgICAgICBsZWFndWVJZDogZml4dHVyZS5sZWFndWVJZD8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgZGF0ZTogZml4dHVyZURhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgICAgICB0aW1lOiBmaXh0dXJlRGF0ZS50b1RpbWVTdHJpbmcoKS5zbGljZSgwLCA1KSxcbiAgICAgICAgLy8gRml4IHZlbnVlIG1hcHBpbmcgLSBBUEkgcmV0dXJucyB2ZW51ZS5uYW1lIGFuZCB2ZW51ZS5jaXR5XG4gICAgICAgIHZlbnVlTmFtZTogZml4dHVyZS52ZW51ZT8ubmFtZSB8fCBmaXh0dXJlLnZlbnVlTmFtZSB8fCAnJyxcbiAgICAgICAgdmVudWVDaXR5OiBmaXh0dXJlLnZlbnVlPy5jaXR5IHx8IGZpeHR1cmUudmVudWVDaXR5IHx8ICcnLFxuICAgICAgICByb3VuZDogZml4dHVyZS5yb3VuZCB8fCAnJyxcbiAgICAgICAgc3RhdHVzOiBmaXh0dXJlLnN0YXR1cyB8fCAnJyxcbiAgICAgICAgZ29hbHNIb21lOiBmaXh0dXJlLmdvYWxzSG9tZT8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgZ29hbHNBd2F5OiBmaXh0dXJlLmdvYWxzQXdheT8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgZWxhcHNlZDogZml4dHVyZS5lbGFwc2VkPy50b1N0cmluZygpIHx8ICcnLFxuICAgICAgICByZWZlcmVlOiBmaXh0dXJlLnJlZmVyZWUgfHwgJycsXG4gICAgICAgIHRlbXBlcmF0dXJlOiBmaXh0dXJlLnRlbXBlcmF0dXJlPy50b1N0cmluZygpIHx8ICcnLFxuICAgICAgICB3ZWF0aGVyOiBmaXh0dXJlLndlYXRoZXIgfHwgJycsXG4gICAgICAgIGF0dGVuZGFuY2U6IGZpeHR1cmUuYXR0ZW5kYW5jZT8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW2ZpeHR1cmVdKTtcblxuICAvLyBNb3ZlIHRoaXMgdXNlRWZmZWN0IGFmdGVyIHRlYW1PcHRpb25zIGFuZCBsZWFndWVPcHRpb25zIGFyZSBkZWZpbmVkXG5cbiAgY29uc3QgdmFsaWRhdGVGb3JtID0gKCk6IGJvb2xlYW4gPT4ge1xuICAgIGNvbnN0IG5ld0Vycm9yczogUGFydGlhbDxGaXh0dXJlRm9ybURhdGE+ID0ge307XG5cbiAgICBpZiAoIWZvcm1EYXRhLmhvbWVUZWFtSWQpIG5ld0Vycm9ycy5ob21lVGVhbUlkID0gJ0hvbWUgdGVhbSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5hd2F5VGVhbUlkKSBuZXdFcnJvcnMuYXdheVRlYW1JZCA9ICdBd2F5IHRlYW0gaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEubGVhZ3VlSWQpIG5ld0Vycm9ycy5sZWFndWVJZCA9ICdMZWFndWUgaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEuZGF0ZSkgbmV3RXJyb3JzLmRhdGUgPSAnRGF0ZSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS50aW1lKSBuZXdFcnJvcnMudGltZSA9ICdUaW1lIGlzIHJlcXVpcmVkJztcbiAgICBpZiAoIWZvcm1EYXRhLnN0YXR1cykgbmV3RXJyb3JzLnN0YXR1cyA9ICdTdGF0dXMgaXMgcmVxdWlyZWQnO1xuXG4gICAgaWYgKGZvcm1EYXRhLmhvbWVUZWFtSWQgPT09IGZvcm1EYXRhLmF3YXlUZWFtSWQpIHtcbiAgICAgIG5ld0Vycm9ycy5hd2F5VGVhbUlkID0gJ0F3YXkgdGVhbSBtdXN0IGJlIGRpZmZlcmVudCBmcm9tIGhvbWUgdGVhbSc7XG4gICAgfVxuXG4gICAgc2V0RXJyb3JzKG5ld0Vycm9ycyk7XG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoID09PSAwO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG5cbiAgICBpZiAoIXZhbGlkYXRlRm9ybSgpKSB7XG4gICAgICB0b2FzdC5lcnJvcignUGxlYXNlIGZpeCB0aGUgZm9ybSBlcnJvcnMnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBDb21iaW5lIGRhdGUgYW5kIHRpbWVcbiAgICBjb25zdCBkYXRlVGltZSA9IG5ldyBEYXRlKGAke2Zvcm1EYXRhLmRhdGV9VCR7Zm9ybURhdGEudGltZX1gKTtcblxuICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB7XG4gICAgICBob21lVGVhbUlkOiBwYXJzZUludChmb3JtRGF0YS5ob21lVGVhbUlkKSxcbiAgICAgIGF3YXlUZWFtSWQ6IHBhcnNlSW50KGZvcm1EYXRhLmF3YXlUZWFtSWQpLFxuICAgICAgbGVhZ3VlSWQ6IHBhcnNlSW50KGZvcm1EYXRhLmxlYWd1ZUlkKSxcbiAgICAgIGRhdGU6IGRhdGVUaW1lLnRvSVNPU3RyaW5nKCksXG4gICAgICB2ZW51ZU5hbWU6IGZvcm1EYXRhLnZlbnVlTmFtZSB8fCBudWxsLFxuICAgICAgdmVudWVDaXR5OiBmb3JtRGF0YS52ZW51ZUNpdHkgfHwgbnVsbCxcbiAgICAgIHJvdW5kOiBmb3JtRGF0YS5yb3VuZCB8fCBudWxsLFxuICAgICAgc3RhdHVzOiBmb3JtRGF0YS5zdGF0dXMsXG4gICAgICBnb2Fsc0hvbWU6IGZvcm1EYXRhLmdvYWxzSG9tZSA/IHBhcnNlSW50KGZvcm1EYXRhLmdvYWxzSG9tZSkgOiBudWxsLFxuICAgICAgZ29hbHNBd2F5OiBmb3JtRGF0YS5nb2Fsc0F3YXkgPyBwYXJzZUludChmb3JtRGF0YS5nb2Fsc0F3YXkpIDogbnVsbCxcbiAgICAgIGVsYXBzZWQ6IGZvcm1EYXRhLmVsYXBzZWQgPyBwYXJzZUludChmb3JtRGF0YS5lbGFwc2VkKSA6IG51bGwsXG4gICAgICByZWZlcmVlOiBmb3JtRGF0YS5yZWZlcmVlIHx8IG51bGwsXG4gICAgICB0ZW1wZXJhdHVyZTogZm9ybURhdGEudGVtcGVyYXR1cmUgPyBwYXJzZUludChmb3JtRGF0YS50ZW1wZXJhdHVyZSkgOiBudWxsLFxuICAgICAgd2VhdGhlcjogZm9ybURhdGEud2VhdGhlciB8fCBudWxsLFxuICAgICAgYXR0ZW5kYW5jZTogZm9ybURhdGEuYXR0ZW5kYW5jZSA/IHBhcnNlSW50KGZvcm1EYXRhLmF0dGVuZGFuY2UpIDogbnVsbCxcbiAgICB9O1xuXG4gICAgdXBkYXRlTXV0YXRpb24ubXV0YXRlKHN1Ym1pdERhdGEpO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZUZvcm1EYXRhID0gKGZpZWxkOiBrZXlvZiBGaXh0dXJlRm9ybURhdGEsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIFtmaWVsZF06IHZhbHVlIH0pKTtcbiAgICAvLyBDbGVhciBlcnJvciB3aGVuIHVzZXIgc3RhcnRzIHR5cGluZ1xuICAgIGlmIChlcnJvcnNbZmllbGRdKSB7XG4gICAgICBzZXRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBbZmllbGRdOiB1bmRlZmluZWQgfSkpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBzdGF0dXNPcHRpb25zID0gW1xuICAgIHsgdmFsdWU6ICdOUycsIGxhYmVsOiAnTm90IFN0YXJ0ZWQnIH0sXG4gICAgeyB2YWx1ZTogJzFIJywgbGFiZWw6ICdGaXJzdCBIYWxmJyB9LFxuICAgIHsgdmFsdWU6ICdIVCcsIGxhYmVsOiAnSGFsZiBUaW1lJyB9LFxuICAgIHsgdmFsdWU6ICcySCcsIGxhYmVsOiAnU2Vjb25kIEhhbGYnIH0sXG4gICAgeyB2YWx1ZTogJ0ZUJywgbGFiZWw6ICdGdWxsIFRpbWUnIH0sXG4gICAgeyB2YWx1ZTogJ1BTVCcsIGxhYmVsOiAnUG9zdHBvbmVkJyB9LFxuICAgIHsgdmFsdWU6ICdDQU5DJywgbGFiZWw6ICdDYW5jZWxsZWQnIH0sXG4gIF07XG5cbiAgY29uc3QgbGVhZ3VlT3B0aW9ucyA9IGxlYWd1ZXM/LmRhdGE/Lm1hcChsZWFndWUgPT4gKHtcbiAgICB2YWx1ZTogbGVhZ3VlLmlkLnRvU3RyaW5nKCksXG4gICAgbGFiZWw6IGAke2xlYWd1ZS5uYW1lfSR7bGVhZ3VlLnNlYXNvbiA/IGAgKCR7bGVhZ3VlLnNlYXNvbn0pYCA6ICcnfWAsXG4gICAgbG9nbzogbGVhZ3VlLmxvZ28sXG4gICAgc2Vhc29uOiBsZWFndWUuc2Vhc29uLFxuICB9KSkgfHwgW107XG5cbiAgY29uc3QgdGVhbU9wdGlvbnMgPSB0ZWFtcz8uZGF0YT8ubWFwKHRlYW0gPT4gKHtcbiAgICB2YWx1ZTogdGVhbS5pZC50b1N0cmluZygpLFxuICAgIGxhYmVsOiB0ZWFtLm5hbWUsXG4gICAgbG9nbzogdGVhbS5sb2dvLFxuICB9KSkgfHwgW107XG5cbiAgLy8gQWRkaXRpb25hbCBlZmZlY3QgdG8gZW5zdXJlIG9wdGlvbnMgYXJlIGxvYWRlZCAtIG1vdmVkIGhlcmUgYWZ0ZXIgb3B0aW9ucyBhcmUgZGVmaW5lZFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5SNIE9wdGlvbnMgTG9hZGluZyBTdGF0dXM6Jywge1xuICAgICAgdGVhbXNMb2FkaW5nLFxuICAgICAgbGVhZ3Vlc0xvYWRpbmcsXG4gICAgICB0ZWFtT3B0aW9uc0NvdW50OiB0ZWFtT3B0aW9ucy5sZW5ndGgsXG4gICAgICBsZWFndWVPcHRpb25zQ291bnQ6IGxlYWd1ZU9wdGlvbnMubGVuZ3RoLFxuICAgICAgZm9ybURhdGFSZWFkeTogISEoZm9ybURhdGEuaG9tZVRlYW1JZCAmJiBmb3JtRGF0YS5hd2F5VGVhbUlkICYmIGZvcm1EYXRhLmxlYWd1ZUlkKSxcbiAgICB9KTtcbiAgfSwgW3RlYW1zTG9hZGluZywgbGVhZ3Vlc0xvYWRpbmcsIHRlYW1PcHRpb25zLmxlbmd0aCwgbGVhZ3VlT3B0aW9ucy5sZW5ndGgsIGZvcm1EYXRhXSk7XG5cbiAgLy8gR2V0IHNlbGVjdGVkIG9wdGlvbnMgZm9yIHByZXZpZXcgLSB3aXRoIHR5cGUgY29udmVyc2lvbiBmaXggYW5kIGRldGFpbGVkIGRlYnVnZ2luZ1xuICBjb25zdCBzZWxlY3RlZExlYWd1ZSA9IGxlYWd1ZU9wdGlvbnMuZmluZChsID0+IGwudmFsdWUgPT09IE51bWJlcihmb3JtRGF0YS5sZWFndWVJZCkpO1xuICBjb25zdCBzZWxlY3RlZEhvbWVUZWFtID0gdGVhbU9wdGlvbnMuZmluZCh0ID0+IHQudmFsdWUgPT09IE51bWJlcihmb3JtRGF0YS5ob21lVGVhbUlkKSk7XG4gIGNvbnN0IHNlbGVjdGVkQXdheVRlYW0gPSB0ZWFtT3B0aW9ucy5maW5kKHQgPT4gdC52YWx1ZSA9PT0gTnVtYmVyKGZvcm1EYXRhLmF3YXlUZWFtSWQpKTtcblxuICAvLyBEZWJ1ZyBzZWxlY3RlZCB2YWx1ZXNcbiAgY29uc29sZS5sb2coJ/Cfjq8gU0VMRUNURUQgVkFMVUVTIERFQlVHOicpO1xuICBjb25zb2xlLmxvZygnc2VsZWN0ZWRMZWFndWU6Jywgc2VsZWN0ZWRMZWFndWUpO1xuICBjb25zb2xlLmxvZygnc2VsZWN0ZWRIb21lVGVhbTonLCBzZWxlY3RlZEhvbWVUZWFtKTtcbiAgY29uc29sZS5sb2coJ3NlbGVjdGVkQXdheVRlYW06Jywgc2VsZWN0ZWRBd2F5VGVhbSk7XG5cbiAgLy8gRGVidWcgZm9ybSBkYXRhXG4gIGNvbnNvbGUubG9nKCfwn5OdIEZPUk0gREFUQTonLCBmb3JtRGF0YSk7XG4gIGNvbnNvbGUubG9nKCfwn5OKIE9QVElPTlMgQ09VTlQ6JywgeyB0ZWFtczogdGVhbU9wdGlvbnMubGVuZ3RoLCBsZWFndWVzOiBsZWFndWVPcHRpb25zLmxlbmd0aCB9KTtcblxuICAvLyBGb3JjZSByZS1yZW5kZXIgd2hlbiBvcHRpb25zIGNoYW5nZSB0byBlbnN1cmUgcHJldmlldyB1cGRhdGVzXG4gIGNvbnN0IFtwcmV2aWV3S2V5LCBzZXRQcmV2aWV3S2V5XSA9IHVzZVN0YXRlKDApO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gVXBkYXRlIHByZXZpZXcgd2hlbiBvcHRpb25zIG9yIGZvcm0gZGF0YSBjaGFuZ2VzXG4gICAgaWYgKHRlYW1PcHRpb25zLmxlbmd0aCA+IDAgJiYgbGVhZ3VlT3B0aW9ucy5sZW5ndGggPiAwICYmIGZvcm1EYXRhLmhvbWVUZWFtSWQpIHtcbiAgICAgIHNldFByZXZpZXdLZXkocHJldiA9PiBwcmV2ICsgMSk7XG4gICAgfVxuICB9LCBbdGVhbU9wdGlvbnMubGVuZ3RoLCBsZWFndWVPcHRpb25zLmxlbmd0aCwgZm9ybURhdGEuaG9tZVRlYW1JZCwgZm9ybURhdGEuYXdheVRlYW1JZCwgZm9ybURhdGEubGVhZ3VlSWRdKTtcblxuICAvLyBTaW1wbGUgZGVidWcgZm9yIElEIG1hdGNoaW5nXG4gIGNvbnNvbGUubG9nKCfwn5SnIElEIE1BVENISU5HIERFQlVHOicpO1xuICBjb25zb2xlLmxvZygnRm9ybSBJRHM6JywgeyBob21lOiBmb3JtRGF0YS5ob21lVGVhbUlkLCBhd2F5OiBmb3JtRGF0YS5hd2F5VGVhbUlkLCBsZWFndWU6IGZvcm1EYXRhLmxlYWd1ZUlkIH0pO1xuICBjb25zb2xlLmxvZygnRm9ybSBJRCBUeXBlczonLCB7XG4gICAgaG9tZTogdHlwZW9mIGZvcm1EYXRhLmhvbWVUZWFtSWQsXG4gICAgYXdheTogdHlwZW9mIGZvcm1EYXRhLmF3YXlUZWFtSWQsXG4gICAgbGVhZ3VlOiB0eXBlb2YgZm9ybURhdGEubGVhZ3VlSWRcbiAgfSk7XG5cbiAgaWYgKHRlYW1PcHRpb25zLmxlbmd0aCA+IDApIHtcbiAgICBjb25zb2xlLmxvZygnU2FtcGxlIFRlYW0gT3B0aW9uczonLCB0ZWFtT3B0aW9ucy5zbGljZSgwLCAzKSk7XG4gICAgY29uc29sZS5sb2coJ1RlYW0gT3B0aW9uIFR5cGVzOicsIHRlYW1PcHRpb25zLnNsaWNlKDAsIDEpLm1hcCh0ID0+ICh7IHZhbHVlOiB0LnZhbHVlLCB0eXBlOiB0eXBlb2YgdC52YWx1ZSB9KSkpO1xuICB9XG5cbiAgaWYgKGxlYWd1ZU9wdGlvbnMubGVuZ3RoID4gMCkge1xuICAgIGNvbnNvbGUubG9nKCdTYW1wbGUgTGVhZ3VlIE9wdGlvbnM6JywgbGVhZ3VlT3B0aW9ucy5zbGljZSgwLCAyKSk7XG4gICAgY29uc29sZS5sb2coJ0xlYWd1ZSBPcHRpb24gVHlwZXM6JywgbGVhZ3VlT3B0aW9ucy5zbGljZSgwLCAxKS5tYXAobCA9PiAoeyB2YWx1ZTogbC52YWx1ZSwgdHlwZTogdHlwZW9mIGwudmFsdWUgfSkpKTtcbiAgfVxuXG4gIGNvbnNvbGUubG9nKCdJRCBFeGlzdHMgQ2hlY2s6Jywge1xuICAgIGhvbWVFeGlzdHM6IHRlYW1PcHRpb25zLnNvbWUodCA9PiB0LnZhbHVlID09PSBOdW1iZXIoZm9ybURhdGEuaG9tZVRlYW1JZCkpLFxuICAgIGF3YXlFeGlzdHM6IHRlYW1PcHRpb25zLnNvbWUodCA9PiB0LnZhbHVlID09PSBOdW1iZXIoZm9ybURhdGEuYXdheVRlYW1JZCkpLFxuICAgIGxlYWd1ZUV4aXN0czogbGVhZ3VlT3B0aW9ucy5zb21lKGwgPT4gbC52YWx1ZSA9PT0gTnVtYmVyKGZvcm1EYXRhLmxlYWd1ZUlkKSksXG4gIH0pO1xuXG4gIC8vIFByZXZpZXcgY29tcG9uZW50IGZvciBzZWxlY3RlZCB2YWx1ZXNcbiAgY29uc3QgU2VsZWN0ZWRWYWx1ZVByZXZpZXcgPSAoe1xuICAgIGxhYmVsLFxuICAgIHNlbGVjdGVkT3B0aW9uLFxuICAgIHBsYWNlaG9sZGVyID0gXCJOb3Qgc2VsZWN0ZWRcIlxuICB9OiB7XG4gICAgbGFiZWw6IHN0cmluZztcbiAgICBzZWxlY3RlZE9wdGlvbj86IHsgdmFsdWU6IG51bWJlcjsgbGFiZWw6IHN0cmluZzsgbG9nbz86IHN0cmluZyB9O1xuICAgIHBsYWNlaG9sZGVyPzogc3RyaW5nO1xuICB9KSA9PiB7XG4gICAgY29uc3QgQ0ROX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0RPTUFJTl9DRE5fUElDVFVSRSB8fCAnaHR0cDovLzExNi4yMDMuMTI1LjY1JztcblxuICAgIC8vIERlYnVnIHByZXZpZXcgY29tcG9uZW50XG4gICAgY29uc29sZS5sb2coYPCfjqggJHtsYWJlbH0gUHJldmlldzpgLCB7IHNlbGVjdGVkT3B0aW9uLCBwbGFjZWhvbGRlciwgaGFzT3B0aW9uOiAhIXNlbGVjdGVkT3B0aW9uIH0pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMyBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPntsYWJlbH08L2Rpdj5cbiAgICAgICAge3NlbGVjdGVkT3B0aW9uID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICB7c2VsZWN0ZWRPcHRpb24ubG9nbyAmJiAoXG4gICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICBzcmM9e2Ake0NETl9VUkx9LyR7c2VsZWN0ZWRPcHRpb24ubG9nb31gfVxuICAgICAgICAgICAgICAgIGFsdD17c2VsZWN0ZWRPcHRpb24ubGFiZWx9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy04IGgtOCBvYmplY3QtY29udGFpbiByb3VuZGVkXCJcbiAgICAgICAgICAgICAgICBvbkVycm9yPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmRpc3BsYXkgPSAnbm9uZSc7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICB7c2VsZWN0ZWRPcHRpb24ubGFiZWx9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmF5LTIwMCByb3VuZGVkIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPj88L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaXRhbGljXCI+e3BsYWNlaG9sZGVyfTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgLy8gSW5saW5lIExlYWd1ZSBjb21wb25lbnQgKGxvZ28gKyBuYW1lIGLDqm4gdHLDoWksIGRyb3Bkb3duIGLDqm4gcGjhuqNpKVxuICBjb25zdCBMZWFndWVJbmxpbmVTZWxlY3RvciA9ICgpID0+IHtcbiAgICBjb25zdCBDRE5fVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRE9NQUlOX0NETl9QSUNUVVJFIHx8ICdodHRwOi8vMTE2LjIwMy4xMjUuNjUnO1xuXG4gICAgLy8gRGVidWcgbGVhZ3VlIHNlbGVjdG9yXG4gICAgY29uc29sZS5sb2coJ/Cfj4YgTGVhZ3VlIElubGluZSBTZWxlY3RvcjonLCB7IHNlbGVjdGVkTGVhZ3VlLCBoYXNMZWFndWU6ICEhc2VsZWN0ZWRMZWFndWUgfSk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgey8qIExlZnQgc2lkZTogTG9nbyArIExlYWd1ZSBuYW1lICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtaW4tdy0wIGZsZXgtMVwiPlxuICAgICAgICAgIHtzZWxlY3RlZExlYWd1ZSA/IChcbiAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgIHtzZWxlY3RlZExlYWd1ZS5sb2dvICYmIChcbiAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICBzcmM9e2Ake0NETl9VUkx9LyR7c2VsZWN0ZWRMZWFndWUubG9nb31gfVxuICAgICAgICAgICAgICAgICAgYWx0PXtzZWxlY3RlZExlYWd1ZS5sYWJlbH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctOCBoLTggb2JqZWN0LWNvbnRhaW4gcm91bmRlZCBmbGV4LXNocmluay0wXCJcbiAgICAgICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZExlYWd1ZS5sYWJlbH1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmF5LTIwMCByb3VuZGVkIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQteHNcIj4/PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBpdGFsaWNcIj5ObyBsZWFndWUgc2VsZWN0ZWQ8L3NwYW4+XG4gICAgICAgICAgICA8Lz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUmlnaHQgc2lkZTogRHJvcGRvd24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCB3LTY0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPkxlYWd1ZSo8L2Rpdj5cbiAgICAgICAgICA8U2VsZWN0RmllbGRcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtsZWFndWVzTG9hZGluZyA/IFwiTG9hZGluZyBsZWFndWVzLi4uXCIgOiBcIlNlbGVjdCBsZWFndWVcIn1cbiAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubGVhZ3VlSWR9XG4gICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IHVwZGF0ZUZvcm1EYXRhKCdsZWFndWVJZCcsIHZhbHVlKX1cbiAgICAgICAgICAgIG9wdGlvbnM9e2xlYWd1ZU9wdGlvbnN9XG4gICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmxlYWd1ZUlkfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2xlYWd1ZXNMb2FkaW5nfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfTtcblxuICAvLyBTaG93IGxvYWRpbmcgc3RhdGUgaWYgYW55IHJlcXVpcmVkIGRhdGEgaXMgbG9hZGluZ1xuICBjb25zdCBpc0RhdGFMb2FkaW5nID0gZml4dHVyZUxvYWRpbmcgfHwgbGVhZ3Vlc0xvYWRpbmcgfHwgdGVhbXNMb2FkaW5nO1xuXG4gIGlmIChpc0RhdGFMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTAgdy0yMFwiIC8+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTggdy02NFwiIC8+XG4gICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC00IHctNDhcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC02IHctNDhcIiAvPlxuICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtNCB3LTY0XCIgLz5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtNCB3LTMyXCIgLz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxuICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTEwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTEwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtNCB3LTI0XCIgLz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxuICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTEwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTAgdy0yMFwiIC8+XG4gICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTEwIHctMzJcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIFNob3cgZXJyb3Igc3RhdGUgaWYgYW55IGNyaXRpY2FsIGRhdGEgZmFpbGVkIHRvIGxvYWRcbiAgaWYgKCFmaXh0dXJlIHx8IGxlYWd1ZXNFcnJvciB8fCB0ZWFtc0Vycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9eygpID0+IHJvdXRlci5iYWNrKCl9PlxuICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgQmFja1xuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIHshZml4dHVyZSAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbWItNFwiPkZpeHR1cmUgbm90IGZvdW5kPC9wPn1cbiAgICAgICAgICAgICAge2xlYWd1ZXNFcnJvciAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbWItNFwiPkZhaWxlZCB0byBsb2FkIGxlYWd1ZXM6IHtsZWFndWVzRXJyb3IubWVzc2FnZX08L3A+fVxuICAgICAgICAgICAgICB7dGVhbXNFcnJvciAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbWItNFwiPkZhaWxlZCB0byBsb2FkIHRlYW1zOiB7dGVhbXNFcnJvci5tZXNzYWdlfTwvcD59XG4gICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9kYXNoYm9hcmQvZml4dHVyZXMnKX0+XG4gICAgICAgICAgICAgICAgUmV0dXJuIHRvIEZpeHR1cmVzXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gcm91dGVyLmJhY2soKX0+XG4gICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgIEJhY2tcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICBFZGl0IEZpeHR1cmU6IHtmaXh0dXJlLmhvbWVUZWFtTmFtZX0gdnMge2ZpeHR1cmUuYXdheVRlYW1OYW1lfVxuICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtdC0xXCI+VXBkYXRlIGZpeHR1cmUgZGV0YWlscyBhbmQgbWF0Y2ggaW5mb3JtYXRpb248L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBGb3JtICovfVxuICAgICAgPENhcmQ+XG4gICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJtci0yIGgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgRml4dHVyZSBEZXRhaWxzXG4gICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIFVwZGF0ZSB0aGUgZml4dHVyZSBpbmZvcm1hdGlvblxuICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIDxGb3JtU2VjdGlvbiB0aXRsZT1cIlRlYW1zICYgQ29tcGV0aXRpb25cIiBkZXNjcmlwdGlvbj1cIlNlbGVjdCB0aGUgdGVhbXMgYW5kIGxlYWd1ZVwiPlxuICAgICAgICAgICAgICB7LyogVGVhbXMgU2VsZWN0aW9uIHdpdGggUHJldmlldyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMiBwLTIgYmcteWVsbG93LTEwMCBib3JkZXIgYm9yZGVyLXllbGxvdy0zMDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPkRFQlVHOjwvc3Ryb25nPiBIb21lIFRlYW0gLSB7c2VsZWN0ZWRIb21lVGVhbSA/IGBGb3VuZDogJHtzZWxlY3RlZEhvbWVUZWFtLmxhYmVsfWAgOiAnTm90IGZvdW5kJ31cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdGVkVmFsdWVQcmV2aWV3XG4gICAgICAgICAgICAgICAgICAgIGtleT17YGhvbWUtJHtwcmV2aWV3S2V5fWB9XG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiU2VsZWN0ZWQgSG9tZSBUZWFtXCJcbiAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRPcHRpb249e3NlbGVjdGVkSG9tZVRlYW19XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTm8gaG9tZSB0ZWFtIHNlbGVjdGVkXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0RmllbGRcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJIb21lIFRlYW1cIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17dGVhbXNMb2FkaW5nID8gXCJMb2FkaW5nIHRlYW1zLi4uXCIgOiBcIlNlbGVjdCBob21lIHRlYW1cIn1cbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmhvbWVUZWFtSWR9XG4gICAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2hvbWVUZWFtSWQnLCB2YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e3RlYW1PcHRpb25zfVxuICAgICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmhvbWVUZWFtSWR9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXt0ZWFtc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMiBwLTIgYmcteWVsbG93LTEwMCBib3JkZXIgYm9yZGVyLXllbGxvdy0zMDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPkRFQlVHOjwvc3Ryb25nPiBBd2F5IFRlYW0gLSB7c2VsZWN0ZWRBd2F5VGVhbSA/IGBGb3VuZDogJHtzZWxlY3RlZEF3YXlUZWFtLmxhYmVsfWAgOiAnTm90IGZvdW5kJ31cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdGVkVmFsdWVQcmV2aWV3XG4gICAgICAgICAgICAgICAgICAgIGtleT17YGF3YXktJHtwcmV2aWV3S2V5fWB9XG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiU2VsZWN0ZWQgQXdheSBUZWFtXCJcbiAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRPcHRpb249e3NlbGVjdGVkQXdheVRlYW19XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTm8gYXdheSB0ZWFtIHNlbGVjdGVkXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0RmllbGRcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJBd2F5IFRlYW1cIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17dGVhbXNMb2FkaW5nID8gXCJMb2FkaW5nIHRlYW1zLi4uXCIgOiBcIlNlbGVjdCBhd2F5IHRlYW1cIn1cbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmF3YXlUZWFtSWR9XG4gICAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2F3YXlUZWFtSWQnLCB2YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e3RlYW1PcHRpb25zLmZpbHRlcih0ZWFtID0+IHRlYW0udmFsdWUgIT09IGZvcm1EYXRhLmhvbWVUZWFtSWQpfVxuICAgICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmF3YXlUZWFtSWR9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXt0ZWFtc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogTGVhZ3VlIFNlbGVjdGlvbiAtIElubGluZSBMYXlvdXQgKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0yIHAtMiBiZy1ibHVlLTEwMCBib3JkZXIgYm9yZGVyLWJsdWUtMzAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgIDxzdHJvbmc+REVCVUc6PC9zdHJvbmc+IExlYWd1ZSAtIHtzZWxlY3RlZExlYWd1ZSA/IGBGb3VuZDogJHtzZWxlY3RlZExlYWd1ZS5sYWJlbH1gIDogJ05vdCBmb3VuZCd9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPExlYWd1ZUlubGluZVNlbGVjdG9yIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9Gb3JtU2VjdGlvbj5cblxuICAgICAgICAgICAgPEZvcm1TZWN0aW9uIHRpdGxlPVwiU2NoZWR1bGVcIiBkZXNjcmlwdGlvbj1cIlNldCB0aGUgZGF0ZSBhbmQgdGltZSAobG9jYWwgdGltZXpvbmUpXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIkRhdGUgKlwiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRhdGV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdkYXRlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5kYXRlfVxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb249XCJNYXRjaCBkYXRlXCJcbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiVGltZSAqXCJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0aW1lXCJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudGltZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3RpbWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLnRpbWV9XG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbj17YExvY2FsIHRpbWUgKCR7SW50bC5EYXRlVGltZUZvcm1hdCgpLnJlc29sdmVkT3B0aW9ucygpLnRpbWVab25lfSlgfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIGJnLWJsdWUtNTAgcC0zIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ibHVlLTIwMFwiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIG1yLTJcIj7ihLnvuI88L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Ryb25nPlRpbWV6b25lIEluZm86PC9zdHJvbmc+IFRpbWVzIGFyZSBkaXNwbGF5ZWQgaW4geW91ciBsb2NhbCB0aW1lem9uZSAoe0ludGwuRGF0ZVRpbWVGb3JtYXQoKS5yZXNvbHZlZE9wdGlvbnMoKS50aW1lWm9uZX0pLlxuICAgICAgICAgICAgICAgICAgVGhlIGFzdGVyaXNrICgqKSBpbmRpY2F0ZXMgcmVxdWlyZWQgZmllbGRzLlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0Zvcm1TZWN0aW9uPlxuXG4gICAgICAgICAgICA8Rm9ybVNlY3Rpb24gdGl0bGU9XCJNYXRjaCBTdGF0dXNcIiBkZXNjcmlwdGlvbj1cIlVwZGF0ZSBtYXRjaCBzdGF0dXMgYW5kIHNjb3JlXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxTZWxlY3RGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJTdGF0dXNcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3Qgc3RhdHVzXCJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB1cGRhdGVGb3JtRGF0YSgnc3RhdHVzJywgdmFsdWUpfVxuICAgICAgICAgICAgICAgICAgb3B0aW9ucz17c3RhdHVzT3B0aW9uc31cbiAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuc3RhdHVzfVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJIb21lIEdvYWxzXCJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZ29hbHNIb21lfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnZ29hbHNIb21lJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJBd2F5IEdvYWxzXCJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZ29hbHNBd2F5fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnZ29hbHNBd2F5JywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgbGFiZWw9XCJFbGFwc2VkIFRpbWUgKG1pbnV0ZXMpXCJcbiAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICBtYXg9XCIxMjBcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbGFwc2VkfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2VsYXBzZWQnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb249XCJNaW51dGVzIHBsYXllZCBpbiB0aGUgbWF0Y2hcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9Gb3JtU2VjdGlvbj5cblxuICAgICAgICAgICAgPEZvcm1TZWN0aW9uIHRpdGxlPVwiVmVudWUgJiBNYXRjaCBJbmZvcm1hdGlvblwiIGRlc2NyaXB0aW9uPVwiVmVudWUgZGV0YWlscyBhbmQgbWF0Y2ggY29udGV4dFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJWZW51ZSBOYW1lXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU3RhZGl1bSBuYW1lXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS52ZW51ZU5hbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCd2ZW51ZU5hbWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlZlbnVlIENpdHlcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJDaXR5XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS52ZW51ZUNpdHl9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCd2ZW51ZUNpdHknLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiUm91bmRcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCBNYXRjaGRheSAxLCBRdWFydGVyLWZpbmFsXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5yb3VuZH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3JvdW5kJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJSZWZlcmVlXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUmVmZXJlZSBuYW1lXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5yZWZlcmVlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgncmVmZXJlZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJUZW1wZXJhdHVyZSAowrBDKVwiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgMjJcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRlbXBlcmF0dXJlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgndGVtcGVyYXR1cmUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIldlYXRoZXJcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCBTdW5ueSwgUmFpbnlcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLndlYXRoZXIgfHwgJyd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCd3ZWF0aGVyJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJBdHRlbmRhbmNlXCJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJOdW1iZXIgb2Ygc3BlY3RhdG9yc1wiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYXR0ZW5kYW5jZSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2F0dGVuZGFuY2UnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0Zvcm1TZWN0aW9uPlxuXG4gICAgICAgICAgICA8Rm9ybUFjdGlvbnM+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLmJhY2soKX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17dXBkYXRlTXV0YXRpb24uaXNQZW5kaW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3VwZGF0ZU11dGF0aW9uLmlzUGVuZGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxTYXZlIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAge3VwZGF0ZU11dGF0aW9uLmlzUGVuZGluZyA/ICdVcGRhdGluZy4uLicgOiAnVXBkYXRlIEZpeHR1cmUnfVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvRm9ybUFjdGlvbnM+XG4gICAgICAgICAgPC9mb3JtPlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUGFyYW1zIiwidXNlUm91dGVyIiwidXNlTXV0YXRpb24iLCJ1c2VRdWVyeSIsInVzZVF1ZXJ5Q2xpZW50IiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIklucHV0RmllbGQiLCJTZWxlY3RGaWVsZCIsIkZvcm1TZWN0aW9uIiwiRm9ybUFjdGlvbnMiLCJTa2VsZXRvbiIsImZpeHR1cmVzQXBpIiwibGVhZ3Vlc0FwaSIsInRlYW1zQXBpIiwiQXJyb3dMZWZ0IiwiU2F2ZSIsIkNhbGVuZGFyIiwidG9hc3QiLCJFZGl0Rml4dHVyZVBhZ2UiLCJsZWFndWVzIiwidGVhbXMiLCJwYXJhbXMiLCJyb3V0ZXIiLCJxdWVyeUNsaWVudCIsImZpeHR1cmVJZCIsInBhcnNlSW50IiwiaWQiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwiaG9tZVRlYW1JZCIsImF3YXlUZWFtSWQiLCJsZWFndWVJZCIsImRhdGUiLCJ0aW1lIiwidmVudWVOYW1lIiwidmVudWVDaXR5Iiwicm91bmQiLCJzdGF0dXMiLCJnb2Fsc0hvbWUiLCJnb2Fsc0F3YXkiLCJlbGFwc2VkIiwiZXJyb3JzIiwic2V0RXJyb3JzIiwiZGF0YSIsImZpeHR1cmUiLCJpc0xvYWRpbmciLCJmaXh0dXJlTG9hZGluZyIsInF1ZXJ5S2V5IiwicXVlcnlGbiIsImdldEZpeHR1cmUiLCJlbmFibGVkIiwibGVhZ3Vlc0xvYWRpbmciLCJlcnJvciIsImxlYWd1ZXNFcnJvciIsImdldExlYWd1ZXMiLCJsaW1pdCIsInRlYW1zTG9hZGluZyIsInRlYW1zRXJyb3IiLCJnZXRUZWFtcyIsInVwZGF0ZU11dGF0aW9uIiwibXV0YXRpb25GbiIsInVwZGF0ZUZpeHR1cmUiLCJvblN1Y2Nlc3MiLCJpbnZhbGlkYXRlUXVlcmllcyIsInN1Y2Nlc3MiLCJwdXNoIiwib25FcnJvciIsIm1lc3NhZ2UiLCJmaXh0dXJlRGF0ZSIsIkRhdGUiLCJjb25zb2xlIiwibG9nIiwidmVudWUiLCJ0b1N0cmluZyIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJ0b1RpbWVTdHJpbmciLCJzbGljZSIsIm5hbWUiLCJjaXR5IiwicmVmZXJlZSIsInRlbXBlcmF0dXJlIiwid2VhdGhlciIsImF0dGVuZGFuY2UiLCJ2YWxpZGF0ZUZvcm0iLCJuZXdFcnJvcnMiLCJPYmplY3QiLCJrZXlzIiwibGVuZ3RoIiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwiZGF0ZVRpbWUiLCJzdWJtaXREYXRhIiwibXV0YXRlIiwidXBkYXRlRm9ybURhdGEiLCJmaWVsZCIsInZhbHVlIiwicHJldiIsInVuZGVmaW5lZCIsInN0YXR1c09wdGlvbnMiLCJsYWJlbCIsImxlYWd1ZU9wdGlvbnMiLCJtYXAiLCJsZWFndWUiLCJzZWFzb24iLCJsb2dvIiwidGVhbU9wdGlvbnMiLCJ0ZWFtIiwidGVhbU9wdGlvbnNDb3VudCIsImxlYWd1ZU9wdGlvbnNDb3VudCIsImZvcm1EYXRhUmVhZHkiLCJzZWxlY3RlZExlYWd1ZSIsImZpbmQiLCJsIiwiTnVtYmVyIiwic2VsZWN0ZWRIb21lVGVhbSIsInQiLCJzZWxlY3RlZEF3YXlUZWFtIiwicHJldmlld0tleSIsInNldFByZXZpZXdLZXkiLCJob21lIiwiYXdheSIsInR5cGUiLCJob21lRXhpc3RzIiwic29tZSIsImF3YXlFeGlzdHMiLCJsZWFndWVFeGlzdHMiLCJTZWxlY3RlZFZhbHVlUHJldmlldyIsInNlbGVjdGVkT3B0aW9uIiwicGxhY2Vob2xkZXIiLCJDRE5fVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0RPTUFJTl9DRE5fUElDVFVSRSIsImhhc09wdGlvbiIsImRpdiIsImNsYXNzTmFtZSIsImltZyIsInNyYyIsImFsdCIsImN1cnJlbnRUYXJnZXQiLCJzdHlsZSIsImRpc3BsYXkiLCJzcGFuIiwiTGVhZ3VlSW5saW5lU2VsZWN0b3IiLCJoYXNMZWFndWUiLCJyZXF1aXJlZCIsIm9uVmFsdWVDaGFuZ2UiLCJvcHRpb25zIiwiZGlzYWJsZWQiLCJpc0RhdGFMb2FkaW5nIiwidmFyaWFudCIsIm9uQ2xpY2siLCJiYWNrIiwicCIsImgxIiwiaG9tZVRlYW1OYW1lIiwiYXdheVRlYW1OYW1lIiwiZm9ybSIsIm9uU3VibWl0IiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInN0cm9uZyIsImZpbHRlciIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwiSW50bCIsIkRhdGVUaW1lRm9ybWF0IiwicmVzb2x2ZWRPcHRpb25zIiwidGltZVpvbmUiLCJtaW4iLCJtYXgiLCJpc1BlbmRpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});