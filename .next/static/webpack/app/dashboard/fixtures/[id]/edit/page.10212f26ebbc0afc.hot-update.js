"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Search states\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearchResults, setHomeTeamSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [awayTeamSearch, setAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [awayTeamSearchResults, setAwayTeamSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues without search initially\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams without search initially\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"teams\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Home Team Search with 3s debounce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!homeTeamSearch.trim()) {\n            setHomeTeamSearchResults([]);\n            return;\n        }\n        const timer = setTimeout(async ()=>{\n            try {\n                const searchResults = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                    limit: 100,\n                    search: homeTeamSearch\n                });\n                if (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data) {\n                    setHomeTeamSearchResults(searchResults.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Home team search error:\", error);\n                setHomeTeamSearchResults([]);\n            }\n        }, 3000); // 3 second debounce\n        return ()=>clearTimeout(timer);\n    }, [\n        homeTeamSearch\n    ]);\n    // Away Team Search with 3s debounce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!awayTeamSearch.trim()) {\n            setAwayTeamSearchResults([]);\n            return;\n        }\n        const timer = setTimeout(async ()=>{\n            try {\n                const searchResults = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                    limit: 100,\n                    search: awayTeamSearch\n                });\n                if (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data) {\n                    setAwayTeamSearchResults(searchResults.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Away team search error:\", error);\n                setAwayTeamSearchResults([]);\n            }\n        }, 3000); // 3 second debounce\n        return ()=>clearTimeout(timer);\n    }, [\n        awayTeamSearch\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            // Debug status mapping\n            console.log(\"\\uD83D\\uDD0D STATUS DEBUG:\", {\n                \"fixture.status\": fixture.status,\n                \"statusType\": typeof fixture.status,\n                \"statusOptions\": statusOptions.map((s)=>({\n                        value: s.value,\n                        label: s.label\n                    })),\n                \"statusMatch\": statusOptions.find((s)=>s.value === fixture.status),\n                \"fixtureObject\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search handlers with useCallback to prevent re-renders\n    const handleLeagueSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setLeagueSearch(query);\n    }, []);\n    const handleHomeTeamSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setHomeTeamSearch(query);\n    }, []);\n    const handleAwayTeamSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setAwayTeamSearch(query);\n    }, []);\n    // Status options - API Football Official Documentation + Additional API Values\n    // Source: https://www.api-football.com/documentation-v3#tag/Fixtures/operation/get-fixtures\n    const statusOptions = [\n        // Time Status\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"ST\",\n            label: \"Scheduled\"\n        },\n        // Match Status - Live\n        {\n            value: \"1H\",\n            label: \"First Half, Kick Off\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half, 2nd Half Started\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time (in Extra Time)\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"LIVE\",\n            label: \"In Progress\"\n        },\n        // Match Finished\n        {\n            value: \"FT\",\n            label: \"Match Finished\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        // Match Suspended\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        // Match Postponed\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        // Match Cancelled\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        // Match Abandoned\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        // Technical Loss\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        // Walk Over\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Memoize options to prevent re-renders\n    const leagueOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _leagues_data;\n        return (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league, index)=>({\n                value: league.externalId.toString(),\n                label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n                logo: league.logo,\n                season: league.season,\n                uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n            }))) || [];\n    }, [\n        leagues === null || leagues === void 0 ? void 0 : leagues.data\n    ]);\n    const teamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _teams_data;\n        return (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team, index)=>({\n                value: team.externalId.toString(),\n                label: team.name,\n                logo: team.logo,\n                uniqueKey: \"team-\".concat(team.id || team.externalId, \"-\").concat(index)\n            }))) || [];\n    }, [\n        teams === null || teams === void 0 ? void 0 : teams.data\n    ]);\n    // Home team options: replace with search results when searching, otherwise use initial teams\n    const homeTeamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // If we have search results, use ONLY search results (replace mode)\n        if (homeTeamSearchResults.length > 0) {\n            return homeTeamSearchResults.map((team, index)=>({\n                    value: team.externalId.toString(),\n                    label: team.name,\n                    logo: team.logo,\n                    uniqueKey: \"search-home-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n                }));\n        }\n        // If no search results, use initial teams (default mode)\n        return teamOptions;\n    }, [\n        teamOptions,\n        homeTeamSearchResults\n    ]);\n    // Away team options: replace with search results when searching, otherwise use initial teams\n    const awayTeamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // If we have search results, use ONLY search results (replace mode)\n        if (awayTeamSearchResults.length > 0) {\n            return awayTeamSearchResults.map((team, index)=>({\n                    value: team.externalId.toString(),\n                    label: team.name,\n                    logo: team.logo,\n                    uniqueKey: \"search-away-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n                }));\n        }\n        // If no search results, use initial teams (default mode)\n        return teamOptions;\n    }, [\n        teamOptions,\n        awayTeamSearchResults\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = homeTeamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = awayTeamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (homeTeamOptions.length > 0 && awayTeamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 415,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            isLoading: leaguesLoading\n                        }, \"league-search-stable\", false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 480,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 450,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 505,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 28\n                                }, this),\n                                !!leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load leagues\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 34\n                                }, this),\n                                !!teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load teams\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 32\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 555,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 547,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 574,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 589,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams... (3s delay)\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: homeTeamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleHomeTeamSearch,\n                                                            isLoading: teamsLoading\n                                                        }, \"home-team-search-stable\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams... (3s delay)\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: awayTeamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleAwayTeamSearch,\n                                                            isLoading: teamsLoading\n                                                        }, \"away-team-search-stable\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isLoading ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 588,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 572,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"cap6ng2akNT9tOqKbDdXay6qyyw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});