"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _leagues_data, _teams_data;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Fetch leagues for dropdown\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams for dropdown\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_9__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Half Time\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"FT\",\n            label: \"Full Time\"\n        },\n        {\n            value: \"PST\",\n            label: \"Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Cancelled\"\n        }\n    ];\n    const leagueOptions = (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league)=>({\n            value: league.id.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season\n        }))) || [];\n    const teamOptions = (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team)=>({\n            value: team.id.toString(),\n            label: team.name,\n            logo: team.logo\n        }))) || [];\n    // Get selected options for preview\n    const selectedLeague = leagueOptions.find((l)=>l.value === formData.leagueId);\n    const selectedHomeTeam = teamOptions.find((t)=>t.value === formData.homeTeamId);\n    const selectedAwayTeam = teamOptions.find((t)=>t.value === formData.awayTeamId);\n    // Debug logging\n    console.log(\"\\uD83D\\uDD0D Form Debug:\", {\n        formData: {\n            homeTeamId: formData.homeTeamId,\n            awayTeamId: formData.awayTeamId,\n            leagueId: formData.leagueId\n        },\n        fixture: {\n            homeTeamId: fixture === null || fixture === void 0 ? void 0 : fixture.homeTeamId,\n            awayTeamId: fixture === null || fixture === void 0 ? void 0 : fixture.awayTeamId,\n            leagueId: fixture === null || fixture === void 0 ? void 0 : fixture.leagueId,\n            homeTeamName: fixture === null || fixture === void 0 ? void 0 : fixture.homeTeamName,\n            awayTeamName: fixture === null || fixture === void 0 ? void 0 : fixture.awayTeamName,\n            leagueName: fixture === null || fixture === void 0 ? void 0 : fixture.leagueName\n        },\n        options: {\n            leagueOptionsCount: leagueOptions.length,\n            teamOptionsCount: teamOptions.length\n        },\n        selected: {\n            selectedLeague,\n            selectedHomeTeam,\n            selectedAwayTeam\n        }\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                        label: \"League\",\n                        placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                        required: true,\n                        value: formData.leagueId,\n                        onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                        options: leagueOptions,\n                        error: errors.leagueId,\n                        disabled: leaguesLoading\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 342,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 384,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: teamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-700 mb-3\",\n                                                    children: \"League Selection\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 409,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"lCYwpocHED69IdHfB6HyS5m83TU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});