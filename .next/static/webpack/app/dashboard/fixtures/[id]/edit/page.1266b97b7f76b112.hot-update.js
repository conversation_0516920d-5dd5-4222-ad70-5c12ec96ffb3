"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _teams, _teams1, _teams2;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Smart loading with search and pagination - SEPARATED for Home/Away\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [awayTeamSearch, setAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leaguePage, setLeaguePage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [homeTeamPage, setHomeTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [awayTeamPage, setAwayTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [allLeagues, setAllLeagues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allHomeTeams, setAllHomeTeams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAwayTeams, setAllAwayTeams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues with search and pagination\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"search\",\n            leagueSearch,\n            leaguePage\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100,\n                page: leaguePage,\n                ...leagueSearch && {\n                    search: leagueSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch home teams with search and pagination\n    const { data: homeTeams, isLoading: homeTeamsLoading, error: homeTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"home-teams\",\n            \"search\",\n            homeTeamSearch,\n            homeTeamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: homeTeamPage,\n                ...homeTeamSearch && {\n                    search: homeTeamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch away teams with search and pagination\n    const { data: awayTeams, isLoading: awayTeamsLoading, error: awayTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"away-teams\",\n            \"search\",\n            awayTeamSearch,\n            awayTeamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: awayTeamPage,\n                ...awayTeamSearch && {\n                    search: awayTeamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Track previous search to detect search changes\n    const [prevLeagueSearch, setPrevLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [prevHomeTeamSearch, setPrevHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [prevAwayTeamSearch, setPrevAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Accumulate leagues data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (leagues === null || leagues === void 0 ? void 0 : leagues.data) {\n            // Check if search changed\n            const searchChanged = leagueSearch !== prevLeagueSearch;\n            if (leaguePage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"\\uD83D\\uDD04 Replacing leagues data:\", {\n                    page: leaguePage,\n                    searchChanged,\n                    search: leagueSearch\n                });\n                setAllLeagues(leagues.data);\n                setPrevLeagueSearch(leagueSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"\\uD83D\\uDD04 Appending leagues data:\", {\n                    page: leaguePage,\n                    existing: allLeagues.length,\n                    new: leagues.data.length\n                });\n                setAllLeagues((prev)=>{\n                    const existingIds = new Set(prev.map((l)=>l.externalId));\n                    const newLeagues = leagues.data.filter((l)=>!existingIds.has(l.externalId));\n                    return [\n                        ...prev,\n                        ...newLeagues\n                    ];\n                });\n            }\n        }\n    }, [\n        leagues,\n        leaguePage,\n        leagueSearch,\n        prevLeagueSearch,\n        allLeagues.length\n    ]);\n    // Accumulate teams data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _teams;\n        if ((_teams = teams) === null || _teams === void 0 ? void 0 : _teams.data) {\n            // Check if search changed\n            const searchChanged = teamSearch !== prevTeamSearch;\n            if (teamPage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"\\uD83D\\uDD04 Replacing teams data:\", {\n                    page: teamPage,\n                    searchChanged,\n                    search: teamSearch\n                });\n                setAllTeams(teams.data);\n                setPrevTeamSearch(teamSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"\\uD83D\\uDD04 Appending teams data:\", {\n                    page: teamPage,\n                    existing: allTeams.length,\n                    new: teams.data.length\n                });\n                setAllTeams((prev)=>{\n                    const existingIds = new Set(prev.map((t)=>t.id || t.externalId));\n                    const newTeams = teams.data.filter((t)=>!existingIds.has(t.id || t.externalId));\n                    console.log(\"\\uD83D\\uDD04 Adding new teams:\", {\n                        existing: prev.length,\n                        new: newTeams.length,\n                        total: prev.length + newTeams.length\n                    });\n                    return [\n                        ...prev,\n                        ...newTeams\n                    ];\n                });\n            }\n        }\n    }, [\n        teams,\n        teamPage,\n        teamSearch,\n        prevTeamSearch,\n        allTeams.length\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            // 🔍 DEBUG: Check if fixture uses internal or external IDs\n            console.log(\"\\uD83D\\uDD0D FIXTURE ID ANALYSIS:\", {\n                \"fixture.homeTeamId\": fixture.homeTeamId,\n                \"fixture.awayTeamId\": fixture.awayTeamId,\n                \"fixture.leagueId\": fixture.leagueId,\n                \"fixture object\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search and pagination handlers\n    const handleLeagueSearch = (query)=>{\n        setLeagueSearch(query);\n        setLeaguePage(1); // Reset to first page on new search\n    };\n    const handleTeamSearch = (query)=>{\n        setTeamSearch(query);\n        setTeamPage(1); // Reset to first page on new search\n    };\n    const handleLeagueLoadMore = ()=>{\n        var _leagues_meta, _leagues_meta1, _leagues_meta2;\n        console.log(\"\\uD83D\\uDD04 League Load More:\", {\n            currentPage: leaguePage,\n            totalPages: leagues === null || leagues === void 0 ? void 0 : (_leagues_meta = leagues.meta) === null || _leagues_meta === void 0 ? void 0 : _leagues_meta.totalPages,\n            hasMore: (leagues === null || leagues === void 0 ? void 0 : (_leagues_meta1 = leagues.meta) === null || _leagues_meta1 === void 0 ? void 0 : _leagues_meta1.totalPages) && leaguePage < leagues.meta.totalPages,\n            allLeaguesCount: allLeagues.length\n        });\n        if ((leagues === null || leagues === void 0 ? void 0 : (_leagues_meta2 = leagues.meta) === null || _leagues_meta2 === void 0 ? void 0 : _leagues_meta2.totalPages) && leaguePage < leagues.meta.totalPages) {\n            console.log(\"✅ Loading more leagues, page:\", leaguePage + 1);\n            setLeaguePage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more leagues\");\n        }\n    };\n    const handleTeamLoadMore = ()=>{\n        var _teams_meta, _teams, _teams_meta1, _teams1, _teams_meta2, _teams2;\n        console.log(\"\\uD83D\\uDD04 Team Load More:\", {\n            currentPage: teamPage,\n            totalPages: (_teams = teams) === null || _teams === void 0 ? void 0 : (_teams_meta = _teams.meta) === null || _teams_meta === void 0 ? void 0 : _teams_meta.totalPages,\n            hasMore: ((_teams1 = teams) === null || _teams1 === void 0 ? void 0 : (_teams_meta1 = _teams1.meta) === null || _teams_meta1 === void 0 ? void 0 : _teams_meta1.totalPages) && teamPage < teams.meta.totalPages,\n            allTeamsCount: allTeams.length\n        });\n        if (((_teams2 = teams) === null || _teams2 === void 0 ? void 0 : (_teams_meta2 = _teams2.meta) === null || _teams_meta2 === void 0 ? void 0 : _teams_meta2.totalPages) && teamPage < teams.meta.totalPages) {\n            console.log(\"✅ Loading more teams, page:\", teamPage + 1);\n            setTeamPage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more teams\");\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        {\n            value: \"FT\",\n            label: \"Match Finished\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Use accumulated data for options with unique keys\n    const leagueOptions = allLeagues.map((league, index)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season,\n            uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n        }));\n    const teamOptions = allTeams.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }));\n    // Debug options count\n    console.log(\"\\uD83D\\uDCCA OPTIONS DEBUG:\", {\n        allLeaguesCount: allLeagues.length,\n        allTeamsCount: allTeams.length,\n        leagueOptionsCount: leagueOptions.length,\n        teamOptionsCount: teamOptions.length,\n        leagueMeta: leagues === null || leagues === void 0 ? void 0 : leagues.meta,\n        teamMeta: (_teams = teams) === null || _teams === void 0 ? void 0 : _teams.meta\n    });\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            teamOptionsCount: teamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        teamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = teamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = teamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        teams: teamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dropdownKey, setDropdownKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        teamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Force dropdown re-render when options change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDropdownKey((prev)=>prev + 1);\n    }, [\n        teamOptions.length,\n        leagueOptions.length\n    ]);\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (teamOptions.length > 0) {\n        console.log(\"Sample Team Options:\", teamOptions.slice(0, 3));\n        console.log(\"Team Option Types:\", teamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const homeTeamInOptions = teamOptions.find((t)=>t.value === \"4450\");\n        const awayTeamInOptions = teamOptions.find((t)=>t.value === \"20022\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC TEAMS CHECK:\", {\n            \"homeTeam (4450)\": homeTeamInOptions,\n            \"awayTeam (20022)\": awayTeamInOptions,\n            \"totalTeams\": teamOptions.length\n        });\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n        // 🔍 Check if our specific league exists\n        const leagueInOptions = leagueOptions.find((l)=>l.value === \"570\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC LEAGUE CHECK:\", {\n            \"league (570)\": leagueInOptions,\n            \"totalLeagues\": leagueOptions.length\n        });\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: teamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: teamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 501,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 503,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 500,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            onLoadMore: handleLeagueLoadMore,\n                            hasMore: (leagues === null || leagues === void 0 ? void 0 : leagues.meta) ? leaguePage < leagues.meta.totalPages : false,\n                            isLoading: leaguesLoading\n                        }, \"league-\".concat(dropdownKey), false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 538,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 597,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 596,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 595,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 637,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 665,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 673,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 664,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: teamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleTeamSearch,\n                                                            onLoadMore: handleTeamLoadMore,\n                                                            hasMore: ((_teams1 = teams) === null || _teams1 === void 0 ? void 0 : _teams1.meta) ? teamPage < teams.meta.totalPages : false,\n                                                            isLoading: teamsLoading\n                                                        }, \"home-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleTeamSearch,\n                                                            onLoadMore: handleTeamLoadMore,\n                                                            hasMore: ((_teams2 = teams) === null || _teams2 === void 0 ? void 0 : _teams2.meta) ? teamPage < teams.meta.totalPages : false,\n                                                            isLoading: teamsLoading\n                                                        }, \"away-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 782,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 864,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 855,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 881,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 678,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 662,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"QqhrWsmP4a+DhgjTsK9j4VSMLtU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});