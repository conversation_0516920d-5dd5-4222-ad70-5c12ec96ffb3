"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _leagues_data, _teams_data;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Fetch leagues for dropdown\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams for dropdown\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_9__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Half Time\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"FT\",\n            label: \"Full Time\"\n        },\n        {\n            value: \"PST\",\n            label: \"Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Cancelled\"\n        }\n    ];\n    // FIXED: Use externalId for mapping to match fixture data\n    const leagueOptions = (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season\n        }))) || [];\n    const teamOptions = (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo\n        }))) || [];\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            teamOptionsCount: teamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        teamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Get selected options for preview - FIXED: option.value is string, formData is string\n    const selectedLeague = leagueOptions.find((l)=>l.value === formData.leagueId);\n    const selectedHomeTeam = teamOptions.find((t)=>t.value === formData.homeTeamId);\n    const selectedAwayTeam = teamOptions.find((t)=>t.value === formData.awayTeamId);\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        teams: teamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        teamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (teamOptions.length > 0) {\n        console.log(\"Sample Team Options:\", teamOptions.slice(0, 3));\n        console.log(\"Team Option Types:\", teamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: teamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: teamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 292,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 330,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 381,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 431,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 423,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 p-2 bg-yellow-100 border border-yellow-300 rounded\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"DEBUG:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" Home Team - \",\n                                                                selectedHomeTeam ? \"Found: \".concat(selectedHomeTeam.label) : \"Not found\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: teamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 p-2 bg-yellow-100 border border-yellow-300 rounded\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"DEBUG:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" Away Team - \",\n                                                                selectedAwayTeam ? \"Found: \".concat(selectedAwayTeam.label) : \"Not found\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 p-2 bg-blue-100 border border-blue-300 rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"DEBUG:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" League - \",\n                                                        selectedLeague ? \"Found: \".concat(selectedLeague.label) : \"Not found\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 448,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"GSHj46XZnVF9G5WIuoIXRmSrd1Q=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});