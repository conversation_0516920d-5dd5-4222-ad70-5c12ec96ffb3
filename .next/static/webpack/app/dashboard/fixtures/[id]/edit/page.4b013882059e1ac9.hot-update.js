"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _leagues_data, _homeTeams_data, _awayTeams_data;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Search only - no pagination/load more\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [awayTeamSearch, setAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Fetch leagues with search only\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"search\",\n            leagueSearch\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100,\n                ...leagueSearch && {\n                    search: leagueSearch\n                }\n            })\n    });\n    // Fetch home teams with search only\n    const { data: homeTeams, isLoading: homeTeamsLoading, error: homeTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"home-teams\",\n            \"search\",\n            homeTeamSearch\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                ...homeTeamSearch && {\n                    search: homeTeamSearch\n                }\n            })\n    });\n    // Fetch away teams with search only\n    const { data: awayTeams, isLoading: awayTeamsLoading, error: awayTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"away-teams\",\n            \"search\",\n            awayTeamSearch\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                ...awayTeamSearch && {\n                    search: awayTeamSearch\n                }\n            })\n    });\n    // No data accumulation needed - direct use of API data\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            // 🔍 DEBUG: Check if fixture uses internal or external IDs\n            console.log(\"\\uD83D\\uDD0D FIXTURE ID ANALYSIS:\", {\n                \"fixture.homeTeamId\": fixture.homeTeamId,\n                \"fixture.awayTeamId\": fixture.awayTeamId,\n                \"fixture.leagueId\": fixture.leagueId,\n                \"fixture object\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search handlers only\n    const handleLeagueSearch = (query)=>{\n        console.log(\"\\uD83C\\uDFC6 League search:\", query);\n        setLeagueSearch(query);\n    };\n    const handleHomeTeamSearch = (query)=>{\n        console.log(\"\\uD83C\\uDFE0 Home team search:\", query);\n        setHomeTeamSearch(query);\n    };\n    const handleAwayTeamSearch = (query)=>{\n        console.log(\"✈️ Away team search:\", query);\n        setAwayTeamSearch(query);\n    };\n    const statusOptions = [\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        {\n            value: \"FT\",\n            label: \"Match Finished\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Use direct API data for options\n    const leagueOptions = (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league, index)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season,\n            uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n        }))) || [];\n    const homeTeamOptions = (homeTeams === null || homeTeams === void 0 ? void 0 : (_homeTeams_data = homeTeams.data) === null || _homeTeams_data === void 0 ? void 0 : _homeTeams_data.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"home-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }))) || [];\n    const awayTeamOptions = (awayTeams === null || awayTeams === void 0 ? void 0 : (_awayTeams_data = awayTeams.data) === null || _awayTeams_data === void 0 ? void 0 : _awayTeams_data.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"away-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }))) || [];\n    // Debug options count\n    console.log(\"\\uD83D\\uDCCA OPTIONS DEBUG:\", {\n        leagueOptionsCount: leagueOptions.length,\n        homeTeamOptionsCount: homeTeamOptions.length,\n        awayTeamOptionsCount: awayTeamOptions.length,\n        leagueMeta: leagues === null || leagues === void 0 ? void 0 : leagues.meta,\n        homeTeamMeta: homeTeams === null || homeTeams === void 0 ? void 0 : homeTeams.meta,\n        awayTeamMeta: awayTeams === null || awayTeams === void 0 ? void 0 : awayTeams.meta\n    });\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            homeTeamsLoading,\n            awayTeamsLoading,\n            leaguesLoading,\n            homeTeamOptionsCount: homeTeamOptions.length,\n            awayTeamOptionsCount: awayTeamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        homeTeamsLoading,\n        awayTeamsLoading,\n        leaguesLoading,\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = homeTeamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = awayTeamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        homeTeams: homeTeamOptions.length,\n        awayTeams: awayTeamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dropdownKey, setDropdownKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (homeTeamOptions.length > 0 && awayTeamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Force dropdown re-render when options change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDropdownKey((prev)=>prev + 1);\n    }, [\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length\n    ]);\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (homeTeamOptions.length > 0) {\n        console.log(\"Sample Home Team Options:\", homeTeamOptions.slice(0, 3));\n        console.log(\"Home Team Option Types:\", homeTeamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const homeTeamInOptions = homeTeamOptions.find((t)=>t.value === \"4450\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC HOME TEAM CHECK:\", {\n            \"homeTeam (4450)\": homeTeamInOptions,\n            \"totalHomeTeams\": homeTeamOptions.length\n        });\n    }\n    if (awayTeamOptions.length > 0) {\n        console.log(\"Sample Away Team Options:\", awayTeamOptions.slice(0, 3));\n        console.log(\"Away Team Option Types:\", awayTeamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const awayTeamInOptions = awayTeamOptions.find((t)=>t.value === \"20022\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC AWAY TEAM CHECK:\", {\n            \"awayTeam (20022)\": awayTeamInOptions,\n            \"totalAwayTeams\": awayTeamOptions.length\n        });\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n        // 🔍 Check if our specific league exists\n        const leagueInOptions = leagueOptions.find((l)=>l.value === \"570\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC LEAGUE CHECK:\", {\n            \"league (570)\": leagueInOptions,\n            \"totalLeagues\": leagueOptions.length\n        });\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: homeTeamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: awayTeamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 432,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            isLoading: leaguesLoading\n                        }, \"league-\".concat(dropdownKey), false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 500,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 470,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || homeTeamsLoading || awayTeamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 526,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 533,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 525,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || homeTeamsError || awayTeamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 32\n                                }, this),\n                                homeTeamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load home teams: \",\n                                        homeTeamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 34\n                                }, this),\n                                awayTeamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load away teams: \",\n                                        awayTeamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 567,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 596,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 595,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: homeTeamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: homeTeamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: homeTeamsLoading,\n                                                            onSearch: handleHomeTeamSearch,\n                                                            isLoading: homeTeamsLoading\n                                                        }, \"home-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: awayTeamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: awayTeamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: awayTeamsLoading,\n                                                            onSearch: handleAwayTeamSearch,\n                                                            onLoadMore: handleAwayTeamLoadMore,\n                                                            hasMore: (awayTeams === null || awayTeams === void 0 ? void 0 : awayTeams.meta) ? awayTeamPage < awayTeams.meta.totalPages : false,\n                                                            isLoading: awayTeamsLoading\n                                                        }, \"away-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 819,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 609,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 593,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"Q1tdbha/CFNq1mghaQs6Hge0bLo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});