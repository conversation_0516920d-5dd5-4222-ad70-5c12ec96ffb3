"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Search states\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearchResults, setHomeTeamSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [awayTeamSearch, setAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [awayTeamSearchResults, setAwayTeamSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues without search initially\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams without search initially\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"teams\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Home Team Search with 3s debounce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!homeTeamSearch.trim()) {\n            setHomeTeamSearchResults([]);\n            return;\n        }\n        const timer = setTimeout(async ()=>{\n            try {\n                const searchResults = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                    limit: 100,\n                    search: homeTeamSearch\n                });\n                if (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data) {\n                    setHomeTeamSearchResults(searchResults.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Home team search error:\", error);\n                setHomeTeamSearchResults([]);\n            }\n        }, 3000); // 3 second debounce\n        return ()=>clearTimeout(timer);\n    }, [\n        homeTeamSearch\n    ]);\n    // Away Team Search with 3s debounce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!awayTeamSearch.trim()) {\n            setAwayTeamSearchResults([]);\n            return;\n        }\n        const timer = setTimeout(async ()=>{\n            try {\n                const searchResults = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                    limit: 100,\n                    search: awayTeamSearch\n                });\n                if (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data) {\n                    setAwayTeamSearchResults(searchResults.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Away team search error:\", error);\n                setAwayTeamSearchResults([]);\n            }\n        }, 3000); // 3 second debounce\n        return ()=>clearTimeout(timer);\n    }, [\n        awayTeamSearch\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            // Debug status mapping\n            console.log(\"STATUS DEBUG:\", {\n                fixtureStatus: fixture.status,\n                statusType: typeof fixture.status,\n                statusOptions: statusOptions.map((s)=>s.value),\n                statusMatch: statusOptions.find((s)=>s.value === fixture.status)\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search handlers with useCallback to prevent re-renders\n    const handleLeagueSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setLeagueSearch(query);\n    }, []);\n    const handleHomeTeamSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setHomeTeamSearch(query);\n    }, []);\n    const handleAwayTeamSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setAwayTeamSearch(query);\n    }, []);\n    // Status options - API Football Official Documentation + Additional API Values\n    // Source: https://www.api-football.com/documentation-v3#tag/Fixtures/operation/get-fixtures\n    const statusOptions = [\n        // Time Status\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"ST\",\n            label: \"Scheduled\"\n        },\n        // Match Status - Live\n        {\n            value: \"1H\",\n            label: \"First Half, Kick Off\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half, 2nd Half Started\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time (in Extra Time)\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"LIVE\",\n            label: \"In Progress\"\n        },\n        // Match Finished\n        {\n            value: \"FT\",\n            label: \"Match Finished (Regular Time)\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        // Match Suspended\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        // Match Postponed\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        // Match Cancelled\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        // Match Abandoned\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        // Technical Loss\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        // Walk Over\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Memoize options to prevent re-renders\n    const leagueOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _leagues_data;\n        return (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league, index)=>({\n                value: league.externalId.toString(),\n                label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n                logo: league.logo,\n                season: league.season,\n                uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n            }))) || [];\n    }, [\n        leagues === null || leagues === void 0 ? void 0 : leagues.data\n    ]);\n    const teamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _teams_data;\n        return (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team, index)=>({\n                value: team.externalId.toString(),\n                label: team.name,\n                logo: team.logo,\n                uniqueKey: \"team-\".concat(team.id || team.externalId, \"-\").concat(index)\n            }))) || [];\n    }, [\n        teams === null || teams === void 0 ? void 0 : teams.data\n    ]);\n    // Home team options: replace with search results when searching, otherwise use initial teams\n    const homeTeamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // If we have search results, use ONLY search results (replace mode)\n        if (homeTeamSearchResults.length > 0) {\n            return homeTeamSearchResults.map((team, index)=>({\n                    value: team.externalId.toString(),\n                    label: team.name,\n                    logo: team.logo,\n                    uniqueKey: \"search-home-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n                }));\n        }\n        // If no search results, use initial teams (default mode)\n        return teamOptions;\n    }, [\n        teamOptions,\n        homeTeamSearchResults\n    ]);\n    // Away team options: replace with search results when searching, otherwise use initial teams\n    const awayTeamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // If we have search results, use ONLY search results (replace mode)\n        if (awayTeamSearchResults.length > 0) {\n            return awayTeamSearchResults.map((team, index)=>({\n                    value: team.externalId.toString(),\n                    label: team.name,\n                    logo: team.logo,\n                    uniqueKey: \"search-away-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n                }));\n        }\n        // If no search results, use initial teams (default mode)\n        return teamOptions;\n    }, [\n        teamOptions,\n        awayTeamSearchResults\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = homeTeamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = awayTeamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (homeTeamOptions.length > 0 && awayTeamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 414,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            isLoading: leaguesLoading\n                        }, \"league-search-stable\", false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 449,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 504,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 547,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 28\n                                }, this),\n                                !!leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load leagues\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 34\n                                }, this),\n                                !!teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load teams\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 32\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 546,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 573,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams... (3s delay)\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: homeTeamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleHomeTeamSearch,\n                                                            isLoading: teamsLoading\n                                                        }, \"home-team-search-stable\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams... (3s delay)\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: awayTeamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleAwayTeamSearch,\n                                                            isLoading: teamsLoading\n                                                        }, \"away-team-search-stable\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isLoading ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 795,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 587,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 571,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"cap6ng2akNT9tOqKbDdXay6qyyw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});