"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _leagues_data, _teams_data;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Fetch leagues for dropdown\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams for dropdown\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_9__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Half Time\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"FT\",\n            label: \"Full Time\"\n        },\n        {\n            value: \"PST\",\n            label: \"Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Cancelled\"\n        }\n    ];\n    const leagueOptions = (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league)=>({\n            value: league.id.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season\n        }))) || [];\n    const teamOptions = (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team)=>({\n            value: team.id.toString(),\n            label: team.name,\n            logo: team.logo\n        }))) || [];\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            teamOptionsCount: teamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        teamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Get selected options for preview - with better debugging\n    const selectedLeague = leagueOptions.find((l)=>l.value === formData.leagueId);\n    const selectedHomeTeam = teamOptions.find((t)=>t.value === formData.homeTeamId);\n    const selectedAwayTeam = teamOptions.find((t)=>t.value === formData.awayTeamId);\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        teamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Debug logging\n    console.log(\"\\uD83D\\uDD0D Form Debug:\", {\n        formData: {\n            homeTeamId: formData.homeTeamId,\n            awayTeamId: formData.awayTeamId,\n            leagueId: formData.leagueId\n        },\n        fixture: {\n            homeTeamId: fixture === null || fixture === void 0 ? void 0 : fixture.homeTeamId,\n            awayTeamId: fixture === null || fixture === void 0 ? void 0 : fixture.awayTeamId,\n            leagueId: fixture === null || fixture === void 0 ? void 0 : fixture.leagueId,\n            homeTeamName: fixture === null || fixture === void 0 ? void 0 : fixture.homeTeamName,\n            awayTeamName: fixture === null || fixture === void 0 ? void 0 : fixture.awayTeamName,\n            leagueName: fixture === null || fixture === void 0 ? void 0 : fixture.leagueName\n        },\n        options: {\n            leagueOptionsCount: leagueOptions.length,\n            teamOptionsCount: teamOptions.length\n        },\n        selected: {\n            selectedLeague: selectedLeague ? {\n                id: selectedLeague.value,\n                name: selectedLeague.label\n            } : null,\n            selectedHomeTeam: selectedHomeTeam ? {\n                id: selectedHomeTeam.value,\n                name: selectedHomeTeam.label\n            } : null,\n            selectedAwayTeam: selectedAwayTeam ? {\n                id: selectedAwayTeam.value,\n                name: selectedAwayTeam.label\n            } : null\n        },\n        previewKey,\n        timing: {\n            optionsReady: teamOptions.length > 0 && leagueOptions.length > 0,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId),\n            allReady: teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId\n        }\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 371,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 413,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: teamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 454,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 438,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"GSHj46XZnVF9G5WIuoIXRmSrd1Q=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});