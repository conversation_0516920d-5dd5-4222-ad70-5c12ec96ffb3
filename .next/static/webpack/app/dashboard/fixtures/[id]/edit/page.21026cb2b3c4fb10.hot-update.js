"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _leagues_data, _teams_data;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Smart loading with search and pagination\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teamSearch, setTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leaguePage, setLeaguePage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [teamPage, setTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Fetch leagues with search and pagination\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"search\",\n            leagueSearch,\n            leaguePage\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100,\n                page: leaguePage,\n                ...leagueSearch && {\n                    search: leagueSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch teams with search and pagination\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"search\",\n            teamSearch,\n            teamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: teamPage,\n                ...teamSearch && {\n                    search: teamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            // 🔍 DEBUG: Check if fixture uses internal or external IDs\n            console.log(\"\\uD83D\\uDD0D FIXTURE ID ANALYSIS:\", {\n                \"fixture.homeTeamId\": fixture.homeTeamId,\n                \"fixture.awayTeamId\": fixture.awayTeamId,\n                \"fixture.leagueId\": fixture.leagueId,\n                \"fixture object\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search and pagination handlers\n    const handleLeagueSearch = (query)=>{\n        setLeagueSearch(query);\n        setLeaguePage(1); // Reset to first page on new search\n    };\n    const handleTeamSearch = (query)=>{\n        setTeamSearch(query);\n        setTeamPage(1); // Reset to first page on new search\n    };\n    const handleLeagueLoadMore = ()=>{\n        var _leagues_meta;\n        if ((leagues === null || leagues === void 0 ? void 0 : (_leagues_meta = leagues.meta) === null || _leagues_meta === void 0 ? void 0 : _leagues_meta.totalPages) && leaguePage < leagues.meta.totalPages) {\n            setLeaguePage((prev)=>prev + 1);\n        }\n    };\n    const handleTeamLoadMore = ()=>{\n        var _teams_meta;\n        if ((teams === null || teams === void 0 ? void 0 : (_teams_meta = teams.meta) === null || _teams_meta === void 0 ? void 0 : _teams_meta.totalPages) && teamPage < teams.meta.totalPages) {\n            setTeamPage((prev)=>prev + 1);\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Half Time\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"FT\",\n            label: \"Full Time\"\n        },\n        {\n            value: \"PST\",\n            label: \"Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Cancelled\"\n        }\n    ];\n    // FIXED: Use externalId for mapping to match fixture data\n    const leagueOptions = (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season\n        }))) || [];\n    const teamOptions = (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo\n        }))) || [];\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            teamOptionsCount: teamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        teamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Smart preview logic - use fixture data initially, then update from selections\n    const getPreviewData = ()=>{\n        var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId;\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // For initial load, use fixture data\n        const isInitialData = formData.homeTeamId === ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) && formData.awayTeamId === ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) && formData.leagueId === ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString());\n        if (isInitialData) {\n            return {\n                league: {\n                    value: formData.leagueId,\n                    label: fixture.leagueName,\n                    logo: \"\" // Will be updated when user selects from dropdown\n                },\n                homeTeam: {\n                    value: formData.homeTeamId,\n                    label: fixture.homeTeamName,\n                    logo: fixture.homeTeamLogo\n                },\n                awayTeam: {\n                    value: formData.awayTeamId,\n                    label: fixture.awayTeamName,\n                    logo: fixture.awayTeamLogo\n                }\n            };\n        }\n        // For user selections, use dropdown options\n        return {\n            league: leagueOptions.find((l)=>l.value === formData.leagueId) || null,\n            homeTeam: teamOptions.find((t)=>t.value === formData.homeTeamId) || null,\n            awayTeam: teamOptions.find((t)=>t.value === formData.awayTeamId) || null\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        teams: teamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        teamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (teamOptions.length > 0) {\n        console.log(\"Sample Team Options:\", teamOptions.slice(0, 3));\n        console.log(\"Team Option Types:\", teamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const homeTeamInOptions = teamOptions.find((t)=>t.value === \"4450\");\n        const awayTeamInOptions = teamOptions.find((t)=>t.value === \"20022\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC TEAMS CHECK:\", {\n            \"homeTeam (4450)\": homeTeamInOptions,\n            \"awayTeam (20022)\": awayTeamInOptions,\n            \"totalTeams\": teamOptions.length\n        });\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n        // 🔍 Check if our specific league exists\n        const leagueInOptions = leagueOptions.find((l)=>l.value === \"570\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC LEAGUE CHECK:\", {\n            \"league (570)\": leagueInOptions,\n            \"totalLeagues\": leagueOptions.length\n        });\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: teamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: teamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 392,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            onLoadMore: handleLeagueLoadMore,\n                            hasMore: (leagues === null || leagues === void 0 ? void 0 : leagues.meta) ? leaguePage < leagues.meta.totalPages : false,\n                            isLoading: leaguesLoading\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 430,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 486,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 529,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 536,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 528,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 555,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 p-2 bg-yellow-100 border border-yellow-300 rounded\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"DEBUG:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" Home Team - \",\n                                                                selectedHomeTeam ? \"Found: \".concat(selectedHomeTeam.label) : \"Not found\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: teamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleTeamSearch,\n                                                            onLoadMore: handleTeamLoadMore,\n                                                            hasMore: (teams === null || teams === void 0 ? void 0 : teams.meta) ? teamPage < teams.meta.totalPages : false,\n                                                            isLoading: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 p-2 bg-yellow-100 border border-yellow-300 rounded\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"DEBUG:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" Away Team - \",\n                                                                selectedAwayTeam ? \"Found: \".concat(selectedAwayTeam.label) : \"Not found\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 615,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleTeamSearch,\n                                                            onLoadMore: handleTeamLoadMore,\n                                                            hasMore: (teams === null || teams === void 0 ? void 0 : teams.meta) ? teamPage < teams.meta.totalPages : false,\n                                                            isLoading: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 p-2 bg-blue-100 border border-blue-300 rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"DEBUG:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" League - \",\n                                                        selectedLeague ? \"Found: \".concat(selectedLeague.label) : \"Not found\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 779,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 569,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 553,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"B6OHKgPtfX9xXCPiaqE9oTz38bI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ZpeHR1cmVzL1tpZF0vZWRpdC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ1c7QUFDdUI7QUFDbUI7QUFDakQ7QUFNWjtBQUMwQztBQUMxQjtBQUNIO0FBQ0Y7QUFDSjtBQUNjO0FBQzFCO0FBcUJoQixTQUFTMEI7UUFpTkFDLGVBT0ZDOztJQXZOcEIsTUFBTUMsU0FBUzNCLDBEQUFTQTtJQUN4QixNQUFNNEIsU0FBUzNCLDBEQUFTQTtJQUN4QixNQUFNNEIsY0FBY3pCLHNFQUFjQTtJQUNsQyxNQUFNMEIsWUFBWUMsU0FBU0osT0FBT0ssRUFBRTtJQUVwQyxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR3BDLCtDQUFRQSxDQUFrQjtRQUN4RHFDLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxTQUFTO0lBQ1g7SUFFQSxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR2xELCtDQUFRQSxDQUEyQixDQUFDO0lBRWhFLHdCQUF3QjtJQUN4QixNQUFNLEVBQUVtRCxNQUFNQyxPQUFPLEVBQUVDLFdBQVdDLGNBQWMsRUFBRSxHQUFHakQsZ0VBQVFBLENBQUM7UUFDNURrRCxVQUFVO1lBQUM7WUFBV3ZCO1NBQVU7UUFDaEN3QixTQUFTLElBQU1yQywwREFBV0EsQ0FBQ3NDLFVBQVUsQ0FBQ3pCO1FBQ3RDMEIsU0FBUyxDQUFDLENBQUMxQjtJQUNiO0lBRUEsMkNBQTJDO0lBQzNDLE1BQU0sQ0FBQzJCLGNBQWNDLGdCQUFnQixHQUFHNUQsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDNkQsWUFBWUMsY0FBYyxHQUFHOUQsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDK0QsWUFBWUMsY0FBYyxHQUFHaEUsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDaUUsVUFBVUMsWUFBWSxHQUFHbEUsK0NBQVFBLENBQUM7SUFFekMsMkNBQTJDO0lBQzNDLE1BQU0sRUFBRW1ELE1BQU14QixPQUFPLEVBQUUwQixXQUFXYyxjQUFjLEVBQUVDLE9BQU9DLFlBQVksRUFBRSxHQUFHaEUsZ0VBQVFBLENBQUM7UUFDakZrRCxVQUFVO1lBQUM7WUFBVztZQUFVSTtZQUFjSTtTQUFXO1FBQ3pEUCxTQUFTLElBQU1wQyx3REFBVUEsQ0FBQ2tELFVBQVUsQ0FBQztnQkFDbkNDLE9BQU87Z0JBQ1BDLE1BQU1UO2dCQUNOLEdBQUlKLGdCQUFnQjtvQkFBRWMsUUFBUWQ7Z0JBQWEsQ0FBQztZQUM5QztRQUNBZSxrQkFBa0I7SUFDcEI7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTSxFQUFFdkIsTUFBTXZCLEtBQUssRUFBRXlCLFdBQVdzQixZQUFZLEVBQUVQLE9BQU9RLFVBQVUsRUFBRSxHQUFHdkUsZ0VBQVFBLENBQUM7UUFDM0VrRCxVQUFVO1lBQUM7WUFBUztZQUFVTTtZQUFZSTtTQUFTO1FBQ25EVCxTQUFTLElBQU1uQyxxREFBUUEsQ0FBQ3dELFFBQVEsQ0FBQztnQkFDL0JOLE9BQU87Z0JBQ1BDLE1BQU1QO2dCQUNOLEdBQUlKLGNBQWM7b0JBQUVZLFFBQVFaO2dCQUFXLENBQUM7WUFDMUM7UUFDQWEsa0JBQWtCO0lBQ3BCO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU1JLGlCQUFpQjFFLG1FQUFXQSxDQUFDO1FBQ2pDMkUsWUFBWSxDQUFDNUIsT0FBY2hDLDBEQUFXQSxDQUFDNkQsYUFBYSxDQUFDaEQsV0FBV21CO1FBQ2hFOEIsV0FBVztZQUNUbEQsWUFBWW1ELGlCQUFpQixDQUFDO2dCQUFFM0IsVUFBVTtvQkFBQztvQkFBV3ZCO2lCQUFVO1lBQUM7WUFDakVELFlBQVltRCxpQkFBaUIsQ0FBQztnQkFBRTNCLFVBQVU7b0JBQUM7aUJBQVc7WUFBQztZQUN2RDlCLDBDQUFLQSxDQUFDMEQsT0FBTyxDQUFDO1lBQ2RyRCxPQUFPc0QsSUFBSSxDQUFDLHVCQUFpQyxPQUFWcEQ7UUFDckM7UUFDQXFELFNBQVMsQ0FBQ2pCO1lBQ1IzQywwQ0FBS0EsQ0FBQzJDLEtBQUssQ0FBQ0EsTUFBTWtCLE9BQU8sSUFBSTtRQUMvQjtJQUNGO0lBRUEsd0NBQXdDO0lBQ3hDckYsZ0RBQVNBLENBQUM7UUFDUixJQUFJbUQsU0FBUztnQkFvQkdBLHFCQUNBQSxxQkFDRkEsbUJBSUNBLGdCQUNBQSxpQkFHQUEsb0JBQ0FBLG9CQUNGQSxrQkFFSUEsc0JBRURBO1lBbkNkLE1BQU1tQyxjQUFjLElBQUlDLEtBQUtwQyxRQUFRWixJQUFJO1lBQ3pDaUQsUUFBUUMsR0FBRyxDQUFDLG1EQUF5QztnQkFDbkRyRCxZQUFZZSxRQUFRZixVQUFVO2dCQUM5QkMsWUFBWWMsUUFBUWQsVUFBVTtnQkFDOUJDLFVBQVVhLFFBQVFiLFFBQVE7Z0JBQzFCb0QsT0FBT3ZDLFFBQVF1QyxLQUFLO2dCQUNwQmpELFdBQVdVLFFBQVFWLFNBQVM7Z0JBQzVCQyxXQUFXUyxRQUFRVCxTQUFTO1lBQzlCO1lBRUEsMkRBQTJEO1lBQzNEOEMsUUFBUUMsR0FBRyxDQUFDLHFDQUEyQjtnQkFDckMsc0JBQXNCdEMsUUFBUWYsVUFBVTtnQkFDeEMsc0JBQXNCZSxRQUFRZCxVQUFVO2dCQUN4QyxvQkFBb0JjLFFBQVFiLFFBQVE7Z0JBQ3BDLGtCQUFrQmE7WUFDcEI7WUFFQWhCLFlBQVk7Z0JBQ1ZDLFlBQVllLEVBQUFBLHNCQUFBQSxRQUFRZixVQUFVLGNBQWxCZSwwQ0FBQUEsb0JBQW9Cd0MsUUFBUSxPQUFNO2dCQUM5Q3RELFlBQVljLEVBQUFBLHNCQUFBQSxRQUFRZCxVQUFVLGNBQWxCYywwQ0FBQUEsb0JBQW9Cd0MsUUFBUSxPQUFNO2dCQUM5Q3JELFVBQVVhLEVBQUFBLG9CQUFBQSxRQUFRYixRQUFRLGNBQWhCYSx3Q0FBQUEsa0JBQWtCd0MsUUFBUSxPQUFNO2dCQUMxQ3BELE1BQU0rQyxZQUFZTSxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDN0NyRCxNQUFNOEMsWUFBWVEsWUFBWSxHQUFHQyxLQUFLLENBQUMsR0FBRztnQkFDMUMsNERBQTREO2dCQUM1RHRELFdBQVdVLEVBQUFBLGlCQUFBQSxRQUFRdUMsS0FBSyxjQUFidkMscUNBQUFBLGVBQWU2QyxJQUFJLEtBQUk3QyxRQUFRVixTQUFTLElBQUk7Z0JBQ3ZEQyxXQUFXUyxFQUFBQSxrQkFBQUEsUUFBUXVDLEtBQUssY0FBYnZDLHNDQUFBQSxnQkFBZThDLElBQUksS0FBSTlDLFFBQVFULFNBQVMsSUFBSTtnQkFDdkRDLE9BQU9RLFFBQVFSLEtBQUssSUFBSTtnQkFDeEJDLFFBQVFPLFFBQVFQLE1BQU0sSUFBSTtnQkFDMUJDLFdBQVdNLEVBQUFBLHFCQUFBQSxRQUFRTixTQUFTLGNBQWpCTSx5Q0FBQUEsbUJBQW1Cd0MsUUFBUSxPQUFNO2dCQUM1QzdDLFdBQVdLLEVBQUFBLHFCQUFBQSxRQUFRTCxTQUFTLGNBQWpCSyx5Q0FBQUEsbUJBQW1Cd0MsUUFBUSxPQUFNO2dCQUM1QzVDLFNBQVNJLEVBQUFBLG1CQUFBQSxRQUFRSixPQUFPLGNBQWZJLHVDQUFBQSxpQkFBaUJ3QyxRQUFRLE9BQU07Z0JBQ3hDTyxTQUFTL0MsUUFBUStDLE9BQU8sSUFBSTtnQkFDNUJDLGFBQWFoRCxFQUFBQSx1QkFBQUEsUUFBUWdELFdBQVcsY0FBbkJoRCwyQ0FBQUEscUJBQXFCd0MsUUFBUSxPQUFNO2dCQUNoRFMsU0FBU2pELFFBQVFpRCxPQUFPLElBQUk7Z0JBQzVCQyxZQUFZbEQsRUFBQUEsc0JBQUFBLFFBQVFrRCxVQUFVLGNBQWxCbEQsMENBQUFBLG9CQUFvQndDLFFBQVEsT0FBTTtZQUNoRDtRQUNGO0lBQ0YsR0FBRztRQUFDeEM7S0FBUTtJQUVaLHNFQUFzRTtJQUV0RSxNQUFNbUQsZUFBZTtRQUNuQixNQUFNQyxZQUFzQyxDQUFDO1FBRTdDLElBQUksQ0FBQ3JFLFNBQVNFLFVBQVUsRUFBRW1FLFVBQVVuRSxVQUFVLEdBQUc7UUFDakQsSUFBSSxDQUFDRixTQUFTRyxVQUFVLEVBQUVrRSxVQUFVbEUsVUFBVSxHQUFHO1FBQ2pELElBQUksQ0FBQ0gsU0FBU0ksUUFBUSxFQUFFaUUsVUFBVWpFLFFBQVEsR0FBRztRQUM3QyxJQUFJLENBQUNKLFNBQVNLLElBQUksRUFBRWdFLFVBQVVoRSxJQUFJLEdBQUc7UUFDckMsSUFBSSxDQUFDTCxTQUFTTSxJQUFJLEVBQUUrRCxVQUFVL0QsSUFBSSxHQUFHO1FBQ3JDLElBQUksQ0FBQ04sU0FBU1UsTUFBTSxFQUFFMkQsVUFBVTNELE1BQU0sR0FBRztRQUV6QyxJQUFJVixTQUFTRSxVQUFVLEtBQUtGLFNBQVNHLFVBQVUsRUFBRTtZQUMvQ2tFLFVBQVVsRSxVQUFVLEdBQUc7UUFDekI7UUFFQVksVUFBVXNEO1FBQ1YsT0FBT0MsT0FBT0MsSUFBSSxDQUFDRixXQUFXRyxNQUFNLEtBQUs7SUFDM0M7SUFFQSxNQUFNQyxlQUFlLENBQUNDO1FBQ3BCQSxFQUFFQyxjQUFjO1FBRWhCLElBQUksQ0FBQ1AsZ0JBQWdCO1lBQ25COUUsMENBQUtBLENBQUMyQyxLQUFLLENBQUM7WUFDWjtRQUNGO1FBRUEsd0JBQXdCO1FBQ3hCLE1BQU0yQyxXQUFXLElBQUl2QixLQUFLLEdBQW9CckQsT0FBakJBLFNBQVNLLElBQUksRUFBQyxLQUFpQixPQUFkTCxTQUFTTSxJQUFJO1FBRTNELE1BQU11RSxhQUFhO1lBQ2pCM0UsWUFBWUosU0FBU0UsU0FBU0UsVUFBVTtZQUN4Q0MsWUFBWUwsU0FBU0UsU0FBU0csVUFBVTtZQUN4Q0MsVUFBVU4sU0FBU0UsU0FBU0ksUUFBUTtZQUNwQ0MsTUFBTXVFLFNBQVNsQixXQUFXO1lBQzFCbkQsV0FBV1AsU0FBU08sU0FBUyxJQUFJO1lBQ2pDQyxXQUFXUixTQUFTUSxTQUFTLElBQUk7WUFDakNDLE9BQU9ULFNBQVNTLEtBQUssSUFBSTtZQUN6QkMsUUFBUVYsU0FBU1UsTUFBTTtZQUN2QkMsV0FBV1gsU0FBU1csU0FBUyxHQUFHYixTQUFTRSxTQUFTVyxTQUFTLElBQUk7WUFDL0RDLFdBQVdaLFNBQVNZLFNBQVMsR0FBR2QsU0FBU0UsU0FBU1ksU0FBUyxJQUFJO1lBQy9EQyxTQUFTYixTQUFTYSxPQUFPLEdBQUdmLFNBQVNFLFNBQVNhLE9BQU8sSUFBSTtZQUN6RG1ELFNBQVNoRSxTQUFTZ0UsT0FBTyxJQUFJO1lBQzdCQyxhQUFhakUsU0FBU2lFLFdBQVcsR0FBR25FLFNBQVNFLFNBQVNpRSxXQUFXLElBQUk7WUFDckVDLFNBQVNsRSxTQUFTa0UsT0FBTyxJQUFJO1lBQzdCQyxZQUFZbkUsU0FBU21FLFVBQVUsR0FBR3JFLFNBQVNFLFNBQVNtRSxVQUFVLElBQUk7UUFDcEU7UUFFQXhCLGVBQWVtQyxNQUFNLENBQUNEO0lBQ3hCO0lBRUEsTUFBTUUsaUJBQWlCLENBQUNDLE9BQThCQztRQUNwRGhGLFlBQVlpRixDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUUsQ0FBQ0YsTUFBTSxFQUFFQztZQUFNO1FBQy9DLHNDQUFzQztRQUN0QyxJQUFJbkUsTUFBTSxDQUFDa0UsTUFBTSxFQUFFO1lBQ2pCakUsVUFBVW1FLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRSxDQUFDRixNQUFNLEVBQUVHO2dCQUFVO1FBQ25EO0lBQ0Y7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTUMscUJBQXFCLENBQUNDO1FBQzFCNUQsZ0JBQWdCNEQ7UUFDaEJ4RCxjQUFjLElBQUksb0NBQW9DO0lBQ3hEO0lBRUEsTUFBTXlELG1CQUFtQixDQUFDRDtRQUN4QjFELGNBQWMwRDtRQUNkdEQsWUFBWSxJQUFJLG9DQUFvQztJQUN0RDtJQUVBLE1BQU13RCx1QkFBdUI7WUFDdkIvRjtRQUFKLElBQUlBLENBQUFBLG9CQUFBQSwrQkFBQUEsZ0JBQUFBLFFBQVNnRyxJQUFJLGNBQWJoRyxvQ0FBQUEsY0FBZWlHLFVBQVUsS0FBSTdELGFBQWFwQyxRQUFRZ0csSUFBSSxDQUFDQyxVQUFVLEVBQUU7WUFDckU1RCxjQUFjcUQsQ0FBQUEsT0FBUUEsT0FBTztRQUMvQjtJQUNGO0lBRUEsTUFBTVEscUJBQXFCO1lBQ3JCakc7UUFBSixJQUFJQSxDQUFBQSxrQkFBQUEsNkJBQUFBLGNBQUFBLE1BQU8rRixJQUFJLGNBQVgvRixrQ0FBQUEsWUFBYWdHLFVBQVUsS0FBSTNELFdBQVdyQyxNQUFNK0YsSUFBSSxDQUFDQyxVQUFVLEVBQUU7WUFDL0QxRCxZQUFZbUQsQ0FBQUEsT0FBUUEsT0FBTztRQUM3QjtJQUNGO0lBRUEsTUFBTVMsZ0JBQWdCO1FBQ3BCO1lBQUVWLE9BQU87WUFBTVcsT0FBTztRQUFjO1FBQ3BDO1lBQUVYLE9BQU87WUFBTVcsT0FBTztRQUFhO1FBQ25DO1lBQUVYLE9BQU87WUFBTVcsT0FBTztRQUFZO1FBQ2xDO1lBQUVYLE9BQU87WUFBTVcsT0FBTztRQUFjO1FBQ3BDO1lBQUVYLE9BQU87WUFBTVcsT0FBTztRQUFZO1FBQ2xDO1lBQUVYLE9BQU87WUFBT1csT0FBTztRQUFZO1FBQ25DO1lBQUVYLE9BQU87WUFBUVcsT0FBTztRQUFZO0tBQ3JDO0lBRUQsMERBQTBEO0lBQzFELE1BQU1DLGdCQUFnQnJHLENBQUFBLG9CQUFBQSwrQkFBQUEsZ0JBQUFBLFFBQVN3QixJQUFJLGNBQWJ4QixvQ0FBQUEsY0FBZXNHLEdBQUcsQ0FBQ0MsQ0FBQUEsU0FBVztZQUNsRGQsT0FBT2MsT0FBT0MsVUFBVSxDQUFDdkMsUUFBUTtZQUNqQ21DLE9BQU8sR0FBaUJHLE9BQWRBLE9BQU9qQyxJQUFJLEVBQThDLE9BQTNDaUMsT0FBT0UsTUFBTSxHQUFHLEtBQW1CLE9BQWRGLE9BQU9FLE1BQU0sRUFBQyxPQUFLO1lBQ2hFQyxNQUFNSCxPQUFPRyxJQUFJO1lBQ2pCRCxRQUFRRixPQUFPRSxNQUFNO1FBQ3ZCLFFBQU8sRUFBRTtJQUVULE1BQU1FLGNBQWMxRyxDQUFBQSxrQkFBQUEsNkJBQUFBLGNBQUFBLE1BQU91QixJQUFJLGNBQVh2QixrQ0FBQUEsWUFBYXFHLEdBQUcsQ0FBQ00sQ0FBQUEsT0FBUztZQUM1Q25CLE9BQU9tQixLQUFLSixVQUFVLENBQUN2QyxRQUFRO1lBQy9CbUMsT0FBT1EsS0FBS3RDLElBQUk7WUFDaEJvQyxNQUFNRSxLQUFLRixJQUFJO1FBQ2pCLFFBQU8sRUFBRTtJQUVULHdGQUF3RjtJQUN4RnBJLGdEQUFTQSxDQUFDO1FBQ1J3RixRQUFRQyxHQUFHLENBQUMsd0NBQThCO1lBQ3hDZjtZQUNBUjtZQUNBcUUsa0JBQWtCRixZQUFZM0IsTUFBTTtZQUNwQzhCLG9CQUFvQlQsY0FBY3JCLE1BQU07WUFDeEMrQixlQUFlLENBQUMsQ0FBRXZHLENBQUFBLFNBQVNFLFVBQVUsSUFBSUYsU0FBU0csVUFBVSxJQUFJSCxTQUFTSSxRQUFRO1FBQ25GO0lBQ0YsR0FBRztRQUFDb0M7UUFBY1I7UUFBZ0JtRSxZQUFZM0IsTUFBTTtRQUFFcUIsY0FBY3JCLE1BQU07UUFBRXhFO0tBQVM7SUFFckYsZ0ZBQWdGO0lBQ2hGLE1BQU13RyxpQkFBaUI7WUFLS3ZGLHFCQUNBQSxxQkFDRkE7UUFOeEIsSUFBSSxDQUFDQSxTQUFTLE9BQU87WUFBRThFLFFBQVE7WUFBTVUsVUFBVTtZQUFNQyxVQUFVO1FBQUs7UUFFcEUscUNBQXFDO1FBQ3JDLE1BQU1DLGdCQUNKM0csU0FBU0UsVUFBVSxPQUFLZSxzQkFBQUEsUUFBUWYsVUFBVSxjQUFsQmUsMENBQUFBLG9CQUFvQndDLFFBQVEsT0FDcER6RCxTQUFTRyxVQUFVLE9BQUtjLHNCQUFBQSxRQUFRZCxVQUFVLGNBQWxCYywwQ0FBQUEsb0JBQW9Cd0MsUUFBUSxPQUNwRHpELFNBQVNJLFFBQVEsT0FBS2Esb0JBQUFBLFFBQVFiLFFBQVEsY0FBaEJhLHdDQUFBQSxrQkFBa0J3QyxRQUFRO1FBR2xELElBQUlrRCxlQUFlO1lBQ2pCLE9BQU87Z0JBQ0xaLFFBQVE7b0JBQ05kLE9BQU9qRixTQUFTSSxRQUFRO29CQUN4QndGLE9BQU8zRSxRQUFRMkYsVUFBVTtvQkFDekJWLE1BQU0sR0FBRyxrREFBa0Q7Z0JBQzdEO2dCQUNBTyxVQUFVO29CQUNSeEIsT0FBT2pGLFNBQVNFLFVBQVU7b0JBQzFCMEYsT0FBTzNFLFFBQVE0RixZQUFZO29CQUMzQlgsTUFBTWpGLFFBQVE2RixZQUFZO2dCQUM1QjtnQkFDQUosVUFBVTtvQkFDUnpCLE9BQU9qRixTQUFTRyxVQUFVO29CQUMxQnlGLE9BQU8zRSxRQUFROEYsWUFBWTtvQkFDM0JiLE1BQU1qRixRQUFRK0YsWUFBWTtnQkFDNUI7WUFDRjtRQUNGO1FBRUEsNENBQTRDO1FBQzVDLE9BQU87WUFDTGpCLFFBQVFGLGNBQWNvQixJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVqQyxLQUFLLEtBQUtqRixTQUFTSSxRQUFRLEtBQUs7WUFDbEVxRyxVQUFVTixZQUFZYyxJQUFJLENBQUNFLENBQUFBLElBQUtBLEVBQUVsQyxLQUFLLEtBQUtqRixTQUFTRSxVQUFVLEtBQUs7WUFDcEV3RyxVQUFVUCxZQUFZYyxJQUFJLENBQUNFLENBQUFBLElBQUtBLEVBQUVsQyxLQUFLLEtBQUtqRixTQUFTRyxVQUFVLEtBQUs7UUFDdEU7SUFDRjtJQUVBLE1BQU0sRUFBRTRGLFFBQVFxQixjQUFjLEVBQUVYLFVBQVVZLGdCQUFnQixFQUFFWCxVQUFVWSxnQkFBZ0IsRUFBRSxHQUFHZDtJQUUzRix3QkFBd0I7SUFDeEJsRCxRQUFRQyxHQUFHLENBQUM7SUFDWkQsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQjZEO0lBQy9COUQsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQjhEO0lBQ2pDL0QsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQitEO0lBRWpDLGtCQUFrQjtJQUNsQmhFLFFBQVFDLEdBQUcsQ0FBQywyQkFBaUJ2RDtJQUM3QnNELFFBQVFDLEdBQUcsQ0FBQywrQkFBcUI7UUFBRTlELE9BQU8wRyxZQUFZM0IsTUFBTTtRQUFFaEYsU0FBU3FHLGNBQWNyQixNQUFNO0lBQUM7SUFFNUYsZ0VBQWdFO0lBQ2hFLE1BQU0sQ0FBQytDLFlBQVlDLGNBQWMsR0FBRzNKLCtDQUFRQSxDQUFDO0lBRTdDQyxnREFBU0EsQ0FBQztRQUNSLG1EQUFtRDtRQUNuRCxJQUFJcUksWUFBWTNCLE1BQU0sR0FBRyxLQUFLcUIsY0FBY3JCLE1BQU0sR0FBRyxLQUFLeEUsU0FBU0UsVUFBVSxFQUFFO1lBQzdFc0gsY0FBY3RDLENBQUFBLE9BQVFBLE9BQU87UUFDL0I7SUFDRixHQUFHO1FBQUNpQixZQUFZM0IsTUFBTTtRQUFFcUIsY0FBY3JCLE1BQU07UUFBRXhFLFNBQVNFLFVBQVU7UUFBRUYsU0FBU0csVUFBVTtRQUFFSCxTQUFTSSxRQUFRO0tBQUM7SUFFMUcsK0JBQStCO0lBQy9Ca0QsUUFBUUMsR0FBRyxDQUFDO0lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxhQUFhO1FBQUVrRSxNQUFNekgsU0FBU0UsVUFBVTtRQUFFd0gsTUFBTTFILFNBQVNHLFVBQVU7UUFBRTRGLFFBQVEvRixTQUFTSSxRQUFRO0lBQUM7SUFDM0drRCxRQUFRQyxHQUFHLENBQUMsa0JBQWtCO1FBQzVCa0UsTUFBTSxPQUFPekgsU0FBU0UsVUFBVTtRQUNoQ3dILE1BQU0sT0FBTzFILFNBQVNHLFVBQVU7UUFDaEM0RixRQUFRLE9BQU8vRixTQUFTSSxRQUFRO0lBQ2xDO0lBRUEsSUFBSStGLFlBQVkzQixNQUFNLEdBQUcsR0FBRztRQUMxQmxCLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0I0QyxZQUFZdEMsS0FBSyxDQUFDLEdBQUc7UUFDekRQLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0I0QyxZQUFZdEMsS0FBSyxDQUFDLEdBQUcsR0FBR2lDLEdBQUcsQ0FBQ3FCLENBQUFBLElBQU07Z0JBQUVsQyxPQUFPa0MsRUFBRWxDLEtBQUs7Z0JBQUUwQyxNQUFNLE9BQU9SLEVBQUVsQyxLQUFLO1lBQUM7UUFFM0csdUNBQXVDO1FBQ3ZDLE1BQU0yQyxvQkFBb0J6QixZQUFZYyxJQUFJLENBQUNFLENBQUFBLElBQUtBLEVBQUVsQyxLQUFLLEtBQUs7UUFDNUQsTUFBTTRDLG9CQUFvQjFCLFlBQVljLElBQUksQ0FBQ0UsQ0FBQUEsSUFBS0EsRUFBRWxDLEtBQUssS0FBSztRQUM1RDNCLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBNEI7WUFDdEMsbUJBQW1CcUU7WUFDbkIsb0JBQW9CQztZQUNwQixjQUFjMUIsWUFBWTNCLE1BQU07UUFDbEM7SUFDRjtJQUVBLElBQUlxQixjQUFjckIsTUFBTSxHQUFHLEdBQUc7UUFDNUJsQixRQUFRQyxHQUFHLENBQUMsMEJBQTBCc0MsY0FBY2hDLEtBQUssQ0FBQyxHQUFHO1FBQzdEUCxRQUFRQyxHQUFHLENBQUMsd0JBQXdCc0MsY0FBY2hDLEtBQUssQ0FBQyxHQUFHLEdBQUdpQyxHQUFHLENBQUNvQixDQUFBQSxJQUFNO2dCQUFFakMsT0FBT2lDLEVBQUVqQyxLQUFLO2dCQUFFMEMsTUFBTSxPQUFPVCxFQUFFakMsS0FBSztZQUFDO1FBRS9HLHlDQUF5QztRQUN6QyxNQUFNNkMsa0JBQWtCakMsY0FBY29CLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWpDLEtBQUssS0FBSztRQUM1RDNCLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBNkI7WUFDdkMsZ0JBQWdCdUU7WUFDaEIsZ0JBQWdCakMsY0FBY3JCLE1BQU07UUFDdEM7SUFDRjtJQUVBbEIsUUFBUUMsR0FBRyxDQUFDLHlDQUErQjtRQUN6Q3dFLFlBQVk1QixZQUFZNkIsSUFBSSxDQUFDYixDQUFBQSxJQUFLQSxFQUFFbEMsS0FBSyxLQUFLakYsU0FBU0UsVUFBVTtRQUNqRStILFlBQVk5QixZQUFZNkIsSUFBSSxDQUFDYixDQUFBQSxJQUFLQSxFQUFFbEMsS0FBSyxLQUFLakYsU0FBU0csVUFBVTtRQUNqRStILGNBQWNyQyxjQUFjbUMsSUFBSSxDQUFDZCxDQUFBQSxJQUFLQSxFQUFFakMsS0FBSyxLQUFLakYsU0FBU0ksUUFBUTtJQUNyRTtJQUVBLHdDQUF3QztJQUN4QyxNQUFNK0gsdUJBQXVCO1lBQUMsRUFDNUJ2QyxLQUFLLEVBQ0x3QyxjQUFjLEVBQ2RDLGNBQWMsY0FBYyxFQUs3QjtRQUNDLE1BQU1DLFVBQVVDLHNCQUEwQyxJQUFJO1FBRTlELDBCQUEwQjtRQUMxQmpGLFFBQVFDLEdBQUcsQ0FBQyxnQkFBWSxPQUFOcUMsT0FBTSxjQUFZO1lBQUV3QztZQUFnQkM7WUFBYUssV0FBVyxDQUFDLENBQUNOO1FBQWU7UUFFL0YscUJBQ0UsOERBQUNPO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFBMENoRDs7Ozs7O2dCQUN4RHdDLCtCQUNDLDhEQUFDTztvQkFBSUMsV0FBVTs7d0JBQ1pSLGVBQWVsQyxJQUFJLGtCQUNsQiw4REFBQzJDOzRCQUNDQyxLQUFLLEdBQWNWLE9BQVhFLFNBQVEsS0FBdUIsT0FBcEJGLGVBQWVsQyxJQUFJOzRCQUN0QzZDLEtBQUtYLGVBQWV4QyxLQUFLOzRCQUN6QmdELFdBQVU7NEJBQ1YxRixTQUFTLENBQUN3QjtnQ0FDUkEsRUFBRXNFLGFBQWEsQ0FBQ0MsS0FBSyxDQUFDQyxPQUFPLEdBQUc7NEJBQ2xDOzs7Ozs7c0NBR0osOERBQUNDOzRCQUFLUCxXQUFVO3NDQUNiUixlQUFleEMsS0FBSzs7Ozs7Ozs7Ozs7eUNBSXpCLDhEQUFDK0M7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ087Z0NBQUtQLFdBQVU7MENBQXdCOzs7Ozs7Ozs7OztzQ0FFMUMsOERBQUNPOzRCQUFLUCxXQUFVO3NDQUF3QlA7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUtsRDtJQUVBLG9FQUFvRTtJQUNwRSxNQUFNZSx1QkFBdUI7UUFDM0IsTUFBTWQsVUFBVUMsc0JBQTBDLElBQUk7UUFFOUQsd0JBQXdCO1FBQ3hCakYsUUFBUUMsR0FBRyxDQUFDLHdDQUE4QjtZQUFFNkQ7WUFBZ0JpQyxXQUFXLENBQUMsQ0FBQ2pDO1FBQWU7UUFFeEYscUJBQ0UsOERBQUN1QjtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1p4QiwrQkFDQzs7NEJBQ0dBLGVBQWVsQixJQUFJLGtCQUNsQiw4REFBQzJDO2dDQUNDQyxLQUFLLEdBQWMxQixPQUFYa0IsU0FBUSxLQUF1QixPQUFwQmxCLGVBQWVsQixJQUFJO2dDQUN0QzZDLEtBQUszQixlQUFleEIsS0FBSztnQ0FDekJnRCxXQUFVO2dDQUNWMUYsU0FBUyxDQUFDd0I7b0NBQ1JBLEVBQUVzRSxhQUFhLENBQUNDLEtBQUssQ0FBQ0MsT0FBTyxHQUFHO2dDQUNsQzs7Ozs7OzBDQUdKLDhEQUFDQztnQ0FBS1AsV0FBVTswQ0FDYnhCLGVBQWV4QixLQUFLOzs7Ozs7O3FEQUl6Qjs7MENBQ0UsOERBQUMrQztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ087b0NBQUtQLFdBQVU7OENBQXdCOzs7Ozs7Ozs7OzswQ0FFMUMsOERBQUNPO2dDQUFLUCxXQUFVOzBDQUF1Qjs7Ozs7Ozs7Ozs7Ozs4QkFNN0MsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQXlDOzs7Ozs7c0NBQ3hELDhEQUFDOUosdUZBQXFCQTs0QkFDcEJ1SixhQUFhckcsaUJBQWlCLHVCQUF1Qjs0QkFDckRzSCxtQkFBa0I7NEJBQ2xCQyxRQUFROzRCQUNSdEUsT0FBT2pGLFNBQVNJLFFBQVE7NEJBQ3hCb0osZUFBZSxDQUFDdkUsUUFBVUYsZUFBZSxZQUFZRTs0QkFDckR3RSxTQUFTNUQ7NEJBQ1Q1RCxPQUFPbkIsT0FBT1YsUUFBUTs0QkFDdEJzSixVQUFVMUg7NEJBQ1YySCxVQUFVdkU7NEJBQ1Z3RSxZQUFZckU7NEJBQ1pzRSxTQUFTckssQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTZ0csSUFBSSxJQUFHNUQsYUFBYXBDLFFBQVFnRyxJQUFJLENBQUNDLFVBQVUsR0FBRzs0QkFDaEV2RSxXQUFXYzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3JCO0lBRUEscURBQXFEO0lBQ3JELE1BQU04SCxnQkFBZ0IzSSxrQkFBa0JhLGtCQUFrQlE7SUFFMUQsSUFBSXNILGVBQWU7UUFDakIscUJBQ0UsOERBQUNuQjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDN0osNkRBQVFBOzRCQUFDNkosV0FBVTs7Ozs7O3NDQUNwQiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDN0osNkRBQVFBO29DQUFDNkosV0FBVTs7Ozs7OzhDQUNwQiw4REFBQzdKLDZEQUFRQTtvQ0FBQzZKLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFHeEIsOERBQUN4SyxxREFBSUE7O3NDQUNILDhEQUFDRywyREFBVUE7OzhDQUNULDhEQUFDUSw2REFBUUE7b0NBQUM2SixXQUFVOzs7Ozs7OENBQ3BCLDhEQUFDN0osNkRBQVFBO29DQUFDNkosV0FBVTs7Ozs7Ozs7Ozs7O3NDQUV0Qiw4REFBQ3ZLLDREQUFXQTs0QkFBQ3VLLFdBQVU7OzhDQUNyQiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDN0osNkRBQVFBOzRDQUFDNkosV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDN0osNkRBQVFBO29EQUFDNkosV0FBVTs7Ozs7OzhEQUNwQiw4REFBQzdKLDZEQUFRQTtvREFBQzZKLFdBQVU7Ozs7Ozs7Ozs7OztzREFFdEIsOERBQUM3Siw2REFBUUE7NENBQUM2SixXQUFVOzs7Ozs7Ozs7Ozs7OENBRXRCLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM3Siw2REFBUUE7NENBQUM2SixXQUFVOzs7Ozs7c0RBQ3BCLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUM3Siw2REFBUUE7b0RBQUM2SixXQUFVOzs7Ozs7OERBQ3BCLDhEQUFDN0osNkRBQVFBO29EQUFDNkosV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUd4Qiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDN0osNkRBQVFBOzRDQUFDNkosV0FBVTs7Ozs7O3NEQUNwQiw4REFBQzdKLDZEQUFRQTs0Q0FBQzZKLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU1oQztJQUVBLHVEQUF1RDtJQUN2RCxJQUFJLENBQUMzSCxXQUFXaUIsZ0JBQWdCTyxZQUFZO1FBQzFDLHFCQUNFLDhEQUFDa0c7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDbksseURBQU1BO3dCQUFDc0wsU0FBUTt3QkFBVUMsU0FBUyxJQUFNckssT0FBT3NLLElBQUk7OzBDQUNsRCw4REFBQzlLLG9HQUFTQTtnQ0FBQ3lKLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7Ozs7Ozs4QkFLMUMsOERBQUN4SyxxREFBSUE7OEJBQ0gsNEVBQUNDLDREQUFXQTt3QkFBQ3VLLFdBQVU7a0NBQ3JCLDRFQUFDRDs0QkFBSUMsV0FBVTs7Z0NBQ1osQ0FBQzNILHlCQUFXLDhEQUFDaUo7b0NBQUV0QixXQUFVOzhDQUFvQjs7Ozs7O2dDQUM3QzFHLDhCQUFnQiw4REFBQ2dJO29DQUFFdEIsV0FBVTs7d0NBQW9CO3dDQUF5QjFHLGFBQWFpQixPQUFPOzs7Ozs7O2dDQUM5RlYsNEJBQWMsOERBQUN5SDtvQ0FBRXRCLFdBQVU7O3dDQUFvQjt3Q0FBdUJuRyxXQUFXVSxPQUFPOzs7Ozs7OzhDQUN6Riw4REFBQzFFLHlEQUFNQTtvQ0FBQ3VMLFNBQVMsSUFBTXJLLE9BQU9zRCxJQUFJLENBQUM7OENBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBUXZFO0lBRUEscUJBQ0UsOERBQUMwRjtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDbksseURBQU1BO3dCQUFDc0wsU0FBUTt3QkFBVUMsU0FBUyxJQUFNckssT0FBT3NLLElBQUk7OzBDQUNsRCw4REFBQzlLLG9HQUFTQTtnQ0FBQ3lKLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7a0NBR3hDLDhEQUFDRDs7MENBQ0MsOERBQUN3QjtnQ0FBR3ZCLFdBQVU7O29DQUFtQztvQ0FDaEMzSCxRQUFRNEYsWUFBWTtvQ0FBQztvQ0FBSzVGLFFBQVE4RixZQUFZOzs7Ozs7OzBDQUUvRCw4REFBQ21EO2dDQUFFdEIsV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLdEMsOERBQUN4SyxxREFBSUE7O2tDQUNILDhEQUFDRywyREFBVUE7OzBDQUNULDhEQUFDQywwREFBU0E7Z0NBQUNvSyxXQUFVOztrREFDbkIsOERBQUN2SixvR0FBUUE7d0NBQUN1SixXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7OzBDQUd2Qyw4REFBQ3RLLGdFQUFlQTswQ0FBQzs7Ozs7Ozs7Ozs7O2tDQUluQiw4REFBQ0QsNERBQVdBO2tDQUNWLDRFQUFDK0w7NEJBQUtDLFVBQVU1Rjs0QkFBY21FLFdBQVU7OzhDQUN0Qyw4REFBQ2hLLGtFQUFXQTtvQ0FBQzBMLE9BQU07b0NBQXNCQyxhQUFZOztzREFFbkQsOERBQUM1Qjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQ0E7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDNEI7OEVBQU87Ozs7OztnRUFBZTtnRUFBY25ELG1CQUFtQixVQUFpQyxPQUF2QkEsaUJBQWlCekIsS0FBSyxJQUFLOzs7Ozs7O3NFQUUvRiw4REFBQ3VDOzREQUVDdkMsT0FBTTs0REFDTndDLGdCQUFnQmY7NERBQ2hCZ0IsYUFBWTsyREFIUCxRQUFtQixPQUFYZDs7Ozs7c0VBS2YsOERBQUN6SSx1RkFBcUJBOzREQUNwQjhHLE9BQU07NERBQ055QyxhQUFhN0YsZUFBZSxxQkFBcUI7NERBQ2pEOEcsbUJBQWtCOzREQUNsQkMsUUFBUTs0REFDUnRFLE9BQU9qRixTQUFTRSxVQUFVOzREQUMxQnNKLGVBQWUsQ0FBQ3ZFLFFBQVVGLGVBQWUsY0FBY0U7NERBQ3ZEd0UsU0FBU3REOzREQUNUbEUsT0FBT25CLE9BQU9aLFVBQVU7NERBQ3hCd0osVUFBVWxIOzREQUNWbUgsVUFBVXJFOzREQUNWc0UsWUFBWWxFOzREQUNabUUsU0FBU3BLLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBTytGLElBQUksSUFBRzFELFdBQVdyQyxNQUFNK0YsSUFBSSxDQUFDQyxVQUFVLEdBQUc7NERBQzFEdkUsV0FBV3NCOzs7Ozs7Ozs7Ozs7OERBSWYsOERBQUNtRzs7c0VBQ0MsOERBQUNBOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQzRCOzhFQUFPOzs7Ozs7Z0VBQWU7Z0VBQWNsRCxtQkFBbUIsVUFBaUMsT0FBdkJBLGlCQUFpQjFCLEtBQUssSUFBSzs7Ozs7OztzRUFFL0YsOERBQUN1Qzs0REFFQ3ZDLE9BQU07NERBQ053QyxnQkFBZ0JkOzREQUNoQmUsYUFBWTsyREFIUCxRQUFtQixPQUFYZDs7Ozs7c0VBS2YsOERBQUN6SSx1RkFBcUJBOzREQUNwQjhHLE9BQU07NERBQ055QyxhQUFhN0YsZUFBZSxxQkFBcUI7NERBQ2pEOEcsbUJBQWtCOzREQUNsQkMsUUFBUTs0REFDUnRFLE9BQU9qRixTQUFTRyxVQUFVOzREQUMxQnFKLGVBQWUsQ0FBQ3ZFLFFBQVVGLGVBQWUsY0FBY0U7NERBQ3ZEd0UsU0FBU3RELFlBQVlzRSxNQUFNLENBQUNyRSxDQUFBQSxPQUFRQSxLQUFLbkIsS0FBSyxLQUFLakYsU0FBU0UsVUFBVTs0REFDdEUrQixPQUFPbkIsT0FBT1gsVUFBVTs0REFDeEJ1SixVQUFVbEg7NERBQ1ZtSCxVQUFVckU7NERBQ1ZzRSxZQUFZbEU7NERBQ1ptRSxTQUFTcEssQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPK0YsSUFBSSxJQUFHMUQsV0FBV3JDLE1BQU0rRixJQUFJLENBQUNDLFVBQVUsR0FBRzs0REFDMUR2RSxXQUFXc0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFNakIsOERBQUNtRzs7OERBQ0MsOERBQUNBO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQzRCO3NFQUFPOzs7Ozs7d0RBQWU7d0RBQVdwRCxpQkFBaUIsVUFBK0IsT0FBckJBLGVBQWV4QixLQUFLLElBQUs7Ozs7Ozs7OERBRXhGLDhEQUFDd0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUlMLDhEQUFDeEssa0VBQVdBO29DQUFDMEwsT0FBTTtvQ0FBV0MsYUFBWTs7c0RBQ3hDLDhEQUFDNUI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDbEssaUVBQVVBO29EQUNUa0gsT0FBTTtvREFDTitCLE1BQUs7b0RBQ0w0QixRQUFRO29EQUNSdEUsT0FBT2pGLFNBQVNLLElBQUk7b0RBQ3BCcUssVUFBVSxDQUFDaEcsSUFBTUssZUFBZSxRQUFRTCxFQUFFaUcsTUFBTSxDQUFDMUYsS0FBSztvREFDdERoRCxPQUFPbkIsT0FBT1QsSUFBSTtvREFDbEJrSyxhQUFZOzs7Ozs7OERBR2QsOERBQUM3TCxpRUFBVUE7b0RBQ1RrSCxPQUFNO29EQUNOK0IsTUFBSztvREFDTDRCLFFBQVE7b0RBQ1J0RSxPQUFPakYsU0FBU00sSUFBSTtvREFDcEJvSyxVQUFVLENBQUNoRyxJQUFNSyxlQUFlLFFBQVFMLEVBQUVpRyxNQUFNLENBQUMxRixLQUFLO29EQUN0RGhELE9BQU9uQixPQUFPUixJQUFJO29EQUNsQmlLLGFBQWEsZUFBZ0UsT0FBakRLLEtBQUtDLGNBQWMsR0FBR0MsZUFBZSxHQUFHQyxRQUFRLEVBQUM7Ozs7Ozs7Ozs7OztzREFJakYsOERBQUNwQzs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ3NCO2dEQUFFdEIsV0FBVTs7a0VBQ1gsOERBQUNPO3dEQUFLUCxXQUFVO2tFQUFxQjs7Ozs7O2tFQUNyQyw4REFBQzRCO2tFQUFPOzs7Ozs7b0RBQXVCO29EQUE4Q0ksS0FBS0MsY0FBYyxHQUFHQyxlQUFlLEdBQUdDLFFBQVE7b0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNcEksOERBQUNuTSxrRUFBV0E7b0NBQUMwTCxPQUFNO29DQUFlQyxhQUFZOztzREFDNUMsOERBQUM1Qjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNqSyxrRUFBV0E7b0RBQ1ZpSCxPQUFNO29EQUNOeUMsYUFBWTtvREFDWmtCLFFBQVE7b0RBQ1J0RSxPQUFPakYsU0FBU1UsTUFBTTtvREFDdEI4SSxlQUFlLENBQUN2RSxRQUFVRixlQUFlLFVBQVVFO29EQUNuRHdFLFNBQVM5RDtvREFDVDFELE9BQU9uQixPQUFPSixNQUFNOzs7Ozs7OERBR3RCLDhEQUFDaEMsaUVBQVVBO29EQUNUa0gsT0FBTTtvREFDTitCLE1BQUs7b0RBQ0xxRCxLQUFJO29EQUNKL0YsT0FBT2pGLFNBQVNXLFNBQVM7b0RBQ3pCK0osVUFBVSxDQUFDaEcsSUFBTUssZUFBZSxhQUFhTCxFQUFFaUcsTUFBTSxDQUFDMUYsS0FBSzs7Ozs7OzhEQUc3RCw4REFBQ3ZHLGlFQUFVQTtvREFDVGtILE9BQU07b0RBQ04rQixNQUFLO29EQUNMcUQsS0FBSTtvREFDSi9GLE9BQU9qRixTQUFTWSxTQUFTO29EQUN6QjhKLFVBQVUsQ0FBQ2hHLElBQU1LLGVBQWUsYUFBYUwsRUFBRWlHLE1BQU0sQ0FBQzFGLEtBQUs7Ozs7Ozs7Ozs7OztzREFJL0QsOERBQUN2RyxpRUFBVUE7NENBQ1RrSCxPQUFNOzRDQUNOK0IsTUFBSzs0Q0FDTHFELEtBQUk7NENBQ0pDLEtBQUk7NENBQ0poRyxPQUFPakYsU0FBU2EsT0FBTzs0Q0FDdkI2SixVQUFVLENBQUNoRyxJQUFNSyxlQUFlLFdBQVdMLEVBQUVpRyxNQUFNLENBQUMxRixLQUFLOzRDQUN6RHNGLGFBQVk7Ozs7Ozs7Ozs7Ozs4Q0FJaEIsOERBQUMzTCxrRUFBV0E7b0NBQUMwTCxPQUFNO29DQUE0QkMsYUFBWTs7c0RBQ3pELDhEQUFDNUI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDbEssaUVBQVVBO29EQUNUa0gsT0FBTTtvREFDTnlDLGFBQVk7b0RBQ1pwRCxPQUFPakYsU0FBU08sU0FBUztvREFDekJtSyxVQUFVLENBQUNoRyxJQUFNSyxlQUFlLGFBQWFMLEVBQUVpRyxNQUFNLENBQUMxRixLQUFLOzs7Ozs7OERBRzdELDhEQUFDdkcsaUVBQVVBO29EQUNUa0gsT0FBTTtvREFDTnlDLGFBQVk7b0RBQ1pwRCxPQUFPakYsU0FBU1EsU0FBUztvREFDekJrSyxVQUFVLENBQUNoRyxJQUFNSyxlQUFlLGFBQWFMLEVBQUVpRyxNQUFNLENBQUMxRixLQUFLOzs7Ozs7Ozs7Ozs7c0RBSS9ELDhEQUFDMEQ7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDbEssaUVBQVVBO29EQUNUa0gsT0FBTTtvREFDTnlDLGFBQVk7b0RBQ1pwRCxPQUFPakYsU0FBU1MsS0FBSztvREFDckJpSyxVQUFVLENBQUNoRyxJQUFNSyxlQUFlLFNBQVNMLEVBQUVpRyxNQUFNLENBQUMxRixLQUFLOzs7Ozs7OERBR3pELDhEQUFDdkcsaUVBQVVBO29EQUNUa0gsT0FBTTtvREFDTnlDLGFBQVk7b0RBQ1pwRCxPQUFPakYsU0FBU2dFLE9BQU8sSUFBSTtvREFDM0IwRyxVQUFVLENBQUNoRyxJQUFNSyxlQUFlLFdBQVdMLEVBQUVpRyxNQUFNLENBQUMxRixLQUFLOzs7Ozs7Ozs7Ozs7c0RBSTdELDhEQUFDMEQ7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDbEssaUVBQVVBO29EQUNUa0gsT0FBTTtvREFDTitCLE1BQUs7b0RBQ0xVLGFBQVk7b0RBQ1pwRCxPQUFPakYsU0FBU2lFLFdBQVcsSUFBSTtvREFDL0J5RyxVQUFVLENBQUNoRyxJQUFNSyxlQUFlLGVBQWVMLEVBQUVpRyxNQUFNLENBQUMxRixLQUFLOzs7Ozs7OERBRy9ELDhEQUFDdkcsaUVBQVVBO29EQUNUa0gsT0FBTTtvREFDTnlDLGFBQVk7b0RBQ1pwRCxPQUFPakYsU0FBU2tFLE9BQU8sSUFBSTtvREFDM0J3RyxVQUFVLENBQUNoRyxJQUFNSyxlQUFlLFdBQVdMLEVBQUVpRyxNQUFNLENBQUMxRixLQUFLOzs7Ozs7OERBRzNELDhEQUFDdkcsaUVBQVVBO29EQUNUa0gsT0FBTTtvREFDTitCLE1BQUs7b0RBQ0xVLGFBQVk7b0RBQ1pwRCxPQUFPakYsU0FBU21FLFVBQVUsSUFBSTtvREFDOUJ1RyxVQUFVLENBQUNoRyxJQUFNSyxlQUFlLGNBQWNMLEVBQUVpRyxNQUFNLENBQUMxRixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS2xFLDhEQUFDcEcsa0VBQVdBOztzREFDViw4REFBQ0oseURBQU1BOzRDQUNMa0osTUFBSzs0Q0FDTG9DLFNBQVE7NENBQ1JDLFNBQVMsSUFBTXJLLE9BQU9zSyxJQUFJOzRDQUMxQlAsVUFBVS9HLGVBQWV1SSxTQUFTO3NEQUNuQzs7Ozs7O3NEQUdELDhEQUFDek0seURBQU1BOzRDQUNMa0osTUFBSzs0Q0FDTCtCLFVBQVUvRyxlQUFldUksU0FBUzs7OERBRWxDLDhEQUFDOUwsb0dBQUlBO29EQUFDd0osV0FBVTs7Ozs7O2dEQUNmakcsZUFBZXVJLFNBQVMsR0FBRyxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVE1RDtHQXh2QndCM0w7O1FBQ1B4QixzREFBU0E7UUFDVEMsc0RBQVNBO1FBQ0pHLGtFQUFjQTtRQXFCbUJELDREQUFRQTtRQWFhQSw0REFBUUE7UUFXZEEsNERBQVFBO1FBV3JERCwrREFBV0E7OztLQTNEWnNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ZpeHR1cmVzL1tpZF0vZWRpdC9wYWdlLnRzeD9iZDFiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVBhcmFtcywgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZU11dGF0aW9uLCB1c2VRdWVyeSwgdXNlUXVlcnlDbGllbnQgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnknO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQge1xuICBJbnB1dEZpZWxkLFxuICBTZWxlY3RGaWVsZCxcbiAgRm9ybVNlY3Rpb24sXG4gIEZvcm1BY3Rpb25zXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9mb3JtLWZpZWxkJztcbmltcG9ydCB7IFNlYXJjaGFibGVTZWxlY3RGaWVsZCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9TZWFyY2hhYmxlU2VsZWN0RmllbGQnO1xuaW1wb3J0IHsgU2tlbGV0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2tlbGV0b24nO1xuaW1wb3J0IHsgZml4dHVyZXNBcGkgfSBmcm9tICdAL2xpYi9hcGkvZml4dHVyZXMnO1xuaW1wb3J0IHsgbGVhZ3Vlc0FwaSB9IGZyb20gJ0AvbGliL2FwaS9sZWFndWVzJztcbmltcG9ydCB7IHRlYW1zQXBpIH0gZnJvbSAnQC9saWIvYXBpL3RlYW1zJztcbmltcG9ydCB7IEFycm93TGVmdCwgU2F2ZSwgQ2FsZW5kYXIgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInO1xuXG5pbnRlcmZhY2UgRml4dHVyZUZvcm1EYXRhIHtcbiAgaG9tZVRlYW1JZDogc3RyaW5nO1xuICBhd2F5VGVhbUlkOiBzdHJpbmc7XG4gIGxlYWd1ZUlkOiBzdHJpbmc7XG4gIGRhdGU6IHN0cmluZztcbiAgdGltZTogc3RyaW5nO1xuICB2ZW51ZU5hbWU6IHN0cmluZztcbiAgdmVudWVDaXR5OiBzdHJpbmc7XG4gIHJvdW5kOiBzdHJpbmc7XG4gIHN0YXR1czogc3RyaW5nO1xuICBnb2Fsc0hvbWU6IHN0cmluZztcbiAgZ29hbHNBd2F5OiBzdHJpbmc7XG4gIGVsYXBzZWQ6IHN0cmluZztcbiAgcmVmZXJlZT86IHN0cmluZztcbiAgdGVtcGVyYXR1cmU/OiBzdHJpbmc7XG4gIHdlYXRoZXI/OiBzdHJpbmc7XG4gIGF0dGVuZGFuY2U/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEVkaXRGaXh0dXJlUGFnZSgpIHtcbiAgY29uc3QgcGFyYW1zID0gdXNlUGFyYW1zKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KCk7XG4gIGNvbnN0IGZpeHR1cmVJZCA9IHBhcnNlSW50KHBhcmFtcy5pZCBhcyBzdHJpbmcpO1xuXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGU8Rml4dHVyZUZvcm1EYXRhPih7XG4gICAgaG9tZVRlYW1JZDogJycsXG4gICAgYXdheVRlYW1JZDogJycsXG4gICAgbGVhZ3VlSWQ6ICcnLFxuICAgIGRhdGU6ICcnLFxuICAgIHRpbWU6ICcnLFxuICAgIHZlbnVlTmFtZTogJycsXG4gICAgdmVudWVDaXR5OiAnJyxcbiAgICByb3VuZDogJycsXG4gICAgc3RhdHVzOiAnJyxcbiAgICBnb2Fsc0hvbWU6ICcnLFxuICAgIGdvYWxzQXdheTogJycsXG4gICAgZWxhcHNlZDogJycsXG4gIH0pO1xuXG4gIGNvbnN0IFtlcnJvcnMsIHNldEVycm9yc10gPSB1c2VTdGF0ZTxQYXJ0aWFsPEZpeHR1cmVGb3JtRGF0YT4+KHt9KTtcblxuICAvLyBGZXRjaCBmaXh0dXJlIGRldGFpbHNcbiAgY29uc3QgeyBkYXRhOiBmaXh0dXJlLCBpc0xvYWRpbmc6IGZpeHR1cmVMb2FkaW5nIH0gPSB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsnZml4dHVyZScsIGZpeHR1cmVJZF0sXG4gICAgcXVlcnlGbjogKCkgPT4gZml4dHVyZXNBcGkuZ2V0Rml4dHVyZShmaXh0dXJlSWQpLFxuICAgIGVuYWJsZWQ6ICEhZml4dHVyZUlkLFxuICB9KTtcblxuICAvLyBTbWFydCBsb2FkaW5nIHdpdGggc2VhcmNoIGFuZCBwYWdpbmF0aW9uXG4gIGNvbnN0IFtsZWFndWVTZWFyY2gsIHNldExlYWd1ZVNlYXJjaF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFt0ZWFtU2VhcmNoLCBzZXRUZWFtU2VhcmNoXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2xlYWd1ZVBhZ2UsIHNldExlYWd1ZVBhZ2VdID0gdXNlU3RhdGUoMSk7XG4gIGNvbnN0IFt0ZWFtUGFnZSwgc2V0VGVhbVBhZ2VdID0gdXNlU3RhdGUoMSk7XG5cbiAgLy8gRmV0Y2ggbGVhZ3VlcyB3aXRoIHNlYXJjaCBhbmQgcGFnaW5hdGlvblxuICBjb25zdCB7IGRhdGE6IGxlYWd1ZXMsIGlzTG9hZGluZzogbGVhZ3Vlc0xvYWRpbmcsIGVycm9yOiBsZWFndWVzRXJyb3IgfSA9IHVzZVF1ZXJ5KHtcbiAgICBxdWVyeUtleTogWydsZWFndWVzJywgJ3NlYXJjaCcsIGxlYWd1ZVNlYXJjaCwgbGVhZ3VlUGFnZV0sXG4gICAgcXVlcnlGbjogKCkgPT4gbGVhZ3Vlc0FwaS5nZXRMZWFndWVzKHtcbiAgICAgIGxpbWl0OiAxMDAsXG4gICAgICBwYWdlOiBsZWFndWVQYWdlLFxuICAgICAgLi4uKGxlYWd1ZVNlYXJjaCAmJiB7IHNlYXJjaDogbGVhZ3VlU2VhcmNoIH0pXG4gICAgfSksXG4gICAga2VlcFByZXZpb3VzRGF0YTogdHJ1ZSxcbiAgfSk7XG5cbiAgLy8gRmV0Y2ggdGVhbXMgd2l0aCBzZWFyY2ggYW5kIHBhZ2luYXRpb25cbiAgY29uc3QgeyBkYXRhOiB0ZWFtcywgaXNMb2FkaW5nOiB0ZWFtc0xvYWRpbmcsIGVycm9yOiB0ZWFtc0Vycm9yIH0gPSB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsndGVhbXMnLCAnc2VhcmNoJywgdGVhbVNlYXJjaCwgdGVhbVBhZ2VdLFxuICAgIHF1ZXJ5Rm46ICgpID0+IHRlYW1zQXBpLmdldFRlYW1zKHtcbiAgICAgIGxpbWl0OiAxMDAsXG4gICAgICBwYWdlOiB0ZWFtUGFnZSxcbiAgICAgIC4uLih0ZWFtU2VhcmNoICYmIHsgc2VhcmNoOiB0ZWFtU2VhcmNoIH0pXG4gICAgfSksXG4gICAga2VlcFByZXZpb3VzRGF0YTogdHJ1ZSxcbiAgfSk7XG5cbiAgLy8gVXBkYXRlIG11dGF0aW9uXG4gIGNvbnN0IHVwZGF0ZU11dGF0aW9uID0gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46IChkYXRhOiBhbnkpID0+IGZpeHR1cmVzQXBpLnVwZGF0ZUZpeHR1cmUoZml4dHVyZUlkLCBkYXRhKSxcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnZml4dHVyZScsIGZpeHR1cmVJZF0gfSk7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2ZpeHR1cmVzJ10gfSk7XG4gICAgICB0b2FzdC5zdWNjZXNzKCdGaXh0dXJlIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgICByb3V0ZXIucHVzaChgL2Rhc2hib2FyZC9maXh0dXJlcy8ke2ZpeHR1cmVJZH1gKTtcbiAgICB9LFxuICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XG4gICAgICB0b2FzdC5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gdXBkYXRlIGZpeHR1cmUnKTtcbiAgICB9LFxuICB9KTtcblxuICAvLyBQb3B1bGF0ZSBmb3JtIHdoZW4gZml4dHVyZSBkYXRhIGxvYWRzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGZpeHR1cmUpIHtcbiAgICAgIGNvbnN0IGZpeHR1cmVEYXRlID0gbmV3IERhdGUoZml4dHVyZS5kYXRlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFBvcHVsYXRpbmcgZm9ybSB3aXRoIGZpeHR1cmUgZGF0YTonLCB7XG4gICAgICAgIGhvbWVUZWFtSWQ6IGZpeHR1cmUuaG9tZVRlYW1JZCxcbiAgICAgICAgYXdheVRlYW1JZDogZml4dHVyZS5hd2F5VGVhbUlkLFxuICAgICAgICBsZWFndWVJZDogZml4dHVyZS5sZWFndWVJZCxcbiAgICAgICAgdmVudWU6IGZpeHR1cmUudmVudWUsXG4gICAgICAgIHZlbnVlTmFtZTogZml4dHVyZS52ZW51ZU5hbWUsXG4gICAgICAgIHZlbnVlQ2l0eTogZml4dHVyZS52ZW51ZUNpdHlcbiAgICAgIH0pO1xuXG4gICAgICAvLyDwn5SNIERFQlVHOiBDaGVjayBpZiBmaXh0dXJlIHVzZXMgaW50ZXJuYWwgb3IgZXh0ZXJuYWwgSURzXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBGSVhUVVJFIElEIEFOQUxZU0lTOicsIHtcbiAgICAgICAgJ2ZpeHR1cmUuaG9tZVRlYW1JZCc6IGZpeHR1cmUuaG9tZVRlYW1JZCxcbiAgICAgICAgJ2ZpeHR1cmUuYXdheVRlYW1JZCc6IGZpeHR1cmUuYXdheVRlYW1JZCxcbiAgICAgICAgJ2ZpeHR1cmUubGVhZ3VlSWQnOiBmaXh0dXJlLmxlYWd1ZUlkLFxuICAgICAgICAnZml4dHVyZSBvYmplY3QnOiBmaXh0dXJlXG4gICAgICB9KTtcblxuICAgICAgc2V0Rm9ybURhdGEoe1xuICAgICAgICBob21lVGVhbUlkOiBmaXh0dXJlLmhvbWVUZWFtSWQ/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgICAgIGF3YXlUZWFtSWQ6IGZpeHR1cmUuYXdheVRlYW1JZD8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgbGVhZ3VlSWQ6IGZpeHR1cmUubGVhZ3VlSWQ/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgICAgIGRhdGU6IGZpeHR1cmVEYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgdGltZTogZml4dHVyZURhdGUudG9UaW1lU3RyaW5nKCkuc2xpY2UoMCwgNSksXG4gICAgICAgIC8vIEZpeCB2ZW51ZSBtYXBwaW5nIC0gQVBJIHJldHVybnMgdmVudWUubmFtZSBhbmQgdmVudWUuY2l0eVxuICAgICAgICB2ZW51ZU5hbWU6IGZpeHR1cmUudmVudWU/Lm5hbWUgfHwgZml4dHVyZS52ZW51ZU5hbWUgfHwgJycsXG4gICAgICAgIHZlbnVlQ2l0eTogZml4dHVyZS52ZW51ZT8uY2l0eSB8fCBmaXh0dXJlLnZlbnVlQ2l0eSB8fCAnJyxcbiAgICAgICAgcm91bmQ6IGZpeHR1cmUucm91bmQgfHwgJycsXG4gICAgICAgIHN0YXR1czogZml4dHVyZS5zdGF0dXMgfHwgJycsXG4gICAgICAgIGdvYWxzSG9tZTogZml4dHVyZS5nb2Fsc0hvbWU/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgICAgIGdvYWxzQXdheTogZml4dHVyZS5nb2Fsc0F3YXk/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgICAgIGVsYXBzZWQ6IGZpeHR1cmUuZWxhcHNlZD8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgcmVmZXJlZTogZml4dHVyZS5yZWZlcmVlIHx8ICcnLFxuICAgICAgICB0ZW1wZXJhdHVyZTogZml4dHVyZS50ZW1wZXJhdHVyZT8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgd2VhdGhlcjogZml4dHVyZS53ZWF0aGVyIHx8ICcnLFxuICAgICAgICBhdHRlbmRhbmNlOiBmaXh0dXJlLmF0dGVuZGFuY2U/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtmaXh0dXJlXSk7XG5cbiAgLy8gTW92ZSB0aGlzIHVzZUVmZmVjdCBhZnRlciB0ZWFtT3B0aW9ucyBhbmQgbGVhZ3VlT3B0aW9ucyBhcmUgZGVmaW5lZFxuXG4gIGNvbnN0IHZhbGlkYXRlRm9ybSA9ICgpOiBib29sZWFuID0+IHtcbiAgICBjb25zdCBuZXdFcnJvcnM6IFBhcnRpYWw8Rml4dHVyZUZvcm1EYXRhPiA9IHt9O1xuXG4gICAgaWYgKCFmb3JtRGF0YS5ob21lVGVhbUlkKSBuZXdFcnJvcnMuaG9tZVRlYW1JZCA9ICdIb21lIHRlYW0gaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEuYXdheVRlYW1JZCkgbmV3RXJyb3JzLmF3YXlUZWFtSWQgPSAnQXdheSB0ZWFtIGlzIHJlcXVpcmVkJztcbiAgICBpZiAoIWZvcm1EYXRhLmxlYWd1ZUlkKSBuZXdFcnJvcnMubGVhZ3VlSWQgPSAnTGVhZ3VlIGlzIHJlcXVpcmVkJztcbiAgICBpZiAoIWZvcm1EYXRhLmRhdGUpIG5ld0Vycm9ycy5kYXRlID0gJ0RhdGUgaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEudGltZSkgbmV3RXJyb3JzLnRpbWUgPSAnVGltZSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5zdGF0dXMpIG5ld0Vycm9ycy5zdGF0dXMgPSAnU3RhdHVzIGlzIHJlcXVpcmVkJztcblxuICAgIGlmIChmb3JtRGF0YS5ob21lVGVhbUlkID09PSBmb3JtRGF0YS5hd2F5VGVhbUlkKSB7XG4gICAgICBuZXdFcnJvcnMuYXdheVRlYW1JZCA9ICdBd2F5IHRlYW0gbXVzdCBiZSBkaWZmZXJlbnQgZnJvbSBob21lIHRlYW0nO1xuICAgIH1cblxuICAgIHNldEVycm9ycyhuZXdFcnJvcnMpO1xuICAgIHJldHVybiBPYmplY3Qua2V5cyhuZXdFcnJvcnMpLmxlbmd0aCA9PT0gMDtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuXG4gICAgaWYgKCF2YWxpZGF0ZUZvcm0oKSkge1xuICAgICAgdG9hc3QuZXJyb3IoJ1BsZWFzZSBmaXggdGhlIGZvcm0gZXJyb3JzJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gQ29tYmluZSBkYXRlIGFuZCB0aW1lXG4gICAgY29uc3QgZGF0ZVRpbWUgPSBuZXcgRGF0ZShgJHtmb3JtRGF0YS5kYXRlfVQke2Zvcm1EYXRhLnRpbWV9YCk7XG5cbiAgICBjb25zdCBzdWJtaXREYXRhID0ge1xuICAgICAgaG9tZVRlYW1JZDogcGFyc2VJbnQoZm9ybURhdGEuaG9tZVRlYW1JZCksXG4gICAgICBhd2F5VGVhbUlkOiBwYXJzZUludChmb3JtRGF0YS5hd2F5VGVhbUlkKSxcbiAgICAgIGxlYWd1ZUlkOiBwYXJzZUludChmb3JtRGF0YS5sZWFndWVJZCksXG4gICAgICBkYXRlOiBkYXRlVGltZS50b0lTT1N0cmluZygpLFxuICAgICAgdmVudWVOYW1lOiBmb3JtRGF0YS52ZW51ZU5hbWUgfHwgbnVsbCxcbiAgICAgIHZlbnVlQ2l0eTogZm9ybURhdGEudmVudWVDaXR5IHx8IG51bGwsXG4gICAgICByb3VuZDogZm9ybURhdGEucm91bmQgfHwgbnVsbCxcbiAgICAgIHN0YXR1czogZm9ybURhdGEuc3RhdHVzLFxuICAgICAgZ29hbHNIb21lOiBmb3JtRGF0YS5nb2Fsc0hvbWUgPyBwYXJzZUludChmb3JtRGF0YS5nb2Fsc0hvbWUpIDogbnVsbCxcbiAgICAgIGdvYWxzQXdheTogZm9ybURhdGEuZ29hbHNBd2F5ID8gcGFyc2VJbnQoZm9ybURhdGEuZ29hbHNBd2F5KSA6IG51bGwsXG4gICAgICBlbGFwc2VkOiBmb3JtRGF0YS5lbGFwc2VkID8gcGFyc2VJbnQoZm9ybURhdGEuZWxhcHNlZCkgOiBudWxsLFxuICAgICAgcmVmZXJlZTogZm9ybURhdGEucmVmZXJlZSB8fCBudWxsLFxuICAgICAgdGVtcGVyYXR1cmU6IGZvcm1EYXRhLnRlbXBlcmF0dXJlID8gcGFyc2VJbnQoZm9ybURhdGEudGVtcGVyYXR1cmUpIDogbnVsbCxcbiAgICAgIHdlYXRoZXI6IGZvcm1EYXRhLndlYXRoZXIgfHwgbnVsbCxcbiAgICAgIGF0dGVuZGFuY2U6IGZvcm1EYXRhLmF0dGVuZGFuY2UgPyBwYXJzZUludChmb3JtRGF0YS5hdHRlbmRhbmNlKSA6IG51bGwsXG4gICAgfTtcblxuICAgIHVwZGF0ZU11dGF0aW9uLm11dGF0ZShzdWJtaXREYXRhKTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVGb3JtRGF0YSA9IChmaWVsZDoga2V5b2YgRml4dHVyZUZvcm1EYXRhLCB2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBbZmllbGRdOiB2YWx1ZSB9KSk7XG4gICAgLy8gQ2xlYXIgZXJyb3Igd2hlbiB1c2VyIHN0YXJ0cyB0eXBpbmdcbiAgICBpZiAoZXJyb3JzW2ZpZWxkXSkge1xuICAgICAgc2V0RXJyb3JzKHByZXYgPT4gKHsgLi4ucHJldiwgW2ZpZWxkXTogdW5kZWZpbmVkIH0pKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gU2VhcmNoIGFuZCBwYWdpbmF0aW9uIGhhbmRsZXJzXG4gIGNvbnN0IGhhbmRsZUxlYWd1ZVNlYXJjaCA9IChxdWVyeTogc3RyaW5nKSA9PiB7XG4gICAgc2V0TGVhZ3VlU2VhcmNoKHF1ZXJ5KTtcbiAgICBzZXRMZWFndWVQYWdlKDEpOyAvLyBSZXNldCB0byBmaXJzdCBwYWdlIG9uIG5ldyBzZWFyY2hcbiAgfTtcblxuICBjb25zdCBoYW5kbGVUZWFtU2VhcmNoID0gKHF1ZXJ5OiBzdHJpbmcpID0+IHtcbiAgICBzZXRUZWFtU2VhcmNoKHF1ZXJ5KTtcbiAgICBzZXRUZWFtUGFnZSgxKTsgLy8gUmVzZXQgdG8gZmlyc3QgcGFnZSBvbiBuZXcgc2VhcmNoXG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTGVhZ3VlTG9hZE1vcmUgPSAoKSA9PiB7XG4gICAgaWYgKGxlYWd1ZXM/Lm1ldGE/LnRvdGFsUGFnZXMgJiYgbGVhZ3VlUGFnZSA8IGxlYWd1ZXMubWV0YS50b3RhbFBhZ2VzKSB7XG4gICAgICBzZXRMZWFndWVQYWdlKHByZXYgPT4gcHJldiArIDEpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVUZWFtTG9hZE1vcmUgPSAoKSA9PiB7XG4gICAgaWYgKHRlYW1zPy5tZXRhPy50b3RhbFBhZ2VzICYmIHRlYW1QYWdlIDwgdGVhbXMubWV0YS50b3RhbFBhZ2VzKSB7XG4gICAgICBzZXRUZWFtUGFnZShwcmV2ID0+IHByZXYgKyAxKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3Qgc3RhdHVzT3B0aW9ucyA9IFtcbiAgICB7IHZhbHVlOiAnTlMnLCBsYWJlbDogJ05vdCBTdGFydGVkJyB9LFxuICAgIHsgdmFsdWU6ICcxSCcsIGxhYmVsOiAnRmlyc3QgSGFsZicgfSxcbiAgICB7IHZhbHVlOiAnSFQnLCBsYWJlbDogJ0hhbGYgVGltZScgfSxcbiAgICB7IHZhbHVlOiAnMkgnLCBsYWJlbDogJ1NlY29uZCBIYWxmJyB9LFxuICAgIHsgdmFsdWU6ICdGVCcsIGxhYmVsOiAnRnVsbCBUaW1lJyB9LFxuICAgIHsgdmFsdWU6ICdQU1QnLCBsYWJlbDogJ1Bvc3Rwb25lZCcgfSxcbiAgICB7IHZhbHVlOiAnQ0FOQycsIGxhYmVsOiAnQ2FuY2VsbGVkJyB9LFxuICBdO1xuXG4gIC8vIEZJWEVEOiBVc2UgZXh0ZXJuYWxJZCBmb3IgbWFwcGluZyB0byBtYXRjaCBmaXh0dXJlIGRhdGFcbiAgY29uc3QgbGVhZ3VlT3B0aW9ucyA9IGxlYWd1ZXM/LmRhdGE/Lm1hcChsZWFndWUgPT4gKHtcbiAgICB2YWx1ZTogbGVhZ3VlLmV4dGVybmFsSWQudG9TdHJpbmcoKSwgIC8vIOKchSBVc2UgZXh0ZXJuYWxJZFxuICAgIGxhYmVsOiBgJHtsZWFndWUubmFtZX0ke2xlYWd1ZS5zZWFzb24gPyBgICgke2xlYWd1ZS5zZWFzb259KWAgOiAnJ31gLFxuICAgIGxvZ286IGxlYWd1ZS5sb2dvLFxuICAgIHNlYXNvbjogbGVhZ3VlLnNlYXNvbixcbiAgfSkpIHx8IFtdO1xuXG4gIGNvbnN0IHRlYW1PcHRpb25zID0gdGVhbXM/LmRhdGE/Lm1hcCh0ZWFtID0+ICh7XG4gICAgdmFsdWU6IHRlYW0uZXh0ZXJuYWxJZC50b1N0cmluZygpLCAgLy8g4pyFIFVzZSBleHRlcm5hbElkXG4gICAgbGFiZWw6IHRlYW0ubmFtZSxcbiAgICBsb2dvOiB0ZWFtLmxvZ28sXG4gIH0pKSB8fCBbXTtcblxuICAvLyBBZGRpdGlvbmFsIGVmZmVjdCB0byBlbnN1cmUgb3B0aW9ucyBhcmUgbG9hZGVkIC0gbW92ZWQgaGVyZSBhZnRlciBvcHRpb25zIGFyZSBkZWZpbmVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/CflI0gT3B0aW9ucyBMb2FkaW5nIFN0YXR1czonLCB7XG4gICAgICB0ZWFtc0xvYWRpbmcsXG4gICAgICBsZWFndWVzTG9hZGluZyxcbiAgICAgIHRlYW1PcHRpb25zQ291bnQ6IHRlYW1PcHRpb25zLmxlbmd0aCxcbiAgICAgIGxlYWd1ZU9wdGlvbnNDb3VudDogbGVhZ3VlT3B0aW9ucy5sZW5ndGgsXG4gICAgICBmb3JtRGF0YVJlYWR5OiAhIShmb3JtRGF0YS5ob21lVGVhbUlkICYmIGZvcm1EYXRhLmF3YXlUZWFtSWQgJiYgZm9ybURhdGEubGVhZ3VlSWQpLFxuICAgIH0pO1xuICB9LCBbdGVhbXNMb2FkaW5nLCBsZWFndWVzTG9hZGluZywgdGVhbU9wdGlvbnMubGVuZ3RoLCBsZWFndWVPcHRpb25zLmxlbmd0aCwgZm9ybURhdGFdKTtcblxuICAvLyBTbWFydCBwcmV2aWV3IGxvZ2ljIC0gdXNlIGZpeHR1cmUgZGF0YSBpbml0aWFsbHksIHRoZW4gdXBkYXRlIGZyb20gc2VsZWN0aW9uc1xuICBjb25zdCBnZXRQcmV2aWV3RGF0YSA9ICgpID0+IHtcbiAgICBpZiAoIWZpeHR1cmUpIHJldHVybiB7IGxlYWd1ZTogbnVsbCwgaG9tZVRlYW06IG51bGwsIGF3YXlUZWFtOiBudWxsIH07XG5cbiAgICAvLyBGb3IgaW5pdGlhbCBsb2FkLCB1c2UgZml4dHVyZSBkYXRhXG4gICAgY29uc3QgaXNJbml0aWFsRGF0YSA9IChcbiAgICAgIGZvcm1EYXRhLmhvbWVUZWFtSWQgPT09IGZpeHR1cmUuaG9tZVRlYW1JZD8udG9TdHJpbmcoKSAmJlxuICAgICAgZm9ybURhdGEuYXdheVRlYW1JZCA9PT0gZml4dHVyZS5hd2F5VGVhbUlkPy50b1N0cmluZygpICYmXG4gICAgICBmb3JtRGF0YS5sZWFndWVJZCA9PT0gZml4dHVyZS5sZWFndWVJZD8udG9TdHJpbmcoKVxuICAgICk7XG5cbiAgICBpZiAoaXNJbml0aWFsRGF0YSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbGVhZ3VlOiB7XG4gICAgICAgICAgdmFsdWU6IGZvcm1EYXRhLmxlYWd1ZUlkLFxuICAgICAgICAgIGxhYmVsOiBmaXh0dXJlLmxlYWd1ZU5hbWUsXG4gICAgICAgICAgbG9nbzogJycgLy8gV2lsbCBiZSB1cGRhdGVkIHdoZW4gdXNlciBzZWxlY3RzIGZyb20gZHJvcGRvd25cbiAgICAgICAgfSxcbiAgICAgICAgaG9tZVRlYW06IHtcbiAgICAgICAgICB2YWx1ZTogZm9ybURhdGEuaG9tZVRlYW1JZCxcbiAgICAgICAgICBsYWJlbDogZml4dHVyZS5ob21lVGVhbU5hbWUsXG4gICAgICAgICAgbG9nbzogZml4dHVyZS5ob21lVGVhbUxvZ29cbiAgICAgICAgfSxcbiAgICAgICAgYXdheVRlYW06IHtcbiAgICAgICAgICB2YWx1ZTogZm9ybURhdGEuYXdheVRlYW1JZCxcbiAgICAgICAgICBsYWJlbDogZml4dHVyZS5hd2F5VGVhbU5hbWUsXG4gICAgICAgICAgbG9nbzogZml4dHVyZS5hd2F5VGVhbUxvZ29cbiAgICAgICAgfVxuICAgICAgfTtcbiAgICB9XG5cbiAgICAvLyBGb3IgdXNlciBzZWxlY3Rpb25zLCB1c2UgZHJvcGRvd24gb3B0aW9uc1xuICAgIHJldHVybiB7XG4gICAgICBsZWFndWU6IGxlYWd1ZU9wdGlvbnMuZmluZChsID0+IGwudmFsdWUgPT09IGZvcm1EYXRhLmxlYWd1ZUlkKSB8fCBudWxsLFxuICAgICAgaG9tZVRlYW06IHRlYW1PcHRpb25zLmZpbmQodCA9PiB0LnZhbHVlID09PSBmb3JtRGF0YS5ob21lVGVhbUlkKSB8fCBudWxsLFxuICAgICAgYXdheVRlYW06IHRlYW1PcHRpb25zLmZpbmQodCA9PiB0LnZhbHVlID09PSBmb3JtRGF0YS5hd2F5VGVhbUlkKSB8fCBudWxsXG4gICAgfTtcbiAgfTtcblxuICBjb25zdCB7IGxlYWd1ZTogc2VsZWN0ZWRMZWFndWUsIGhvbWVUZWFtOiBzZWxlY3RlZEhvbWVUZWFtLCBhd2F5VGVhbTogc2VsZWN0ZWRBd2F5VGVhbSB9ID0gZ2V0UHJldmlld0RhdGEoKTtcblxuICAvLyBEZWJ1ZyBzZWxlY3RlZCB2YWx1ZXNcbiAgY29uc29sZS5sb2coJ/Cfjq8gU0VMRUNURUQgVkFMVUVTIERFQlVHOicpO1xuICBjb25zb2xlLmxvZygnc2VsZWN0ZWRMZWFndWU6Jywgc2VsZWN0ZWRMZWFndWUpO1xuICBjb25zb2xlLmxvZygnc2VsZWN0ZWRIb21lVGVhbTonLCBzZWxlY3RlZEhvbWVUZWFtKTtcbiAgY29uc29sZS5sb2coJ3NlbGVjdGVkQXdheVRlYW06Jywgc2VsZWN0ZWRBd2F5VGVhbSk7XG5cbiAgLy8gRGVidWcgZm9ybSBkYXRhXG4gIGNvbnNvbGUubG9nKCfwn5OdIEZPUk0gREFUQTonLCBmb3JtRGF0YSk7XG4gIGNvbnNvbGUubG9nKCfwn5OKIE9QVElPTlMgQ09VTlQ6JywgeyB0ZWFtczogdGVhbU9wdGlvbnMubGVuZ3RoLCBsZWFndWVzOiBsZWFndWVPcHRpb25zLmxlbmd0aCB9KTtcblxuICAvLyBGb3JjZSByZS1yZW5kZXIgd2hlbiBvcHRpb25zIGNoYW5nZSB0byBlbnN1cmUgcHJldmlldyB1cGRhdGVzXG4gIGNvbnN0IFtwcmV2aWV3S2V5LCBzZXRQcmV2aWV3S2V5XSA9IHVzZVN0YXRlKDApO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gVXBkYXRlIHByZXZpZXcgd2hlbiBvcHRpb25zIG9yIGZvcm0gZGF0YSBjaGFuZ2VzXG4gICAgaWYgKHRlYW1PcHRpb25zLmxlbmd0aCA+IDAgJiYgbGVhZ3VlT3B0aW9ucy5sZW5ndGggPiAwICYmIGZvcm1EYXRhLmhvbWVUZWFtSWQpIHtcbiAgICAgIHNldFByZXZpZXdLZXkocHJldiA9PiBwcmV2ICsgMSk7XG4gICAgfVxuICB9LCBbdGVhbU9wdGlvbnMubGVuZ3RoLCBsZWFndWVPcHRpb25zLmxlbmd0aCwgZm9ybURhdGEuaG9tZVRlYW1JZCwgZm9ybURhdGEuYXdheVRlYW1JZCwgZm9ybURhdGEubGVhZ3VlSWRdKTtcblxuICAvLyBTaW1wbGUgZGVidWcgZm9yIElEIG1hdGNoaW5nXG4gIGNvbnNvbGUubG9nKCfwn5SnIElEIE1BVENISU5HIERFQlVHOicpO1xuICBjb25zb2xlLmxvZygnRm9ybSBJRHM6JywgeyBob21lOiBmb3JtRGF0YS5ob21lVGVhbUlkLCBhd2F5OiBmb3JtRGF0YS5hd2F5VGVhbUlkLCBsZWFndWU6IGZvcm1EYXRhLmxlYWd1ZUlkIH0pO1xuICBjb25zb2xlLmxvZygnRm9ybSBJRCBUeXBlczonLCB7XG4gICAgaG9tZTogdHlwZW9mIGZvcm1EYXRhLmhvbWVUZWFtSWQsXG4gICAgYXdheTogdHlwZW9mIGZvcm1EYXRhLmF3YXlUZWFtSWQsXG4gICAgbGVhZ3VlOiB0eXBlb2YgZm9ybURhdGEubGVhZ3VlSWRcbiAgfSk7XG5cbiAgaWYgKHRlYW1PcHRpb25zLmxlbmd0aCA+IDApIHtcbiAgICBjb25zb2xlLmxvZygnU2FtcGxlIFRlYW0gT3B0aW9uczonLCB0ZWFtT3B0aW9ucy5zbGljZSgwLCAzKSk7XG4gICAgY29uc29sZS5sb2coJ1RlYW0gT3B0aW9uIFR5cGVzOicsIHRlYW1PcHRpb25zLnNsaWNlKDAsIDEpLm1hcCh0ID0+ICh7IHZhbHVlOiB0LnZhbHVlLCB0eXBlOiB0eXBlb2YgdC52YWx1ZSB9KSkpO1xuXG4gICAgLy8g8J+UjSBDaGVjayBpZiBvdXIgc3BlY2lmaWMgdGVhbXMgZXhpc3RcbiAgICBjb25zdCBob21lVGVhbUluT3B0aW9ucyA9IHRlYW1PcHRpb25zLmZpbmQodCA9PiB0LnZhbHVlID09PSAnNDQ1MCcpO1xuICAgIGNvbnN0IGF3YXlUZWFtSW5PcHRpb25zID0gdGVhbU9wdGlvbnMuZmluZCh0ID0+IHQudmFsdWUgPT09ICcyMDAyMicpO1xuICAgIGNvbnNvbGUubG9nKCfwn46vIFNQRUNJRklDIFRFQU1TIENIRUNLOicsIHtcbiAgICAgICdob21lVGVhbSAoNDQ1MCknOiBob21lVGVhbUluT3B0aW9ucyxcbiAgICAgICdhd2F5VGVhbSAoMjAwMjIpJzogYXdheVRlYW1Jbk9wdGlvbnMsXG4gICAgICAndG90YWxUZWFtcyc6IHRlYW1PcHRpb25zLmxlbmd0aFxuICAgIH0pO1xuICB9XG5cbiAgaWYgKGxlYWd1ZU9wdGlvbnMubGVuZ3RoID4gMCkge1xuICAgIGNvbnNvbGUubG9nKCdTYW1wbGUgTGVhZ3VlIE9wdGlvbnM6JywgbGVhZ3VlT3B0aW9ucy5zbGljZSgwLCAyKSk7XG4gICAgY29uc29sZS5sb2coJ0xlYWd1ZSBPcHRpb24gVHlwZXM6JywgbGVhZ3VlT3B0aW9ucy5zbGljZSgwLCAxKS5tYXAobCA9PiAoeyB2YWx1ZTogbC52YWx1ZSwgdHlwZTogdHlwZW9mIGwudmFsdWUgfSkpKTtcblxuICAgIC8vIPCflI0gQ2hlY2sgaWYgb3VyIHNwZWNpZmljIGxlYWd1ZSBleGlzdHNcbiAgICBjb25zdCBsZWFndWVJbk9wdGlvbnMgPSBsZWFndWVPcHRpb25zLmZpbmQobCA9PiBsLnZhbHVlID09PSAnNTcwJyk7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8gU1BFQ0lGSUMgTEVBR1VFIENIRUNLOicsIHtcbiAgICAgICdsZWFndWUgKDU3MCknOiBsZWFndWVJbk9wdGlvbnMsXG4gICAgICAndG90YWxMZWFndWVzJzogbGVhZ3VlT3B0aW9ucy5sZW5ndGhcbiAgICB9KTtcbiAgfVxuXG4gIGNvbnNvbGUubG9nKCfwn5SNIElEIEV4aXN0cyBDaGVjayAtIEZJWEVEOicsIHtcbiAgICBob21lRXhpc3RzOiB0ZWFtT3B0aW9ucy5zb21lKHQgPT4gdC52YWx1ZSA9PT0gZm9ybURhdGEuaG9tZVRlYW1JZCksXG4gICAgYXdheUV4aXN0czogdGVhbU9wdGlvbnMuc29tZSh0ID0+IHQudmFsdWUgPT09IGZvcm1EYXRhLmF3YXlUZWFtSWQpLFxuICAgIGxlYWd1ZUV4aXN0czogbGVhZ3VlT3B0aW9ucy5zb21lKGwgPT4gbC52YWx1ZSA9PT0gZm9ybURhdGEubGVhZ3VlSWQpLFxuICB9KTtcblxuICAvLyBQcmV2aWV3IGNvbXBvbmVudCBmb3Igc2VsZWN0ZWQgdmFsdWVzXG4gIGNvbnN0IFNlbGVjdGVkVmFsdWVQcmV2aWV3ID0gKHtcbiAgICBsYWJlbCxcbiAgICBzZWxlY3RlZE9wdGlvbixcbiAgICBwbGFjZWhvbGRlciA9IFwiTm90IHNlbGVjdGVkXCJcbiAgfToge1xuICAgIGxhYmVsOiBzdHJpbmc7XG4gICAgc2VsZWN0ZWRPcHRpb24/OiB7IHZhbHVlOiBudW1iZXI7IGxhYmVsOiBzdHJpbmc7IGxvZ28/OiBzdHJpbmcgfTtcbiAgICBwbGFjZWhvbGRlcj86IHN0cmluZztcbiAgfSkgPT4ge1xuICAgIGNvbnN0IENETl9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19ET01BSU5fQ0ROX1BJQ1RVUkUgfHwgJ2h0dHA6Ly8xMTYuMjAzLjEyNS42NSc7XG5cbiAgICAvLyBEZWJ1ZyBwcmV2aWV3IGNvbXBvbmVudFxuICAgIGNvbnNvbGUubG9nKGDwn46oICR7bGFiZWx9IFByZXZpZXc6YCwgeyBzZWxlY3RlZE9wdGlvbiwgcGxhY2Vob2xkZXIsIGhhc09wdGlvbjogISFzZWxlY3RlZE9wdGlvbiB9KTtcblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTMgcC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj57bGFiZWx9PC9kaXY+XG4gICAgICAgIHtzZWxlY3RlZE9wdGlvbiA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAge3NlbGVjdGVkT3B0aW9uLmxvZ28gJiYgKFxuICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgc3JjPXtgJHtDRE5fVVJMfS8ke3NlbGVjdGVkT3B0aW9uLmxvZ299YH1cbiAgICAgICAgICAgICAgICBhbHQ9e3NlbGVjdGVkT3B0aW9uLmxhYmVsfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctOCBoLTggb2JqZWN0LWNvbnRhaW4gcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAge3NlbGVjdGVkT3B0aW9uLmxhYmVsfVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctZ3JheS0yMDAgcm91bmRlZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQteHNcIj4/PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGl0YWxpY1wiPntwbGFjZWhvbGRlcn08L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIElubGluZSBMZWFndWUgY29tcG9uZW50IChsb2dvICsgbmFtZSBiw6puIHRyw6FpLCBkcm9wZG93biBiw6puIHBo4bqjaSlcbiAgY29uc3QgTGVhZ3VlSW5saW5lU2VsZWN0b3IgPSAoKSA9PiB7XG4gICAgY29uc3QgQ0ROX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0RPTUFJTl9DRE5fUElDVFVSRSB8fCAnaHR0cDovLzExNi4yMDMuMTI1LjY1JztcblxuICAgIC8vIERlYnVnIGxlYWd1ZSBzZWxlY3RvclxuICAgIGNvbnNvbGUubG9nKCfwn4+GIExlYWd1ZSBJbmxpbmUgU2VsZWN0b3I6JywgeyBzZWxlY3RlZExlYWd1ZSwgaGFzTGVhZ3VlOiAhIXNlbGVjdGVkTGVhZ3VlIH0pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgIHsvKiBMZWZ0IHNpZGU6IExvZ28gKyBMZWFndWUgbmFtZSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWluLXctMCBmbGV4LTFcIj5cbiAgICAgICAgICB7c2VsZWN0ZWRMZWFndWUgPyAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICB7c2VsZWN0ZWRMZWFndWUubG9nbyAmJiAoXG4gICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgc3JjPXtgJHtDRE5fVVJMfS8ke3NlbGVjdGVkTGVhZ3VlLmxvZ299YH1cbiAgICAgICAgICAgICAgICAgIGFsdD17c2VsZWN0ZWRMZWFndWUubGFiZWx9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTggaC04IG9iamVjdC1jb250YWluIHJvdW5kZWQgZmxleC1zaHJpbmstMFwiXG4gICAgICAgICAgICAgICAgICBvbkVycm9yPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuZGlzcGxheSA9ICdub25lJztcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRMZWFndWUubGFiZWx9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvPlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctZ3JheS0yMDAgcm91bmRlZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXhzXCI+Pzwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaXRhbGljXCI+Tm8gbGVhZ3VlIHNlbGVjdGVkPC9zcGFuPlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFJpZ2h0IHNpZGU6IERyb3Bkb3duICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgdy02NFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5MZWFndWUqPC9kaXY+XG4gICAgICAgICAgPFNlYXJjaGFibGVTZWxlY3RGaWVsZFxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2xlYWd1ZXNMb2FkaW5nID8gXCJMb2FkaW5nIGxlYWd1ZXMuLi5cIiA6IFwiU2VsZWN0IGxlYWd1ZVwifVxuICAgICAgICAgICAgc2VhcmNoUGxhY2Vob2xkZXI9XCJTZWFyY2ggbGVhZ3Vlcy4uLlwiXG4gICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmxlYWd1ZUlkfVxuICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB1cGRhdGVGb3JtRGF0YSgnbGVhZ3VlSWQnLCB2YWx1ZSl9XG4gICAgICAgICAgICBvcHRpb25zPXtsZWFndWVPcHRpb25zfVxuICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5sZWFndWVJZH1cbiAgICAgICAgICAgIGRpc2FibGVkPXtsZWFndWVzTG9hZGluZ31cbiAgICAgICAgICAgIG9uU2VhcmNoPXtoYW5kbGVMZWFndWVTZWFyY2h9XG4gICAgICAgICAgICBvbkxvYWRNb3JlPXtoYW5kbGVMZWFndWVMb2FkTW9yZX1cbiAgICAgICAgICAgIGhhc01vcmU9e2xlYWd1ZXM/Lm1ldGEgPyBsZWFndWVQYWdlIDwgbGVhZ3Vlcy5tZXRhLnRvdGFsUGFnZXMgOiBmYWxzZX1cbiAgICAgICAgICAgIGlzTG9hZGluZz17bGVhZ3Vlc0xvYWRpbmd9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIFNob3cgbG9hZGluZyBzdGF0ZSBpZiBhbnkgcmVxdWlyZWQgZGF0YSBpcyBsb2FkaW5nXG4gIGNvbnN0IGlzRGF0YUxvYWRpbmcgPSBmaXh0dXJlTG9hZGluZyB8fCBsZWFndWVzTG9hZGluZyB8fCB0ZWFtc0xvYWRpbmc7XG5cbiAgaWYgKGlzRGF0YUxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMCB3LTIwXCIgLz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtOCB3LTY0XCIgLz5cbiAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTQgdy00OFwiIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTYgdy00OFwiIC8+XG4gICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC00IHctNjRcIiAvPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC00IHctMzJcIiAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMFwiIC8+XG4gICAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC00IHctMjRcIiAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMFwiIC8+XG4gICAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMCB3LTIwXCIgLz5cbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTAgdy0zMlwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gU2hvdyBlcnJvciBzdGF0ZSBpZiBhbnkgY3JpdGljYWwgZGF0YSBmYWlsZWQgdG8gbG9hZFxuICBpZiAoIWZpeHR1cmUgfHwgbGVhZ3Vlc0Vycm9yIHx8IHRlYW1zRXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gcm91dGVyLmJhY2soKX0+XG4gICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICBCYWNrXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgeyFmaXh0dXJlICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi00XCI+Rml4dHVyZSBub3QgZm91bmQ8L3A+fVxuICAgICAgICAgICAgICB7bGVhZ3Vlc0Vycm9yICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi00XCI+RmFpbGVkIHRvIGxvYWQgbGVhZ3Vlczoge2xlYWd1ZXNFcnJvci5tZXNzYWdlfTwvcD59XG4gICAgICAgICAgICAgIHt0ZWFtc0Vycm9yICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi00XCI+RmFpbGVkIHRvIGxvYWQgdGVhbXM6IHt0ZWFtc0Vycm9yLm1lc3NhZ2V9PC9wPn1cbiAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZC9maXh0dXJlcycpfT5cbiAgICAgICAgICAgICAgICBSZXR1cm4gdG8gRml4dHVyZXNcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIuYmFjaygpfT5cbiAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgQmFja1xuICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgIEVkaXQgRml4dHVyZToge2ZpeHR1cmUuaG9tZVRlYW1OYW1lfSB2cyB7Zml4dHVyZS5hd2F5VGVhbU5hbWV9XG4gICAgICAgICAgPC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTFcIj5VcGRhdGUgZml4dHVyZSBkZXRhaWxzIGFuZCBtYXRjaCBpbmZvcm1hdGlvbjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEZvcm0gKi99XG4gICAgICA8Q2FyZD5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cIm1yLTIgaC01IHctNVwiIC8+XG4gICAgICAgICAgICBGaXh0dXJlIERldGFpbHNcbiAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgVXBkYXRlIHRoZSBmaXh0dXJlIGluZm9ybWF0aW9uXG4gICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgPEZvcm1TZWN0aW9uIHRpdGxlPVwiVGVhbXMgJiBDb21wZXRpdGlvblwiIGRlc2NyaXB0aW9uPVwiU2VsZWN0IHRoZSB0ZWFtcyBhbmQgbGVhZ3VlXCI+XG4gICAgICAgICAgICAgIHsvKiBUZWFtcyBTZWxlY3Rpb24gd2l0aCBQcmV2aWV3ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0yIHAtMiBiZy15ZWxsb3ctMTAwIGJvcmRlciBib3JkZXIteWVsbG93LTMwMCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+REVCVUc6PC9zdHJvbmc+IEhvbWUgVGVhbSAtIHtzZWxlY3RlZEhvbWVUZWFtID8gYEZvdW5kOiAke3NlbGVjdGVkSG9tZVRlYW0ubGFiZWx9YCA6ICdOb3QgZm91bmQnfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0ZWRWYWx1ZVByZXZpZXdcbiAgICAgICAgICAgICAgICAgICAga2V5PXtgaG9tZS0ke3ByZXZpZXdLZXl9YH1cbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJTZWxlY3RlZCBIb21lIFRlYW1cIlxuICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZE9wdGlvbj17c2VsZWN0ZWRIb21lVGVhbX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJObyBob21lIHRlYW0gc2VsZWN0ZWRcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxTZWFyY2hhYmxlU2VsZWN0RmllbGRcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJIb21lIFRlYW1cIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17dGVhbXNMb2FkaW5nID8gXCJMb2FkaW5nIHRlYW1zLi4uXCIgOiBcIlNlbGVjdCBob21lIHRlYW1cIn1cbiAgICAgICAgICAgICAgICAgICAgc2VhcmNoUGxhY2Vob2xkZXI9XCJTZWFyY2ggdGVhbXMuLi5cIlxuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuaG9tZVRlYW1JZH1cbiAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB1cGRhdGVGb3JtRGF0YSgnaG9tZVRlYW1JZCcsIHZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgb3B0aW9ucz17dGVhbU9wdGlvbnN9XG4gICAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuaG9tZVRlYW1JZH1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3RlYW1zTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgb25TZWFyY2g9e2hhbmRsZVRlYW1TZWFyY2h9XG4gICAgICAgICAgICAgICAgICAgIG9uTG9hZE1vcmU9e2hhbmRsZVRlYW1Mb2FkTW9yZX1cbiAgICAgICAgICAgICAgICAgICAgaGFzTW9yZT17dGVhbXM/Lm1ldGEgPyB0ZWFtUGFnZSA8IHRlYW1zLm1ldGEudG90YWxQYWdlcyA6IGZhbHNlfVxuICAgICAgICAgICAgICAgICAgICBpc0xvYWRpbmc9e3RlYW1zTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0yIHAtMiBiZy15ZWxsb3ctMTAwIGJvcmRlciBib3JkZXIteWVsbG93LTMwMCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+REVCVUc6PC9zdHJvbmc+IEF3YXkgVGVhbSAtIHtzZWxlY3RlZEF3YXlUZWFtID8gYEZvdW5kOiAke3NlbGVjdGVkQXdheVRlYW0ubGFiZWx9YCA6ICdOb3QgZm91bmQnfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0ZWRWYWx1ZVByZXZpZXdcbiAgICAgICAgICAgICAgICAgICAga2V5PXtgYXdheS0ke3ByZXZpZXdLZXl9YH1cbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJTZWxlY3RlZCBBd2F5IFRlYW1cIlxuICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZE9wdGlvbj17c2VsZWN0ZWRBd2F5VGVhbX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJObyBhd2F5IHRlYW0gc2VsZWN0ZWRcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxTZWFyY2hhYmxlU2VsZWN0RmllbGRcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJBd2F5IFRlYW1cIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17dGVhbXNMb2FkaW5nID8gXCJMb2FkaW5nIHRlYW1zLi4uXCIgOiBcIlNlbGVjdCBhd2F5IHRlYW1cIn1cbiAgICAgICAgICAgICAgICAgICAgc2VhcmNoUGxhY2Vob2xkZXI9XCJTZWFyY2ggdGVhbXMuLi5cIlxuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYXdheVRlYW1JZH1cbiAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB1cGRhdGVGb3JtRGF0YSgnYXdheVRlYW1JZCcsIHZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgb3B0aW9ucz17dGVhbU9wdGlvbnMuZmlsdGVyKHRlYW0gPT4gdGVhbS52YWx1ZSAhPT0gZm9ybURhdGEuaG9tZVRlYW1JZCl9XG4gICAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuYXdheVRlYW1JZH1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3RlYW1zTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgb25TZWFyY2g9e2hhbmRsZVRlYW1TZWFyY2h9XG4gICAgICAgICAgICAgICAgICAgIG9uTG9hZE1vcmU9e2hhbmRsZVRlYW1Mb2FkTW9yZX1cbiAgICAgICAgICAgICAgICAgICAgaGFzTW9yZT17dGVhbXM/Lm1ldGEgPyB0ZWFtUGFnZSA8IHRlYW1zLm1ldGEudG90YWxQYWdlcyA6IGZhbHNlfVxuICAgICAgICAgICAgICAgICAgICBpc0xvYWRpbmc9e3RlYW1zTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBMZWFndWUgU2VsZWN0aW9uIC0gSW5saW5lIExheW91dCAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTIgcC0yIGJnLWJsdWUtMTAwIGJvcmRlciBib3JkZXItYmx1ZS0zMDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgPHN0cm9uZz5ERUJVRzo8L3N0cm9uZz4gTGVhZ3VlIC0ge3NlbGVjdGVkTGVhZ3VlID8gYEZvdW5kOiAke3NlbGVjdGVkTGVhZ3VlLmxhYmVsfWAgOiAnTm90IGZvdW5kJ31cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8TGVhZ3VlSW5saW5lU2VsZWN0b3IgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0Zvcm1TZWN0aW9uPlxuXG4gICAgICAgICAgICA8Rm9ybVNlY3Rpb24gdGl0bGU9XCJTY2hlZHVsZVwiIGRlc2NyaXB0aW9uPVwiU2V0IHRoZSBkYXRlIGFuZCB0aW1lIChsb2NhbCB0aW1lem9uZSlcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiRGF0ZSAqXCJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZGF0ZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2RhdGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmRhdGV9XG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbj1cIk1hdGNoIGRhdGVcIlxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJUaW1lICpcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRpbWVcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50aW1lfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgndGltZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMudGltZX1cbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPXtgTG9jYWwgdGltZSAoJHtJbnRsLkRhdGVUaW1lRm9ybWF0KCkucmVzb2x2ZWRPcHRpb25zKCkudGltZVpvbmV9KWB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgYmctYmx1ZS01MCBwLTMgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgbXItMlwiPuKEue+4jzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzdHJvbmc+VGltZXpvbmUgSW5mbzo8L3N0cm9uZz4gVGltZXMgYXJlIGRpc3BsYXllZCBpbiB5b3VyIGxvY2FsIHRpbWV6b25lICh7SW50bC5EYXRlVGltZUZvcm1hdCgpLnJlc29sdmVkT3B0aW9ucygpLnRpbWVab25lfSkuXG4gICAgICAgICAgICAgICAgICBUaGUgYXN0ZXJpc2sgKCopIGluZGljYXRlcyByZXF1aXJlZCBmaWVsZHMuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvRm9ybVNlY3Rpb24+XG5cbiAgICAgICAgICAgIDxGb3JtU2VjdGlvbiB0aXRsZT1cIk1hdGNoIFN0YXR1c1wiIGRlc2NyaXB0aW9uPVwiVXBkYXRlIG1hdGNoIHN0YXR1cyBhbmQgc2NvcmVcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPFNlbGVjdEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlN0YXR1c1wiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjdCBzdGF0dXNcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5zdGF0dXN9XG4gICAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IHVwZGF0ZUZvcm1EYXRhKCdzdGF0dXMnLCB2YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtzdGF0dXNPcHRpb25zfVxuICAgICAgICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5zdGF0dXN9XG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIkhvbWUgR29hbHNcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5nb2Fsc0hvbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdnb2Fsc0hvbWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIkF3YXkgR29hbHNcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5nb2Fsc0F3YXl9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdnb2Fsc0F3YXknLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkVsYXBzZWQgVGltZSAobWludXRlcylcIlxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgIG1heD1cIjEyMFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmVsYXBzZWR9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnZWxhcHNlZCcsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbj1cIk1pbnV0ZXMgcGxheWVkIGluIHRoZSBtYXRjaFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L0Zvcm1TZWN0aW9uPlxuXG4gICAgICAgICAgICA8Rm9ybVNlY3Rpb24gdGl0bGU9XCJWZW51ZSAmIE1hdGNoIEluZm9ybWF0aW9uXCIgZGVzY3JpcHRpb249XCJWZW51ZSBkZXRhaWxzIGFuZCBtYXRjaCBjb250ZXh0XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlZlbnVlIE5hbWVcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTdGFkaXVtIG5hbWVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnZlbnVlTmFtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3ZlbnVlTmFtZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiVmVudWUgQ2l0eVwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNpdHlcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnZlbnVlQ2l0eX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3ZlbnVlQ2l0eScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJSb3VuZFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIE1hdGNoZGF5IDEsIFF1YXJ0ZXItZmluYWxcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnJvdW5kfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgncm91bmQnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlJlZmVyZWVcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJSZWZlcmVlIG5hbWVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnJlZmVyZWUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdyZWZlcmVlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlRlbXBlcmF0dXJlICjCsEMpXCJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCAyMlwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudGVtcGVyYXR1cmUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCd0ZW1wZXJhdHVyZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiV2VhdGhlclwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIFN1bm55LCBSYWlueVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEud2VhdGhlciB8fCAnJ31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3dlYXRoZXInLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIkF0dGVuZGFuY2VcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk51bWJlciBvZiBzcGVjdGF0b3JzXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5hdHRlbmRhbmNlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnYXR0ZW5kYW5jZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvRm9ybVNlY3Rpb24+XG5cbiAgICAgICAgICAgIDxGb3JtQWN0aW9ucz5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIuYmFjaygpfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXt1cGRhdGVNdXRhdGlvbi5pc1BlbmRpbmd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17dXBkYXRlTXV0YXRpb24uaXNQZW5kaW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFNhdmUgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICB7dXBkYXRlTXV0YXRpb24uaXNQZW5kaW5nID8gJ1VwZGF0aW5nLi4uJyA6ICdVcGRhdGUgRml4dHVyZSd9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9Gb3JtQWN0aW9ucz5cbiAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VQYXJhbXMiLCJ1c2VSb3V0ZXIiLCJ1c2VNdXRhdGlvbiIsInVzZVF1ZXJ5IiwidXNlUXVlcnlDbGllbnQiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiSW5wdXRGaWVsZCIsIlNlbGVjdEZpZWxkIiwiRm9ybVNlY3Rpb24iLCJGb3JtQWN0aW9ucyIsIlNlYXJjaGFibGVTZWxlY3RGaWVsZCIsIlNrZWxldG9uIiwiZml4dHVyZXNBcGkiLCJsZWFndWVzQXBpIiwidGVhbXNBcGkiLCJBcnJvd0xlZnQiLCJTYXZlIiwiQ2FsZW5kYXIiLCJ0b2FzdCIsIkVkaXRGaXh0dXJlUGFnZSIsImxlYWd1ZXMiLCJ0ZWFtcyIsInBhcmFtcyIsInJvdXRlciIsInF1ZXJ5Q2xpZW50IiwiZml4dHVyZUlkIiwicGFyc2VJbnQiLCJpZCIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJob21lVGVhbUlkIiwiYXdheVRlYW1JZCIsImxlYWd1ZUlkIiwiZGF0ZSIsInRpbWUiLCJ2ZW51ZU5hbWUiLCJ2ZW51ZUNpdHkiLCJyb3VuZCIsInN0YXR1cyIsImdvYWxzSG9tZSIsImdvYWxzQXdheSIsImVsYXBzZWQiLCJlcnJvcnMiLCJzZXRFcnJvcnMiLCJkYXRhIiwiZml4dHVyZSIsImlzTG9hZGluZyIsImZpeHR1cmVMb2FkaW5nIiwicXVlcnlLZXkiLCJxdWVyeUZuIiwiZ2V0Rml4dHVyZSIsImVuYWJsZWQiLCJsZWFndWVTZWFyY2giLCJzZXRMZWFndWVTZWFyY2giLCJ0ZWFtU2VhcmNoIiwic2V0VGVhbVNlYXJjaCIsImxlYWd1ZVBhZ2UiLCJzZXRMZWFndWVQYWdlIiwidGVhbVBhZ2UiLCJzZXRUZWFtUGFnZSIsImxlYWd1ZXNMb2FkaW5nIiwiZXJyb3IiLCJsZWFndWVzRXJyb3IiLCJnZXRMZWFndWVzIiwibGltaXQiLCJwYWdlIiwic2VhcmNoIiwia2VlcFByZXZpb3VzRGF0YSIsInRlYW1zTG9hZGluZyIsInRlYW1zRXJyb3IiLCJnZXRUZWFtcyIsInVwZGF0ZU11dGF0aW9uIiwibXV0YXRpb25GbiIsInVwZGF0ZUZpeHR1cmUiLCJvblN1Y2Nlc3MiLCJpbnZhbGlkYXRlUXVlcmllcyIsInN1Y2Nlc3MiLCJwdXNoIiwib25FcnJvciIsIm1lc3NhZ2UiLCJmaXh0dXJlRGF0ZSIsIkRhdGUiLCJjb25zb2xlIiwibG9nIiwidmVudWUiLCJ0b1N0cmluZyIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJ0b1RpbWVTdHJpbmciLCJzbGljZSIsIm5hbWUiLCJjaXR5IiwicmVmZXJlZSIsInRlbXBlcmF0dXJlIiwid2VhdGhlciIsImF0dGVuZGFuY2UiLCJ2YWxpZGF0ZUZvcm0iLCJuZXdFcnJvcnMiLCJPYmplY3QiLCJrZXlzIiwibGVuZ3RoIiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwiZGF0ZVRpbWUiLCJzdWJtaXREYXRhIiwibXV0YXRlIiwidXBkYXRlRm9ybURhdGEiLCJmaWVsZCIsInZhbHVlIiwicHJldiIsInVuZGVmaW5lZCIsImhhbmRsZUxlYWd1ZVNlYXJjaCIsInF1ZXJ5IiwiaGFuZGxlVGVhbVNlYXJjaCIsImhhbmRsZUxlYWd1ZUxvYWRNb3JlIiwibWV0YSIsInRvdGFsUGFnZXMiLCJoYW5kbGVUZWFtTG9hZE1vcmUiLCJzdGF0dXNPcHRpb25zIiwibGFiZWwiLCJsZWFndWVPcHRpb25zIiwibWFwIiwibGVhZ3VlIiwiZXh0ZXJuYWxJZCIsInNlYXNvbiIsImxvZ28iLCJ0ZWFtT3B0aW9ucyIsInRlYW0iLCJ0ZWFtT3B0aW9uc0NvdW50IiwibGVhZ3VlT3B0aW9uc0NvdW50IiwiZm9ybURhdGFSZWFkeSIsImdldFByZXZpZXdEYXRhIiwiaG9tZVRlYW0iLCJhd2F5VGVhbSIsImlzSW5pdGlhbERhdGEiLCJsZWFndWVOYW1lIiwiaG9tZVRlYW1OYW1lIiwiaG9tZVRlYW1Mb2dvIiwiYXdheVRlYW1OYW1lIiwiYXdheVRlYW1Mb2dvIiwiZmluZCIsImwiLCJ0Iiwic2VsZWN0ZWRMZWFndWUiLCJzZWxlY3RlZEhvbWVUZWFtIiwic2VsZWN0ZWRBd2F5VGVhbSIsInByZXZpZXdLZXkiLCJzZXRQcmV2aWV3S2V5IiwiaG9tZSIsImF3YXkiLCJ0eXBlIiwiaG9tZVRlYW1Jbk9wdGlvbnMiLCJhd2F5VGVhbUluT3B0aW9ucyIsImxlYWd1ZUluT3B0aW9ucyIsImhvbWVFeGlzdHMiLCJzb21lIiwiYXdheUV4aXN0cyIsImxlYWd1ZUV4aXN0cyIsIlNlbGVjdGVkVmFsdWVQcmV2aWV3Iiwic2VsZWN0ZWRPcHRpb24iLCJwbGFjZWhvbGRlciIsIkNETl9VUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfRE9NQUlOX0NETl9QSUNUVVJFIiwiaGFzT3B0aW9uIiwiZGl2IiwiY2xhc3NOYW1lIiwiaW1nIiwic3JjIiwiYWx0IiwiY3VycmVudFRhcmdldCIsInN0eWxlIiwiZGlzcGxheSIsInNwYW4iLCJMZWFndWVJbmxpbmVTZWxlY3RvciIsImhhc0xlYWd1ZSIsInNlYXJjaFBsYWNlaG9sZGVyIiwicmVxdWlyZWQiLCJvblZhbHVlQ2hhbmdlIiwib3B0aW9ucyIsImRpc2FibGVkIiwib25TZWFyY2giLCJvbkxvYWRNb3JlIiwiaGFzTW9yZSIsImlzRGF0YUxvYWRpbmciLCJ2YXJpYW50Iiwib25DbGljayIsImJhY2siLCJwIiwiaDEiLCJmb3JtIiwib25TdWJtaXQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwic3Ryb25nIiwiZmlsdGVyIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJJbnRsIiwiRGF0ZVRpbWVGb3JtYXQiLCJyZXNvbHZlZE9wdGlvbnMiLCJ0aW1lWm9uZSIsIm1pbiIsIm1heCIsImlzUGVuZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});