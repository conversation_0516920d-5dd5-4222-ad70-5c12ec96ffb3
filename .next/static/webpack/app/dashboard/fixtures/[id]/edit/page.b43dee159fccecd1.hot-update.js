"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/lib/api/teams.ts":
/*!******************************!*\
  !*** ./src/lib/api/teams.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   teamsApi: function() { return /* binding */ teamsApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst teamsApi = {\n    // Use Next.js API proxy (similar to leagues)\n    getTeams: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        // Get token from auth store for authorization\n        const getAuthHeaders = ()=>{\n            if (true) {\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            return {\n                                \"Content-Type\": \"application/json\",\n                                \"Authorization\": \"Bearer \".concat(token)\n                            };\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    return {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(fallbackToken)\n                    };\n                }\n            }\n            return {\n                \"Content-Type\": \"application/json\"\n            };\n        };\n        const response = await fetch(\"/api/teams?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch teams\");\n        }\n        return await response.json();\n    },\n    // Requires authentication\n    getTeamById: async (externalId)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/teams/\".concat(externalId));\n        return response;\n    },\n    // Requires authentication\n    getTeamStatistics: async (league, season, team)=>{\n        const params = new URLSearchParams({\n            league: league.toString(),\n            season: season.toString(),\n            team: team.toString()\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/teams/statistics?\".concat(params.toString()));\n        return response;\n    },\n    // Helper methods for common operations\n    getTeamsByLeague: async (league, season)=>{\n        const filters = {\n            league\n        };\n        if (season) filters.season = season;\n        return teamsApi.getTeams(filters);\n    },\n    getTeamsByCountry: async (country)=>{\n        return teamsApi.getTeams({\n            country\n        });\n    },\n    searchTeams: async function(query) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        // Note: This would need to be implemented on the backend\n        // For now, we'll get all teams and filter client-side (not ideal for production)\n        const teams = await teamsApi.getTeams(filters);\n        const filteredTeams = teams.data.filter((team)=>{\n            var _team_code;\n            return team.name.toLowerCase().includes(query.toLowerCase()) || ((_team_code = team.code) === null || _team_code === void 0 ? void 0 : _team_code.toLowerCase().includes(query.toLowerCase()));\n        });\n        return {\n            data: filteredTeams,\n            meta: {\n                ...teams.meta,\n                totalItems: filteredTeams.length,\n                totalPages: Math.ceil(filteredTeams.length / (filters.limit || 10))\n            }\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/teams.ts\n"));

/***/ })

});