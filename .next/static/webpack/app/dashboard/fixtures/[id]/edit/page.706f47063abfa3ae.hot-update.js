"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Smart loading with search and pagination\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teamSearch, setTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leaguePage, setLeaguePage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [teamPage, setTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [allLeagues, setAllLeagues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allTeams, setAllTeams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues with search and pagination\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"search\",\n            leagueSearch,\n            leaguePage\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100,\n                page: leaguePage,\n                ...leagueSearch && {\n                    search: leagueSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch teams with search and pagination\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"search\",\n            teamSearch,\n            teamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: teamPage,\n                ...teamSearch && {\n                    search: teamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Accumulate leagues data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (leagues === null || leagues === void 0 ? void 0 : leagues.data) {\n            if (leaguePage === 1) {\n                // New search or first load - replace data\n                setAllLeagues(leagues.data);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                setAllLeagues((prev)=>{\n                    const existingIds = new Set(prev.map((l)=>l.externalId));\n                    const newLeagues = leagues.data.filter((l)=>!existingIds.has(l.externalId));\n                    return [\n                        ...prev,\n                        ...newLeagues\n                    ];\n                });\n            }\n        }\n    }, [\n        leagues,\n        leaguePage\n    ]);\n    // Accumulate teams data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (teams === null || teams === void 0 ? void 0 : teams.data) {\n            if (teamPage === 1) {\n                // New search or first load - replace data\n                setAllTeams(teams.data);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                setAllTeams((prev)=>{\n                    const existingIds = new Set(prev.map((t)=>t.id || t.externalId));\n                    const newTeams = teams.data.filter((t)=>!existingIds.has(t.id || t.externalId));\n                    console.log(\"\\uD83D\\uDD04 Adding new teams:\", {\n                        existing: prev.length,\n                        new: newTeams.length,\n                        total: prev.length + newTeams.length\n                    });\n                    return [\n                        ...prev,\n                        ...newTeams\n                    ];\n                });\n            }\n        }\n    }, [\n        teams,\n        teamPage\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            // 🔍 DEBUG: Check if fixture uses internal or external IDs\n            console.log(\"\\uD83D\\uDD0D FIXTURE ID ANALYSIS:\", {\n                \"fixture.homeTeamId\": fixture.homeTeamId,\n                \"fixture.awayTeamId\": fixture.awayTeamId,\n                \"fixture.leagueId\": fixture.leagueId,\n                \"fixture object\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search and pagination handlers\n    const handleLeagueSearch = (query)=>{\n        setLeagueSearch(query);\n        setLeaguePage(1); // Reset to first page on new search\n    };\n    const handleTeamSearch = (query)=>{\n        setTeamSearch(query);\n        setTeamPage(1); // Reset to first page on new search\n    };\n    const handleLeagueLoadMore = ()=>{\n        var _leagues_meta, _leagues_meta1, _leagues_meta2;\n        console.log(\"\\uD83D\\uDD04 League Load More:\", {\n            currentPage: leaguePage,\n            totalPages: leagues === null || leagues === void 0 ? void 0 : (_leagues_meta = leagues.meta) === null || _leagues_meta === void 0 ? void 0 : _leagues_meta.totalPages,\n            hasMore: (leagues === null || leagues === void 0 ? void 0 : (_leagues_meta1 = leagues.meta) === null || _leagues_meta1 === void 0 ? void 0 : _leagues_meta1.totalPages) && leaguePage < leagues.meta.totalPages\n        });\n        if ((leagues === null || leagues === void 0 ? void 0 : (_leagues_meta2 = leagues.meta) === null || _leagues_meta2 === void 0 ? void 0 : _leagues_meta2.totalPages) && leaguePage < leagues.meta.totalPages) {\n            setLeaguePage((prev)=>prev + 1);\n        }\n    };\n    const handleTeamLoadMore = ()=>{\n        var _teams_meta, _teams_meta1, _teams_meta2;\n        console.log(\"\\uD83D\\uDD04 Team Load More:\", {\n            currentPage: teamPage,\n            totalPages: teams === null || teams === void 0 ? void 0 : (_teams_meta = teams.meta) === null || _teams_meta === void 0 ? void 0 : _teams_meta.totalPages,\n            hasMore: (teams === null || teams === void 0 ? void 0 : (_teams_meta1 = teams.meta) === null || _teams_meta1 === void 0 ? void 0 : _teams_meta1.totalPages) && teamPage < teams.meta.totalPages\n        });\n        if ((teams === null || teams === void 0 ? void 0 : (_teams_meta2 = teams.meta) === null || _teams_meta2 === void 0 ? void 0 : _teams_meta2.totalPages) && teamPage < teams.meta.totalPages) {\n            setTeamPage((prev)=>prev + 1);\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        {\n            value: \"FT\",\n            label: \"Match Finished\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Use accumulated data for options with unique keys\n    const leagueOptions = allLeagues.map((league, index)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season,\n            uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n        }));\n    const teamOptions = allTeams.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }));\n    // Debug options count\n    console.log(\"\\uD83D\\uDCCA OPTIONS DEBUG:\", {\n        allLeaguesCount: allLeagues.length,\n        allTeamsCount: allTeams.length,\n        leagueOptionsCount: leagueOptions.length,\n        teamOptionsCount: teamOptions.length,\n        leagueMeta: leagues === null || leagues === void 0 ? void 0 : leagues.meta,\n        teamMeta: teams === null || teams === void 0 ? void 0 : teams.meta\n    });\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            teamOptionsCount: teamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        teamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Smart preview logic - use fixture data initially, then update from selections\n    const getPreviewData = ()=>{\n        var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId;\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // For initial load, use fixture data\n        const isInitialData = formData.homeTeamId === ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) && formData.awayTeamId === ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) && formData.leagueId === ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString());\n        if (isInitialData) {\n            return {\n                league: {\n                    value: formData.leagueId,\n                    label: fixture.leagueName,\n                    logo: \"\" // Will be updated when user selects from dropdown\n                },\n                homeTeam: {\n                    value: formData.homeTeamId,\n                    label: fixture.homeTeamName,\n                    logo: fixture.homeTeamLogo\n                },\n                awayTeam: {\n                    value: formData.awayTeamId,\n                    label: fixture.awayTeamName,\n                    logo: fixture.awayTeamLogo\n                }\n            };\n        }\n        // For user selections, use dropdown options\n        return {\n            league: leagueOptions.find((l)=>l.value === formData.leagueId) || null,\n            homeTeam: teamOptions.find((t)=>t.value === formData.homeTeamId) || null,\n            awayTeam: teamOptions.find((t)=>t.value === formData.awayTeamId) || null\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        teams: teamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dropdownKey, setDropdownKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        teamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Force dropdown re-render when options change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDropdownKey((prev)=>prev + 1);\n    }, [\n        teamOptions.length,\n        leagueOptions.length\n    ]);\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (teamOptions.length > 0) {\n        console.log(\"Sample Team Options:\", teamOptions.slice(0, 3));\n        console.log(\"Team Option Types:\", teamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const homeTeamInOptions = teamOptions.find((t)=>t.value === \"4450\");\n        const awayTeamInOptions = teamOptions.find((t)=>t.value === \"20022\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC TEAMS CHECK:\", {\n            \"homeTeam (4450)\": homeTeamInOptions,\n            \"awayTeam (20022)\": awayTeamInOptions,\n            \"totalTeams\": teamOptions.length\n        });\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n        // 🔍 Check if our specific league exists\n        const leagueInOptions = leagueOptions.find((l)=>l.value === \"570\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC LEAGUE CHECK:\", {\n            \"league (570)\": leagueInOptions,\n            \"totalLeagues\": leagueOptions.length\n        });\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: teamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: teamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 472,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            onLoadMore: handleLeagueLoadMore,\n                            hasMore: (leagues === null || leagues === void 0 ? void 0 : leagues.meta) ? leaguePage < leagues.meta.totalPages : false,\n                            isLoading: leaguesLoading\n                        }, \"league-\".concat(dropdownKey), false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 510,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 567,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 617,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 609,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 636,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 651,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: teamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleTeamSearch,\n                                                            onLoadMore: handleTeamLoadMore,\n                                                            hasMore: (teams === null || teams === void 0 ? void 0 : teams.meta) ? teamPage < teams.meta.totalPages : false,\n                                                            isLoading: teamsLoading\n                                                        }, \"home-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleTeamSearch,\n                                                            onLoadMore: handleTeamLoadMore,\n                                                            hasMore: (teams === null || teams === void 0 ? void 0 : teams.meta) ? teamPage < teams.meta.totalPages : false,\n                                                            isLoading: teamsLoading\n                                                        }, \"away-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 722,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 795,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 828,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 650,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 634,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"hNBsXHYUH1+djF2xv4nlJSCWsnk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});