"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _leagues_data, _teams_data;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Fetch leagues for dropdown\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams for dropdown\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_9__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Half Time\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"FT\",\n            label: \"Full Time\"\n        },\n        {\n            value: \"PST\",\n            label: \"Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Cancelled\"\n        }\n    ];\n    const leagueOptions = (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league)=>({\n            value: league.id.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season\n        }))) || [];\n    const teamOptions = (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team)=>({\n            value: team.id.toString(),\n            label: team.name,\n            logo: team.logo\n        }))) || [];\n    // Get selected options for preview\n    const selectedLeague = leagueOptions.find((l)=>l.value === formData.leagueId);\n    const selectedHomeTeam = teamOptions.find((t)=>t.value === formData.homeTeamId);\n    const selectedAwayTeam = teamOptions.find((t)=>t.value === formData.awayTeamId);\n    // Debug logging\n    console.log(\"\\uD83D\\uDD0D Form Debug:\", {\n        formData: {\n            homeTeamId: formData.homeTeamId,\n            awayTeamId: formData.awayTeamId,\n            leagueId: formData.leagueId\n        },\n        fixture: {\n            homeTeamId: fixture === null || fixture === void 0 ? void 0 : fixture.homeTeamId,\n            awayTeamId: fixture === null || fixture === void 0 ? void 0 : fixture.awayTeamId,\n            leagueId: fixture === null || fixture === void 0 ? void 0 : fixture.leagueId,\n            homeTeamName: fixture === null || fixture === void 0 ? void 0 : fixture.homeTeamName,\n            awayTeamName: fixture === null || fixture === void 0 ? void 0 : fixture.awayTeamName,\n            leagueName: fixture === null || fixture === void 0 ? void 0 : fixture.leagueName\n        },\n        options: {\n            leagueOptionsCount: leagueOptions.length,\n            teamOptionsCount: teamOptions.length\n        },\n        selected: {\n            selectedLeague,\n            selectedHomeTeam,\n            selectedAwayTeam\n        }\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 342,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 384,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: teamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 409,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"lCYwpocHED69IdHfB6HyS5m83TU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});