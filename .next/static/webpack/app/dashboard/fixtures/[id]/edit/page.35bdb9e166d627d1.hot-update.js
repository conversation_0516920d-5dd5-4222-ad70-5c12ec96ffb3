"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Search states\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearchResults, setHomeTeamSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [awayTeamSearch, setAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [awayTeamSearchResults, setAwayTeamSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues without search initially\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100\n            })\n    });\n    // Fetch teams without search initially\n    const { data: teams, isLoading: teamsLoading, error: teamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"teams\"\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100\n            })\n    });\n    // Home Team Search with 3s debounce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!homeTeamSearch.trim()) {\n            setHomeTeamSearchResults([]);\n            return;\n        }\n        const timer = setTimeout(async ()=>{\n            try {\n                const searchResults = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                    limit: 100,\n                    search: homeTeamSearch\n                });\n                if (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data) {\n                    setHomeTeamSearchResults(searchResults.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Home team search error:\", error);\n                setHomeTeamSearchResults([]);\n            }\n        }, 3000); // 3 second debounce\n        return ()=>clearTimeout(timer);\n    }, [\n        homeTeamSearch\n    ]);\n    // Away Team Search with 3s debounce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!awayTeamSearch.trim()) {\n            setAwayTeamSearchResults([]);\n            return;\n        }\n        const timer = setTimeout(async ()=>{\n            try {\n                const searchResults = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                    limit: 100,\n                    search: awayTeamSearch\n                });\n                if (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data) {\n                    setAwayTeamSearchResults(searchResults.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Away team search error:\", error);\n                setAwayTeamSearchResults([]);\n            }\n        }, 3000); // 3 second debounce\n        return ()=>clearTimeout(timer);\n    }, [\n        awayTeamSearch\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search handlers with useCallback to prevent re-renders\n    const handleLeagueSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setLeagueSearch(query);\n    }, []);\n    const handleHomeTeamSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setHomeTeamSearch(query);\n    }, []);\n    const handleAwayTeamSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((query)=>{\n        setAwayTeamSearch(query);\n    }, []);\n    // Status options - API Football Official Documentation + Additional API Values\n    // Source: https://www.api-football.com/documentation-v3#tag/Fixtures/operation/get-fixtures\n    const statusOptions = [\n        // Time Status\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"ST\",\n            label: \"Scheduled\"\n        },\n        // Match Status - Live\n        {\n            value: \"1H\",\n            label: \"First Half, Kick Off\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half, 2nd Half Started\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time (in Extra Time)\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"LIVE\",\n            label: \"In Progress\"\n        },\n        // Match Finished\n        {\n            value: \"FT\",\n            label: \"Match Finished (Regular Time)\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        // Match Suspended\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        // Match Postponed\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        // Match Cancelled\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        // Match Abandoned\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        // Technical Loss\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        // Walk Over\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Memoize options to prevent re-renders\n    const leagueOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _leagues_data;\n        return (leagues === null || leagues === void 0 ? void 0 : (_leagues_data = leagues.data) === null || _leagues_data === void 0 ? void 0 : _leagues_data.map((league, index)=>({\n                value: league.externalId.toString(),\n                label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n                logo: league.logo,\n                season: league.season,\n                uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n            }))) || [];\n    }, [\n        leagues === null || leagues === void 0 ? void 0 : leagues.data\n    ]);\n    const teamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _teams_data;\n        return (teams === null || teams === void 0 ? void 0 : (_teams_data = teams.data) === null || _teams_data === void 0 ? void 0 : _teams_data.map((team, index)=>({\n                value: team.externalId.toString(),\n                label: team.name,\n                logo: team.logo,\n                uniqueKey: \"team-\".concat(team.id || team.externalId, \"-\").concat(index)\n            }))) || [];\n    }, [\n        teams === null || teams === void 0 ? void 0 : teams.data\n    ]);\n    // Home team options: replace with search results when searching, otherwise use initial teams\n    const homeTeamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // If we have search results, use ONLY search results (replace mode)\n        if (homeTeamSearchResults.length > 0) {\n            return homeTeamSearchResults.map((team, index)=>({\n                    value: team.externalId.toString(),\n                    label: team.name,\n                    logo: team.logo,\n                    uniqueKey: \"search-home-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n                }));\n        }\n        // If no search results, use initial teams (default mode)\n        return teamOptions;\n    }, [\n        teamOptions,\n        homeTeamSearchResults\n    ]);\n    // Away team options: replace with search results when searching, otherwise use initial teams\n    const awayTeamOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // If we have search results, use ONLY search results (replace mode)\n        if (awayTeamSearchResults.length > 0) {\n            return awayTeamSearchResults.map((team, index)=>({\n                    value: team.externalId.toString(),\n                    label: team.name,\n                    logo: team.logo,\n                    uniqueKey: \"search-away-team-\".concat(team.id || team.externalId, \"-\").concat(index)\n                }));\n        }\n        // If no search results, use initial teams (default mode)\n        return teamOptions;\n    }, [\n        teamOptions,\n        awayTeamSearchResults\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = homeTeamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = awayTeamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (homeTeamOptions.length > 0 && awayTeamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        homeTeamOptions.length,\n        awayTeamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 408,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            isLoading: leaguesLoading\n                        }, \"league-search-stable\", false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 443,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 499,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 498,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 542,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 541,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 28\n                                }, this),\n                                !!leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load leagues\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 34\n                                }, this),\n                                !!teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load teams\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 32\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 540,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 567,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams... (3s delay)\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: homeTeamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleHomeTeamSearch,\n                                                            isLoading: teamsLoading\n                                                        }, \"home-team-search-stable\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams... (3s delay)\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: awayTeamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleAwayTeamSearch,\n                                                            isLoading: teamsLoading\n                                                        }, \"away-team-search-stable\", false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isLoading ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 581,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 565,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"cap6ng2akNT9tOqKbDdXay6qyyw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});