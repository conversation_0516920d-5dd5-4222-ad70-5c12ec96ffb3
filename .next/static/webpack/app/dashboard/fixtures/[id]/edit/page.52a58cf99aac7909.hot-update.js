"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/edit/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditFixturePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/SearchableSelectField */ \"(app-pages-browser)/./src/components/ui/SearchableSelectField.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditFixturePage() {\n    var _teams, _teams1, _teams2;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient)();\n    const fixtureId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        homeTeamId: \"\",\n        awayTeamId: \"\",\n        leagueId: \"\",\n        date: \"\",\n        time: \"\",\n        venueName: \"\",\n        venueCity: \"\",\n        round: \"\",\n        status: \"\",\n        goalsHome: \"\",\n        goalsAway: \"\",\n        elapsed: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch fixture details\n    const { data: fixture, isLoading: fixtureLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Smart loading with search and pagination - SEPARATED for Home/Away\n    const [leagueSearch, setLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [homeTeamSearch, setHomeTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [awayTeamSearch, setAwayTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leaguePage, setLeaguePage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [homeTeamPage, setHomeTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [awayTeamPage, setAwayTeamPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [allLeagues, setAllLeagues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allHomeTeams, setAllHomeTeams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAwayTeams, setAllAwayTeams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch leagues with search and pagination\n    const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"search\",\n            leagueSearch,\n            leaguePage\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_9__.leaguesApi.getLeagues({\n                limit: 100,\n                page: leaguePage,\n                ...leagueSearch && {\n                    search: leagueSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch home teams with search and pagination\n    const { data: homeTeams, isLoading: homeTeamsLoading, error: homeTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"home-teams\",\n            \"search\",\n            homeTeamSearch,\n            homeTeamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: homeTeamPage,\n                ...homeTeamSearch && {\n                    search: homeTeamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Fetch away teams with search and pagination\n    const { data: awayTeams, isLoading: awayTeamsLoading, error: awayTeamsError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            \"away-teams\",\n            \"search\",\n            awayTeamSearch,\n            awayTeamPage\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.getTeams({\n                limit: 100,\n                page: awayTeamPage,\n                ...awayTeamSearch && {\n                    search: awayTeamSearch\n                }\n            }),\n        keepPreviousData: true\n    });\n    // Track previous search to detect search changes\n    const [prevLeagueSearch, setPrevLeagueSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [prevTeamSearch, setPrevTeamSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Accumulate leagues data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (leagues === null || leagues === void 0 ? void 0 : leagues.data) {\n            // Check if search changed\n            const searchChanged = leagueSearch !== prevLeagueSearch;\n            if (leaguePage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"\\uD83D\\uDD04 Replacing leagues data:\", {\n                    page: leaguePage,\n                    searchChanged,\n                    search: leagueSearch\n                });\n                setAllLeagues(leagues.data);\n                setPrevLeagueSearch(leagueSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"\\uD83D\\uDD04 Appending leagues data:\", {\n                    page: leaguePage,\n                    existing: allLeagues.length,\n                    new: leagues.data.length\n                });\n                setAllLeagues((prev)=>{\n                    const existingIds = new Set(prev.map((l)=>l.externalId));\n                    const newLeagues = leagues.data.filter((l)=>!existingIds.has(l.externalId));\n                    return [\n                        ...prev,\n                        ...newLeagues\n                    ];\n                });\n            }\n        }\n    }, [\n        leagues,\n        leaguePage,\n        leagueSearch,\n        prevLeagueSearch,\n        allLeagues.length\n    ]);\n    // Accumulate teams data for load more functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _teams;\n        if ((_teams = teams) === null || _teams === void 0 ? void 0 : _teams.data) {\n            // Check if search changed\n            const searchChanged = teamSearch !== prevTeamSearch;\n            if (teamPage === 1 || searchChanged) {\n                // New search or first load - replace data\n                console.log(\"\\uD83D\\uDD04 Replacing teams data:\", {\n                    page: teamPage,\n                    searchChanged,\n                    search: teamSearch\n                });\n                setAllTeams(teams.data);\n                setPrevTeamSearch(teamSearch);\n            } else {\n                // Load more - append to existing, avoid duplicates\n                console.log(\"\\uD83D\\uDD04 Appending teams data:\", {\n                    page: teamPage,\n                    existing: allTeams.length,\n                    new: teams.data.length\n                });\n                setAllTeams((prev)=>{\n                    const existingIds = new Set(prev.map((t)=>t.id || t.externalId));\n                    const newTeams = teams.data.filter((t)=>!existingIds.has(t.id || t.externalId));\n                    console.log(\"\\uD83D\\uDD04 Adding new teams:\", {\n                        existing: prev.length,\n                        new: newTeams.length,\n                        total: prev.length + newTeams.length\n                    });\n                    return [\n                        ...prev,\n                        ...newTeams\n                    ];\n                });\n            }\n        }\n    }, [\n        teams,\n        teamPage,\n        teamSearch,\n        prevTeamSearch,\n        allTeams.length\n    ]);\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: (data)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_8__.fixturesApi.updateFixture(fixtureId, data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixture\",\n                    fixtureId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Fixture updated successfully\");\n            router.push(\"/dashboard/fixtures/\".concat(fixtureId));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message || \"Failed to update fixture\");\n        }\n    });\n    // Populate form when fixture data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fixture) {\n            var _fixture_homeTeamId, _fixture_awayTeamId, _fixture_leagueId, _fixture_venue, _fixture_venue1, _fixture_goalsHome, _fixture_goalsAway, _fixture_elapsed, _fixture_temperature, _fixture_attendance;\n            const fixtureDate = new Date(fixture.date);\n            console.log(\"\\uD83D\\uDD04 Populating form with fixture data:\", {\n                homeTeamId: fixture.homeTeamId,\n                awayTeamId: fixture.awayTeamId,\n                leagueId: fixture.leagueId,\n                venue: fixture.venue,\n                venueName: fixture.venueName,\n                venueCity: fixture.venueCity\n            });\n            // 🔍 DEBUG: Check if fixture uses internal or external IDs\n            console.log(\"\\uD83D\\uDD0D FIXTURE ID ANALYSIS:\", {\n                \"fixture.homeTeamId\": fixture.homeTeamId,\n                \"fixture.awayTeamId\": fixture.awayTeamId,\n                \"fixture.leagueId\": fixture.leagueId,\n                \"fixture object\": fixture\n            });\n            setFormData({\n                homeTeamId: ((_fixture_homeTeamId = fixture.homeTeamId) === null || _fixture_homeTeamId === void 0 ? void 0 : _fixture_homeTeamId.toString()) || \"\",\n                awayTeamId: ((_fixture_awayTeamId = fixture.awayTeamId) === null || _fixture_awayTeamId === void 0 ? void 0 : _fixture_awayTeamId.toString()) || \"\",\n                leagueId: ((_fixture_leagueId = fixture.leagueId) === null || _fixture_leagueId === void 0 ? void 0 : _fixture_leagueId.toString()) || \"\",\n                date: fixtureDate.toISOString().split(\"T\")[0],\n                time: fixtureDate.toTimeString().slice(0, 5),\n                // Fix venue mapping - API returns venue.name and venue.city\n                venueName: ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.name) || fixture.venueName || \"\",\n                venueCity: ((_fixture_venue1 = fixture.venue) === null || _fixture_venue1 === void 0 ? void 0 : _fixture_venue1.city) || fixture.venueCity || \"\",\n                round: fixture.round || \"\",\n                status: fixture.status || \"\",\n                goalsHome: ((_fixture_goalsHome = fixture.goalsHome) === null || _fixture_goalsHome === void 0 ? void 0 : _fixture_goalsHome.toString()) || \"\",\n                goalsAway: ((_fixture_goalsAway = fixture.goalsAway) === null || _fixture_goalsAway === void 0 ? void 0 : _fixture_goalsAway.toString()) || \"\",\n                elapsed: ((_fixture_elapsed = fixture.elapsed) === null || _fixture_elapsed === void 0 ? void 0 : _fixture_elapsed.toString()) || \"\",\n                referee: fixture.referee || \"\",\n                temperature: ((_fixture_temperature = fixture.temperature) === null || _fixture_temperature === void 0 ? void 0 : _fixture_temperature.toString()) || \"\",\n                weather: fixture.weather || \"\",\n                attendance: ((_fixture_attendance = fixture.attendance) === null || _fixture_attendance === void 0 ? void 0 : _fixture_attendance.toString()) || \"\"\n            });\n        }\n    }, [\n        fixture\n    ]);\n    // Move this useEffect after teamOptions and leagueOptions are defined\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.homeTeamId) newErrors.homeTeamId = \"Home team is required\";\n        if (!formData.awayTeamId) newErrors.awayTeamId = \"Away team is required\";\n        if (!formData.leagueId) newErrors.leagueId = \"League is required\";\n        if (!formData.date) newErrors.date = \"Date is required\";\n        if (!formData.time) newErrors.time = \"Time is required\";\n        if (!formData.status) newErrors.status = \"Status is required\";\n        if (formData.homeTeamId === formData.awayTeamId) {\n            newErrors.awayTeamId = \"Away team must be different from home team\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const dateTime = new Date(\"\".concat(formData.date, \"T\").concat(formData.time));\n        const submitData = {\n            homeTeamId: parseInt(formData.homeTeamId),\n            awayTeamId: parseInt(formData.awayTeamId),\n            leagueId: parseInt(formData.leagueId),\n            date: dateTime.toISOString(),\n            venueName: formData.venueName || null,\n            venueCity: formData.venueCity || null,\n            round: formData.round || null,\n            status: formData.status,\n            goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,\n            goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,\n            elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,\n            referee: formData.referee || null,\n            temperature: formData.temperature ? parseInt(formData.temperature) : null,\n            weather: formData.weather || null,\n            attendance: formData.attendance ? parseInt(formData.attendance) : null\n        };\n        updateMutation.mutate(submitData);\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    // Search and pagination handlers\n    const handleLeagueSearch = (query)=>{\n        setLeagueSearch(query);\n        setLeaguePage(1); // Reset to first page on new search\n    };\n    const handleTeamSearch = (query)=>{\n        setTeamSearch(query);\n        setTeamPage(1); // Reset to first page on new search\n    };\n    const handleLeagueLoadMore = ()=>{\n        var _leagues_meta, _leagues_meta1, _leagues_meta2;\n        console.log(\"\\uD83D\\uDD04 League Load More:\", {\n            currentPage: leaguePage,\n            totalPages: leagues === null || leagues === void 0 ? void 0 : (_leagues_meta = leagues.meta) === null || _leagues_meta === void 0 ? void 0 : _leagues_meta.totalPages,\n            hasMore: (leagues === null || leagues === void 0 ? void 0 : (_leagues_meta1 = leagues.meta) === null || _leagues_meta1 === void 0 ? void 0 : _leagues_meta1.totalPages) && leaguePage < leagues.meta.totalPages,\n            allLeaguesCount: allLeagues.length\n        });\n        if ((leagues === null || leagues === void 0 ? void 0 : (_leagues_meta2 = leagues.meta) === null || _leagues_meta2 === void 0 ? void 0 : _leagues_meta2.totalPages) && leaguePage < leagues.meta.totalPages) {\n            console.log(\"✅ Loading more leagues, page:\", leaguePage + 1);\n            setLeaguePage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more leagues\");\n        }\n    };\n    const handleTeamLoadMore = ()=>{\n        var _teams_meta, _teams, _teams_meta1, _teams1, _teams_meta2, _teams2;\n        console.log(\"\\uD83D\\uDD04 Team Load More:\", {\n            currentPage: teamPage,\n            totalPages: (_teams = teams) === null || _teams === void 0 ? void 0 : (_teams_meta = _teams.meta) === null || _teams_meta === void 0 ? void 0 : _teams_meta.totalPages,\n            hasMore: ((_teams1 = teams) === null || _teams1 === void 0 ? void 0 : (_teams_meta1 = _teams1.meta) === null || _teams_meta1 === void 0 ? void 0 : _teams_meta1.totalPages) && teamPage < teams.meta.totalPages,\n            allTeamsCount: allTeams.length\n        });\n        if (((_teams2 = teams) === null || _teams2 === void 0 ? void 0 : (_teams_meta2 = _teams2.meta) === null || _teams_meta2 === void 0 ? void 0 : _teams_meta2.totalPages) && teamPage < teams.meta.totalPages) {\n            console.log(\"✅ Loading more teams, page:\", teamPage + 1);\n            setTeamPage((prev)=>prev + 1);\n        } else {\n            console.log(\"❌ Cannot load more teams\");\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"TBD\",\n            label: \"Time To Be Defined\"\n        },\n        {\n            value: \"NS\",\n            label: \"Not Started\"\n        },\n        {\n            value: \"1H\",\n            label: \"First Half\"\n        },\n        {\n            value: \"HT\",\n            label: \"Halftime\"\n        },\n        {\n            value: \"2H\",\n            label: \"Second Half\"\n        },\n        {\n            value: \"ET\",\n            label: \"Extra Time\"\n        },\n        {\n            value: \"BT\",\n            label: \"Break Time\"\n        },\n        {\n            value: \"P\",\n            label: \"Penalty In Progress\"\n        },\n        {\n            value: \"SUSP\",\n            label: \"Match Suspended\"\n        },\n        {\n            value: \"INT\",\n            label: \"Match Interrupted\"\n        },\n        {\n            value: \"FT\",\n            label: \"Match Finished\"\n        },\n        {\n            value: \"AET\",\n            label: \"Match Finished After Extra Time\"\n        },\n        {\n            value: \"PEN\",\n            label: \"Match Finished After Penalty\"\n        },\n        {\n            value: \"PST\",\n            label: \"Match Postponed\"\n        },\n        {\n            value: \"CANC\",\n            label: \"Match Cancelled\"\n        },\n        {\n            value: \"ABD\",\n            label: \"Match Abandoned\"\n        },\n        {\n            value: \"AWD\",\n            label: \"Technical Loss\"\n        },\n        {\n            value: \"WO\",\n            label: \"WalkOver\"\n        }\n    ];\n    // Use accumulated data for options with unique keys\n    const leagueOptions = allLeagues.map((league, index)=>({\n            value: league.externalId.toString(),\n            label: \"\".concat(league.name).concat(league.season ? \" (\".concat(league.season, \")\") : \"\"),\n            logo: league.logo,\n            season: league.season,\n            uniqueKey: \"league-\".concat(league.id || league.externalId, \"-\").concat(index)\n        }));\n    const teamOptions = allTeams.map((team, index)=>({\n            value: team.externalId.toString(),\n            label: team.name,\n            logo: team.logo,\n            uniqueKey: \"team-\".concat(team.id || team.externalId, \"-\").concat(index)\n        }));\n    // Debug options count\n    console.log(\"\\uD83D\\uDCCA OPTIONS DEBUG:\", {\n        allLeaguesCount: allLeagues.length,\n        allTeamsCount: allTeams.length,\n        leagueOptionsCount: leagueOptions.length,\n        teamOptionsCount: teamOptions.length,\n        leagueMeta: leagues === null || leagues === void 0 ? void 0 : leagues.meta,\n        teamMeta: (_teams = teams) === null || _teams === void 0 ? void 0 : _teams.meta\n    });\n    // Additional effect to ensure options are loaded - moved here after options are defined\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D Options Loading Status:\", {\n            teamsLoading,\n            leaguesLoading,\n            teamOptionsCount: teamOptions.length,\n            leagueOptionsCount: leagueOptions.length,\n            formDataReady: !!(formData.homeTeamId && formData.awayTeamId && formData.leagueId)\n        });\n    }, [\n        teamsLoading,\n        leaguesLoading,\n        teamOptions.length,\n        leagueOptions.length,\n        formData\n    ]);\n    // Smart preview logic - always try to find from options first, fallback to fixture data\n    const getPreviewData = ()=>{\n        if (!fixture) return {\n            league: null,\n            homeTeam: null,\n            awayTeam: null\n        };\n        // Try to find from dropdown options first\n        const leagueFromOptions = leagueOptions.find((l)=>l.value === formData.leagueId);\n        const homeTeamFromOptions = teamOptions.find((t)=>t.value === formData.homeTeamId);\n        const awayTeamFromOptions = teamOptions.find((t)=>t.value === formData.awayTeamId);\n        return {\n            league: leagueFromOptions || {\n                value: formData.leagueId,\n                label: fixture.leagueName,\n                logo: \"\"\n            },\n            homeTeam: homeTeamFromOptions || {\n                value: formData.homeTeamId,\n                label: fixture.homeTeamName,\n                logo: fixture.homeTeamLogo\n            },\n            awayTeam: awayTeamFromOptions || {\n                value: formData.awayTeamId,\n                label: fixture.awayTeamName,\n                logo: fixture.awayTeamLogo\n            }\n        };\n    };\n    const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();\n    // Debug selected values\n    console.log(\"\\uD83C\\uDFAF SELECTED VALUES DEBUG:\");\n    console.log(\"selectedLeague:\", selectedLeague);\n    console.log(\"selectedHomeTeam:\", selectedHomeTeam);\n    console.log(\"selectedAwayTeam:\", selectedAwayTeam);\n    // Debug form data\n    console.log(\"\\uD83D\\uDCDD FORM DATA:\", formData);\n    console.log(\"\\uD83D\\uDCCA OPTIONS COUNT:\", {\n        teams: teamOptions.length,\n        leagues: leagueOptions.length\n    });\n    // Force re-render when options change to ensure preview updates\n    const [previewKey, setPreviewKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dropdownKey, setDropdownKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update preview when options or form data changes\n        if (teamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {\n            setPreviewKey((prev)=>prev + 1);\n        }\n    }, [\n        teamOptions.length,\n        leagueOptions.length,\n        formData.homeTeamId,\n        formData.awayTeamId,\n        formData.leagueId\n    ]);\n    // Force dropdown re-render when options change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDropdownKey((prev)=>prev + 1);\n    }, [\n        teamOptions.length,\n        leagueOptions.length\n    ]);\n    // Simple debug for ID matching\n    console.log(\"\\uD83D\\uDD27 ID MATCHING DEBUG:\");\n    console.log(\"Form IDs:\", {\n        home: formData.homeTeamId,\n        away: formData.awayTeamId,\n        league: formData.leagueId\n    });\n    console.log(\"Form ID Types:\", {\n        home: typeof formData.homeTeamId,\n        away: typeof formData.awayTeamId,\n        league: typeof formData.leagueId\n    });\n    if (teamOptions.length > 0) {\n        console.log(\"Sample Team Options:\", teamOptions.slice(0, 3));\n        console.log(\"Team Option Types:\", teamOptions.slice(0, 1).map((t)=>({\n                value: t.value,\n                type: typeof t.value\n            })));\n        // 🔍 Check if our specific teams exist\n        const homeTeamInOptions = teamOptions.find((t)=>t.value === \"4450\");\n        const awayTeamInOptions = teamOptions.find((t)=>t.value === \"20022\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC TEAMS CHECK:\", {\n            \"homeTeam (4450)\": homeTeamInOptions,\n            \"awayTeam (20022)\": awayTeamInOptions,\n            \"totalTeams\": teamOptions.length\n        });\n    }\n    if (leagueOptions.length > 0) {\n        console.log(\"Sample League Options:\", leagueOptions.slice(0, 2));\n        console.log(\"League Option Types:\", leagueOptions.slice(0, 1).map((l)=>({\n                value: l.value,\n                type: typeof l.value\n            })));\n        // 🔍 Check if our specific league exists\n        const leagueInOptions = leagueOptions.find((l)=>l.value === \"570\");\n        console.log(\"\\uD83C\\uDFAF SPECIFIC LEAGUE CHECK:\", {\n            \"league (570)\": leagueInOptions,\n            \"totalLeagues\": leagueOptions.length\n        });\n    }\n    console.log(\"\\uD83D\\uDD0D ID Exists Check - FIXED:\", {\n        homeExists: teamOptions.some((t)=>t.value === formData.homeTeamId),\n        awayExists: teamOptions.some((t)=>t.value === formData.awayTeamId),\n        leagueExists: leagueOptions.some((l)=>l.value === formData.leagueId)\n    });\n    // Preview component for selected values\n    const SelectedValuePreview = (param)=>{\n        let { label, selectedOption, placeholder = \"Not selected\" } = param;\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug preview component\n        console.log(\"\\uD83C\\uDFA8 \".concat(label, \" Preview:\"), {\n            selectedOption,\n            placeholder,\n            hasOption: !!selectedOption\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 500,\n                    columnNumber: 9\n                }, this),\n                selectedOption ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        selectedOption.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"\".concat(CDN_URL, \"/\").concat(selectedOption.logo),\n                            alt: selectedOption.label,\n                            className: \"w-8 h-8 object-contain rounded\",\n                            onError: (e)=>{\n                                e.currentTarget.style.display = \"none\";\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: selectedOption.label\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 502,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xs\",\n                                children: \"?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500 italic\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 499,\n            columnNumber: 7\n        }, this);\n    };\n    // Inline League component (logo + name bên trái, dropdown bên phải)\n    const LeagueInlineSelector = ()=>{\n        const CDN_URL = \"http://172.31.213.61\" || 0;\n        // Debug league selector\n        console.log(\"\\uD83C\\uDFC6 League Inline Selector:\", {\n            selectedLeague,\n            hasLeague: !!selectedLeague\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 min-w-0 flex-1\",\n                    children: selectedLeague ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            selectedLeague.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"\".concat(CDN_URL, \"/\").concat(selectedLeague.logo),\n                                alt: selectedLeague.label,\n                                className: \"w-8 h-8 object-contain rounded flex-shrink-0\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: selectedLeague.label\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No league selected\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                            children: \"League*\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                            placeholder: leaguesLoading ? \"Loading leagues...\" : \"Select league\",\n                            searchPlaceholder: \"Search leagues...\",\n                            required: true,\n                            value: formData.leagueId,\n                            onValueChange: (value)=>updateFormData(\"leagueId\", value),\n                            options: leagueOptions,\n                            error: errors.leagueId,\n                            disabled: leaguesLoading,\n                            onSearch: handleLeagueSearch,\n                            onLoadMore: handleLeagueLoadMore,\n                            hasMore: (leagues === null || leagues === void 0 ? void 0 : leagues.meta) ? leaguePage < leagues.meta.totalPages : false,\n                            isLoading: leaguesLoading\n                        }, \"league-\".concat(dropdownKey), false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 567,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 537,\n            columnNumber: 7\n        }, this);\n    };\n    // Show loading state if any required data is loading\n    const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;\n    if (isDataLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 597,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 595,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 594,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if any critical data failed to load\n    if (!fixture || leaguesError || teamsError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 637,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                !fixture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Fixture not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 28\n                                }, this),\n                                leaguesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load leagues: \",\n                                        leaguesError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 32\n                                }, this),\n                                teamsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: [\n                                        \"Failed to load teams: \",\n                                        teamsError.message\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 30\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                    lineNumber: 644,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n            lineNumber: 636,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: [\n                                    \"Edit Fixture: \",\n                                    fixture.homeTeamName,\n                                    \" vs \",\n                                    fixture.awayTeamName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update fixture details and match information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 663,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Fixture Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 679,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the fixture information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Teams & Competition\",\n                                    description: \"Select the teams and league\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Home Team\",\n                                                            selectedOption: selectedHomeTeam,\n                                                            placeholder: \"No home team selected\"\n                                                        }, \"home-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Home Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select home team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.homeTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"homeTeamId\", value),\n                                                            options: teamOptions,\n                                                            error: errors.homeTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleTeamSearch,\n                                                            onLoadMore: handleTeamLoadMore,\n                                                            hasMore: ((_teams1 = teams) === null || _teams1 === void 0 ? void 0 : _teams1.meta) ? teamPage < teams.meta.totalPages : false,\n                                                            isLoading: teamsLoading\n                                                        }, \"home-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectedValuePreview, {\n                                                            label: \"Selected Away Team\",\n                                                            selectedOption: selectedAwayTeam,\n                                                            placeholder: \"No away team selected\"\n                                                        }, \"away-\".concat(previewKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchableSelectField__WEBPACK_IMPORTED_MODULE_6__.SearchableSelectField, {\n                                                            label: \"Away Team\",\n                                                            placeholder: teamsLoading ? \"Loading teams...\" : \"Select away team\",\n                                                            searchPlaceholder: \"Search teams...\",\n                                                            required: true,\n                                                            value: formData.awayTeamId,\n                                                            onValueChange: (value)=>updateFormData(\"awayTeamId\", value),\n                                                            options: teamOptions.filter((team)=>team.value !== formData.homeTeamId),\n                                                            error: errors.awayTeamId,\n                                                            disabled: teamsLoading,\n                                                            onSearch: handleTeamSearch,\n                                                            onLoadMore: handleTeamLoadMore,\n                                                            hasMore: ((_teams2 = teams) === null || _teams2 === void 0 ? void 0 : _teams2.meta) ? teamPage < teams.meta.totalPages : false,\n                                                            isLoading: teamsLoading\n                                                        }, \"away-team-\".concat(dropdownKey), false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeagueInlineSelector, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Schedule\",\n                                    description: \"Set the date and time (local timezone)\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.date,\n                                                    onChange: (e)=>updateFormData(\"date\", e.target.value),\n                                                    error: errors.date,\n                                                    description: \"Match date\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.time,\n                                                    onChange: (e)=>updateFormData(\"time\", e.target.value),\n                                                    error: errors.time,\n                                                    description: \"Local time (\".concat(Intl.DateTimeFormat().resolvedOptions().timeZone, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Timezone Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Times are displayed in your local timezone (\",\n                                                    Intl.DateTimeFormat().resolvedOptions().timeZone,\n                                                    \"). The asterisk (*) indicates required fields.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 749,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Match Status\",\n                                    description: \"Update match status and score\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Status\",\n                                                    placeholder: \"Select status\",\n                                                    required: true,\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>updateFormData(\"status\", value),\n                                                    options: statusOptions,\n                                                    error: errors.status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Home Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsHome,\n                                                    onChange: (e)=>updateFormData(\"goalsHome\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Away Goals\",\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    value: formData.goalsAway,\n                                                    onChange: (e)=>updateFormData(\"goalsAway\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Elapsed Time (minutes)\",\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"120\",\n                                            value: formData.elapsed,\n                                            onChange: (e)=>updateFormData(\"elapsed\", e.target.value),\n                                            description: \"Minutes played in the match\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Venue & Match Information\",\n                                    description: \"Venue details and match context\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue Name\",\n                                                    placeholder: \"Stadium name\",\n                                                    value: formData.venueName,\n                                                    onChange: (e)=>updateFormData(\"venueName\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Venue City\",\n                                                    placeholder: \"City\",\n                                                    value: formData.venueCity,\n                                                    onChange: (e)=>updateFormData(\"venueCity\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Round\",\n                                                    placeholder: \"e.g., Matchday 1, Quarter-final\",\n                                                    value: formData.round,\n                                                    onChange: (e)=>updateFormData(\"round\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 839,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Referee\",\n                                                    placeholder: \"Referee name\",\n                                                    value: formData.referee || \"\",\n                                                    onChange: (e)=>updateFormData(\"referee\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Temperature (\\xb0C)\",\n                                                    type: \"number\",\n                                                    placeholder: \"e.g., 22\",\n                                                    value: formData.temperature || \"\",\n                                                    onChange: (e)=>updateFormData(\"temperature\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Weather\",\n                                                    placeholder: \"e.g., Sunny, Rainy\",\n                                                    value: formData.weather || \"\",\n                                                    onChange: (e)=>updateFormData(\"weather\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Attendance\",\n                                                    type: \"number\",\n                                                    placeholder: \"Number of spectators\",\n                                                    value: formData.attendance || \"\",\n                                                    onChange: (e)=>updateFormData(\"attendance\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateMutation.isPending,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateMutation.isPending,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 17\n                                                }, this),\n                                                updateMutation.isPending ? \"Updating...\" : \"Update Fixture\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                                    lineNumber: 880,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n                lineNumber: 677,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx\",\n        lineNumber: 661,\n        columnNumber: 5\n    }, this);\n}\n_s(EditFixturePage, \"qABdB0ZQbal41kGEDaVkl+B+ZEM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = EditFixturePage;\nvar _c;\n$RefreshReg$(_c, \"EditFixturePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ZpeHR1cmVzL1tpZF0vZWRpdC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ1c7QUFDdUI7QUFDbUI7QUFDakQ7QUFNWjtBQUMwQztBQUMxQjtBQUNIO0FBQ0Y7QUFDSjtBQUNjO0FBQzFCO0FBcUJoQixTQUFTMEI7UUEyVVZDLFFBb1ZlQSxTQXlCQUE7O0lBdnJCM0IsTUFBTUMsU0FBUzFCLDBEQUFTQTtJQUN4QixNQUFNMkIsU0FBUzFCLDBEQUFTQTtJQUN4QixNQUFNMkIsY0FBY3hCLHNFQUFjQTtJQUNsQyxNQUFNeUIsWUFBWUMsU0FBU0osT0FBT0ssRUFBRTtJQUVwQyxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR25DLCtDQUFRQSxDQUFrQjtRQUN4RG9DLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxTQUFTO0lBQ1g7SUFFQSxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR2pELCtDQUFRQSxDQUEyQixDQUFDO0lBRWhFLHdCQUF3QjtJQUN4QixNQUFNLEVBQUVrRCxNQUFNQyxPQUFPLEVBQUVDLFdBQVdDLGNBQWMsRUFBRSxHQUFHaEQsZ0VBQVFBLENBQUM7UUFDNURpRCxVQUFVO1lBQUM7WUFBV3ZCO1NBQVU7UUFDaEN3QixTQUFTLElBQU1wQywwREFBV0EsQ0FBQ3FDLFVBQVUsQ0FBQ3pCO1FBQ3RDMEIsU0FBUyxDQUFDLENBQUMxQjtJQUNiO0lBRUEscUVBQXFFO0lBQ3JFLE1BQU0sQ0FBQzJCLGNBQWNDLGdCQUFnQixHQUFHM0QsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDNEQsZ0JBQWdCQyxrQkFBa0IsR0FBRzdELCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQzhELGdCQUFnQkMsa0JBQWtCLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNnRSxZQUFZQyxjQUFjLEdBQUdqRSwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNrRSxjQUFjQyxnQkFBZ0IsR0FBR25FLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ29FLGNBQWNDLGdCQUFnQixHQUFHckUsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDc0UsWUFBWUMsY0FBYyxHQUFHdkUsK0NBQVFBLENBQVEsRUFBRTtJQUN0RCxNQUFNLENBQUN3RSxjQUFjQyxnQkFBZ0IsR0FBR3pFLCtDQUFRQSxDQUFRLEVBQUU7SUFDMUQsTUFBTSxDQUFDMEUsY0FBY0MsZ0JBQWdCLEdBQUczRSwrQ0FBUUEsQ0FBUSxFQUFFO0lBRTFELDJDQUEyQztJQUMzQyxNQUFNLEVBQUVrRCxNQUFNMEIsT0FBTyxFQUFFeEIsV0FBV3lCLGNBQWMsRUFBRUMsT0FBT0MsWUFBWSxFQUFFLEdBQUcxRSxnRUFBUUEsQ0FBQztRQUNqRmlELFVBQVU7WUFBQztZQUFXO1lBQVVJO1lBQWNNO1NBQVc7UUFDekRULFNBQVMsSUFBTW5DLHdEQUFVQSxDQUFDNEQsVUFBVSxDQUFDO2dCQUNuQ0MsT0FBTztnQkFDUEMsTUFBTWxCO2dCQUNOLEdBQUlOLGdCQUFnQjtvQkFBRXlCLFFBQVF6QjtnQkFBYSxDQUFDO1lBQzlDO1FBQ0EwQixrQkFBa0I7SUFDcEI7SUFFQSw4Q0FBOEM7SUFDOUMsTUFBTSxFQUFFbEMsTUFBTW1DLFNBQVMsRUFBRWpDLFdBQVdrQyxnQkFBZ0IsRUFBRVIsT0FBT1MsY0FBYyxFQUFFLEdBQUdsRixnRUFBUUEsQ0FBQztRQUN2RmlELFVBQVU7WUFBQztZQUFjO1lBQVVNO1lBQWdCTTtTQUFhO1FBQ2hFWCxTQUFTLElBQU1sQyxxREFBUUEsQ0FBQ21FLFFBQVEsQ0FBQztnQkFDL0JQLE9BQU87Z0JBQ1BDLE1BQU1oQjtnQkFDTixHQUFJTixrQkFBa0I7b0JBQUV1QixRQUFRdkI7Z0JBQWUsQ0FBQztZQUNsRDtRQUNBd0Isa0JBQWtCO0lBQ3BCO0lBRUEsOENBQThDO0lBQzlDLE1BQU0sRUFBRWxDLE1BQU11QyxTQUFTLEVBQUVyQyxXQUFXc0MsZ0JBQWdCLEVBQUVaLE9BQU9hLGNBQWMsRUFBRSxHQUFHdEYsZ0VBQVFBLENBQUM7UUFDdkZpRCxVQUFVO1lBQUM7WUFBYztZQUFVUTtZQUFnQk07U0FBYTtRQUNoRWIsU0FBUyxJQUFNbEMscURBQVFBLENBQUNtRSxRQUFRLENBQUM7Z0JBQy9CUCxPQUFPO2dCQUNQQyxNQUFNZDtnQkFDTixHQUFJTixrQkFBa0I7b0JBQUVxQixRQUFRckI7Z0JBQWUsQ0FBQztZQUNsRDtRQUNBc0Isa0JBQWtCO0lBQ3BCO0lBRUEsaURBQWlEO0lBQ2pELE1BQU0sQ0FBQ1Esa0JBQWtCQyxvQkFBb0IsR0FBRzdGLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzhGLGdCQUFnQkMsa0JBQWtCLEdBQUcvRiwrQ0FBUUEsQ0FBQztJQUVyRCxzREFBc0Q7SUFDdERDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTJFLG9CQUFBQSw4QkFBQUEsUUFBUzFCLElBQUksRUFBRTtZQUNqQiwwQkFBMEI7WUFDMUIsTUFBTThDLGdCQUFnQnRDLGlCQUFpQmtDO1lBRXZDLElBQUk1QixlQUFlLEtBQUtnQyxlQUFlO2dCQUNyQywwQ0FBMEM7Z0JBQzFDQyxRQUFRQyxHQUFHLENBQUMsd0NBQThCO29CQUFFaEIsTUFBTWxCO29CQUFZZ0M7b0JBQWViLFFBQVF6QjtnQkFBYTtnQkFDbEdhLGNBQWNLLFFBQVExQixJQUFJO2dCQUMxQjJDLG9CQUFvQm5DO1lBQ3RCLE9BQU87Z0JBQ0wsbURBQW1EO2dCQUNuRHVDLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEI7b0JBQUVoQixNQUFNbEI7b0JBQVltQyxVQUFVN0IsV0FBVzhCLE1BQU07b0JBQUVDLEtBQUt6QixRQUFRMUIsSUFBSSxDQUFDa0QsTUFBTTtnQkFBQztnQkFDcEg3QixjQUFjK0IsQ0FBQUE7b0JBQ1osTUFBTUMsY0FBYyxJQUFJQyxJQUFJRixLQUFLRyxHQUFHLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLFVBQVU7b0JBQ3RELE1BQU1DLGFBQWFoQyxRQUFRMUIsSUFBSSxDQUFDMkQsTUFBTSxDQUFDSCxDQUFBQSxJQUFLLENBQUNILFlBQVlPLEdBQUcsQ0FBQ0osRUFBRUMsVUFBVTtvQkFDekUsT0FBTzsyQkFBSUw7MkJBQVNNO3FCQUFXO2dCQUNqQztZQUNGO1FBQ0Y7SUFDRixHQUFHO1FBQUNoQztRQUFTWjtRQUFZTjtRQUFja0M7UUFBa0J0QixXQUFXOEIsTUFBTTtLQUFDO0lBRTNFLG9EQUFvRDtJQUNwRG5HLGdEQUFTQSxDQUFDO1lBQ0owQjtRQUFKLEtBQUlBLFNBQUFBLG1CQUFBQSw2QkFBQUEsT0FBT3VCLElBQUksRUFBRTtZQUNmLDBCQUEwQjtZQUMxQixNQUFNOEMsZ0JBQWdCZSxlQUFlakI7WUFFckMsSUFBSWtCLGFBQWEsS0FBS2hCLGVBQWU7Z0JBQ25DLDBDQUEwQztnQkFDMUNDLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBNEI7b0JBQUVoQixNQUFNOEI7b0JBQVVoQjtvQkFBZWIsUUFBUTRCO2dCQUFXO2dCQUM1RkUsWUFBWXRGLE1BQU11QixJQUFJO2dCQUN0QjZDLGtCQUFrQmdCO1lBQ3BCLE9BQU87Z0JBQ0wsbURBQW1EO2dCQUNuRGQsUUFBUUMsR0FBRyxDQUFDLHNDQUE0QjtvQkFBRWhCLE1BQU04QjtvQkFBVWIsVUFBVWUsU0FBU2QsTUFBTTtvQkFBRUMsS0FBSzFFLE1BQU11QixJQUFJLENBQUNrRCxNQUFNO2dCQUFDO2dCQUM1R2EsWUFBWVgsQ0FBQUE7b0JBQ1YsTUFBTUMsY0FBYyxJQUFJQyxJQUFJRixLQUFLRyxHQUFHLENBQUNVLENBQUFBLElBQUtBLEVBQUVsRixFQUFFLElBQUlrRixFQUFFUixVQUFVO29CQUM5RCxNQUFNUyxXQUFXekYsTUFBTXVCLElBQUksQ0FBQzJELE1BQU0sQ0FBQ00sQ0FBQUEsSUFBSyxDQUFDWixZQUFZTyxHQUFHLENBQUNLLEVBQUVsRixFQUFFLElBQUlrRixFQUFFUixVQUFVO29CQUM3RVYsUUFBUUMsR0FBRyxDQUFDLGtDQUF3Qjt3QkFDbENDLFVBQVVHLEtBQUtGLE1BQU07d0JBQ3JCQyxLQUFLZSxTQUFTaEIsTUFBTTt3QkFDcEJpQixPQUFPZixLQUFLRixNQUFNLEdBQUdnQixTQUFTaEIsTUFBTTtvQkFDdEM7b0JBQ0EsT0FBTzsyQkFBSUU7MkJBQVNjO3FCQUFTO2dCQUMvQjtZQUNGO1FBQ0Y7SUFDRixHQUFHO1FBQUN6RjtRQUFPcUY7UUFBVUQ7UUFBWWpCO1FBQWdCb0IsU0FBU2QsTUFBTTtLQUFDO0lBRWpFLGtCQUFrQjtJQUNsQixNQUFNa0IsaUJBQWlCbEgsbUVBQVdBLENBQUM7UUFDakNtSCxZQUFZLENBQUNyRSxPQUFjL0IsMERBQVdBLENBQUNxRyxhQUFhLENBQUN6RixXQUFXbUI7UUFDaEV1RSxXQUFXO1lBQ1QzRixZQUFZNEYsaUJBQWlCLENBQUM7Z0JBQUVwRSxVQUFVO29CQUFDO29CQUFXdkI7aUJBQVU7WUFBQztZQUNqRUQsWUFBWTRGLGlCQUFpQixDQUFDO2dCQUFFcEUsVUFBVTtvQkFBQztpQkFBVztZQUFDO1lBQ3ZEN0IsMENBQUtBLENBQUNrRyxPQUFPLENBQUM7WUFDZDlGLE9BQU8rRixJQUFJLENBQUMsdUJBQWlDLE9BQVY3RjtRQUNyQztRQUNBOEYsU0FBUyxDQUFDL0M7WUFDUnJELDBDQUFLQSxDQUFDcUQsS0FBSyxDQUFDQSxNQUFNZ0QsT0FBTyxJQUFJO1FBQy9CO0lBQ0Y7SUFFQSx3Q0FBd0M7SUFDeEM3SCxnREFBU0EsQ0FBQztRQUNSLElBQUlrRCxTQUFTO2dCQW9CR0EscUJBQ0FBLHFCQUNGQSxtQkFJQ0EsZ0JBQ0FBLGlCQUdBQSxvQkFDQUEsb0JBQ0ZBLGtCQUVJQSxzQkFFREE7WUFuQ2QsTUFBTTRFLGNBQWMsSUFBSUMsS0FBSzdFLFFBQVFaLElBQUk7WUFDekMwRCxRQUFRQyxHQUFHLENBQUMsbURBQXlDO2dCQUNuRDlELFlBQVllLFFBQVFmLFVBQVU7Z0JBQzlCQyxZQUFZYyxRQUFRZCxVQUFVO2dCQUM5QkMsVUFBVWEsUUFBUWIsUUFBUTtnQkFDMUIyRixPQUFPOUUsUUFBUThFLEtBQUs7Z0JBQ3BCeEYsV0FBV1UsUUFBUVYsU0FBUztnQkFDNUJDLFdBQVdTLFFBQVFULFNBQVM7WUFDOUI7WUFFQSwyREFBMkQ7WUFDM0R1RCxRQUFRQyxHQUFHLENBQUMscUNBQTJCO2dCQUNyQyxzQkFBc0IvQyxRQUFRZixVQUFVO2dCQUN4QyxzQkFBc0JlLFFBQVFkLFVBQVU7Z0JBQ3hDLG9CQUFvQmMsUUFBUWIsUUFBUTtnQkFDcEMsa0JBQWtCYTtZQUNwQjtZQUVBaEIsWUFBWTtnQkFDVkMsWUFBWWUsRUFBQUEsc0JBQUFBLFFBQVFmLFVBQVUsY0FBbEJlLDBDQUFBQSxvQkFBb0IrRSxRQUFRLE9BQU07Z0JBQzlDN0YsWUFBWWMsRUFBQUEsc0JBQUFBLFFBQVFkLFVBQVUsY0FBbEJjLDBDQUFBQSxvQkFBb0IrRSxRQUFRLE9BQU07Z0JBQzlDNUYsVUFBVWEsRUFBQUEsb0JBQUFBLFFBQVFiLFFBQVEsY0FBaEJhLHdDQUFBQSxrQkFBa0IrRSxRQUFRLE9BQU07Z0JBQzFDM0YsTUFBTXdGLFlBQVlJLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUM3QzVGLE1BQU11RixZQUFZTSxZQUFZLEdBQUdDLEtBQUssQ0FBQyxHQUFHO2dCQUMxQyw0REFBNEQ7Z0JBQzVEN0YsV0FBV1UsRUFBQUEsaUJBQUFBLFFBQVE4RSxLQUFLLGNBQWI5RSxxQ0FBQUEsZUFBZW9GLElBQUksS0FBSXBGLFFBQVFWLFNBQVMsSUFBSTtnQkFDdkRDLFdBQVdTLEVBQUFBLGtCQUFBQSxRQUFROEUsS0FBSyxjQUFiOUUsc0NBQUFBLGdCQUFlcUYsSUFBSSxLQUFJckYsUUFBUVQsU0FBUyxJQUFJO2dCQUN2REMsT0FBT1EsUUFBUVIsS0FBSyxJQUFJO2dCQUN4QkMsUUFBUU8sUUFBUVAsTUFBTSxJQUFJO2dCQUMxQkMsV0FBV00sRUFBQUEscUJBQUFBLFFBQVFOLFNBQVMsY0FBakJNLHlDQUFBQSxtQkFBbUIrRSxRQUFRLE9BQU07Z0JBQzVDcEYsV0FBV0ssRUFBQUEscUJBQUFBLFFBQVFMLFNBQVMsY0FBakJLLHlDQUFBQSxtQkFBbUIrRSxRQUFRLE9BQU07Z0JBQzVDbkYsU0FBU0ksRUFBQUEsbUJBQUFBLFFBQVFKLE9BQU8sY0FBZkksdUNBQUFBLGlCQUFpQitFLFFBQVEsT0FBTTtnQkFDeENPLFNBQVN0RixRQUFRc0YsT0FBTyxJQUFJO2dCQUM1QkMsYUFBYXZGLEVBQUFBLHVCQUFBQSxRQUFRdUYsV0FBVyxjQUFuQnZGLDJDQUFBQSxxQkFBcUIrRSxRQUFRLE9BQU07Z0JBQ2hEUyxTQUFTeEYsUUFBUXdGLE9BQU8sSUFBSTtnQkFDNUJDLFlBQVl6RixFQUFBQSxzQkFBQUEsUUFBUXlGLFVBQVUsY0FBbEJ6RiwwQ0FBQUEsb0JBQW9CK0UsUUFBUSxPQUFNO1lBQ2hEO1FBQ0Y7SUFDRixHQUFHO1FBQUMvRTtLQUFRO0lBRVosc0VBQXNFO0lBRXRFLE1BQU0wRixlQUFlO1FBQ25CLE1BQU1DLFlBQXNDLENBQUM7UUFFN0MsSUFBSSxDQUFDNUcsU0FBU0UsVUFBVSxFQUFFMEcsVUFBVTFHLFVBQVUsR0FBRztRQUNqRCxJQUFJLENBQUNGLFNBQVNHLFVBQVUsRUFBRXlHLFVBQVV6RyxVQUFVLEdBQUc7UUFDakQsSUFBSSxDQUFDSCxTQUFTSSxRQUFRLEVBQUV3RyxVQUFVeEcsUUFBUSxHQUFHO1FBQzdDLElBQUksQ0FBQ0osU0FBU0ssSUFBSSxFQUFFdUcsVUFBVXZHLElBQUksR0FBRztRQUNyQyxJQUFJLENBQUNMLFNBQVNNLElBQUksRUFBRXNHLFVBQVV0RyxJQUFJLEdBQUc7UUFDckMsSUFBSSxDQUFDTixTQUFTVSxNQUFNLEVBQUVrRyxVQUFVbEcsTUFBTSxHQUFHO1FBRXpDLElBQUlWLFNBQVNFLFVBQVUsS0FBS0YsU0FBU0csVUFBVSxFQUFFO1lBQy9DeUcsVUFBVXpHLFVBQVUsR0FBRztRQUN6QjtRQUVBWSxVQUFVNkY7UUFDVixPQUFPQyxPQUFPQyxJQUFJLENBQUNGLFdBQVcxQyxNQUFNLEtBQUs7SUFDM0M7SUFFQSxNQUFNNkMsZUFBZSxDQUFDQztRQUNwQkEsRUFBRUMsY0FBYztRQUVoQixJQUFJLENBQUNOLGdCQUFnQjtZQUNuQnBILDBDQUFLQSxDQUFDcUQsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBLHdCQUF3QjtRQUN4QixNQUFNc0UsV0FBVyxJQUFJcEIsS0FBSyxHQUFvQjlGLE9BQWpCQSxTQUFTSyxJQUFJLEVBQUMsS0FBaUIsT0FBZEwsU0FBU00sSUFBSTtRQUUzRCxNQUFNNkcsYUFBYTtZQUNqQmpILFlBQVlKLFNBQVNFLFNBQVNFLFVBQVU7WUFDeENDLFlBQVlMLFNBQVNFLFNBQVNHLFVBQVU7WUFDeENDLFVBQVVOLFNBQVNFLFNBQVNJLFFBQVE7WUFDcENDLE1BQU02RyxTQUFTakIsV0FBVztZQUMxQjFGLFdBQVdQLFNBQVNPLFNBQVMsSUFBSTtZQUNqQ0MsV0FBV1IsU0FBU1EsU0FBUyxJQUFJO1lBQ2pDQyxPQUFPVCxTQUFTUyxLQUFLLElBQUk7WUFDekJDLFFBQVFWLFNBQVNVLE1BQU07WUFDdkJDLFdBQVdYLFNBQVNXLFNBQVMsR0FBR2IsU0FBU0UsU0FBU1csU0FBUyxJQUFJO1lBQy9EQyxXQUFXWixTQUFTWSxTQUFTLEdBQUdkLFNBQVNFLFNBQVNZLFNBQVMsSUFBSTtZQUMvREMsU0FBU2IsU0FBU2EsT0FBTyxHQUFHZixTQUFTRSxTQUFTYSxPQUFPLElBQUk7WUFDekQwRixTQUFTdkcsU0FBU3VHLE9BQU8sSUFBSTtZQUM3QkMsYUFBYXhHLFNBQVN3RyxXQUFXLEdBQUcxRyxTQUFTRSxTQUFTd0csV0FBVyxJQUFJO1lBQ3JFQyxTQUFTekcsU0FBU3lHLE9BQU8sSUFBSTtZQUM3QkMsWUFBWTFHLFNBQVMwRyxVQUFVLEdBQUc1RyxTQUFTRSxTQUFTMEcsVUFBVSxJQUFJO1FBQ3BFO1FBRUF0QixlQUFlZ0MsTUFBTSxDQUFDRDtJQUN4QjtJQUVBLE1BQU1FLGlCQUFpQixDQUFDQyxPQUE4QkM7UUFDcER0SCxZQUFZbUUsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFLENBQUNrRCxNQUFNLEVBQUVDO1lBQU07UUFDL0Msc0NBQXNDO1FBQ3RDLElBQUl6RyxNQUFNLENBQUN3RyxNQUFNLEVBQUU7WUFDakJ2RyxVQUFVcUQsQ0FBQUEsT0FBUztvQkFBRSxHQUFHQSxJQUFJO29CQUFFLENBQUNrRCxNQUFNLEVBQUVFO2dCQUFVO1FBQ25EO0lBQ0Y7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTUMscUJBQXFCLENBQUNDO1FBQzFCakcsZ0JBQWdCaUc7UUFDaEIzRixjQUFjLElBQUksb0NBQW9DO0lBQ3hEO0lBRUEsTUFBTTRGLG1CQUFtQixDQUFDRDtRQUN4QkUsY0FBY0Y7UUFDZEcsWUFBWSxJQUFJLG9DQUFvQztJQUN0RDtJQUVBLE1BQU1DLHVCQUF1QjtZQUdicEYsZUFDSEEsZ0JBR1BBO1FBTkpxQixRQUFRQyxHQUFHLENBQUMsa0NBQXdCO1lBQ2xDK0QsYUFBYWpHO1lBQ2JrRyxVQUFVLEVBQUV0RixvQkFBQUEsK0JBQUFBLGdCQUFBQSxRQUFTdUYsSUFBSSxjQUFidkYsb0NBQUFBLGNBQWVzRixVQUFVO1lBQ3JDRSxTQUFTeEYsQ0FBQUEsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU3VGLElBQUksY0FBYnZGLHFDQUFBQSxlQUFlc0YsVUFBVSxLQUFJbEcsYUFBYVksUUFBUXVGLElBQUksQ0FBQ0QsVUFBVTtZQUMxRUcsaUJBQWlCL0YsV0FBVzhCLE1BQU07UUFDcEM7UUFDQSxJQUFJeEIsQ0FBQUEsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU3VGLElBQUksY0FBYnZGLHFDQUFBQSxlQUFlc0YsVUFBVSxLQUFJbEcsYUFBYVksUUFBUXVGLElBQUksQ0FBQ0QsVUFBVSxFQUFFO1lBQ3JFakUsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ2xDLGFBQWE7WUFDMURDLGNBQWNxQyxDQUFBQSxPQUFRQSxPQUFPO1FBQy9CLE9BQU87WUFDTEwsUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1vRSxxQkFBcUI7WUFHWDNJLGFBQUFBLFFBQ0hBLGNBQUFBLFNBR1BBLGNBQUFBO1FBTkpzRSxRQUFRQyxHQUFHLENBQUMsZ0NBQXNCO1lBQ2hDK0QsYUFBYWpEO1lBQ2JrRCxVQUFVLEdBQUV2SSxTQUFBQSxtQkFBQUEsOEJBQUFBLGNBQUFBLE9BQU93SSxJQUFJLGNBQVh4SSxrQ0FBQUEsWUFBYXVJLFVBQVU7WUFDbkNFLFNBQVN6SSxFQUFBQSxVQUFBQSxtQkFBQUEsK0JBQUFBLGVBQUFBLFFBQU93SSxJQUFJLGNBQVh4SSxtQ0FBQUEsYUFBYXVJLFVBQVUsS0FBSWxELFdBQVdyRixNQUFNd0ksSUFBSSxDQUFDRCxVQUFVO1lBQ3BFSyxlQUFlckQsU0FBU2QsTUFBTTtRQUNoQztRQUNBLElBQUl6RSxFQUFBQSxVQUFBQSxtQkFBQUEsK0JBQUFBLGVBQUFBLFFBQU93SSxJQUFJLGNBQVh4SSxtQ0FBQUEsYUFBYXVJLFVBQVUsS0FBSWxELFdBQVdyRixNQUFNd0ksSUFBSSxDQUFDRCxVQUFVLEVBQUU7WUFDL0RqRSxRQUFRQyxHQUFHLENBQUMsK0JBQStCYyxXQUFXO1lBQ3REK0MsWUFBWXpELENBQUFBLE9BQVFBLE9BQU87UUFDN0IsT0FBTztZQUNMTCxRQUFRQyxHQUFHLENBQUM7UUFDZDtJQUNGO0lBRUEsTUFBTXNFLGdCQUFnQjtRQUNwQjtZQUFFZixPQUFPO1lBQU9nQixPQUFPO1FBQXFCO1FBQzVDO1lBQUVoQixPQUFPO1lBQU1nQixPQUFPO1FBQWM7UUFDcEM7WUFBRWhCLE9BQU87WUFBTWdCLE9BQU87UUFBYTtRQUNuQztZQUFFaEIsT0FBTztZQUFNZ0IsT0FBTztRQUFXO1FBQ2pDO1lBQUVoQixPQUFPO1lBQU1nQixPQUFPO1FBQWM7UUFDcEM7WUFBRWhCLE9BQU87WUFBTWdCLE9BQU87UUFBYTtRQUNuQztZQUFFaEIsT0FBTztZQUFNZ0IsT0FBTztRQUFhO1FBQ25DO1lBQUVoQixPQUFPO1lBQUtnQixPQUFPO1FBQXNCO1FBQzNDO1lBQUVoQixPQUFPO1lBQVFnQixPQUFPO1FBQWtCO1FBQzFDO1lBQUVoQixPQUFPO1lBQU9nQixPQUFPO1FBQW9CO1FBQzNDO1lBQUVoQixPQUFPO1lBQU1nQixPQUFPO1FBQWlCO1FBQ3ZDO1lBQUVoQixPQUFPO1lBQU9nQixPQUFPO1FBQWtDO1FBQ3pEO1lBQUVoQixPQUFPO1lBQU9nQixPQUFPO1FBQStCO1FBQ3REO1lBQUVoQixPQUFPO1lBQU9nQixPQUFPO1FBQWtCO1FBQ3pDO1lBQUVoQixPQUFPO1lBQVFnQixPQUFPO1FBQWtCO1FBQzFDO1lBQUVoQixPQUFPO1lBQU9nQixPQUFPO1FBQWtCO1FBQ3pDO1lBQUVoQixPQUFPO1lBQU9nQixPQUFPO1FBQWlCO1FBQ3hDO1lBQUVoQixPQUFPO1lBQU1nQixPQUFPO1FBQVc7S0FDbEM7SUFFRCxvREFBb0Q7SUFDcEQsTUFBTUMsZ0JBQWdCcEcsV0FBV21DLEdBQUcsQ0FBQyxDQUFDa0UsUUFBUUMsUUFBVztZQUN2RG5CLE9BQU9rQixPQUFPaEUsVUFBVSxDQUFDdUIsUUFBUTtZQUNqQ3VDLE9BQU8sR0FBaUJFLE9BQWRBLE9BQU9wQyxJQUFJLEVBQThDLE9BQTNDb0MsT0FBT0UsTUFBTSxHQUFHLEtBQW1CLE9BQWRGLE9BQU9FLE1BQU0sRUFBQyxPQUFLO1lBQ2hFQyxNQUFNSCxPQUFPRyxJQUFJO1lBQ2pCRCxRQUFRRixPQUFPRSxNQUFNO1lBQ3JCRSxXQUFXLFVBQTRDSCxPQUFsQ0QsT0FBTzFJLEVBQUUsSUFBSTBJLE9BQU9oRSxVQUFVLEVBQUMsS0FBUyxPQUFOaUU7UUFDekQ7SUFFQSxNQUFNSSxjQUFjOUQsU0FBU1QsR0FBRyxDQUFDLENBQUN3RSxNQUFNTCxRQUFXO1lBQ2pEbkIsT0FBT3dCLEtBQUt0RSxVQUFVLENBQUN1QixRQUFRO1lBQy9CdUMsT0FBT1EsS0FBSzFDLElBQUk7WUFDaEJ1QyxNQUFNRyxLQUFLSCxJQUFJO1lBQ2ZDLFdBQVcsUUFBc0NILE9BQTlCSyxLQUFLaEosRUFBRSxJQUFJZ0osS0FBS3RFLFVBQVUsRUFBQyxLQUFTLE9BQU5pRTtRQUNuRDtJQUVBLHNCQUFzQjtJQUN0QjNFLFFBQVFDLEdBQUcsQ0FBQywrQkFBcUI7UUFDL0JtRSxpQkFBaUIvRixXQUFXOEIsTUFBTTtRQUNsQ21FLGVBQWVyRCxTQUFTZCxNQUFNO1FBQzlCOEUsb0JBQW9CUixjQUFjdEUsTUFBTTtRQUN4QytFLGtCQUFrQkgsWUFBWTVFLE1BQU07UUFDcENnRixVQUFVLEVBQUV4RyxvQkFBQUEsOEJBQUFBLFFBQVN1RixJQUFJO1FBQ3pCa0IsUUFBUSxHQUFFMUosU0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFPd0ksSUFBSTtJQUN2QjtJQUVBLHdGQUF3RjtJQUN4RmxLLGdEQUFTQSxDQUFDO1FBQ1JnRyxRQUFRQyxHQUFHLENBQUMsd0NBQThCO1lBQ3hDb0Y7WUFDQXpHO1lBQ0FzRyxrQkFBa0JILFlBQVk1RSxNQUFNO1lBQ3BDOEUsb0JBQW9CUixjQUFjdEUsTUFBTTtZQUN4Q21GLGVBQWUsQ0FBQyxDQUFFckosQ0FBQUEsU0FBU0UsVUFBVSxJQUFJRixTQUFTRyxVQUFVLElBQUlILFNBQVNJLFFBQVE7UUFDbkY7SUFDRixHQUFHO1FBQUNnSjtRQUFjekc7UUFBZ0JtRyxZQUFZNUUsTUFBTTtRQUFFc0UsY0FBY3RFLE1BQU07UUFBRWxFO0tBQVM7SUFFckYsd0ZBQXdGO0lBQ3hGLE1BQU1zSixpQkFBaUI7UUFDckIsSUFBSSxDQUFDckksU0FBUyxPQUFPO1lBQUV3SCxRQUFRO1lBQU1jLFVBQVU7WUFBTUMsVUFBVTtRQUFLO1FBRXBFLDBDQUEwQztRQUMxQyxNQUFNQyxvQkFBb0JqQixjQUFja0IsSUFBSSxDQUFDbEYsQ0FBQUEsSUFBS0EsRUFBRStDLEtBQUssS0FBS3ZILFNBQVNJLFFBQVE7UUFDL0UsTUFBTXVKLHNCQUFzQmIsWUFBWVksSUFBSSxDQUFDekUsQ0FBQUEsSUFBS0EsRUFBRXNDLEtBQUssS0FBS3ZILFNBQVNFLFVBQVU7UUFDakYsTUFBTTBKLHNCQUFzQmQsWUFBWVksSUFBSSxDQUFDekUsQ0FBQUEsSUFBS0EsRUFBRXNDLEtBQUssS0FBS3ZILFNBQVNHLFVBQVU7UUFFakYsT0FBTztZQUNMc0ksUUFBUWdCLHFCQUFxQjtnQkFDM0JsQyxPQUFPdkgsU0FBU0ksUUFBUTtnQkFDeEJtSSxPQUFPdEgsUUFBUTRJLFVBQVU7Z0JBQ3pCakIsTUFBTTtZQUNSO1lBQ0FXLFVBQVVJLHVCQUF1QjtnQkFDL0JwQyxPQUFPdkgsU0FBU0UsVUFBVTtnQkFDMUJxSSxPQUFPdEgsUUFBUTZJLFlBQVk7Z0JBQzNCbEIsTUFBTTNILFFBQVE4SSxZQUFZO1lBQzVCO1lBQ0FQLFVBQVVJLHVCQUF1QjtnQkFDL0JyQyxPQUFPdkgsU0FBU0csVUFBVTtnQkFDMUJvSSxPQUFPdEgsUUFBUStJLFlBQVk7Z0JBQzNCcEIsTUFBTTNILFFBQVFnSixZQUFZO1lBQzVCO1FBQ0Y7SUFDRjtJQUVBLE1BQU0sRUFBRXhCLFFBQVF5QixjQUFjLEVBQUVYLFVBQVVZLGdCQUFnQixFQUFFWCxVQUFVWSxnQkFBZ0IsRUFBRSxHQUFHZDtJQUUzRix3QkFBd0I7SUFDeEJ2RixRQUFRQyxHQUFHLENBQUM7SUFDWkQsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQmtHO0lBQy9CbkcsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQm1HO0lBQ2pDcEcsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQm9HO0lBRWpDLGtCQUFrQjtJQUNsQnJHLFFBQVFDLEdBQUcsQ0FBQywyQkFBaUJoRTtJQUM3QitELFFBQVFDLEdBQUcsQ0FBQywrQkFBcUI7UUFBRXZFLE9BQU9xSixZQUFZNUUsTUFBTTtRQUFFeEIsU0FBUzhGLGNBQWN0RSxNQUFNO0lBQUM7SUFFNUYsZ0VBQWdFO0lBQ2hFLE1BQU0sQ0FBQ21HLFlBQVlDLGNBQWMsR0FBR3hNLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3lNLGFBQWFDLGVBQWUsR0FBRzFNLCtDQUFRQSxDQUFDO0lBRS9DQyxnREFBU0EsQ0FBQztRQUNSLG1EQUFtRDtRQUNuRCxJQUFJK0ssWUFBWTVFLE1BQU0sR0FBRyxLQUFLc0UsY0FBY3RFLE1BQU0sR0FBRyxLQUFLbEUsU0FBU0UsVUFBVSxFQUFFO1lBQzdFb0ssY0FBY2xHLENBQUFBLE9BQVFBLE9BQU87UUFDL0I7SUFDRixHQUFHO1FBQUMwRSxZQUFZNUUsTUFBTTtRQUFFc0UsY0FBY3RFLE1BQU07UUFBRWxFLFNBQVNFLFVBQVU7UUFBRUYsU0FBU0csVUFBVTtRQUFFSCxTQUFTSSxRQUFRO0tBQUM7SUFFMUcsK0NBQStDO0lBQy9DckMsZ0RBQVNBLENBQUM7UUFDUnlNLGVBQWVwRyxDQUFBQSxPQUFRQSxPQUFPO0lBQ2hDLEdBQUc7UUFBQzBFLFlBQVk1RSxNQUFNO1FBQUVzRSxjQUFjdEUsTUFBTTtLQUFDO0lBRTdDLCtCQUErQjtJQUMvQkgsUUFBUUMsR0FBRyxDQUFDO0lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxhQUFhO1FBQUV5RyxNQUFNekssU0FBU0UsVUFBVTtRQUFFd0ssTUFBTTFLLFNBQVNHLFVBQVU7UUFBRXNJLFFBQVF6SSxTQUFTSSxRQUFRO0lBQUM7SUFDM0cyRCxRQUFRQyxHQUFHLENBQUMsa0JBQWtCO1FBQzVCeUcsTUFBTSxPQUFPekssU0FBU0UsVUFBVTtRQUNoQ3dLLE1BQU0sT0FBTzFLLFNBQVNHLFVBQVU7UUFDaENzSSxRQUFRLE9BQU96SSxTQUFTSSxRQUFRO0lBQ2xDO0lBRUEsSUFBSTBJLFlBQVk1RSxNQUFNLEdBQUcsR0FBRztRQUMxQkgsUUFBUUMsR0FBRyxDQUFDLHdCQUF3QjhFLFlBQVkxQyxLQUFLLENBQUMsR0FBRztRQUN6RHJDLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0I4RSxZQUFZMUMsS0FBSyxDQUFDLEdBQUcsR0FBRzdCLEdBQUcsQ0FBQ1UsQ0FBQUEsSUFBTTtnQkFBRXNDLE9BQU90QyxFQUFFc0MsS0FBSztnQkFBRW9ELE1BQU0sT0FBTzFGLEVBQUVzQyxLQUFLO1lBQUM7UUFFM0csdUNBQXVDO1FBQ3ZDLE1BQU1xRCxvQkFBb0I5QixZQUFZWSxJQUFJLENBQUN6RSxDQUFBQSxJQUFLQSxFQUFFc0MsS0FBSyxLQUFLO1FBQzVELE1BQU1zRCxvQkFBb0IvQixZQUFZWSxJQUFJLENBQUN6RSxDQUFBQSxJQUFLQSxFQUFFc0MsS0FBSyxLQUFLO1FBQzVEeEQsUUFBUUMsR0FBRyxDQUFDLHNDQUE0QjtZQUN0QyxtQkFBbUI0RztZQUNuQixvQkFBb0JDO1lBQ3BCLGNBQWMvQixZQUFZNUUsTUFBTTtRQUNsQztJQUNGO0lBRUEsSUFBSXNFLGNBQWN0RSxNQUFNLEdBQUcsR0FBRztRQUM1QkgsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQndFLGNBQWNwQyxLQUFLLENBQUMsR0FBRztRQUM3RHJDLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0J3RSxjQUFjcEMsS0FBSyxDQUFDLEdBQUcsR0FBRzdCLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBTTtnQkFBRStDLE9BQU8vQyxFQUFFK0MsS0FBSztnQkFBRW9ELE1BQU0sT0FBT25HLEVBQUUrQyxLQUFLO1lBQUM7UUFFL0cseUNBQXlDO1FBQ3pDLE1BQU11RCxrQkFBa0J0QyxjQUFja0IsSUFBSSxDQUFDbEYsQ0FBQUEsSUFBS0EsRUFBRStDLEtBQUssS0FBSztRQUM1RHhELFFBQVFDLEdBQUcsQ0FBQyx1Q0FBNkI7WUFDdkMsZ0JBQWdCOEc7WUFDaEIsZ0JBQWdCdEMsY0FBY3RFLE1BQU07UUFDdEM7SUFDRjtJQUVBSCxRQUFRQyxHQUFHLENBQUMseUNBQStCO1FBQ3pDK0csWUFBWWpDLFlBQVlrQyxJQUFJLENBQUMvRixDQUFBQSxJQUFLQSxFQUFFc0MsS0FBSyxLQUFLdkgsU0FBU0UsVUFBVTtRQUNqRStLLFlBQVluQyxZQUFZa0MsSUFBSSxDQUFDL0YsQ0FBQUEsSUFBS0EsRUFBRXNDLEtBQUssS0FBS3ZILFNBQVNHLFVBQVU7UUFDakUrSyxjQUFjMUMsY0FBY3dDLElBQUksQ0FBQ3hHLENBQUFBLElBQUtBLEVBQUUrQyxLQUFLLEtBQUt2SCxTQUFTSSxRQUFRO0lBQ3JFO0lBRUEsd0NBQXdDO0lBQ3hDLE1BQU0rSyx1QkFBdUI7WUFBQyxFQUM1QjVDLEtBQUssRUFDTDZDLGNBQWMsRUFDZEMsY0FBYyxjQUFjLEVBSzdCO1FBQ0MsTUFBTUMsVUFBVUMsc0JBQTBDLElBQUk7UUFFOUQsMEJBQTBCO1FBQzFCeEgsUUFBUUMsR0FBRyxDQUFDLGdCQUFZLE9BQU51RSxPQUFNLGNBQVk7WUFBRTZDO1lBQWdCQztZQUFhSyxXQUFXLENBQUMsQ0FBQ047UUFBZTtRQUUvRixxQkFDRSw4REFBQ087WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzhCQUEwQ3JEOzs7Ozs7Z0JBQ3hENkMsK0JBQ0MsOERBQUNPO29CQUFJQyxXQUFVOzt3QkFDWlIsZUFBZXhDLElBQUksa0JBQ2xCLDhEQUFDaUQ7NEJBQ0NDLEtBQUssR0FBY1YsT0FBWEUsU0FBUSxLQUF1QixPQUFwQkYsZUFBZXhDLElBQUk7NEJBQ3RDbUQsS0FBS1gsZUFBZTdDLEtBQUs7NEJBQ3pCcUQsV0FBVTs0QkFDVmpHLFNBQVMsQ0FBQ3FCO2dDQUNSQSxFQUFFZ0YsYUFBYSxDQUFDQyxLQUFLLENBQUNDLE9BQU8sR0FBRzs0QkFDbEM7Ozs7OztzQ0FHSiw4REFBQ0M7NEJBQUtQLFdBQVU7c0NBQ2JSLGVBQWU3QyxLQUFLOzs7Ozs7Ozs7Ozt5Q0FJekIsOERBQUNvRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDTztnQ0FBS1AsV0FBVTswQ0FBd0I7Ozs7Ozs7Ozs7O3NDQUUxQyw4REFBQ087NEJBQUtQLFdBQVU7c0NBQXdCUDs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS2xEO0lBRUEsb0VBQW9FO0lBQ3BFLE1BQU1lLHVCQUF1QjtRQUMzQixNQUFNZCxVQUFVQyxzQkFBMEMsSUFBSTtRQUU5RCx3QkFBd0I7UUFDeEJ4SCxRQUFRQyxHQUFHLENBQUMsd0NBQThCO1lBQUVrRztZQUFnQm1DLFdBQVcsQ0FBQyxDQUFDbkM7UUFBZTtRQUV4RixxQkFDRSw4REFBQ3lCO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDWjFCLCtCQUNDOzs0QkFDR0EsZUFBZXRCLElBQUksa0JBQ2xCLDhEQUFDaUQ7Z0NBQ0NDLEtBQUssR0FBYzVCLE9BQVhvQixTQUFRLEtBQXVCLE9BQXBCcEIsZUFBZXRCLElBQUk7Z0NBQ3RDbUQsS0FBSzdCLGVBQWUzQixLQUFLO2dDQUN6QnFELFdBQVU7Z0NBQ1ZqRyxTQUFTLENBQUNxQjtvQ0FDUkEsRUFBRWdGLGFBQWEsQ0FBQ0MsS0FBSyxDQUFDQyxPQUFPLEdBQUc7Z0NBQ2xDOzs7Ozs7MENBR0osOERBQUNDO2dDQUFLUCxXQUFVOzBDQUNiMUIsZUFBZTNCLEtBQUs7Ozs7Ozs7cURBSXpCOzswQ0FDRSw4REFBQ29EO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDTztvQ0FBS1AsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OzBDQUUxQyw4REFBQ087Z0NBQUtQLFdBQVU7MENBQXVCOzs7Ozs7Ozs7Ozs7OzhCQU03Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FBeUM7Ozs7OztzQ0FDeEQsOERBQUM3TSx1RkFBcUJBOzRCQUVwQnNNLGFBQWExSSxpQkFBaUIsdUJBQXVCOzRCQUNyRDJKLG1CQUFrQjs0QkFDbEJDLFFBQVE7NEJBQ1JoRixPQUFPdkgsU0FBU0ksUUFBUTs0QkFDeEJvTSxlQUFlLENBQUNqRixRQUFVRixlQUFlLFlBQVlFOzRCQUNyRGtGLFNBQVNqRTs0QkFDVDVGLE9BQU85QixPQUFPVixRQUFROzRCQUN0QnNNLFVBQVUvSjs0QkFDVmdLLFVBQVVsRjs0QkFDVm1GLFlBQVk5RTs0QkFDWkksU0FBU3hGLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU3VGLElBQUksSUFBR25HLGFBQWFZLFFBQVF1RixJQUFJLENBQUNELFVBQVUsR0FBRzs0QkFDaEU5RyxXQUFXeUI7MkJBWk4sVUFBc0IsT0FBWjRIOzs7Ozs7Ozs7Ozs7Ozs7OztJQWlCekI7SUFFQSxxREFBcUQ7SUFDckQsTUFBTXNDLGdCQUFnQjFMLGtCQUFrQndCLGtCQUFrQnlHO0lBRTFELElBQUl5RCxlQUFlO1FBQ2pCLHFCQUNFLDhEQUFDbEI7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQzVNLDZEQUFRQTs0QkFBQzRNLFdBQVU7Ozs7OztzQ0FDcEIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzVNLDZEQUFRQTtvQ0FBQzRNLFdBQVU7Ozs7Ozs4Q0FDcEIsOERBQUM1TSw2REFBUUE7b0NBQUM0TSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBR3hCLDhEQUFDdk4scURBQUlBOztzQ0FDSCw4REFBQ0csMkRBQVVBOzs4Q0FDVCw4REFBQ1EsNkRBQVFBO29DQUFDNE0sV0FBVTs7Ozs7OzhDQUNwQiw4REFBQzVNLDZEQUFRQTtvQ0FBQzRNLFdBQVU7Ozs7Ozs7Ozs7OztzQ0FFdEIsOERBQUN0Tiw0REFBV0E7NEJBQUNzTixXQUFVOzs4Q0FDckIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzVNLDZEQUFRQTs0Q0FBQzRNLFdBQVU7Ozs7OztzREFDcEIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQzVNLDZEQUFRQTtvREFBQzRNLFdBQVU7Ozs7Ozs4REFDcEIsOERBQUM1TSw2REFBUUE7b0RBQUM0TSxXQUFVOzs7Ozs7Ozs7Ozs7c0RBRXRCLDhEQUFDNU0sNkRBQVFBOzRDQUFDNE0sV0FBVTs7Ozs7Ozs7Ozs7OzhDQUV0Qiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDNU0sNkRBQVFBOzRDQUFDNE0sV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDNU0sNkRBQVFBO29EQUFDNE0sV0FBVTs7Ozs7OzhEQUNwQiw4REFBQzVNLDZEQUFRQTtvREFBQzRNLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHeEIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzVNLDZEQUFRQTs0Q0FBQzRNLFdBQVU7Ozs7OztzREFDcEIsOERBQUM1TSw2REFBUUE7NENBQUM0TSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNaEM7SUFFQSx1REFBdUQ7SUFDdkQsSUFBSSxDQUFDM0ssV0FBVzRCLGdCQUFnQmlLLFlBQVk7UUFDMUMscUJBQ0UsOERBQUNuQjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNsTix5REFBTUE7d0JBQUNxTyxTQUFRO3dCQUFVQyxTQUFTLElBQU1yTixPQUFPc04sSUFBSTs7MENBQ2xELDhEQUFDN04sb0dBQVNBO2dDQUFDd00sV0FBVTs7Ozs7OzRCQUFpQjs7Ozs7Ozs7Ozs7OzhCQUsxQyw4REFBQ3ZOLHFEQUFJQTs4QkFDSCw0RUFBQ0MsNERBQVdBO3dCQUFDc04sV0FBVTtrQ0FDckIsNEVBQUNEOzRCQUFJQyxXQUFVOztnQ0FDWixDQUFDM0sseUJBQVcsOERBQUNpTTtvQ0FBRXRCLFdBQVU7OENBQW9COzs7Ozs7Z0NBQzdDL0ksOEJBQWdCLDhEQUFDcUs7b0NBQUV0QixXQUFVOzt3Q0FBb0I7d0NBQXlCL0ksYUFBYStDLE9BQU87Ozs7Ozs7Z0NBQzlGa0gsNEJBQWMsOERBQUNJO29DQUFFdEIsV0FBVTs7d0NBQW9CO3dDQUF1QmtCLFdBQVdsSCxPQUFPOzs7Ozs7OzhDQUN6Riw4REFBQ2xILHlEQUFNQTtvQ0FBQ3NPLFNBQVMsSUFBTXJOLE9BQU8rRixJQUFJLENBQUM7OENBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBUXZFO0lBRUEscUJBQ0UsOERBQUNpRztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDbE4seURBQU1BO3dCQUFDcU8sU0FBUTt3QkFBVUMsU0FBUyxJQUFNck4sT0FBT3NOLElBQUk7OzBDQUNsRCw4REFBQzdOLG9HQUFTQTtnQ0FBQ3dNLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7a0NBR3hDLDhEQUFDRDs7MENBQ0MsOERBQUN3QjtnQ0FBR3ZCLFdBQVU7O29DQUFtQztvQ0FDaEMzSyxRQUFRNkksWUFBWTtvQ0FBQztvQ0FBSzdJLFFBQVErSSxZQUFZOzs7Ozs7OzBDQUUvRCw4REFBQ2tEO2dDQUFFdEIsV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLdEMsOERBQUN2TixxREFBSUE7O2tDQUNILDhEQUFDRywyREFBVUE7OzBDQUNULDhEQUFDQywwREFBU0E7Z0NBQUNtTixXQUFVOztrREFDbkIsOERBQUN0TSxvR0FBUUE7d0NBQUNzTSxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7OzBDQUd2Qyw4REFBQ3JOLGdFQUFlQTswQ0FBQzs7Ozs7Ozs7Ozs7O2tDQUluQiw4REFBQ0QsNERBQVdBO2tDQUNWLDRFQUFDOE87NEJBQUtDLFVBQVV0Rzs0QkFBYzZFLFdBQVU7OzhDQUN0Qyw4REFBQy9NLGtFQUFXQTtvQ0FBQ3lPLE9BQU07b0NBQXNCQyxhQUFZOztzREFFbkQsOERBQUM1Qjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQ1I7NERBRUM1QyxPQUFNOzREQUNONkMsZ0JBQWdCakI7NERBQ2hCa0IsYUFBWTsyREFIUCxRQUFtQixPQUFYaEI7Ozs7O3NFQUtmLDhEQUFDdEwsdUZBQXFCQTs0REFFcEJ3SixPQUFNOzREQUNOOEMsYUFBYWpDLGVBQWUscUJBQXFCOzREQUNqRGtELG1CQUFrQjs0REFDbEJDLFFBQVE7NERBQ1JoRixPQUFPdkgsU0FBU0UsVUFBVTs0REFDMUJzTSxlQUFlLENBQUNqRixRQUFVRixlQUFlLGNBQWNFOzREQUN2RGtGLFNBQVMzRDs0REFDVGxHLE9BQU85QixPQUFPWixVQUFVOzREQUN4QndNLFVBQVV0RDs0REFDVnVELFVBQVVoRjs0REFDVmlGLFlBQVl4RTs0REFDWkYsU0FBU3pJLEVBQUFBLFVBQUFBLG1CQUFBQSw4QkFBQUEsUUFBT3dJLElBQUksSUFBR25ELFdBQVdyRixNQUFNd0ksSUFBSSxDQUFDRCxVQUFVLEdBQUc7NERBQzFEOUcsV0FBV2tJOzJEQWJOLGFBQXlCLE9BQVptQjs7Ozs7Ozs7Ozs7OERBaUJ0Qiw4REFBQ29COztzRUFDQyw4REFBQ1I7NERBRUM1QyxPQUFNOzREQUNONkMsZ0JBQWdCaEI7NERBQ2hCaUIsYUFBWTsyREFIUCxRQUFtQixPQUFYaEI7Ozs7O3NFQUtmLDhEQUFDdEwsdUZBQXFCQTs0REFFcEJ3SixPQUFNOzREQUNOOEMsYUFBYWpDLGVBQWUscUJBQXFCOzREQUNqRGtELG1CQUFrQjs0REFDbEJDLFFBQVE7NERBQ1JoRixPQUFPdkgsU0FBU0csVUFBVTs0REFDMUJxTSxlQUFlLENBQUNqRixRQUFVRixlQUFlLGNBQWNFOzREQUN2RGtGLFNBQVMzRCxZQUFZbkUsTUFBTSxDQUFDb0UsQ0FBQUEsT0FBUUEsS0FBS3hCLEtBQUssS0FBS3ZILFNBQVNFLFVBQVU7NERBQ3RFMEMsT0FBTzlCLE9BQU9YLFVBQVU7NERBQ3hCdU0sVUFBVXREOzREQUNWdUQsVUFBVWhGOzREQUNWaUYsWUFBWXhFOzREQUNaRixTQUFTekksRUFBQUEsVUFBQUEsbUJBQUFBLDhCQUFBQSxRQUFPd0ksSUFBSSxJQUFHbkQsV0FBV3JGLE1BQU13SSxJQUFJLENBQUNELFVBQVUsR0FBRzs0REFDMUQ5RyxXQUFXa0k7MkRBYk4sYUFBeUIsT0FBWm1COzs7Ozs7Ozs7Ozs7Ozs7OztzREFtQnhCLDhEQUFDb0I7c0RBQ0MsNEVBQUNTOzs7Ozs7Ozs7Ozs7Ozs7OzhDQUlMLDhEQUFDdk4sa0VBQVdBO29DQUFDeU8sT0FBTTtvQ0FBV0MsYUFBWTs7c0RBQ3hDLDhEQUFDNUI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDak4saUVBQVVBO29EQUNUNEosT0FBTTtvREFDTm9DLE1BQUs7b0RBQ0w0QixRQUFRO29EQUNSaEYsT0FBT3ZILFNBQVNLLElBQUk7b0RBQ3BCbU4sVUFBVSxDQUFDeEcsSUFBTUssZUFBZSxRQUFRTCxFQUFFeUcsTUFBTSxDQUFDbEcsS0FBSztvREFDdEQzRSxPQUFPOUIsT0FBT1QsSUFBSTtvREFDbEJrTixhQUFZOzs7Ozs7OERBR2QsOERBQUM1TyxpRUFBVUE7b0RBQ1Q0SixPQUFNO29EQUNOb0MsTUFBSztvREFDTDRCLFFBQVE7b0RBQ1JoRixPQUFPdkgsU0FBU00sSUFBSTtvREFDcEJrTixVQUFVLENBQUN4RyxJQUFNSyxlQUFlLFFBQVFMLEVBQUV5RyxNQUFNLENBQUNsRyxLQUFLO29EQUN0RDNFLE9BQU85QixPQUFPUixJQUFJO29EQUNsQmlOLGFBQWEsZUFBZ0UsT0FBakRHLEtBQUtDLGNBQWMsR0FBR0MsZUFBZSxHQUFHQyxRQUFRLEVBQUM7Ozs7Ozs7Ozs7OztzREFJakYsOERBQUNsQzs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ3NCO2dEQUFFdEIsV0FBVTs7a0VBQ1gsOERBQUNPO3dEQUFLUCxXQUFVO2tFQUFxQjs7Ozs7O2tFQUNyQyw4REFBQ2tDO2tFQUFPOzs7Ozs7b0RBQXVCO29EQUE4Q0osS0FBS0MsY0FBYyxHQUFHQyxlQUFlLEdBQUdDLFFBQVE7b0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNcEksOERBQUNoUCxrRUFBV0E7b0NBQUN5TyxPQUFNO29DQUFlQyxhQUFZOztzREFDNUMsOERBQUM1Qjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNoTixrRUFBV0E7b0RBQ1YySixPQUFNO29EQUNOOEMsYUFBWTtvREFDWmtCLFFBQVE7b0RBQ1JoRixPQUFPdkgsU0FBU1UsTUFBTTtvREFDdEI4TCxlQUFlLENBQUNqRixRQUFVRixlQUFlLFVBQVVFO29EQUNuRGtGLFNBQVNuRTtvREFDVDFGLE9BQU85QixPQUFPSixNQUFNOzs7Ozs7OERBR3RCLDhEQUFDL0IsaUVBQVVBO29EQUNUNEosT0FBTTtvREFDTm9DLE1BQUs7b0RBQ0xvRCxLQUFJO29EQUNKeEcsT0FBT3ZILFNBQVNXLFNBQVM7b0RBQ3pCNk0sVUFBVSxDQUFDeEcsSUFBTUssZUFBZSxhQUFhTCxFQUFFeUcsTUFBTSxDQUFDbEcsS0FBSzs7Ozs7OzhEQUc3RCw4REFBQzVJLGlFQUFVQTtvREFDVDRKLE9BQU07b0RBQ05vQyxNQUFLO29EQUNMb0QsS0FBSTtvREFDSnhHLE9BQU92SCxTQUFTWSxTQUFTO29EQUN6QjRNLFVBQVUsQ0FBQ3hHLElBQU1LLGVBQWUsYUFBYUwsRUFBRXlHLE1BQU0sQ0FBQ2xHLEtBQUs7Ozs7Ozs7Ozs7OztzREFJL0QsOERBQUM1SSxpRUFBVUE7NENBQ1Q0SixPQUFNOzRDQUNOb0MsTUFBSzs0Q0FDTG9ELEtBQUk7NENBQ0pDLEtBQUk7NENBQ0p6RyxPQUFPdkgsU0FBU2EsT0FBTzs0Q0FDdkIyTSxVQUFVLENBQUN4RyxJQUFNSyxlQUFlLFdBQVdMLEVBQUV5RyxNQUFNLENBQUNsRyxLQUFLOzRDQUN6RGdHLGFBQVk7Ozs7Ozs7Ozs7Ozs4Q0FJaEIsOERBQUMxTyxrRUFBV0E7b0NBQUN5TyxPQUFNO29DQUE0QkMsYUFBWTs7c0RBQ3pELDhEQUFDNUI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDak4saUVBQVVBO29EQUNUNEosT0FBTTtvREFDTjhDLGFBQVk7b0RBQ1o5RCxPQUFPdkgsU0FBU08sU0FBUztvREFDekJpTixVQUFVLENBQUN4RyxJQUFNSyxlQUFlLGFBQWFMLEVBQUV5RyxNQUFNLENBQUNsRyxLQUFLOzs7Ozs7OERBRzdELDhEQUFDNUksaUVBQVVBO29EQUNUNEosT0FBTTtvREFDTjhDLGFBQVk7b0RBQ1o5RCxPQUFPdkgsU0FBU1EsU0FBUztvREFDekJnTixVQUFVLENBQUN4RyxJQUFNSyxlQUFlLGFBQWFMLEVBQUV5RyxNQUFNLENBQUNsRyxLQUFLOzs7Ozs7Ozs7Ozs7c0RBSS9ELDhEQUFDb0U7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDak4saUVBQVVBO29EQUNUNEosT0FBTTtvREFDTjhDLGFBQVk7b0RBQ1o5RCxPQUFPdkgsU0FBU1MsS0FBSztvREFDckIrTSxVQUFVLENBQUN4RyxJQUFNSyxlQUFlLFNBQVNMLEVBQUV5RyxNQUFNLENBQUNsRyxLQUFLOzs7Ozs7OERBR3pELDhEQUFDNUksaUVBQVVBO29EQUNUNEosT0FBTTtvREFDTjhDLGFBQVk7b0RBQ1o5RCxPQUFPdkgsU0FBU3VHLE9BQU8sSUFBSTtvREFDM0JpSCxVQUFVLENBQUN4RyxJQUFNSyxlQUFlLFdBQVdMLEVBQUV5RyxNQUFNLENBQUNsRyxLQUFLOzs7Ozs7Ozs7Ozs7c0RBSTdELDhEQUFDb0U7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDak4saUVBQVVBO29EQUNUNEosT0FBTTtvREFDTm9DLE1BQUs7b0RBQ0xVLGFBQVk7b0RBQ1o5RCxPQUFPdkgsU0FBU3dHLFdBQVcsSUFBSTtvREFDL0JnSCxVQUFVLENBQUN4RyxJQUFNSyxlQUFlLGVBQWVMLEVBQUV5RyxNQUFNLENBQUNsRyxLQUFLOzs7Ozs7OERBRy9ELDhEQUFDNUksaUVBQVVBO29EQUNUNEosT0FBTTtvREFDTjhDLGFBQVk7b0RBQ1o5RCxPQUFPdkgsU0FBU3lHLE9BQU8sSUFBSTtvREFDM0IrRyxVQUFVLENBQUN4RyxJQUFNSyxlQUFlLFdBQVdMLEVBQUV5RyxNQUFNLENBQUNsRyxLQUFLOzs7Ozs7OERBRzNELDhEQUFDNUksaUVBQVVBO29EQUNUNEosT0FBTTtvREFDTm9DLE1BQUs7b0RBQ0xVLGFBQVk7b0RBQ1o5RCxPQUFPdkgsU0FBUzBHLFVBQVUsSUFBSTtvREFDOUI4RyxVQUFVLENBQUN4RyxJQUFNSyxlQUFlLGNBQWNMLEVBQUV5RyxNQUFNLENBQUNsRyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS2xFLDhEQUFDekksa0VBQVdBOztzREFDViw4REFBQ0oseURBQU1BOzRDQUNMaU0sTUFBSzs0Q0FDTG9DLFNBQVE7NENBQ1JDLFNBQVMsSUFBTXJOLE9BQU9zTixJQUFJOzRDQUMxQlAsVUFBVXRILGVBQWU2SSxTQUFTO3NEQUNuQzs7Ozs7O3NEQUdELDhEQUFDdlAseURBQU1BOzRDQUNMaU0sTUFBSzs0Q0FDTCtCLFVBQVV0SCxlQUFlNkksU0FBUzs7OERBRWxDLDhEQUFDNU8sb0dBQUlBO29EQUFDdU0sV0FBVTs7Ozs7O2dEQUNmeEcsZUFBZTZJLFNBQVMsR0FBRyxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVE1RDtHQTcxQndCek87O1FBQ1B4QixzREFBU0E7UUFDVEMsc0RBQVNBO1FBQ0pHLGtFQUFjQTtRQXFCbUJELDREQUFRQTtRQWtCYUEsNERBQVFBO1FBV0ZBLDREQUFRQTtRQVdSQSw0REFBUUE7UUFrRWpFRCwrREFBV0E7OztLQWxJWnNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ZpeHR1cmVzL1tpZF0vZWRpdC9wYWdlLnRzeD9iZDFiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVBhcmFtcywgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZU11dGF0aW9uLCB1c2VRdWVyeSwgdXNlUXVlcnlDbGllbnQgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnknO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQge1xuICBJbnB1dEZpZWxkLFxuICBTZWxlY3RGaWVsZCxcbiAgRm9ybVNlY3Rpb24sXG4gIEZvcm1BY3Rpb25zXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9mb3JtLWZpZWxkJztcbmltcG9ydCB7IFNlYXJjaGFibGVTZWxlY3RGaWVsZCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9TZWFyY2hhYmxlU2VsZWN0RmllbGQnO1xuaW1wb3J0IHsgU2tlbGV0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2tlbGV0b24nO1xuaW1wb3J0IHsgZml4dHVyZXNBcGkgfSBmcm9tICdAL2xpYi9hcGkvZml4dHVyZXMnO1xuaW1wb3J0IHsgbGVhZ3Vlc0FwaSB9IGZyb20gJ0AvbGliL2FwaS9sZWFndWVzJztcbmltcG9ydCB7IHRlYW1zQXBpIH0gZnJvbSAnQC9saWIvYXBpL3RlYW1zJztcbmltcG9ydCB7IEFycm93TGVmdCwgU2F2ZSwgQ2FsZW5kYXIgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInO1xuXG5pbnRlcmZhY2UgRml4dHVyZUZvcm1EYXRhIHtcbiAgaG9tZVRlYW1JZDogc3RyaW5nO1xuICBhd2F5VGVhbUlkOiBzdHJpbmc7XG4gIGxlYWd1ZUlkOiBzdHJpbmc7XG4gIGRhdGU6IHN0cmluZztcbiAgdGltZTogc3RyaW5nO1xuICB2ZW51ZU5hbWU6IHN0cmluZztcbiAgdmVudWVDaXR5OiBzdHJpbmc7XG4gIHJvdW5kOiBzdHJpbmc7XG4gIHN0YXR1czogc3RyaW5nO1xuICBnb2Fsc0hvbWU6IHN0cmluZztcbiAgZ29hbHNBd2F5OiBzdHJpbmc7XG4gIGVsYXBzZWQ6IHN0cmluZztcbiAgcmVmZXJlZT86IHN0cmluZztcbiAgdGVtcGVyYXR1cmU/OiBzdHJpbmc7XG4gIHdlYXRoZXI/OiBzdHJpbmc7XG4gIGF0dGVuZGFuY2U/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEVkaXRGaXh0dXJlUGFnZSgpIHtcbiAgY29uc3QgcGFyYW1zID0gdXNlUGFyYW1zKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KCk7XG4gIGNvbnN0IGZpeHR1cmVJZCA9IHBhcnNlSW50KHBhcmFtcy5pZCBhcyBzdHJpbmcpO1xuXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGU8Rml4dHVyZUZvcm1EYXRhPih7XG4gICAgaG9tZVRlYW1JZDogJycsXG4gICAgYXdheVRlYW1JZDogJycsXG4gICAgbGVhZ3VlSWQ6ICcnLFxuICAgIGRhdGU6ICcnLFxuICAgIHRpbWU6ICcnLFxuICAgIHZlbnVlTmFtZTogJycsXG4gICAgdmVudWVDaXR5OiAnJyxcbiAgICByb3VuZDogJycsXG4gICAgc3RhdHVzOiAnJyxcbiAgICBnb2Fsc0hvbWU6ICcnLFxuICAgIGdvYWxzQXdheTogJycsXG4gICAgZWxhcHNlZDogJycsXG4gIH0pO1xuXG4gIGNvbnN0IFtlcnJvcnMsIHNldEVycm9yc10gPSB1c2VTdGF0ZTxQYXJ0aWFsPEZpeHR1cmVGb3JtRGF0YT4+KHt9KTtcblxuICAvLyBGZXRjaCBmaXh0dXJlIGRldGFpbHNcbiAgY29uc3QgeyBkYXRhOiBmaXh0dXJlLCBpc0xvYWRpbmc6IGZpeHR1cmVMb2FkaW5nIH0gPSB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsnZml4dHVyZScsIGZpeHR1cmVJZF0sXG4gICAgcXVlcnlGbjogKCkgPT4gZml4dHVyZXNBcGkuZ2V0Rml4dHVyZShmaXh0dXJlSWQpLFxuICAgIGVuYWJsZWQ6ICEhZml4dHVyZUlkLFxuICB9KTtcblxuICAvLyBTbWFydCBsb2FkaW5nIHdpdGggc2VhcmNoIGFuZCBwYWdpbmF0aW9uIC0gU0VQQVJBVEVEIGZvciBIb21lL0F3YXlcbiAgY29uc3QgW2xlYWd1ZVNlYXJjaCwgc2V0TGVhZ3VlU2VhcmNoXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2hvbWVUZWFtU2VhcmNoLCBzZXRIb21lVGVhbVNlYXJjaF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFthd2F5VGVhbVNlYXJjaCwgc2V0QXdheVRlYW1TZWFyY2hdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbGVhZ3VlUGFnZSwgc2V0TGVhZ3VlUGFnZV0gPSB1c2VTdGF0ZSgxKTtcbiAgY29uc3QgW2hvbWVUZWFtUGFnZSwgc2V0SG9tZVRlYW1QYWdlXSA9IHVzZVN0YXRlKDEpO1xuICBjb25zdCBbYXdheVRlYW1QYWdlLCBzZXRBd2F5VGVhbVBhZ2VdID0gdXNlU3RhdGUoMSk7XG4gIGNvbnN0IFthbGxMZWFndWVzLCBzZXRBbGxMZWFndWVzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFthbGxIb21lVGVhbXMsIHNldEFsbEhvbWVUZWFtc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbYWxsQXdheVRlYW1zLCBzZXRBbGxBd2F5VGVhbXNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcblxuICAvLyBGZXRjaCBsZWFndWVzIHdpdGggc2VhcmNoIGFuZCBwYWdpbmF0aW9uXG4gIGNvbnN0IHsgZGF0YTogbGVhZ3VlcywgaXNMb2FkaW5nOiBsZWFndWVzTG9hZGluZywgZXJyb3I6IGxlYWd1ZXNFcnJvciB9ID0gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ2xlYWd1ZXMnLCAnc2VhcmNoJywgbGVhZ3VlU2VhcmNoLCBsZWFndWVQYWdlXSxcbiAgICBxdWVyeUZuOiAoKSA9PiBsZWFndWVzQXBpLmdldExlYWd1ZXMoe1xuICAgICAgbGltaXQ6IDEwMCxcbiAgICAgIHBhZ2U6IGxlYWd1ZVBhZ2UsXG4gICAgICAuLi4obGVhZ3VlU2VhcmNoICYmIHsgc2VhcmNoOiBsZWFndWVTZWFyY2ggfSlcbiAgICB9KSxcbiAgICBrZWVwUHJldmlvdXNEYXRhOiB0cnVlLFxuICB9KTtcblxuICAvLyBGZXRjaCBob21lIHRlYW1zIHdpdGggc2VhcmNoIGFuZCBwYWdpbmF0aW9uXG4gIGNvbnN0IHsgZGF0YTogaG9tZVRlYW1zLCBpc0xvYWRpbmc6IGhvbWVUZWFtc0xvYWRpbmcsIGVycm9yOiBob21lVGVhbXNFcnJvciB9ID0gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ2hvbWUtdGVhbXMnLCAnc2VhcmNoJywgaG9tZVRlYW1TZWFyY2gsIGhvbWVUZWFtUGFnZV0sXG4gICAgcXVlcnlGbjogKCkgPT4gdGVhbXNBcGkuZ2V0VGVhbXMoe1xuICAgICAgbGltaXQ6IDEwMCxcbiAgICAgIHBhZ2U6IGhvbWVUZWFtUGFnZSxcbiAgICAgIC4uLihob21lVGVhbVNlYXJjaCAmJiB7IHNlYXJjaDogaG9tZVRlYW1TZWFyY2ggfSlcbiAgICB9KSxcbiAgICBrZWVwUHJldmlvdXNEYXRhOiB0cnVlLFxuICB9KTtcblxuICAvLyBGZXRjaCBhd2F5IHRlYW1zIHdpdGggc2VhcmNoIGFuZCBwYWdpbmF0aW9uXG4gIGNvbnN0IHsgZGF0YTogYXdheVRlYW1zLCBpc0xvYWRpbmc6IGF3YXlUZWFtc0xvYWRpbmcsIGVycm9yOiBhd2F5VGVhbXNFcnJvciB9ID0gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ2F3YXktdGVhbXMnLCAnc2VhcmNoJywgYXdheVRlYW1TZWFyY2gsIGF3YXlUZWFtUGFnZV0sXG4gICAgcXVlcnlGbjogKCkgPT4gdGVhbXNBcGkuZ2V0VGVhbXMoe1xuICAgICAgbGltaXQ6IDEwMCxcbiAgICAgIHBhZ2U6IGF3YXlUZWFtUGFnZSxcbiAgICAgIC4uLihhd2F5VGVhbVNlYXJjaCAmJiB7IHNlYXJjaDogYXdheVRlYW1TZWFyY2ggfSlcbiAgICB9KSxcbiAgICBrZWVwUHJldmlvdXNEYXRhOiB0cnVlLFxuICB9KTtcblxuICAvLyBUcmFjayBwcmV2aW91cyBzZWFyY2ggdG8gZGV0ZWN0IHNlYXJjaCBjaGFuZ2VzXG4gIGNvbnN0IFtwcmV2TGVhZ3VlU2VhcmNoLCBzZXRQcmV2TGVhZ3VlU2VhcmNoXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3ByZXZUZWFtU2VhcmNoLCBzZXRQcmV2VGVhbVNlYXJjaF0gPSB1c2VTdGF0ZSgnJyk7XG5cbiAgLy8gQWNjdW11bGF0ZSBsZWFndWVzIGRhdGEgZm9yIGxvYWQgbW9yZSBmdW5jdGlvbmFsaXR5XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGxlYWd1ZXM/LmRhdGEpIHtcbiAgICAgIC8vIENoZWNrIGlmIHNlYXJjaCBjaGFuZ2VkXG4gICAgICBjb25zdCBzZWFyY2hDaGFuZ2VkID0gbGVhZ3VlU2VhcmNoICE9PSBwcmV2TGVhZ3VlU2VhcmNoO1xuXG4gICAgICBpZiAobGVhZ3VlUGFnZSA9PT0gMSB8fCBzZWFyY2hDaGFuZ2VkKSB7XG4gICAgICAgIC8vIE5ldyBzZWFyY2ggb3IgZmlyc3QgbG9hZCAtIHJlcGxhY2UgZGF0YVxuICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBSZXBsYWNpbmcgbGVhZ3VlcyBkYXRhOicsIHsgcGFnZTogbGVhZ3VlUGFnZSwgc2VhcmNoQ2hhbmdlZCwgc2VhcmNoOiBsZWFndWVTZWFyY2ggfSk7XG4gICAgICAgIHNldEFsbExlYWd1ZXMobGVhZ3Vlcy5kYXRhKTtcbiAgICAgICAgc2V0UHJldkxlYWd1ZVNlYXJjaChsZWFndWVTZWFyY2gpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gTG9hZCBtb3JlIC0gYXBwZW5kIHRvIGV4aXN0aW5nLCBhdm9pZCBkdXBsaWNhdGVzXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIEFwcGVuZGluZyBsZWFndWVzIGRhdGE6JywgeyBwYWdlOiBsZWFndWVQYWdlLCBleGlzdGluZzogYWxsTGVhZ3Vlcy5sZW5ndGgsIG5ldzogbGVhZ3Vlcy5kYXRhLmxlbmd0aCB9KTtcbiAgICAgICAgc2V0QWxsTGVhZ3VlcyhwcmV2ID0+IHtcbiAgICAgICAgICBjb25zdCBleGlzdGluZ0lkcyA9IG5ldyBTZXQocHJldi5tYXAobCA9PiBsLmV4dGVybmFsSWQpKTtcbiAgICAgICAgICBjb25zdCBuZXdMZWFndWVzID0gbGVhZ3Vlcy5kYXRhLmZpbHRlcihsID0+ICFleGlzdGluZ0lkcy5oYXMobC5leHRlcm5hbElkKSk7XG4gICAgICAgICAgcmV0dXJuIFsuLi5wcmV2LCAuLi5uZXdMZWFndWVzXTtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbbGVhZ3VlcywgbGVhZ3VlUGFnZSwgbGVhZ3VlU2VhcmNoLCBwcmV2TGVhZ3VlU2VhcmNoLCBhbGxMZWFndWVzLmxlbmd0aF0pO1xuXG4gIC8vIEFjY3VtdWxhdGUgdGVhbXMgZGF0YSBmb3IgbG9hZCBtb3JlIGZ1bmN0aW9uYWxpdHlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodGVhbXM/LmRhdGEpIHtcbiAgICAgIC8vIENoZWNrIGlmIHNlYXJjaCBjaGFuZ2VkXG4gICAgICBjb25zdCBzZWFyY2hDaGFuZ2VkID0gdGVhbVNlYXJjaCAhPT0gcHJldlRlYW1TZWFyY2g7XG5cbiAgICAgIGlmICh0ZWFtUGFnZSA9PT0gMSB8fCBzZWFyY2hDaGFuZ2VkKSB7XG4gICAgICAgIC8vIE5ldyBzZWFyY2ggb3IgZmlyc3QgbG9hZCAtIHJlcGxhY2UgZGF0YVxuICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBSZXBsYWNpbmcgdGVhbXMgZGF0YTonLCB7IHBhZ2U6IHRlYW1QYWdlLCBzZWFyY2hDaGFuZ2VkLCBzZWFyY2g6IHRlYW1TZWFyY2ggfSk7XG4gICAgICAgIHNldEFsbFRlYW1zKHRlYW1zLmRhdGEpO1xuICAgICAgICBzZXRQcmV2VGVhbVNlYXJjaCh0ZWFtU2VhcmNoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIExvYWQgbW9yZSAtIGFwcGVuZCB0byBleGlzdGluZywgYXZvaWQgZHVwbGljYXRlc1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBBcHBlbmRpbmcgdGVhbXMgZGF0YTonLCB7IHBhZ2U6IHRlYW1QYWdlLCBleGlzdGluZzogYWxsVGVhbXMubGVuZ3RoLCBuZXc6IHRlYW1zLmRhdGEubGVuZ3RoIH0pO1xuICAgICAgICBzZXRBbGxUZWFtcyhwcmV2ID0+IHtcbiAgICAgICAgICBjb25zdCBleGlzdGluZ0lkcyA9IG5ldyBTZXQocHJldi5tYXAodCA9PiB0LmlkIHx8IHQuZXh0ZXJuYWxJZCkpO1xuICAgICAgICAgIGNvbnN0IG5ld1RlYW1zID0gdGVhbXMuZGF0YS5maWx0ZXIodCA9PiAhZXhpc3RpbmdJZHMuaGFzKHQuaWQgfHwgdC5leHRlcm5hbElkKSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgQWRkaW5nIG5ldyB0ZWFtczonLCB7XG4gICAgICAgICAgICBleGlzdGluZzogcHJldi5sZW5ndGgsXG4gICAgICAgICAgICBuZXc6IG5ld1RlYW1zLmxlbmd0aCxcbiAgICAgICAgICAgIHRvdGFsOiBwcmV2Lmxlbmd0aCArIG5ld1RlYW1zLmxlbmd0aFxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybiBbLi4ucHJldiwgLi4ubmV3VGVhbXNdO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFt0ZWFtcywgdGVhbVBhZ2UsIHRlYW1TZWFyY2gsIHByZXZUZWFtU2VhcmNoLCBhbGxUZWFtcy5sZW5ndGhdKTtcblxuICAvLyBVcGRhdGUgbXV0YXRpb25cbiAgY29uc3QgdXBkYXRlTXV0YXRpb24gPSB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKGRhdGE6IGFueSkgPT4gZml4dHVyZXNBcGkudXBkYXRlRml4dHVyZShmaXh0dXJlSWQsIGRhdGEpLFxuICAgIG9uU3VjY2VzczogKCkgPT4ge1xuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydmaXh0dXJlJywgZml4dHVyZUlkXSB9KTtcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnZml4dHVyZXMnXSB9KTtcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ0ZpeHR1cmUgdXBkYXRlZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIHJvdXRlci5wdXNoKGAvZGFzaGJvYXJkL2ZpeHR1cmVzLyR7Zml4dHVyZUlkfWApO1xuICAgIH0sXG4gICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byB1cGRhdGUgZml4dHVyZScpO1xuICAgIH0sXG4gIH0pO1xuXG4gIC8vIFBvcHVsYXRlIGZvcm0gd2hlbiBmaXh0dXJlIGRhdGEgbG9hZHNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoZml4dHVyZSkge1xuICAgICAgY29uc3QgZml4dHVyZURhdGUgPSBuZXcgRGF0ZShmaXh0dXJlLmRhdGUpO1xuICAgICAgY29uc29sZS5sb2coJ/CflIQgUG9wdWxhdGluZyBmb3JtIHdpdGggZml4dHVyZSBkYXRhOicsIHtcbiAgICAgICAgaG9tZVRlYW1JZDogZml4dHVyZS5ob21lVGVhbUlkLFxuICAgICAgICBhd2F5VGVhbUlkOiBmaXh0dXJlLmF3YXlUZWFtSWQsXG4gICAgICAgIGxlYWd1ZUlkOiBmaXh0dXJlLmxlYWd1ZUlkLFxuICAgICAgICB2ZW51ZTogZml4dHVyZS52ZW51ZSxcbiAgICAgICAgdmVudWVOYW1lOiBmaXh0dXJlLnZlbnVlTmFtZSxcbiAgICAgICAgdmVudWVDaXR5OiBmaXh0dXJlLnZlbnVlQ2l0eVxuICAgICAgfSk7XG5cbiAgICAgIC8vIPCflI0gREVCVUc6IENoZWNrIGlmIGZpeHR1cmUgdXNlcyBpbnRlcm5hbCBvciBleHRlcm5hbCBJRHNcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIEZJWFRVUkUgSUQgQU5BTFlTSVM6Jywge1xuICAgICAgICAnZml4dHVyZS5ob21lVGVhbUlkJzogZml4dHVyZS5ob21lVGVhbUlkLFxuICAgICAgICAnZml4dHVyZS5hd2F5VGVhbUlkJzogZml4dHVyZS5hd2F5VGVhbUlkLFxuICAgICAgICAnZml4dHVyZS5sZWFndWVJZCc6IGZpeHR1cmUubGVhZ3VlSWQsXG4gICAgICAgICdmaXh0dXJlIG9iamVjdCc6IGZpeHR1cmVcbiAgICAgIH0pO1xuXG4gICAgICBzZXRGb3JtRGF0YSh7XG4gICAgICAgIGhvbWVUZWFtSWQ6IGZpeHR1cmUuaG9tZVRlYW1JZD8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgYXdheVRlYW1JZDogZml4dHVyZS5hd2F5VGVhbUlkPy50b1N0cmluZygpIHx8ICcnLFxuICAgICAgICBsZWFndWVJZDogZml4dHVyZS5sZWFndWVJZD8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgZGF0ZTogZml4dHVyZURhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgICAgICB0aW1lOiBmaXh0dXJlRGF0ZS50b1RpbWVTdHJpbmcoKS5zbGljZSgwLCA1KSxcbiAgICAgICAgLy8gRml4IHZlbnVlIG1hcHBpbmcgLSBBUEkgcmV0dXJucyB2ZW51ZS5uYW1lIGFuZCB2ZW51ZS5jaXR5XG4gICAgICAgIHZlbnVlTmFtZTogZml4dHVyZS52ZW51ZT8ubmFtZSB8fCBmaXh0dXJlLnZlbnVlTmFtZSB8fCAnJyxcbiAgICAgICAgdmVudWVDaXR5OiBmaXh0dXJlLnZlbnVlPy5jaXR5IHx8IGZpeHR1cmUudmVudWVDaXR5IHx8ICcnLFxuICAgICAgICByb3VuZDogZml4dHVyZS5yb3VuZCB8fCAnJyxcbiAgICAgICAgc3RhdHVzOiBmaXh0dXJlLnN0YXR1cyB8fCAnJyxcbiAgICAgICAgZ29hbHNIb21lOiBmaXh0dXJlLmdvYWxzSG9tZT8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgZ29hbHNBd2F5OiBmaXh0dXJlLmdvYWxzQXdheT8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgZWxhcHNlZDogZml4dHVyZS5lbGFwc2VkPy50b1N0cmluZygpIHx8ICcnLFxuICAgICAgICByZWZlcmVlOiBmaXh0dXJlLnJlZmVyZWUgfHwgJycsXG4gICAgICAgIHRlbXBlcmF0dXJlOiBmaXh0dXJlLnRlbXBlcmF0dXJlPy50b1N0cmluZygpIHx8ICcnLFxuICAgICAgICB3ZWF0aGVyOiBmaXh0dXJlLndlYXRoZXIgfHwgJycsXG4gICAgICAgIGF0dGVuZGFuY2U6IGZpeHR1cmUuYXR0ZW5kYW5jZT8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW2ZpeHR1cmVdKTtcblxuICAvLyBNb3ZlIHRoaXMgdXNlRWZmZWN0IGFmdGVyIHRlYW1PcHRpb25zIGFuZCBsZWFndWVPcHRpb25zIGFyZSBkZWZpbmVkXG5cbiAgY29uc3QgdmFsaWRhdGVGb3JtID0gKCk6IGJvb2xlYW4gPT4ge1xuICAgIGNvbnN0IG5ld0Vycm9yczogUGFydGlhbDxGaXh0dXJlRm9ybURhdGE+ID0ge307XG5cbiAgICBpZiAoIWZvcm1EYXRhLmhvbWVUZWFtSWQpIG5ld0Vycm9ycy5ob21lVGVhbUlkID0gJ0hvbWUgdGVhbSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5hd2F5VGVhbUlkKSBuZXdFcnJvcnMuYXdheVRlYW1JZCA9ICdBd2F5IHRlYW0gaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEubGVhZ3VlSWQpIG5ld0Vycm9ycy5sZWFndWVJZCA9ICdMZWFndWUgaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEuZGF0ZSkgbmV3RXJyb3JzLmRhdGUgPSAnRGF0ZSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS50aW1lKSBuZXdFcnJvcnMudGltZSA9ICdUaW1lIGlzIHJlcXVpcmVkJztcbiAgICBpZiAoIWZvcm1EYXRhLnN0YXR1cykgbmV3RXJyb3JzLnN0YXR1cyA9ICdTdGF0dXMgaXMgcmVxdWlyZWQnO1xuXG4gICAgaWYgKGZvcm1EYXRhLmhvbWVUZWFtSWQgPT09IGZvcm1EYXRhLmF3YXlUZWFtSWQpIHtcbiAgICAgIG5ld0Vycm9ycy5hd2F5VGVhbUlkID0gJ0F3YXkgdGVhbSBtdXN0IGJlIGRpZmZlcmVudCBmcm9tIGhvbWUgdGVhbSc7XG4gICAgfVxuXG4gICAgc2V0RXJyb3JzKG5ld0Vycm9ycyk7XG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoID09PSAwO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG5cbiAgICBpZiAoIXZhbGlkYXRlRm9ybSgpKSB7XG4gICAgICB0b2FzdC5lcnJvcignUGxlYXNlIGZpeCB0aGUgZm9ybSBlcnJvcnMnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBDb21iaW5lIGRhdGUgYW5kIHRpbWVcbiAgICBjb25zdCBkYXRlVGltZSA9IG5ldyBEYXRlKGAke2Zvcm1EYXRhLmRhdGV9VCR7Zm9ybURhdGEudGltZX1gKTtcblxuICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB7XG4gICAgICBob21lVGVhbUlkOiBwYXJzZUludChmb3JtRGF0YS5ob21lVGVhbUlkKSxcbiAgICAgIGF3YXlUZWFtSWQ6IHBhcnNlSW50KGZvcm1EYXRhLmF3YXlUZWFtSWQpLFxuICAgICAgbGVhZ3VlSWQ6IHBhcnNlSW50KGZvcm1EYXRhLmxlYWd1ZUlkKSxcbiAgICAgIGRhdGU6IGRhdGVUaW1lLnRvSVNPU3RyaW5nKCksXG4gICAgICB2ZW51ZU5hbWU6IGZvcm1EYXRhLnZlbnVlTmFtZSB8fCBudWxsLFxuICAgICAgdmVudWVDaXR5OiBmb3JtRGF0YS52ZW51ZUNpdHkgfHwgbnVsbCxcbiAgICAgIHJvdW5kOiBmb3JtRGF0YS5yb3VuZCB8fCBudWxsLFxuICAgICAgc3RhdHVzOiBmb3JtRGF0YS5zdGF0dXMsXG4gICAgICBnb2Fsc0hvbWU6IGZvcm1EYXRhLmdvYWxzSG9tZSA/IHBhcnNlSW50KGZvcm1EYXRhLmdvYWxzSG9tZSkgOiBudWxsLFxuICAgICAgZ29hbHNBd2F5OiBmb3JtRGF0YS5nb2Fsc0F3YXkgPyBwYXJzZUludChmb3JtRGF0YS5nb2Fsc0F3YXkpIDogbnVsbCxcbiAgICAgIGVsYXBzZWQ6IGZvcm1EYXRhLmVsYXBzZWQgPyBwYXJzZUludChmb3JtRGF0YS5lbGFwc2VkKSA6IG51bGwsXG4gICAgICByZWZlcmVlOiBmb3JtRGF0YS5yZWZlcmVlIHx8IG51bGwsXG4gICAgICB0ZW1wZXJhdHVyZTogZm9ybURhdGEudGVtcGVyYXR1cmUgPyBwYXJzZUludChmb3JtRGF0YS50ZW1wZXJhdHVyZSkgOiBudWxsLFxuICAgICAgd2VhdGhlcjogZm9ybURhdGEud2VhdGhlciB8fCBudWxsLFxuICAgICAgYXR0ZW5kYW5jZTogZm9ybURhdGEuYXR0ZW5kYW5jZSA/IHBhcnNlSW50KGZvcm1EYXRhLmF0dGVuZGFuY2UpIDogbnVsbCxcbiAgICB9O1xuXG4gICAgdXBkYXRlTXV0YXRpb24ubXV0YXRlKHN1Ym1pdERhdGEpO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZUZvcm1EYXRhID0gKGZpZWxkOiBrZXlvZiBGaXh0dXJlRm9ybURhdGEsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIFtmaWVsZF06IHZhbHVlIH0pKTtcbiAgICAvLyBDbGVhciBlcnJvciB3aGVuIHVzZXIgc3RhcnRzIHR5cGluZ1xuICAgIGlmIChlcnJvcnNbZmllbGRdKSB7XG4gICAgICBzZXRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBbZmllbGRdOiB1bmRlZmluZWQgfSkpO1xuICAgIH1cbiAgfTtcblxuICAvLyBTZWFyY2ggYW5kIHBhZ2luYXRpb24gaGFuZGxlcnNcbiAgY29uc3QgaGFuZGxlTGVhZ3VlU2VhcmNoID0gKHF1ZXJ5OiBzdHJpbmcpID0+IHtcbiAgICBzZXRMZWFndWVTZWFyY2gocXVlcnkpO1xuICAgIHNldExlYWd1ZVBhZ2UoMSk7IC8vIFJlc2V0IHRvIGZpcnN0IHBhZ2Ugb24gbmV3IHNlYXJjaFxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVRlYW1TZWFyY2ggPSAocXVlcnk6IHN0cmluZykgPT4ge1xuICAgIHNldFRlYW1TZWFyY2gocXVlcnkpO1xuICAgIHNldFRlYW1QYWdlKDEpOyAvLyBSZXNldCB0byBmaXJzdCBwYWdlIG9uIG5ldyBzZWFyY2hcbiAgfTtcblxuICBjb25zdCBoYW5kbGVMZWFndWVMb2FkTW9yZSA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+UhCBMZWFndWUgTG9hZCBNb3JlOicsIHtcbiAgICAgIGN1cnJlbnRQYWdlOiBsZWFndWVQYWdlLFxuICAgICAgdG90YWxQYWdlczogbGVhZ3Vlcz8ubWV0YT8udG90YWxQYWdlcyxcbiAgICAgIGhhc01vcmU6IGxlYWd1ZXM/Lm1ldGE/LnRvdGFsUGFnZXMgJiYgbGVhZ3VlUGFnZSA8IGxlYWd1ZXMubWV0YS50b3RhbFBhZ2VzLFxuICAgICAgYWxsTGVhZ3Vlc0NvdW50OiBhbGxMZWFndWVzLmxlbmd0aFxuICAgIH0pO1xuICAgIGlmIChsZWFndWVzPy5tZXRhPy50b3RhbFBhZ2VzICYmIGxlYWd1ZVBhZ2UgPCBsZWFndWVzLm1ldGEudG90YWxQYWdlcykge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBMb2FkaW5nIG1vcmUgbGVhZ3VlcywgcGFnZTonLCBsZWFndWVQYWdlICsgMSk7XG4gICAgICBzZXRMZWFndWVQYWdlKHByZXYgPT4gcHJldiArIDEpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZygn4p2MIENhbm5vdCBsb2FkIG1vcmUgbGVhZ3VlcycpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVUZWFtTG9hZE1vcmUgPSAoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/CflIQgVGVhbSBMb2FkIE1vcmU6Jywge1xuICAgICAgY3VycmVudFBhZ2U6IHRlYW1QYWdlLFxuICAgICAgdG90YWxQYWdlczogdGVhbXM/Lm1ldGE/LnRvdGFsUGFnZXMsXG4gICAgICBoYXNNb3JlOiB0ZWFtcz8ubWV0YT8udG90YWxQYWdlcyAmJiB0ZWFtUGFnZSA8IHRlYW1zLm1ldGEudG90YWxQYWdlcyxcbiAgICAgIGFsbFRlYW1zQ291bnQ6IGFsbFRlYW1zLmxlbmd0aFxuICAgIH0pO1xuICAgIGlmICh0ZWFtcz8ubWV0YT8udG90YWxQYWdlcyAmJiB0ZWFtUGFnZSA8IHRlYW1zLm1ldGEudG90YWxQYWdlcykge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBMb2FkaW5nIG1vcmUgdGVhbXMsIHBhZ2U6JywgdGVhbVBhZ2UgKyAxKTtcbiAgICAgIHNldFRlYW1QYWdlKHByZXYgPT4gcHJldiArIDEpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZygn4p2MIENhbm5vdCBsb2FkIG1vcmUgdGVhbXMnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3Qgc3RhdHVzT3B0aW9ucyA9IFtcbiAgICB7IHZhbHVlOiAnVEJEJywgbGFiZWw6ICdUaW1lIFRvIEJlIERlZmluZWQnIH0sXG4gICAgeyB2YWx1ZTogJ05TJywgbGFiZWw6ICdOb3QgU3RhcnRlZCcgfSxcbiAgICB7IHZhbHVlOiAnMUgnLCBsYWJlbDogJ0ZpcnN0IEhhbGYnIH0sXG4gICAgeyB2YWx1ZTogJ0hUJywgbGFiZWw6ICdIYWxmdGltZScgfSxcbiAgICB7IHZhbHVlOiAnMkgnLCBsYWJlbDogJ1NlY29uZCBIYWxmJyB9LFxuICAgIHsgdmFsdWU6ICdFVCcsIGxhYmVsOiAnRXh0cmEgVGltZScgfSxcbiAgICB7IHZhbHVlOiAnQlQnLCBsYWJlbDogJ0JyZWFrIFRpbWUnIH0sXG4gICAgeyB2YWx1ZTogJ1AnLCBsYWJlbDogJ1BlbmFsdHkgSW4gUHJvZ3Jlc3MnIH0sXG4gICAgeyB2YWx1ZTogJ1NVU1AnLCBsYWJlbDogJ01hdGNoIFN1c3BlbmRlZCcgfSxcbiAgICB7IHZhbHVlOiAnSU5UJywgbGFiZWw6ICdNYXRjaCBJbnRlcnJ1cHRlZCcgfSxcbiAgICB7IHZhbHVlOiAnRlQnLCBsYWJlbDogJ01hdGNoIEZpbmlzaGVkJyB9LFxuICAgIHsgdmFsdWU6ICdBRVQnLCBsYWJlbDogJ01hdGNoIEZpbmlzaGVkIEFmdGVyIEV4dHJhIFRpbWUnIH0sXG4gICAgeyB2YWx1ZTogJ1BFTicsIGxhYmVsOiAnTWF0Y2ggRmluaXNoZWQgQWZ0ZXIgUGVuYWx0eScgfSxcbiAgICB7IHZhbHVlOiAnUFNUJywgbGFiZWw6ICdNYXRjaCBQb3N0cG9uZWQnIH0sXG4gICAgeyB2YWx1ZTogJ0NBTkMnLCBsYWJlbDogJ01hdGNoIENhbmNlbGxlZCcgfSxcbiAgICB7IHZhbHVlOiAnQUJEJywgbGFiZWw6ICdNYXRjaCBBYmFuZG9uZWQnIH0sXG4gICAgeyB2YWx1ZTogJ0FXRCcsIGxhYmVsOiAnVGVjaG5pY2FsIExvc3MnIH0sXG4gICAgeyB2YWx1ZTogJ1dPJywgbGFiZWw6ICdXYWxrT3ZlcicgfSxcbiAgXTtcblxuICAvLyBVc2UgYWNjdW11bGF0ZWQgZGF0YSBmb3Igb3B0aW9ucyB3aXRoIHVuaXF1ZSBrZXlzXG4gIGNvbnN0IGxlYWd1ZU9wdGlvbnMgPSBhbGxMZWFndWVzLm1hcCgobGVhZ3VlLCBpbmRleCkgPT4gKHtcbiAgICB2YWx1ZTogbGVhZ3VlLmV4dGVybmFsSWQudG9TdHJpbmcoKSxcbiAgICBsYWJlbDogYCR7bGVhZ3VlLm5hbWV9JHtsZWFndWUuc2Vhc29uID8gYCAoJHtsZWFndWUuc2Vhc29ufSlgIDogJyd9YCxcbiAgICBsb2dvOiBsZWFndWUubG9nbyxcbiAgICBzZWFzb246IGxlYWd1ZS5zZWFzb24sXG4gICAgdW5pcXVlS2V5OiBgbGVhZ3VlLSR7bGVhZ3VlLmlkIHx8IGxlYWd1ZS5leHRlcm5hbElkfS0ke2luZGV4fWAsXG4gIH0pKTtcblxuICBjb25zdCB0ZWFtT3B0aW9ucyA9IGFsbFRlYW1zLm1hcCgodGVhbSwgaW5kZXgpID0+ICh7XG4gICAgdmFsdWU6IHRlYW0uZXh0ZXJuYWxJZC50b1N0cmluZygpLFxuICAgIGxhYmVsOiB0ZWFtLm5hbWUsXG4gICAgbG9nbzogdGVhbS5sb2dvLFxuICAgIHVuaXF1ZUtleTogYHRlYW0tJHt0ZWFtLmlkIHx8IHRlYW0uZXh0ZXJuYWxJZH0tJHtpbmRleH1gLFxuICB9KSk7XG5cbiAgLy8gRGVidWcgb3B0aW9ucyBjb3VudFxuICBjb25zb2xlLmxvZygn8J+TiiBPUFRJT05TIERFQlVHOicsIHtcbiAgICBhbGxMZWFndWVzQ291bnQ6IGFsbExlYWd1ZXMubGVuZ3RoLFxuICAgIGFsbFRlYW1zQ291bnQ6IGFsbFRlYW1zLmxlbmd0aCxcbiAgICBsZWFndWVPcHRpb25zQ291bnQ6IGxlYWd1ZU9wdGlvbnMubGVuZ3RoLFxuICAgIHRlYW1PcHRpb25zQ291bnQ6IHRlYW1PcHRpb25zLmxlbmd0aCxcbiAgICBsZWFndWVNZXRhOiBsZWFndWVzPy5tZXRhLFxuICAgIHRlYW1NZXRhOiB0ZWFtcz8ubWV0YVxuICB9KTtcblxuICAvLyBBZGRpdGlvbmFsIGVmZmVjdCB0byBlbnN1cmUgb3B0aW9ucyBhcmUgbG9hZGVkIC0gbW92ZWQgaGVyZSBhZnRlciBvcHRpb25zIGFyZSBkZWZpbmVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/CflI0gT3B0aW9ucyBMb2FkaW5nIFN0YXR1czonLCB7XG4gICAgICB0ZWFtc0xvYWRpbmcsXG4gICAgICBsZWFndWVzTG9hZGluZyxcbiAgICAgIHRlYW1PcHRpb25zQ291bnQ6IHRlYW1PcHRpb25zLmxlbmd0aCxcbiAgICAgIGxlYWd1ZU9wdGlvbnNDb3VudDogbGVhZ3VlT3B0aW9ucy5sZW5ndGgsXG4gICAgICBmb3JtRGF0YVJlYWR5OiAhIShmb3JtRGF0YS5ob21lVGVhbUlkICYmIGZvcm1EYXRhLmF3YXlUZWFtSWQgJiYgZm9ybURhdGEubGVhZ3VlSWQpLFxuICAgIH0pO1xuICB9LCBbdGVhbXNMb2FkaW5nLCBsZWFndWVzTG9hZGluZywgdGVhbU9wdGlvbnMubGVuZ3RoLCBsZWFndWVPcHRpb25zLmxlbmd0aCwgZm9ybURhdGFdKTtcblxuICAvLyBTbWFydCBwcmV2aWV3IGxvZ2ljIC0gYWx3YXlzIHRyeSB0byBmaW5kIGZyb20gb3B0aW9ucyBmaXJzdCwgZmFsbGJhY2sgdG8gZml4dHVyZSBkYXRhXG4gIGNvbnN0IGdldFByZXZpZXdEYXRhID0gKCkgPT4ge1xuICAgIGlmICghZml4dHVyZSkgcmV0dXJuIHsgbGVhZ3VlOiBudWxsLCBob21lVGVhbTogbnVsbCwgYXdheVRlYW06IG51bGwgfTtcblxuICAgIC8vIFRyeSB0byBmaW5kIGZyb20gZHJvcGRvd24gb3B0aW9ucyBmaXJzdFxuICAgIGNvbnN0IGxlYWd1ZUZyb21PcHRpb25zID0gbGVhZ3VlT3B0aW9ucy5maW5kKGwgPT4gbC52YWx1ZSA9PT0gZm9ybURhdGEubGVhZ3VlSWQpO1xuICAgIGNvbnN0IGhvbWVUZWFtRnJvbU9wdGlvbnMgPSB0ZWFtT3B0aW9ucy5maW5kKHQgPT4gdC52YWx1ZSA9PT0gZm9ybURhdGEuaG9tZVRlYW1JZCk7XG4gICAgY29uc3QgYXdheVRlYW1Gcm9tT3B0aW9ucyA9IHRlYW1PcHRpb25zLmZpbmQodCA9PiB0LnZhbHVlID09PSBmb3JtRGF0YS5hd2F5VGVhbUlkKTtcblxuICAgIHJldHVybiB7XG4gICAgICBsZWFndWU6IGxlYWd1ZUZyb21PcHRpb25zIHx8IHtcbiAgICAgICAgdmFsdWU6IGZvcm1EYXRhLmxlYWd1ZUlkLFxuICAgICAgICBsYWJlbDogZml4dHVyZS5sZWFndWVOYW1lLFxuICAgICAgICBsb2dvOiAnJ1xuICAgICAgfSxcbiAgICAgIGhvbWVUZWFtOiBob21lVGVhbUZyb21PcHRpb25zIHx8IHtcbiAgICAgICAgdmFsdWU6IGZvcm1EYXRhLmhvbWVUZWFtSWQsXG4gICAgICAgIGxhYmVsOiBmaXh0dXJlLmhvbWVUZWFtTmFtZSxcbiAgICAgICAgbG9nbzogZml4dHVyZS5ob21lVGVhbUxvZ29cbiAgICAgIH0sXG4gICAgICBhd2F5VGVhbTogYXdheVRlYW1Gcm9tT3B0aW9ucyB8fCB7XG4gICAgICAgIHZhbHVlOiBmb3JtRGF0YS5hd2F5VGVhbUlkLFxuICAgICAgICBsYWJlbDogZml4dHVyZS5hd2F5VGVhbU5hbWUsXG4gICAgICAgIGxvZ286IGZpeHR1cmUuYXdheVRlYW1Mb2dvXG4gICAgICB9XG4gICAgfTtcbiAgfTtcblxuICBjb25zdCB7IGxlYWd1ZTogc2VsZWN0ZWRMZWFndWUsIGhvbWVUZWFtOiBzZWxlY3RlZEhvbWVUZWFtLCBhd2F5VGVhbTogc2VsZWN0ZWRBd2F5VGVhbSB9ID0gZ2V0UHJldmlld0RhdGEoKTtcblxuICAvLyBEZWJ1ZyBzZWxlY3RlZCB2YWx1ZXNcbiAgY29uc29sZS5sb2coJ/Cfjq8gU0VMRUNURUQgVkFMVUVTIERFQlVHOicpO1xuICBjb25zb2xlLmxvZygnc2VsZWN0ZWRMZWFndWU6Jywgc2VsZWN0ZWRMZWFndWUpO1xuICBjb25zb2xlLmxvZygnc2VsZWN0ZWRIb21lVGVhbTonLCBzZWxlY3RlZEhvbWVUZWFtKTtcbiAgY29uc29sZS5sb2coJ3NlbGVjdGVkQXdheVRlYW06Jywgc2VsZWN0ZWRBd2F5VGVhbSk7XG5cbiAgLy8gRGVidWcgZm9ybSBkYXRhXG4gIGNvbnNvbGUubG9nKCfwn5OdIEZPUk0gREFUQTonLCBmb3JtRGF0YSk7XG4gIGNvbnNvbGUubG9nKCfwn5OKIE9QVElPTlMgQ09VTlQ6JywgeyB0ZWFtczogdGVhbU9wdGlvbnMubGVuZ3RoLCBsZWFndWVzOiBsZWFndWVPcHRpb25zLmxlbmd0aCB9KTtcblxuICAvLyBGb3JjZSByZS1yZW5kZXIgd2hlbiBvcHRpb25zIGNoYW5nZSB0byBlbnN1cmUgcHJldmlldyB1cGRhdGVzXG4gIGNvbnN0IFtwcmV2aWV3S2V5LCBzZXRQcmV2aWV3S2V5XSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbZHJvcGRvd25LZXksIHNldERyb3Bkb3duS2V5XSA9IHVzZVN0YXRlKDApO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gVXBkYXRlIHByZXZpZXcgd2hlbiBvcHRpb25zIG9yIGZvcm0gZGF0YSBjaGFuZ2VzXG4gICAgaWYgKHRlYW1PcHRpb25zLmxlbmd0aCA+IDAgJiYgbGVhZ3VlT3B0aW9ucy5sZW5ndGggPiAwICYmIGZvcm1EYXRhLmhvbWVUZWFtSWQpIHtcbiAgICAgIHNldFByZXZpZXdLZXkocHJldiA9PiBwcmV2ICsgMSk7XG4gICAgfVxuICB9LCBbdGVhbU9wdGlvbnMubGVuZ3RoLCBsZWFndWVPcHRpb25zLmxlbmd0aCwgZm9ybURhdGEuaG9tZVRlYW1JZCwgZm9ybURhdGEuYXdheVRlYW1JZCwgZm9ybURhdGEubGVhZ3VlSWRdKTtcblxuICAvLyBGb3JjZSBkcm9wZG93biByZS1yZW5kZXIgd2hlbiBvcHRpb25zIGNoYW5nZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldERyb3Bkb3duS2V5KHByZXYgPT4gcHJldiArIDEpO1xuICB9LCBbdGVhbU9wdGlvbnMubGVuZ3RoLCBsZWFndWVPcHRpb25zLmxlbmd0aF0pO1xuXG4gIC8vIFNpbXBsZSBkZWJ1ZyBmb3IgSUQgbWF0Y2hpbmdcbiAgY29uc29sZS5sb2coJ/CflKcgSUQgTUFUQ0hJTkcgREVCVUc6Jyk7XG4gIGNvbnNvbGUubG9nKCdGb3JtIElEczonLCB7IGhvbWU6IGZvcm1EYXRhLmhvbWVUZWFtSWQsIGF3YXk6IGZvcm1EYXRhLmF3YXlUZWFtSWQsIGxlYWd1ZTogZm9ybURhdGEubGVhZ3VlSWQgfSk7XG4gIGNvbnNvbGUubG9nKCdGb3JtIElEIFR5cGVzOicsIHtcbiAgICBob21lOiB0eXBlb2YgZm9ybURhdGEuaG9tZVRlYW1JZCxcbiAgICBhd2F5OiB0eXBlb2YgZm9ybURhdGEuYXdheVRlYW1JZCxcbiAgICBsZWFndWU6IHR5cGVvZiBmb3JtRGF0YS5sZWFndWVJZFxuICB9KTtcblxuICBpZiAodGVhbU9wdGlvbnMubGVuZ3RoID4gMCkge1xuICAgIGNvbnNvbGUubG9nKCdTYW1wbGUgVGVhbSBPcHRpb25zOicsIHRlYW1PcHRpb25zLnNsaWNlKDAsIDMpKTtcbiAgICBjb25zb2xlLmxvZygnVGVhbSBPcHRpb24gVHlwZXM6JywgdGVhbU9wdGlvbnMuc2xpY2UoMCwgMSkubWFwKHQgPT4gKHsgdmFsdWU6IHQudmFsdWUsIHR5cGU6IHR5cGVvZiB0LnZhbHVlIH0pKSk7XG5cbiAgICAvLyDwn5SNIENoZWNrIGlmIG91ciBzcGVjaWZpYyB0ZWFtcyBleGlzdFxuICAgIGNvbnN0IGhvbWVUZWFtSW5PcHRpb25zID0gdGVhbU9wdGlvbnMuZmluZCh0ID0+IHQudmFsdWUgPT09ICc0NDUwJyk7XG4gICAgY29uc3QgYXdheVRlYW1Jbk9wdGlvbnMgPSB0ZWFtT3B0aW9ucy5maW5kKHQgPT4gdC52YWx1ZSA9PT0gJzIwMDIyJyk7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8gU1BFQ0lGSUMgVEVBTVMgQ0hFQ0s6Jywge1xuICAgICAgJ2hvbWVUZWFtICg0NDUwKSc6IGhvbWVUZWFtSW5PcHRpb25zLFxuICAgICAgJ2F3YXlUZWFtICgyMDAyMiknOiBhd2F5VGVhbUluT3B0aW9ucyxcbiAgICAgICd0b3RhbFRlYW1zJzogdGVhbU9wdGlvbnMubGVuZ3RoXG4gICAgfSk7XG4gIH1cblxuICBpZiAobGVhZ3VlT3B0aW9ucy5sZW5ndGggPiAwKSB7XG4gICAgY29uc29sZS5sb2coJ1NhbXBsZSBMZWFndWUgT3B0aW9uczonLCBsZWFndWVPcHRpb25zLnNsaWNlKDAsIDIpKTtcbiAgICBjb25zb2xlLmxvZygnTGVhZ3VlIE9wdGlvbiBUeXBlczonLCBsZWFndWVPcHRpb25zLnNsaWNlKDAsIDEpLm1hcChsID0+ICh7IHZhbHVlOiBsLnZhbHVlLCB0eXBlOiB0eXBlb2YgbC52YWx1ZSB9KSkpO1xuXG4gICAgLy8g8J+UjSBDaGVjayBpZiBvdXIgc3BlY2lmaWMgbGVhZ3VlIGV4aXN0c1xuICAgIGNvbnN0IGxlYWd1ZUluT3B0aW9ucyA9IGxlYWd1ZU9wdGlvbnMuZmluZChsID0+IGwudmFsdWUgPT09ICc1NzAnKTtcbiAgICBjb25zb2xlLmxvZygn8J+OryBTUEVDSUZJQyBMRUFHVUUgQ0hFQ0s6Jywge1xuICAgICAgJ2xlYWd1ZSAoNTcwKSc6IGxlYWd1ZUluT3B0aW9ucyxcbiAgICAgICd0b3RhbExlYWd1ZXMnOiBsZWFndWVPcHRpb25zLmxlbmd0aFxuICAgIH0pO1xuICB9XG5cbiAgY29uc29sZS5sb2coJ/CflI0gSUQgRXhpc3RzIENoZWNrIC0gRklYRUQ6Jywge1xuICAgIGhvbWVFeGlzdHM6IHRlYW1PcHRpb25zLnNvbWUodCA9PiB0LnZhbHVlID09PSBmb3JtRGF0YS5ob21lVGVhbUlkKSxcbiAgICBhd2F5RXhpc3RzOiB0ZWFtT3B0aW9ucy5zb21lKHQgPT4gdC52YWx1ZSA9PT0gZm9ybURhdGEuYXdheVRlYW1JZCksXG4gICAgbGVhZ3VlRXhpc3RzOiBsZWFndWVPcHRpb25zLnNvbWUobCA9PiBsLnZhbHVlID09PSBmb3JtRGF0YS5sZWFndWVJZCksXG4gIH0pO1xuXG4gIC8vIFByZXZpZXcgY29tcG9uZW50IGZvciBzZWxlY3RlZCB2YWx1ZXNcbiAgY29uc3QgU2VsZWN0ZWRWYWx1ZVByZXZpZXcgPSAoe1xuICAgIGxhYmVsLFxuICAgIHNlbGVjdGVkT3B0aW9uLFxuICAgIHBsYWNlaG9sZGVyID0gXCJOb3Qgc2VsZWN0ZWRcIlxuICB9OiB7XG4gICAgbGFiZWw6IHN0cmluZztcbiAgICBzZWxlY3RlZE9wdGlvbj86IHsgdmFsdWU6IG51bWJlcjsgbGFiZWw6IHN0cmluZzsgbG9nbz86IHN0cmluZyB9O1xuICAgIHBsYWNlaG9sZGVyPzogc3RyaW5nO1xuICB9KSA9PiB7XG4gICAgY29uc3QgQ0ROX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0RPTUFJTl9DRE5fUElDVFVSRSB8fCAnaHR0cDovLzExNi4yMDMuMTI1LjY1JztcblxuICAgIC8vIERlYnVnIHByZXZpZXcgY29tcG9uZW50XG4gICAgY29uc29sZS5sb2coYPCfjqggJHtsYWJlbH0gUHJldmlldzpgLCB7IHNlbGVjdGVkT3B0aW9uLCBwbGFjZWhvbGRlciwgaGFzT3B0aW9uOiAhIXNlbGVjdGVkT3B0aW9uIH0pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMyBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPntsYWJlbH08L2Rpdj5cbiAgICAgICAge3NlbGVjdGVkT3B0aW9uID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICB7c2VsZWN0ZWRPcHRpb24ubG9nbyAmJiAoXG4gICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICBzcmM9e2Ake0NETl9VUkx9LyR7c2VsZWN0ZWRPcHRpb24ubG9nb31gfVxuICAgICAgICAgICAgICAgIGFsdD17c2VsZWN0ZWRPcHRpb24ubGFiZWx9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy04IGgtOCBvYmplY3QtY29udGFpbiByb3VuZGVkXCJcbiAgICAgICAgICAgICAgICBvbkVycm9yPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmRpc3BsYXkgPSAnbm9uZSc7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICB7c2VsZWN0ZWRPcHRpb24ubGFiZWx9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmF5LTIwMCByb3VuZGVkIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPj88L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaXRhbGljXCI+e3BsYWNlaG9sZGVyfTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgLy8gSW5saW5lIExlYWd1ZSBjb21wb25lbnQgKGxvZ28gKyBuYW1lIGLDqm4gdHLDoWksIGRyb3Bkb3duIGLDqm4gcGjhuqNpKVxuICBjb25zdCBMZWFndWVJbmxpbmVTZWxlY3RvciA9ICgpID0+IHtcbiAgICBjb25zdCBDRE5fVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRE9NQUlOX0NETl9QSUNUVVJFIHx8ICdodHRwOi8vMTE2LjIwMy4xMjUuNjUnO1xuXG4gICAgLy8gRGVidWcgbGVhZ3VlIHNlbGVjdG9yXG4gICAgY29uc29sZS5sb2coJ/Cfj4YgTGVhZ3VlIElubGluZSBTZWxlY3RvcjonLCB7IHNlbGVjdGVkTGVhZ3VlLCBoYXNMZWFndWU6ICEhc2VsZWN0ZWRMZWFndWUgfSk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgey8qIExlZnQgc2lkZTogTG9nbyArIExlYWd1ZSBuYW1lICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtaW4tdy0wIGZsZXgtMVwiPlxuICAgICAgICAgIHtzZWxlY3RlZExlYWd1ZSA/IChcbiAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgIHtzZWxlY3RlZExlYWd1ZS5sb2dvICYmIChcbiAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICBzcmM9e2Ake0NETl9VUkx9LyR7c2VsZWN0ZWRMZWFndWUubG9nb31gfVxuICAgICAgICAgICAgICAgICAgYWx0PXtzZWxlY3RlZExlYWd1ZS5sYWJlbH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctOCBoLTggb2JqZWN0LWNvbnRhaW4gcm91bmRlZCBmbGV4LXNocmluay0wXCJcbiAgICAgICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZExlYWd1ZS5sYWJlbH1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmF5LTIwMCByb3VuZGVkIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQteHNcIj4/PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBpdGFsaWNcIj5ObyBsZWFndWUgc2VsZWN0ZWQ8L3NwYW4+XG4gICAgICAgICAgICA8Lz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUmlnaHQgc2lkZTogRHJvcGRvd24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCB3LTY0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPkxlYWd1ZSo8L2Rpdj5cbiAgICAgICAgICA8U2VhcmNoYWJsZVNlbGVjdEZpZWxkXG4gICAgICAgICAgICBrZXk9e2BsZWFndWUtJHtkcm9wZG93bktleX1gfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2xlYWd1ZXNMb2FkaW5nID8gXCJMb2FkaW5nIGxlYWd1ZXMuLi5cIiA6IFwiU2VsZWN0IGxlYWd1ZVwifVxuICAgICAgICAgICAgc2VhcmNoUGxhY2Vob2xkZXI9XCJTZWFyY2ggbGVhZ3Vlcy4uLlwiXG4gICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmxlYWd1ZUlkfVxuICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB1cGRhdGVGb3JtRGF0YSgnbGVhZ3VlSWQnLCB2YWx1ZSl9XG4gICAgICAgICAgICBvcHRpb25zPXtsZWFndWVPcHRpb25zfVxuICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5sZWFndWVJZH1cbiAgICAgICAgICAgIGRpc2FibGVkPXtsZWFndWVzTG9hZGluZ31cbiAgICAgICAgICAgIG9uU2VhcmNoPXtoYW5kbGVMZWFndWVTZWFyY2h9XG4gICAgICAgICAgICBvbkxvYWRNb3JlPXtoYW5kbGVMZWFndWVMb2FkTW9yZX1cbiAgICAgICAgICAgIGhhc01vcmU9e2xlYWd1ZXM/Lm1ldGEgPyBsZWFndWVQYWdlIDwgbGVhZ3Vlcy5tZXRhLnRvdGFsUGFnZXMgOiBmYWxzZX1cbiAgICAgICAgICAgIGlzTG9hZGluZz17bGVhZ3Vlc0xvYWRpbmd9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIFNob3cgbG9hZGluZyBzdGF0ZSBpZiBhbnkgcmVxdWlyZWQgZGF0YSBpcyBsb2FkaW5nXG4gIGNvbnN0IGlzRGF0YUxvYWRpbmcgPSBmaXh0dXJlTG9hZGluZyB8fCBsZWFndWVzTG9hZGluZyB8fCB0ZWFtc0xvYWRpbmc7XG5cbiAgaWYgKGlzRGF0YUxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMCB3LTIwXCIgLz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtOCB3LTY0XCIgLz5cbiAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTQgdy00OFwiIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTYgdy00OFwiIC8+XG4gICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC00IHctNjRcIiAvPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC00IHctMzJcIiAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMFwiIC8+XG4gICAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC00IHctMjRcIiAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMFwiIC8+XG4gICAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMCB3LTIwXCIgLz5cbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTAgdy0zMlwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gU2hvdyBlcnJvciBzdGF0ZSBpZiBhbnkgY3JpdGljYWwgZGF0YSBmYWlsZWQgdG8gbG9hZFxuICBpZiAoIWZpeHR1cmUgfHwgbGVhZ3Vlc0Vycm9yIHx8IHRlYW1zRXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gcm91dGVyLmJhY2soKX0+XG4gICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICBCYWNrXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgeyFmaXh0dXJlICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi00XCI+Rml4dHVyZSBub3QgZm91bmQ8L3A+fVxuICAgICAgICAgICAgICB7bGVhZ3Vlc0Vycm9yICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi00XCI+RmFpbGVkIHRvIGxvYWQgbGVhZ3Vlczoge2xlYWd1ZXNFcnJvci5tZXNzYWdlfTwvcD59XG4gICAgICAgICAgICAgIHt0ZWFtc0Vycm9yICYmIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi00XCI+RmFpbGVkIHRvIGxvYWQgdGVhbXM6IHt0ZWFtc0Vycm9yLm1lc3NhZ2V9PC9wPn1cbiAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZC9maXh0dXJlcycpfT5cbiAgICAgICAgICAgICAgICBSZXR1cm4gdG8gRml4dHVyZXNcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIuYmFjaygpfT5cbiAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgQmFja1xuICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgIEVkaXQgRml4dHVyZToge2ZpeHR1cmUuaG9tZVRlYW1OYW1lfSB2cyB7Zml4dHVyZS5hd2F5VGVhbU5hbWV9XG4gICAgICAgICAgPC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTFcIj5VcGRhdGUgZml4dHVyZSBkZXRhaWxzIGFuZCBtYXRjaCBpbmZvcm1hdGlvbjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEZvcm0gKi99XG4gICAgICA8Q2FyZD5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cIm1yLTIgaC01IHctNVwiIC8+XG4gICAgICAgICAgICBGaXh0dXJlIERldGFpbHNcbiAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgVXBkYXRlIHRoZSBmaXh0dXJlIGluZm9ybWF0aW9uXG4gICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgPEZvcm1TZWN0aW9uIHRpdGxlPVwiVGVhbXMgJiBDb21wZXRpdGlvblwiIGRlc2NyaXB0aW9uPVwiU2VsZWN0IHRoZSB0ZWFtcyBhbmQgbGVhZ3VlXCI+XG4gICAgICAgICAgICAgIHsvKiBUZWFtcyBTZWxlY3Rpb24gd2l0aCBQcmV2aWV3ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdGVkVmFsdWVQcmV2aWV3XG4gICAgICAgICAgICAgICAgICAgIGtleT17YGhvbWUtJHtwcmV2aWV3S2V5fWB9XG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiU2VsZWN0ZWQgSG9tZSBUZWFtXCJcbiAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRPcHRpb249e3NlbGVjdGVkSG9tZVRlYW19XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTm8gaG9tZSB0ZWFtIHNlbGVjdGVkXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8U2VhcmNoYWJsZVNlbGVjdEZpZWxkXG4gICAgICAgICAgICAgICAgICAgIGtleT17YGhvbWUtdGVhbS0ke2Ryb3Bkb3duS2V5fWB9XG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiSG9tZSBUZWFtXCJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3RlYW1zTG9hZGluZyA/IFwiTG9hZGluZyB0ZWFtcy4uLlwiIDogXCJTZWxlY3QgaG9tZSB0ZWFtXCJ9XG4gICAgICAgICAgICAgICAgICAgIHNlYXJjaFBsYWNlaG9sZGVyPVwiU2VhcmNoIHRlYW1zLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmhvbWVUZWFtSWR9XG4gICAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2hvbWVUZWFtSWQnLCB2YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e3RlYW1PcHRpb25zfVxuICAgICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmhvbWVUZWFtSWR9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXt0ZWFtc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgIG9uU2VhcmNoPXtoYW5kbGVUZWFtU2VhcmNofVxuICAgICAgICAgICAgICAgICAgICBvbkxvYWRNb3JlPXtoYW5kbGVUZWFtTG9hZE1vcmV9XG4gICAgICAgICAgICAgICAgICAgIGhhc01vcmU9e3RlYW1zPy5tZXRhID8gdGVhbVBhZ2UgPCB0ZWFtcy5tZXRhLnRvdGFsUGFnZXMgOiBmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgaXNMb2FkaW5nPXt0ZWFtc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RlZFZhbHVlUHJldmlld1xuICAgICAgICAgICAgICAgICAgICBrZXk9e2Bhd2F5LSR7cHJldmlld0tleX1gfVxuICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlNlbGVjdGVkIEF3YXkgVGVhbVwiXG4gICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkT3B0aW9uPXtzZWxlY3RlZEF3YXlUZWFtfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk5vIGF3YXkgdGVhbSBzZWxlY3RlZFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPFNlYXJjaGFibGVTZWxlY3RGaWVsZFxuICAgICAgICAgICAgICAgICAgICBrZXk9e2Bhd2F5LXRlYW0tJHtkcm9wZG93bktleX1gfVxuICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkF3YXkgVGVhbVwiXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0ZWFtc0xvYWRpbmcgPyBcIkxvYWRpbmcgdGVhbXMuLi5cIiA6IFwiU2VsZWN0IGF3YXkgdGVhbVwifVxuICAgICAgICAgICAgICAgICAgICBzZWFyY2hQbGFjZWhvbGRlcj1cIlNlYXJjaCB0ZWFtcy4uLlwiXG4gICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5hd2F5VGVhbUlkfVxuICAgICAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IHVwZGF0ZUZvcm1EYXRhKCdhd2F5VGVhbUlkJywgdmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXt0ZWFtT3B0aW9ucy5maWx0ZXIodGVhbSA9PiB0ZWFtLnZhbHVlICE9PSBmb3JtRGF0YS5ob21lVGVhbUlkKX1cbiAgICAgICAgICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5hd2F5VGVhbUlkfVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17dGVhbXNMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICBvblNlYXJjaD17aGFuZGxlVGVhbVNlYXJjaH1cbiAgICAgICAgICAgICAgICAgICAgb25Mb2FkTW9yZT17aGFuZGxlVGVhbUxvYWRNb3JlfVxuICAgICAgICAgICAgICAgICAgICBoYXNNb3JlPXt0ZWFtcz8ubWV0YSA/IHRlYW1QYWdlIDwgdGVhbXMubWV0YS50b3RhbFBhZ2VzIDogZmFsc2V9XG4gICAgICAgICAgICAgICAgICAgIGlzTG9hZGluZz17dGVhbXNMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIExlYWd1ZSBTZWxlY3Rpb24gLSBJbmxpbmUgTGF5b3V0ICovfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxMZWFndWVJbmxpbmVTZWxlY3RvciAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvRm9ybVNlY3Rpb24+XG5cbiAgICAgICAgICAgIDxGb3JtU2VjdGlvbiB0aXRsZT1cIlNjaGVkdWxlXCIgZGVzY3JpcHRpb249XCJTZXQgdGhlIGRhdGUgYW5kIHRpbWUgKGxvY2FsIHRpbWV6b25lKVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJEYXRlICpcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kYXRlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnZGF0ZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuZGF0ZX1cbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiTWF0Y2ggZGF0ZVwiXG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlRpbWUgKlwiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGltZVwiXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRpbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCd0aW1lJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy50aW1lfVxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb249e2BMb2NhbCB0aW1lICgke0ludGwuRGF0ZVRpbWVGb3JtYXQoKS5yZXNvbHZlZE9wdGlvbnMoKS50aW1lWm9uZX0pYH1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBiZy1ibHVlLTUwIHAtMyByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItYmx1ZS0yMDBcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBtci0yXCI+4oS577iPPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHN0cm9uZz5UaW1lem9uZSBJbmZvOjwvc3Ryb25nPiBUaW1lcyBhcmUgZGlzcGxheWVkIGluIHlvdXIgbG9jYWwgdGltZXpvbmUgKHtJbnRsLkRhdGVUaW1lRm9ybWF0KCkucmVzb2x2ZWRPcHRpb25zKCkudGltZVpvbmV9KS5cbiAgICAgICAgICAgICAgICAgIFRoZSBhc3RlcmlzayAoKikgaW5kaWNhdGVzIHJlcXVpcmVkIGZpZWxkcy5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9Gb3JtU2VjdGlvbj5cblxuICAgICAgICAgICAgPEZvcm1TZWN0aW9uIHRpdGxlPVwiTWF0Y2ggU3RhdHVzXCIgZGVzY3JpcHRpb249XCJVcGRhdGUgbWF0Y2ggc3RhdHVzIGFuZCBzY29yZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8U2VsZWN0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiU3RhdHVzXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IHN0YXR1c1wiXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnN0YXR1c31cbiAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3N0YXR1cycsIHZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e3N0YXR1c09wdGlvbnN9XG4gICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLnN0YXR1c31cbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiSG9tZSBHb2Fsc1wiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmdvYWxzSG9tZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2dvYWxzSG9tZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiQXdheSBHb2Fsc1wiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmdvYWxzQXdheX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2dvYWxzQXdheScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgIGxhYmVsPVwiRWxhcHNlZCBUaW1lIChtaW51dGVzKVwiXG4gICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgICAgbWF4PVwiMTIwXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZWxhcHNlZH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdlbGFwc2VkJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiTWludXRlcyBwbGF5ZWQgaW4gdGhlIG1hdGNoXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvRm9ybVNlY3Rpb24+XG5cbiAgICAgICAgICAgIDxGb3JtU2VjdGlvbiB0aXRsZT1cIlZlbnVlICYgTWF0Y2ggSW5mb3JtYXRpb25cIiBkZXNjcmlwdGlvbj1cIlZlbnVlIGRldGFpbHMgYW5kIG1hdGNoIGNvbnRleHRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiVmVudWUgTmFtZVwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlN0YWRpdW0gbmFtZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudmVudWVOYW1lfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgndmVudWVOYW1lJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJWZW51ZSBDaXR5XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQ2l0eVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudmVudWVDaXR5fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgndmVudWVDaXR5JywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlJvdW5kXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgTWF0Y2hkYXkgMSwgUXVhcnRlci1maW5hbFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucm91bmR9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdyb3VuZCcsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiUmVmZXJlZVwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlJlZmVyZWUgbmFtZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucmVmZXJlZSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3JlZmVyZWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiVGVtcGVyYXR1cmUgKMKwQylcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIDIyXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50ZW1wZXJhdHVyZSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3RlbXBlcmF0dXJlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJXZWF0aGVyXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgU3VubnksIFJhaW55XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS53ZWF0aGVyIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnd2VhdGhlcicsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiQXR0ZW5kYW5jZVwiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTnVtYmVyIG9mIHNwZWN0YXRvcnNcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmF0dGVuZGFuY2UgfHwgJyd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdhdHRlbmRhbmNlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9Gb3JtU2VjdGlvbj5cblxuICAgICAgICAgICAgPEZvcm1BY3Rpb25zPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5iYWNrKCl9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3VwZGF0ZU11dGF0aW9uLmlzUGVuZGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXt1cGRhdGVNdXRhdGlvbi5pc1BlbmRpbmd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8U2F2ZSBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgIHt1cGRhdGVNdXRhdGlvbi5pc1BlbmRpbmcgPyAnVXBkYXRpbmcuLi4nIDogJ1VwZGF0ZSBGaXh0dXJlJ31cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L0Zvcm1BY3Rpb25zPlxuICAgICAgICAgIDwvZm9ybT5cbiAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgIDwvQ2FyZD5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVBhcmFtcyIsInVzZVJvdXRlciIsInVzZU11dGF0aW9uIiwidXNlUXVlcnkiLCJ1c2VRdWVyeUNsaWVudCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJJbnB1dEZpZWxkIiwiU2VsZWN0RmllbGQiLCJGb3JtU2VjdGlvbiIsIkZvcm1BY3Rpb25zIiwiU2VhcmNoYWJsZVNlbGVjdEZpZWxkIiwiU2tlbGV0b24iLCJmaXh0dXJlc0FwaSIsImxlYWd1ZXNBcGkiLCJ0ZWFtc0FwaSIsIkFycm93TGVmdCIsIlNhdmUiLCJDYWxlbmRhciIsInRvYXN0IiwiRWRpdEZpeHR1cmVQYWdlIiwidGVhbXMiLCJwYXJhbXMiLCJyb3V0ZXIiLCJxdWVyeUNsaWVudCIsImZpeHR1cmVJZCIsInBhcnNlSW50IiwiaWQiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwiaG9tZVRlYW1JZCIsImF3YXlUZWFtSWQiLCJsZWFndWVJZCIsImRhdGUiLCJ0aW1lIiwidmVudWVOYW1lIiwidmVudWVDaXR5Iiwicm91bmQiLCJzdGF0dXMiLCJnb2Fsc0hvbWUiLCJnb2Fsc0F3YXkiLCJlbGFwc2VkIiwiZXJyb3JzIiwic2V0RXJyb3JzIiwiZGF0YSIsImZpeHR1cmUiLCJpc0xvYWRpbmciLCJmaXh0dXJlTG9hZGluZyIsInF1ZXJ5S2V5IiwicXVlcnlGbiIsImdldEZpeHR1cmUiLCJlbmFibGVkIiwibGVhZ3VlU2VhcmNoIiwic2V0TGVhZ3VlU2VhcmNoIiwiaG9tZVRlYW1TZWFyY2giLCJzZXRIb21lVGVhbVNlYXJjaCIsImF3YXlUZWFtU2VhcmNoIiwic2V0QXdheVRlYW1TZWFyY2giLCJsZWFndWVQYWdlIiwic2V0TGVhZ3VlUGFnZSIsImhvbWVUZWFtUGFnZSIsInNldEhvbWVUZWFtUGFnZSIsImF3YXlUZWFtUGFnZSIsInNldEF3YXlUZWFtUGFnZSIsImFsbExlYWd1ZXMiLCJzZXRBbGxMZWFndWVzIiwiYWxsSG9tZVRlYW1zIiwic2V0QWxsSG9tZVRlYW1zIiwiYWxsQXdheVRlYW1zIiwic2V0QWxsQXdheVRlYW1zIiwibGVhZ3VlcyIsImxlYWd1ZXNMb2FkaW5nIiwiZXJyb3IiLCJsZWFndWVzRXJyb3IiLCJnZXRMZWFndWVzIiwibGltaXQiLCJwYWdlIiwic2VhcmNoIiwia2VlcFByZXZpb3VzRGF0YSIsImhvbWVUZWFtcyIsImhvbWVUZWFtc0xvYWRpbmciLCJob21lVGVhbXNFcnJvciIsImdldFRlYW1zIiwiYXdheVRlYW1zIiwiYXdheVRlYW1zTG9hZGluZyIsImF3YXlUZWFtc0Vycm9yIiwicHJldkxlYWd1ZVNlYXJjaCIsInNldFByZXZMZWFndWVTZWFyY2giLCJwcmV2VGVhbVNlYXJjaCIsInNldFByZXZUZWFtU2VhcmNoIiwic2VhcmNoQ2hhbmdlZCIsImNvbnNvbGUiLCJsb2ciLCJleGlzdGluZyIsImxlbmd0aCIsIm5ldyIsInByZXYiLCJleGlzdGluZ0lkcyIsIlNldCIsIm1hcCIsImwiLCJleHRlcm5hbElkIiwibmV3TGVhZ3VlcyIsImZpbHRlciIsImhhcyIsInRlYW1TZWFyY2giLCJ0ZWFtUGFnZSIsInNldEFsbFRlYW1zIiwiYWxsVGVhbXMiLCJ0IiwibmV3VGVhbXMiLCJ0b3RhbCIsInVwZGF0ZU11dGF0aW9uIiwibXV0YXRpb25GbiIsInVwZGF0ZUZpeHR1cmUiLCJvblN1Y2Nlc3MiLCJpbnZhbGlkYXRlUXVlcmllcyIsInN1Y2Nlc3MiLCJwdXNoIiwib25FcnJvciIsIm1lc3NhZ2UiLCJmaXh0dXJlRGF0ZSIsIkRhdGUiLCJ2ZW51ZSIsInRvU3RyaW5nIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInRvVGltZVN0cmluZyIsInNsaWNlIiwibmFtZSIsImNpdHkiLCJyZWZlcmVlIiwidGVtcGVyYXR1cmUiLCJ3ZWF0aGVyIiwiYXR0ZW5kYW5jZSIsInZhbGlkYXRlRm9ybSIsIm5ld0Vycm9ycyIsIk9iamVjdCIsImtleXMiLCJoYW5kbGVTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJkYXRlVGltZSIsInN1Ym1pdERhdGEiLCJtdXRhdGUiLCJ1cGRhdGVGb3JtRGF0YSIsImZpZWxkIiwidmFsdWUiLCJ1bmRlZmluZWQiLCJoYW5kbGVMZWFndWVTZWFyY2giLCJxdWVyeSIsImhhbmRsZVRlYW1TZWFyY2giLCJzZXRUZWFtU2VhcmNoIiwic2V0VGVhbVBhZ2UiLCJoYW5kbGVMZWFndWVMb2FkTW9yZSIsImN1cnJlbnRQYWdlIiwidG90YWxQYWdlcyIsIm1ldGEiLCJoYXNNb3JlIiwiYWxsTGVhZ3Vlc0NvdW50IiwiaGFuZGxlVGVhbUxvYWRNb3JlIiwiYWxsVGVhbXNDb3VudCIsInN0YXR1c09wdGlvbnMiLCJsYWJlbCIsImxlYWd1ZU9wdGlvbnMiLCJsZWFndWUiLCJpbmRleCIsInNlYXNvbiIsImxvZ28iLCJ1bmlxdWVLZXkiLCJ0ZWFtT3B0aW9ucyIsInRlYW0iLCJsZWFndWVPcHRpb25zQ291bnQiLCJ0ZWFtT3B0aW9uc0NvdW50IiwibGVhZ3VlTWV0YSIsInRlYW1NZXRhIiwidGVhbXNMb2FkaW5nIiwiZm9ybURhdGFSZWFkeSIsImdldFByZXZpZXdEYXRhIiwiaG9tZVRlYW0iLCJhd2F5VGVhbSIsImxlYWd1ZUZyb21PcHRpb25zIiwiZmluZCIsImhvbWVUZWFtRnJvbU9wdGlvbnMiLCJhd2F5VGVhbUZyb21PcHRpb25zIiwibGVhZ3VlTmFtZSIsImhvbWVUZWFtTmFtZSIsImhvbWVUZWFtTG9nbyIsImF3YXlUZWFtTmFtZSIsImF3YXlUZWFtTG9nbyIsInNlbGVjdGVkTGVhZ3VlIiwic2VsZWN0ZWRIb21lVGVhbSIsInNlbGVjdGVkQXdheVRlYW0iLCJwcmV2aWV3S2V5Iiwic2V0UHJldmlld0tleSIsImRyb3Bkb3duS2V5Iiwic2V0RHJvcGRvd25LZXkiLCJob21lIiwiYXdheSIsInR5cGUiLCJob21lVGVhbUluT3B0aW9ucyIsImF3YXlUZWFtSW5PcHRpb25zIiwibGVhZ3VlSW5PcHRpb25zIiwiaG9tZUV4aXN0cyIsInNvbWUiLCJhd2F5RXhpc3RzIiwibGVhZ3VlRXhpc3RzIiwiU2VsZWN0ZWRWYWx1ZVByZXZpZXciLCJzZWxlY3RlZE9wdGlvbiIsInBsYWNlaG9sZGVyIiwiQ0ROX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19ET01BSU5fQ0ROX1BJQ1RVUkUiLCJoYXNPcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJpbWciLCJzcmMiLCJhbHQiLCJjdXJyZW50VGFyZ2V0Iiwic3R5bGUiLCJkaXNwbGF5Iiwic3BhbiIsIkxlYWd1ZUlubGluZVNlbGVjdG9yIiwiaGFzTGVhZ3VlIiwic2VhcmNoUGxhY2Vob2xkZXIiLCJyZXF1aXJlZCIsIm9uVmFsdWVDaGFuZ2UiLCJvcHRpb25zIiwiZGlzYWJsZWQiLCJvblNlYXJjaCIsIm9uTG9hZE1vcmUiLCJpc0RhdGFMb2FkaW5nIiwidGVhbXNFcnJvciIsInZhcmlhbnQiLCJvbkNsaWNrIiwiYmFjayIsInAiLCJoMSIsImZvcm0iLCJvblN1Ym1pdCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJvbkNoYW5nZSIsInRhcmdldCIsIkludGwiLCJEYXRlVGltZUZvcm1hdCIsInJlc29sdmVkT3B0aW9ucyIsInRpbWVab25lIiwic3Ryb25nIiwibWluIiwibWF4IiwiaXNQZW5kaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx\n"));

/***/ })

});