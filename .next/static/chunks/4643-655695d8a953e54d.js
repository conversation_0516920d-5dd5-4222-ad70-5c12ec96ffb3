(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4643],{834:function(t,e,i){"use strict";i.d(e,{Z:function(){return s}});let s=(0,i(7977).Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},9724:function(t,e,i){"use strict";i.d(e,{Z:function(){return s}});let s=(0,i(7977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},9646:function(t){t.exports={style:{fontFamily:"'__Inter_d65c78', '__Inter_Fallback_d65c78'",fontStyle:"normal"},className:"__className_d65c78"}},1266:function(t,e,i){"use strict";i.d(e,{F:function(){return a},e:function(){return n}});var s=i(2265);function r(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function a(...t){return e=>{let i=!1,s=t.map(t=>{let s=r(t,e);return i||"function"!=typeof s||(i=!0),s});if(i)return()=>{for(let e=0;e<s.length;e++){let i=s[e];"function"==typeof i?i():r(t[e],null)}}}}function n(...t){return s.useCallback(a(...t),t)}},9143:function(t,e,i){"use strict";i.d(e,{Z8:function(){return n},g7:function(){return u},sA:function(){return l}});var s=i(2265),r=i(1266),a=i(7437);function n(t){let e=function(t){let e=s.forwardRef((t,e)=>{let{children:i,...a}=t;if(s.isValidElement(i)){let t,n;let u=(t=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?i.ref:(t=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?i.props.ref:i.props.ref||i.ref,o=function(t,e){let i={...e};for(let s in e){let r=t[s],a=e[s];/^on[A-Z]/.test(s)?r&&a?i[s]=(...t)=>{let e=a(...t);return r(...t),e}:r&&(i[s]=r):"style"===s?i[s]={...r,...a}:"className"===s&&(i[s]=[r,a].filter(Boolean).join(" "))}return{...t,...i}}(a,i.props);return i.type!==s.Fragment&&(o.ref=e?(0,r.F)(e,u):u),s.cloneElement(i,o)}return s.Children.count(i)>1?s.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),i=s.forwardRef((t,i)=>{let{children:r,...n}=t,u=s.Children.toArray(r),o=u.find(h);if(o){let t=o.props.children,r=u.map(e=>e!==o?e:s.Children.count(t)>1?s.Children.only(null):s.isValidElement(t)?t.props.children:null);return(0,a.jsx)(e,{...n,ref:i,children:s.isValidElement(t)?s.cloneElement(t,void 0,r):null})}return(0,a.jsx)(e,{...n,ref:i,children:r})});return i.displayName=`${t}.Slot`,i}var u=n("Slot"),o=Symbol("radix.slottable");function l(t){let e=({children:t})=>(0,a.jsx)(a.Fragment,{children:t});return e.displayName=`${t}.Slottable`,e.__radixId=o,e}function h(t){return s.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===o}},5899:function(t,e,i){"use strict";i.d(e,{_:function(){return s}});let s=console},4654:function(t,e,i){"use strict";i.d(e,{R:function(){return o},m:function(){return u}});var s=i(5899),r=i(9522),a=i(3864),n=i(4500);class u extends a.F{constructor(t){super(),this.defaultOptions=t.defaultOptions,this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.logger=t.logger||s._,this.observers=[],this.state=t.state||o(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options={...this.defaultOptions,...t},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(t){this.dispatch({type:"setState",state:t})}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.observers=this.observers.filter(e=>e!==t),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.observers.length||("loading"===this.state.status?this.scheduleGc():this.mutationCache.remove(this))}continue(){var t,e;return null!=(t=null==(e=this.retryer)?void 0:e.continue())?t:this.execute()}async execute(){var t,e,i,s,r,a,u,o,l,h,c,d,f,p,y,v,m,g,b,C;let q="loading"===this.state.status;try{if(!q){this.dispatch({type:"loading",variables:this.options.variables}),await (null==(l=(h=this.mutationCache.config).onMutate)?void 0:l.call(h,this.state.variables,this));let t=await (null==(c=(d=this.options).onMutate)?void 0:c.call(d,this.state.variables));t!==this.state.context&&this.dispatch({type:"loading",context:t,variables:this.state.variables})}let f=await (()=>{var t;return this.retryer=(0,n.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(t,e)=>{this.dispatch({type:"failed",failureCount:t,error:e})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise})();return await (null==(t=(e=this.mutationCache.config).onSuccess)?void 0:t.call(e,f,this.state.variables,this.state.context,this)),await (null==(i=(s=this.options).onSuccess)?void 0:i.call(s,f,this.state.variables,this.state.context)),await (null==(r=(a=this.mutationCache.config).onSettled)?void 0:r.call(a,f,null,this.state.variables,this.state.context,this)),await (null==(u=(o=this.options).onSettled)?void 0:u.call(o,f,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:f}),f}catch(t){try{throw await (null==(f=(p=this.mutationCache.config).onError)?void 0:f.call(p,t,this.state.variables,this.state.context,this)),await (null==(y=(v=this.options).onError)?void 0:y.call(v,t,this.state.variables,this.state.context)),await (null==(m=(g=this.mutationCache.config).onSettled)?void 0:m.call(g,void 0,t,this.state.variables,this.state.context,this)),await (null==(b=(C=this.options).onSettled)?void 0:b.call(C,void 0,t,this.state.variables,this.state.context)),t}finally{this.dispatch({type:"error",error:t})}}}dispatch(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"loading":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,n.Kw)(this.options.networkMode),status:"loading",variables:t.variables};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"};case"setState":return{...e,...t.state}}})(this.state),r.V.batch(()=>{this.observers.forEach(e=>{e.onMutationUpdate(t)}),this.mutationCache.notify({mutation:this,type:"updated",action:t})})}}function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0}}},6962:function(t,e,i){"use strict";i.d(e,{S:function(){return v}});var s=i(1678),r=i(5899),a=i(9522),n=i(4500),u=i(3864);class o extends u.F{constructor(t){super(),this.abortSignalConsumed=!1,this.defaultOptions=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.cache=t.cache,this.logger=t.logger||r._,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.initialState=t.state||function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,i=void 0!==e,s=i?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:i?null!=s?s:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"loading",fetchStatus:"idle"}}(this.options),this.state=this.initialState,this.scheduleGc()}get meta(){return this.options.meta}setOptions(t){this.options={...this.defaultOptions,...t},this.updateCacheTime(this.options.cacheTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.cache.remove(this)}setData(t,e){let i=(0,s.oE)(this.state.data,t,this.options);return this.dispatch({data:i,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),i}setState(t,e){this.dispatch({type:"setState",state:t,setStateOptions:e})}cancel(t){var e;let i=this.promise;return null==(e=this.retryer)||e.cancel(t),i?i.then(s.ZT).catch(s.ZT):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.initialState)}isActive(){return this.observers.some(t=>!1!==t.options.enabled)}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(t=>t.getCurrentResult().isStale)}isStaleByTime(t=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,s.Kp)(this.state.dataUpdatedAt,t)}onFocus(){var t;let e=this.observers.find(t=>t.shouldFetchOnWindowFocus());e&&e.refetch({cancelRefetch:!1}),null==(t=this.retryer)||t.continue()}onOnline(){var t;let e=this.observers.find(t=>t.shouldFetchOnReconnect());e&&e.refetch({cancelRefetch:!1}),null==(t=this.retryer)||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.retryer&&(this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.scheduleGc()),this.cache.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.dispatch({type:"invalidate"})}fetch(t,e){var i,r,a,u;if("idle"!==this.state.fetchStatus){if(this.state.dataUpdatedAt&&null!=e&&e.cancelRefetch)this.cancel({silent:!0});else if(this.promise)return null==(a=this.retryer)||a.continueRetry(),this.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let o=(0,s.G9)(),l={queryKey:this.queryKey,pageParam:void 0,meta:this.meta},h=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>{if(o)return this.abortSignalConsumed=!0,o.signal}})};h(l);let c={fetchOptions:e,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.abortSignalConsumed=!1,this.options.queryFn(l)):Promise.reject("Missing queryFn for queryKey '"+this.options.queryHash+"'")};h(c),null==(i=this.options.behavior)||i.onFetch(c),this.revertState=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==(null==(r=c.fetchOptions)?void 0:r.meta))&&this.dispatch({type:"fetch",meta:null==(u=c.fetchOptions)?void 0:u.meta});let d=t=>{if((0,n.DV)(t)&&t.silent||this.dispatch({type:"error",error:t}),!(0,n.DV)(t)){var e,i,s,r;null==(e=(i=this.cache.config).onError)||e.call(i,t,this),null==(s=(r=this.cache.config).onSettled)||s.call(r,this.state.data,t,this)}this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.retryer=(0,n.Mz)({fn:c.fetchFn,abort:null==o?void 0:o.abort.bind(o),onSuccess:t=>{var e,i,s,r;if(void 0===t){d(Error(this.queryHash+" data is undefined"));return}this.setData(t),null==(e=(i=this.cache.config).onSuccess)||e.call(i,t,this),null==(s=(r=this.cache.config).onSettled)||s.call(r,t,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1},onError:d,onFail:(t,e)=>{this.dispatch({type:"failed",failureCount:t,error:e})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:c.options.retry,retryDelay:c.options.retryDelay,networkMode:c.options.networkMode}),this.promise=this.retryer.promise,this.promise}dispatch(t){this.state=(e=>{var i,s;switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null!=(i=t.meta)?i:null,fetchStatus:(0,n.Kw)(this.options.networkMode)?"fetching":"paused",...!e.dataUpdatedAt&&{error:null,status:"loading"}};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(s=t.dataUpdatedAt)?s:Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=t.error;if((0,n.DV)(r)&&r.revert&&this.revertState)return{...this.revertState,fetchStatus:"idle"};return{...e,error:r,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),a.V.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate(t)}),this.cache.notify({query:this,type:"updated",action:t})})}}var l=i(6761);class h extends l.l{constructor(t){super(),this.config=t||{},this.queries=[],this.queriesMap={}}build(t,e,i){var r;let a=e.queryKey,n=null!=(r=e.queryHash)?r:(0,s.Rm)(a,e),u=this.get(n);return u||(u=new o({cache:this,logger:t.getLogger(),queryKey:a,queryHash:n,options:t.defaultQueryOptions(e),state:i,defaultOptions:t.getQueryDefaults(a)}),this.add(u)),u}add(t){this.queriesMap[t.queryHash]||(this.queriesMap[t.queryHash]=t,this.queries.push(t),this.notify({type:"added",query:t}))}remove(t){let e=this.queriesMap[t.queryHash];e&&(t.destroy(),this.queries=this.queries.filter(e=>e!==t),e===t&&delete this.queriesMap[t.queryHash],this.notify({type:"removed",query:t}))}clear(){a.V.batch(()=>{this.queries.forEach(t=>{this.remove(t)})})}get(t){return this.queriesMap[t]}getAll(){return this.queries}find(t,e){let[i]=(0,s.I6)(t,e);return void 0===i.exact&&(i.exact=!0),this.queries.find(t=>(0,s._x)(i,t))}findAll(t,e){let[i]=(0,s.I6)(t,e);return Object.keys(i).length>0?this.queries.filter(t=>(0,s._x)(i,t)):this.queries}notify(t){a.V.batch(()=>{this.listeners.forEach(({listener:e})=>{e(t)})})}onFocus(){a.V.batch(()=>{this.queries.forEach(t=>{t.onFocus()})})}onOnline(){a.V.batch(()=>{this.queries.forEach(t=>{t.onOnline()})})}}var c=i(4654);class d extends l.l{constructor(t){super(),this.config=t||{},this.mutations=[],this.mutationId=0}build(t,e,i){let s=new c.m({mutationCache:this,logger:t.getLogger(),mutationId:++this.mutationId,options:t.defaultMutationOptions(e),state:i,defaultOptions:e.mutationKey?t.getMutationDefaults(e.mutationKey):void 0});return this.add(s),s}add(t){this.mutations.push(t),this.notify({type:"added",mutation:t})}remove(t){this.mutations=this.mutations.filter(e=>e!==t),this.notify({type:"removed",mutation:t})}clear(){a.V.batch(()=>{this.mutations.forEach(t=>{this.remove(t)})})}getAll(){return this.mutations}find(t){return void 0===t.exact&&(t.exact=!0),this.mutations.find(e=>(0,s.X7)(t,e))}findAll(t){return this.mutations.filter(e=>(0,s.X7)(t,e))}notify(t){a.V.batch(()=>{this.listeners.forEach(({listener:e})=>{e(t)})})}resumePausedMutations(){var t;return this.resuming=(null!=(t=this.resuming)?t:Promise.resolve()).then(()=>{let t=this.mutations.filter(t=>t.state.isPaused);return a.V.batch(()=>t.reduce((t,e)=>t.then(()=>e.continue().catch(s.ZT)),Promise.resolve()))}).then(()=>{this.resuming=void 0}),this.resuming}}var f=i(3597),p=i(1507);function y(t,e){return null==t.getNextPageParam?void 0:t.getNextPageParam(e[e.length-1],e)}class v{constructor(t={}){this.queryCache=t.queryCache||new h,this.mutationCache=t.mutationCache||new d,this.logger=t.logger||r._,this.defaultOptions=t.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[],this.mountCount=0}mount(){this.mountCount++,1===this.mountCount&&(this.unsubscribeFocus=f.j.subscribe(()=>{f.j.isFocused()&&(this.resumePausedMutations(),this.queryCache.onFocus())}),this.unsubscribeOnline=p.N.subscribe(()=>{p.N.isOnline()&&(this.resumePausedMutations(),this.queryCache.onOnline())}))}unmount(){var t,e;this.mountCount--,0===this.mountCount&&(null==(t=this.unsubscribeFocus)||t.call(this),this.unsubscribeFocus=void 0,null==(e=this.unsubscribeOnline)||e.call(this),this.unsubscribeOnline=void 0)}isFetching(t,e){let[i]=(0,s.I6)(t,e);return i.fetchStatus="fetching",this.queryCache.findAll(i).length}isMutating(t){return this.mutationCache.findAll({...t,fetching:!0}).length}getQueryData(t,e){var i;return null==(i=this.queryCache.find(t,e))?void 0:i.state.data}ensureQueryData(t,e,i){let r=(0,s._v)(t,e,i),a=this.getQueryData(r.queryKey);return a?Promise.resolve(a):this.fetchQuery(r)}getQueriesData(t){return this.getQueryCache().findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,i){let r=this.queryCache.find(t),a=null==r?void 0:r.state.data,n=(0,s.SE)(e,a);if(void 0===n)return;let u=(0,s._v)(t),o=this.defaultQueryOptions(u);return this.queryCache.build(this,o).setData(n,{...i,manual:!0})}setQueriesData(t,e,i){return a.V.batch(()=>this.getQueryCache().findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,i)]))}getQueryState(t,e){var i;return null==(i=this.queryCache.find(t,e))?void 0:i.state}removeQueries(t,e){let[i]=(0,s.I6)(t,e),r=this.queryCache;a.V.batch(()=>{r.findAll(i).forEach(t=>{r.remove(t)})})}resetQueries(t,e,i){let[r,n]=(0,s.I6)(t,e,i),u=this.queryCache,o={type:"active",...r};return a.V.batch(()=>(u.findAll(r).forEach(t=>{t.reset()}),this.refetchQueries(o,n)))}cancelQueries(t,e,i){let[r,n={}]=(0,s.I6)(t,e,i);return void 0===n.revert&&(n.revert=!0),Promise.all(a.V.batch(()=>this.queryCache.findAll(r).map(t=>t.cancel(n)))).then(s.ZT).catch(s.ZT)}invalidateQueries(t,e,i){let[r,n]=(0,s.I6)(t,e,i);return a.V.batch(()=>{var t,e;if(this.queryCache.findAll(r).forEach(t=>{t.invalidate()}),"none"===r.refetchType)return Promise.resolve();let i={...r,type:null!=(t=null!=(e=r.refetchType)?e:r.type)?t:"active"};return this.refetchQueries(i,n)})}refetchQueries(t,e,i){let[r,n]=(0,s.I6)(t,e,i),u=Promise.all(a.V.batch(()=>this.queryCache.findAll(r).filter(t=>!t.isDisabled()).map(t=>{var e;return t.fetch(void 0,{...n,cancelRefetch:null==(e=null==n?void 0:n.cancelRefetch)||e,meta:{refetchPage:r.refetchPage}})}))).then(s.ZT);return null!=n&&n.throwOnError||(u=u.catch(s.ZT)),u}fetchQuery(t,e,i){let r=(0,s._v)(t,e,i),a=this.defaultQueryOptions(r);void 0===a.retry&&(a.retry=!1);let n=this.queryCache.build(this,a);return n.isStaleByTime(a.staleTime)?n.fetch(a):Promise.resolve(n.state.data)}prefetchQuery(t,e,i){return this.fetchQuery(t,e,i).then(s.ZT).catch(s.ZT)}fetchInfiniteQuery(t,e,i){let r=(0,s._v)(t,e,i);return r.behavior={onFetch:t=>{t.fetchFn=()=>{var e,i,s,r,a,n,u;let o;let l=null==(e=t.fetchOptions)?void 0:null==(i=e.meta)?void 0:i.refetchPage,h=null==(s=t.fetchOptions)?void 0:null==(r=s.meta)?void 0:r.fetchMore,c=null==h?void 0:h.pageParam,d=(null==h?void 0:h.direction)==="forward",f=(null==h?void 0:h.direction)==="backward",p=(null==(a=t.state.data)?void 0:a.pages)||[],v=(null==(n=t.state.data)?void 0:n.pageParams)||[],m=v,g=!1,b=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>{var e,i;return null!=(e=t.signal)&&e.aborted?g=!0:null==(i=t.signal)||i.addEventListener("abort",()=>{g=!0}),t.signal}})},C=t.options.queryFn||(()=>Promise.reject("Missing queryFn for queryKey '"+t.options.queryHash+"'")),q=(t,e,i,s)=>(m=s?[e,...m]:[...m,e],s?[i,...t]:[...t,i]),O=(e,i,s,r)=>{if(g)return Promise.reject("Cancelled");if(void 0===s&&!i&&e.length)return Promise.resolve(e);let a={queryKey:t.queryKey,pageParam:s,meta:t.options.meta};return b(a),Promise.resolve(C(a)).then(t=>q(e,s,t,r))};if(p.length){if(d){let e=void 0!==c,i=e?c:y(t.options,p);o=O(p,e,i)}else if(f){let e=void 0!==c,i=e?c:null==(u=t.options).getPreviousPageParam?void 0:u.getPreviousPageParam(p[0],p);o=O(p,e,i,!0)}else{m=[];let e=void 0===t.options.getNextPageParam;o=!l||!p[0]||l(p[0],0,p)?O([],e,v[0]):Promise.resolve(q([],v[0],p[0]));for(let i=1;i<p.length;i++)o=o.then(s=>{if(!l||!p[i]||l(p[i],i,p)){let r=e?v[i]:y(t.options,s);return O(s,e,r)}return Promise.resolve(q(s,v[i],p[i]))})}}else o=O([]);return o.then(t=>({pages:t,pageParams:m}))}}},this.fetchQuery(r)}prefetchInfiniteQuery(t,e,i){return this.fetchInfiniteQuery(t,e,i).then(s.ZT).catch(s.ZT)}resumePausedMutations(){return this.mutationCache.resumePausedMutations()}getQueryCache(){return this.queryCache}getMutationCache(){return this.mutationCache}getLogger(){return this.logger}getDefaultOptions(){return this.defaultOptions}setDefaultOptions(t){this.defaultOptions=t}setQueryDefaults(t,e){let i=this.queryDefaults.find(e=>(0,s.yF)(t)===(0,s.yF)(e.queryKey));i?i.defaultOptions=e:this.queryDefaults.push({queryKey:t,defaultOptions:e})}getQueryDefaults(t){if(!t)return;let e=this.queryDefaults.find(e=>(0,s.to)(t,e.queryKey));return null==e?void 0:e.defaultOptions}setMutationDefaults(t,e){let i=this.mutationDefaults.find(e=>(0,s.yF)(t)===(0,s.yF)(e.mutationKey));i?i.defaultOptions=e:this.mutationDefaults.push({mutationKey:t,defaultOptions:e})}getMutationDefaults(t){if(!t)return;let e=this.mutationDefaults.find(e=>(0,s.to)(t,e.mutationKey));return null==e?void 0:e.defaultOptions}defaultQueryOptions(t){if(null!=t&&t._defaulted)return t;let e={...this.defaultOptions.queries,...this.getQueryDefaults(null==t?void 0:t.queryKey),...t,_defaulted:!0};return!e.queryHash&&e.queryKey&&(e.queryHash=(0,s.Rm)(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.useErrorBoundary&&(e.useErrorBoundary=!!e.suspense),e}defaultMutationOptions(t){return null!=t&&t._defaulted?t:{...this.defaultOptions.mutations,...this.getMutationDefaults(null==t?void 0:t.mutationKey),...t,_defaulted:!0}}clear(){this.queryCache.clear(),this.mutationCache.clear()}}},3864:function(t,e,i){"use strict";i.d(e,{F:function(){return r}});var s=i(1678);class r{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,s.PN)(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(t){this.cacheTime=Math.max(this.cacheTime||0,null!=t?t:s.sk?1/0:3e5)}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}},9769:function(t,e,i){"use strict";i.d(e,{j:function(){return n}});var s=i(3167);let r=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,a=s.W,n=(t,e)=>i=>{var s;if((null==e?void 0:e.variants)==null)return a(t,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:n,defaultVariants:u}=e,o=Object.keys(n).map(t=>{let e=null==i?void 0:i[t],s=null==u?void 0:u[t];if(null===e)return null;let a=r(e)||r(s);return n[t][a]}),l=i&&Object.entries(i).reduce((t,e)=>{let[i,s]=e;return void 0===s||(t[i]=s),t},{});return a(t,o,null==e?void 0:null===(s=e.compoundVariants)||void 0===s?void 0:s.reduce((t,e)=>{let{class:i,className:s,...r}=e;return Object.entries(r).every(t=>{let[e,i]=t;return Array.isArray(i)?i.includes({...u,...l}[e]):({...u,...l})[e]===i})?[...t,i,s]:t},[]),null==i?void 0:i.class,null==i?void 0:i.className)}}}]);