"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2608],{6674:function(e,t,n){n.d(t,{Ry:function(){return l}});var r=new WeakMap,o=new WeakMap,a={},c=0,i=function(e){return e&&(e.host||i(e.parentNode))},u=function(e,t,n,u){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=i(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var d=a[n],s=[],f=new Set,v=new Set(l),p=function(e){!e||f.has(e)||(f.add(e),p(e.parentNode))};l.forEach(p);var m=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(u),a=null!==t&&"false"!==t,c=(r.get(e)||0)+1,i=(d.get(e)||0)+1;r.set(e,c),d.set(e,i),s.push(e),1===c&&a&&o.set(e,!0),1===i&&e.setAttribute(n,"true"),a||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),c++,function(){s.forEach(function(e){var t=r.get(e)-1,a=d.get(e)-1;r.set(e,t),d.set(e,a),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),a||e.removeAttribute(n)}),--c||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}},l=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),u(r,o,n,"aria-hidden")):function(){return null}}},9259:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(7977).Z)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3441:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(7977).Z)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7225:function(e,t,n){n.d(t,{Z:function(){return V}});var r,o,a,c,i,u,l=function(){return(l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function d(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var s=n(2265),f="right-scroll-bar-position",v="width-before-scroll-bar";function p(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var m="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,h=new WeakMap,g=(void 0===o&&(o={}),(void 0===a&&(a=function(e){return e}),c=[],i=!1,u={read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(e){var t=a(e,i);return c.push(t),function(){c=c.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;c.length;){var t=c;c=[],t.forEach(e)}c={push:function(t){return e(t)},filter:function(){return c}}},assignMedium:function(e){i=!0;var t=[];if(c.length){var n=c;c=[],n.forEach(e),t=c}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),c={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),c}}}}).options=l({async:!0,ssr:!1},o),u),y=function(){},b=s.forwardRef(function(e,t){var n,r,o,a,c=s.useRef(null),i=s.useState({onScrollCapture:y,onWheelCapture:y,onTouchMoveCapture:y}),u=i[0],f=i[1],v=e.forwardProps,b=e.children,E=e.className,w=e.removeScrollBar,S=e.enabled,C=e.shards,R=e.sideCar,M=e.noRelative,k=e.noIsolation,A=e.inert,N=e.allowPinchZoom,x=e.as,L=e.gapMode,T=d(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[c,t],r=function(e){return n.forEach(function(t){return p(t,e)})},(o=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,m(function(){var e=h.get(a);if(e){var t=new Set(e),r=new Set(n),o=a.current;t.forEach(function(e){r.has(e)||p(e,null)}),r.forEach(function(e){t.has(e)||p(e,o)})}h.set(a,n)},[n]),a),I=l(l({},T),u);return s.createElement(s.Fragment,null,S&&s.createElement(R,{sideCar:g,removeScrollBar:w,shards:C,noRelative:M,noIsolation:k,inert:A,setCallbacks:f,allowPinchZoom:!!N,lockRef:c,gapMode:L}),v?s.cloneElement(s.Children.only(b),l(l({},I),{ref:P})):s.createElement(void 0===x?"div":x,l({},I,{className:E,ref:P}),b))});b.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b.classNames={fullWidth:v,zeroRight:f};var E=function(e){var t=e.sideCar,n=d(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,l({},n))};E.isSideCarExport=!0;var w=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,c;(a=t).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),c=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(c)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},S=function(){var e=w();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},C=function(){var e=S();return function(t){return e(t.styles,t.dynamic),null}},R={left:0,top:0,right:0,gap:0},M=function(e){return parseInt(e||"",10)||0},k=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[M(n),M(r),M(o)]},A=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return R;var t=k(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},N=C(),x="data-scroll-locked",L=function(e,t,n,r){var o=e.left,a=e.top,c=e.right,i=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(x,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(f," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(v," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(f," .").concat(f," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(v," .").concat(v," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(x,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(x)||"0",10);return isFinite(e)?e:0},P=function(){s.useEffect(function(){return document.body.setAttribute(x,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(x):document.body.setAttribute(x,e.toString())}},[])},I=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;P();var a=s.useMemo(function(){return A(o)},[o]);return s.createElement(N,{styles:L(a,!t,o,n?"":"!important")})},O=!1;if("undefined"!=typeof window)try{var W=Object.defineProperty({},"passive",{get:function(){return O=!0,!0}});window.addEventListener("test",W,W),window.removeEventListener("test",W,W)}catch(e){O=!1}var F=!!O&&{passive:!1},j=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},Z=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),B(e,r)){var o=D(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},B=function(e,t){return"v"===e?j(t,"overflowY"):j(t,"overflowX")},D=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},K=function(e,t,n,r,o){var a,c=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=c*r,u=n.target,l=t.contains(u),d=!1,s=i>0,f=0,v=0;do{var p=D(e,u),m=p[0],h=p[1]-p[2]-c*m;(m||h)&&B(e,u)&&(f+=h,v+=m),u=u.parentNode.host||u.parentNode}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return s&&(o&&1>Math.abs(f)||!o&&i>f)?d=!0:!s&&(o&&1>Math.abs(v)||!o&&-i>v)&&(d=!0),d},_=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},X=function(e){return[e.deltaX,e.deltaY]},Y=function(e){return e&&"current"in e?e.current:e},q=0,H=[],U=(g.useMedium(function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),o=s.useState(q++)[0],a=s.useState(C)[0],c=s.useRef(e);s.useEffect(function(){c.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(Y),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var o,a=_(e),i=n.current,u="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],d=e.target,s=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===s&&"range"===d.type)return!1;var f=Z(s,d);if(!f)return!0;if(f?o=s:(o="v"===s?"h":"v",f=Z(s,d)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var v=r.current||o;return K(v,t,e,"h"===v?u:l,!0)},[]),u=s.useCallback(function(e){if(H.length&&H[H.length-1]===a){var n="deltaY"in e?X(e):_(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(Y).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?i(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),l=s.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=s.useCallback(function(e){n.current=_(e),r.current=void 0},[]),f=s.useCallback(function(t){l(t.type,X(t),t.target,i(t,e.lockRef.current))},[]),v=s.useCallback(function(t){l(t.type,_(t),t.target,i(t,e.lockRef.current))},[]);s.useEffect(function(){return H.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",u,F),document.addEventListener("touchmove",u,F),document.addEventListener("touchstart",d,F),function(){H=H.filter(function(e){return e!==a}),document.removeEventListener("wheel",u,F),document.removeEventListener("touchmove",u,F),document.removeEventListener("touchstart",d,F)}},[]);var p=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?s.createElement(I,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),E),z=s.forwardRef(function(e,t){return s.createElement(b,l({},e,{ref:t,sideCar:U}))});z.classNames=b.classNames;var V=z},5528:function(e,t,n){n.d(t,{B:function(){return u}});var r=n(2265),o=n(4104),a=n(1266),c=n(9143),i=n(7437);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.b)(t),[l,d]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,o=r.useRef(null),a=r.useRef(new Map).current;return(0,i.jsx)(l,{scope:t,itemMap:a,collectionRef:o,children:n})};s.displayName=t;let f=e+"CollectionSlot",v=(0,c.Z8)(f),p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=d(f,n),c=(0,a.e)(t,o.collectionRef);return(0,i.jsx)(v,{ref:c,children:r})});p.displayName=f;let m=e+"CollectionItemSlot",h="data-radix-collection-item",g=(0,c.Z8)(m),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...c}=e,u=r.useRef(null),l=(0,a.e)(t,u),s=d(m,n);return r.useEffect(()=>(s.itemMap.set(u,{ref:u,...c}),()=>void s.itemMap.delete(u))),(0,i.jsx)(g,{[h]:"",ref:l,children:o})});return y.displayName=m,[{Provider:s,Slot:p,ItemSlot:y},function(t){let n=d(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}Map},3876:function(e,t,n){n.d(t,{gm:function(){return a}});var r=n(2265);n(7437);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},6007:function(e,t,n){n.d(t,{EW:function(){return a}});var r=n(2265),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:c()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:c()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function c(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},8082:function(e,t,n){let r;n.d(t,{M:function(){return f}});var o=n(2265),a=n(1266),c=n(9586),i=n(9830),u=n(7437),l="focusScope.autoFocusOnMount",d="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:f,onUnmountAutoFocus:g,...y}=e,[b,E]=o.useState(null),w=(0,i.W)(f),S=(0,i.W)(g),C=o.useRef(null),R=(0,a.e)(t,e=>E(e)),M=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(M.paused||!b)return;let t=e.target;b.contains(t)?C.current=t:m(C.current,{select:!0})},t=function(e){if(M.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||m(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,b,M.paused]),o.useEffect(()=>{if(b){h.add(M);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(l,s);b.addEventListener(l,w),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(v(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(b))}return()=>{b.removeEventListener(l,w),setTimeout(()=>{let t=new CustomEvent(d,s);b.addEventListener(d,S),b.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),b.removeEventListener(d,S),h.remove(M)},0)}}},[b,w,S,M]);let k=o.useCallback(e=>{if(!n&&!r||M.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=v(e);return[p(t,e),p(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&m(a,{select:!0})):(e.preventDefault(),n&&m(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,M.paused]);return(0,u.jsx)(c.WV.div,{tabIndex:-1,...y,ref:R,onKeyDown:k})});function v(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var h=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=g(r,e)).unshift(e)},remove(e){var t;null===(t=(r=g(r,e))[0])||void 0===t||t.resume()}});function g(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}}}]);