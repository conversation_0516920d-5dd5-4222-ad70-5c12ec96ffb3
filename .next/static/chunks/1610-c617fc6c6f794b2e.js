"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1610],{4991:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},1266:function(e,t,n){n.d(t,{F:function(){return o},e:function(){return l}});var r=n(2265);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function l(...e){return r.useCallback(o(...e),e)}},4104:function(e,t,n){n.d(t,{b:function(){return l},k:function(){return o}});var r=n(2265),i=n(7437);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,l=r.useMemo(()=>o,Object.values(o));return(0,i.jsx)(n.Provider,{value:l,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return o.scopeName=e,[function(t,o){let l=r.createContext(o),u=n.length;n=[...n,o];let a=t=>{let{scope:n,children:o,...a}=t,s=n?.[e]?.[u]||l,c=r.useMemo(()=>a,Object.values(a));return(0,i.jsx)(s.Provider,{value:c,children:o})};return a.displayName=t+"Provider",[a,function(n,i){let a=i?.[e]?.[u]||l,s=r.useContext(a);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(o,...t)]}},1260:function(e,t,n){n.d(t,{XB:function(){return d}});var r,i=n(2265),o=n(4991),l=n(9586),u=n(1266),a=n(9830),s=n(7437),c="dismissableLayer.update",f=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,...b}=e,E=i.useContext(f),[R,C]=i.useState(null),A=null!==(d=null==R?void 0:R.ownerDocument)&&void 0!==d?d:null===(n=globalThis)||void 0===n?void 0:n.document,[,P]=i.useState({}),S=(0,u.e)(t,e=>C(e)),L=Array.from(E.layers),[O]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),T=L.indexOf(O),D=R?L.indexOf(R):-1,W=E.layersWithOutsidePointerEventsDisabled.size>0,j=D>=T,k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,a.W)(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!j||n||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==x||x())},A),N=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,a.W)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},A);return!function(e,t=globalThis?.document){let n=(0,a.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D!==E.layers.size-1||(null==v||v(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},A),i.useEffect(()=>{if(R)return m&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),p(),()=>{m&&1===E.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=r)}},[R,A,m,E]),i.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),p())},[R,E]),i.useEffect(()=>{let e=()=>P({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(l.WV.div,{...b,ref:S,style:{pointerEvents:W?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,k.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,l.jH)(o,u):o.dispatchEvent(u)}d.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(f),r=i.useRef(null),o=(0,u.e)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(l.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},8687:function(e,t,n){n.d(t,{M:function(){return a}});var r,i=n(2265),o=n(2618),l=(r||(r=n.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function a(e){let[t,n]=i.useState(l());return(0,o.b)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},2338:function(e,t,n){n.d(t,{ee:function(){return eJ},Eh:function(){return eQ},VY:function(){return eK},fC:function(){return eG},D7:function(){return ej}});var r=n(2265);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,u=Math.round,a=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function E(e,t,n){let r,{reference:i,floating:o}=e,l=g(t),u=m(g(t)),a=v(u),s=p(t),c="y"===l,f=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[a]/2-o[a]/2;switch(s){case"top":r={x:f,y:i.y-o.height};break;case"bottom":r={x:f,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[u]-=y*(n&&c?-1:1);break;case"end":r[u]+=y*(n&&c?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,u=o.filter(Boolean),a=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:f}=E(s,r,a),d=r,p={},h=0;for(let n=0;n<u.length;n++){let{name:o,fn:m}=u[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,f=null!=g?g:f,p={...p,[o]:{...p[o],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:f}=E(s,d,a)),n=-1)}return{x:c,y:f,placement:d,strategy:i,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:u,strategy:a}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=x(h),v=u[p?"floating"===f?"reference":"floating":f],g=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(u.floating)),boundary:s,rootBoundary:c,strategy:a})),y="floating"===f?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(u.floating)),E=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},R=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:u,rect:y,offsetParent:w,strategy:a}):y);return{top:(g.top-R.top+m.top)/E.y,bottom:(R.bottom-g.bottom+m.bottom)/E.y,left:(g.left-R.left+m.left)/E.x,right:(R.right-g.right+m.right)/E.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return i.some(t=>e[t]>=0)}async function S(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),u=h(n),a="y"===g(n),s=["left","top"].includes(l)?-1:1,c=o&&a?-1:1,f=d(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return u&&"number"==typeof y&&(v="end"===u?-1*y:y),a?{x:v*c,y:m*s}:{x:m*s,y:v*c}}function L(){return"undefined"!=typeof window}function O(e){return W(e)?(e.nodeName||"").toLowerCase():"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(W(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function W(e){return!!L()&&(e instanceof Node||e instanceof T(e).Node)}function j(e){return!!L()&&(e instanceof Element||e instanceof T(e).Element)}function k(e){return!!L()&&(e instanceof HTMLElement||e instanceof T(e).HTMLElement)}function N(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof T(e).ShadowRoot)}function F(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=_(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function H(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function M(e){let t=V(),n=j(e)?_(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function V(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function $(e){return["html","body","#document"].includes(O(e))}function _(e){return T(e).getComputedStyle(e)}function B(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||D(e);return N(t)?t.host:t}function I(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=z(t);return $(n)?t.ownerDocument?t.ownerDocument.body:t.body:k(n)&&F(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=T(i);if(o){let e=Y(l);return t.concat(l,l.visualViewport||[],F(i)?i:[],e&&n?I(e):[])}return t.concat(i,I(i,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function X(e){let t=_(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=k(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,a=u(n)!==o||u(r)!==l;return a&&(n=o,r=l),{width:n,height:r,$:a}}function Z(e){return j(e)?e:e.contextElement}function q(e){let t=Z(e);if(!k(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=X(t),l=(o?u(n.width):n.width)/r,a=(o?u(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let G=s(0);function J(e){let t=T(e);return V()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:G}function K(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=Z(e),u=s(1);t&&(r?j(r)&&(u=q(r)):u=q(e));let a=(void 0===(i=n)&&(i=!1),r&&(!i||r===T(l))&&i)?J(l):s(0),c=(o.left+a.x)/u.x,f=(o.top+a.y)/u.y,d=o.width/u.x,p=o.height/u.y;if(l){let e=T(l),t=r&&j(r)?T(r):r,n=e,i=Y(n);for(;i&&r&&t!==n;){let e=q(i),t=i.getBoundingClientRect(),r=_(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,f*=e.y,d*=e.x,p*=e.y,c+=o,f+=l,i=Y(n=T(i))}}return b({width:d,height:p,x:c,y:f})}function Q(e,t){let n=B(e).scrollLeft;return t?t.left+n:K(D(e)).left+n}function U(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=T(e),r=D(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,u=0,a=0;if(i){o=i.width,l=i.height;let e=V();(!e||e&&"fixed"===t)&&(u=i.offsetLeft,a=i.offsetTop)}return{width:o,height:l,x:u,y:a}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=B(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),u=-n.scrollLeft+Q(e),a=-n.scrollTop;return"rtl"===_(r).direction&&(u+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:u,y:a}}(D(e));else if(j(t))r=function(e,t){let n=K(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=k(e)?q(e):s(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=J(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===_(e).position}function en(e,t){if(!k(e)||"fixed"===_(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=T(e);if(H(e))return n;if(!k(e)){let t=z(e);for(;t&&!$(t);){if(j(t)&&!et(t))return t;t=z(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(O(r))&&et(r);)r=en(r,t);return r&&$(r)&&et(r)&&!M(r)?n:r||function(e){let t=z(e);for(;k(t)&&!$(t);){if(M(t))return t;if(H(t))break;t=z(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=k(t),i=D(t),o="fixed"===n,l=K(e,!0,o,t),u={scrollLeft:0,scrollTop:0},a=s(0);if(r||!r&&!o){if(("body"!==O(t)||F(i))&&(u=B(t)),r){let e=K(t,!0,o,t);a.x=e.x+t.clientLeft,a.y=e.y+t.clientTop}else i&&(a.x=Q(i))}o&&!r&&i&&(a.x=Q(i));let c=!i||r||o?s(0):U(i,u);return{x:l.left+u.scrollLeft-a.x-c.x,y:l.top+u.scrollTop-a.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=D(r),u=!!t&&H(t.floating);if(r===l||u&&o)return n;let a={scrollLeft:0,scrollTop:0},c=s(1),f=s(0),d=k(r);if((d||!d&&!o)&&(("body"!==O(r)||F(l))&&(a=B(r)),k(r))){let e=K(r);c=q(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||o?s(0):U(l,a,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+f.x+p.x,y:n.y*c.y-a.scrollTop*c.y+f.y+p.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,u=[..."clippingAncestors"===n?H(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=I(e,[],!1).filter(e=>j(e)&&"body"!==O(e)),i=null,o="fixed"===_(e).position,l=o?z(e):e;for(;j(l)&&!$(l);){let t=_(l),n=M(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||F(l)&&!n&&function e(t,n){let r=z(t);return!(r===n||!j(r)||$(r))&&("fixed"===_(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=z(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=u[0],s=u.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,a,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=X(e);return{width:t,height:n}},getScale:q,isElement:j,isRTL:function(e){return"rtl"===_(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eu=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:u,platform:a,elements:s,middlewareData:c}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=x(p),w={x:n,y:r},b=m(g(i)),E=v(b),R=await a.getDimensions(f),C="y"===b,A=C?"clientHeight":"clientWidth",P=u.reference[E]+u.reference[b]-w[b]-u.floating[E],S=w[b]-u.reference[b],L=await (null==a.getOffsetParent?void 0:a.getOffsetParent(f)),O=L?L[A]:0;O&&await (null==a.isElement?void 0:a.isElement(L))||(O=s.floating[A]||u.floating[E]);let T=O/2-R[E]/2-1,D=o(y[C?"top":"left"],T),W=o(y[C?"bottom":"right"],T),j=O-R[E]-W,k=O/2-R[E]/2+(P/2-S/2),N=l(D,o(k,j)),F=!c.arrow&&null!=h(i)&&k!==N&&u.reference[E]/2-(k<D?D:W)-R[E]/2<0,H=F?k<D?k-D:k-j:0;return{[b]:w[b]+H,data:{[b]:N,centerOffset:k-N-H,...F&&{alignmentOffset:H}},reset:F}}}),ea=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return R(e,t,{...i,platform:o})};var es=n(4887),ec="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ef(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eu({element:n.current,padding:r}).fn(t):{}:n?eu({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:i,y:o,placement:l,middlewareData:u}=e,a=await S(e,n);return l===(null==(t=u.offset)?void 0:t.placement)&&null!=(r=u.arrow)&&r.alignmentOffset?{}:{x:i+a.x,y:o+a.y,data:{...a,placement:l}}}}),options:[e,t]}},eg=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:i}=e,{mainAxis:u=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(n,e),f={x:t,y:r},h=await C(e,c),v=g(p(i)),y=m(v),w=f[y],x=f[v];if(u){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,o(w,r))}if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}let b=s.fn({...e,[y]:w,[v]:x});return{...b,data:{x:b.x-t,y:b.y-r,enabled:{[y]:u,[v]:a}}}}}),options:[e,t]}},ey=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:u=0,mainAxis:a=!0,crossAxis:s=!0}=d(n,e),c={x:t,y:r},f=g(i),h=m(f),v=c[h],y=c[f],w=d(u,e),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(a){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+x.mainAxis,n=o.reference[h]+o.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(s){var b,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[f]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[f])||0)+(t?0:x.crossAxis),r=o.reference[f]+o.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[f])||0)-(t?x.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[f]:y}}}),options:[e,t]}},ew=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,i,o,l,u;let{placement:a,middlewareData:s,rects:c,initialPlacement:f,platform:x,elements:b}=e,{mainAxis:E=!0,crossAxis:R=!0,fallbackPlacements:A,fallbackStrategy:P="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:L=!0,...O}=d(n,e);if(null!=(t=s.arrow)&&t.alignmentOffset)return{};let T=p(a),D=g(f),W=p(f)===f,j=await (null==x.isRTL?void 0:x.isRTL(b.floating)),k=A||(W||!L?[w(f)]:function(e){let t=w(e);return[y(e),t,y(t)]}(f)),N="none"!==S;!A&&N&&k.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(y)))),o}(f,L,S,j));let F=[f,...k],H=await C(e,O),M=[],V=(null==(r=s.flip)?void 0:r.overflows)||[];if(E&&M.push(H[T]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(g(e)),o=v(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=w(l)),[l,w(l)]}(a,c,j);M.push(H[e[0]],H[e[1]])}if(V=[...V,{placement:a,overflows:M}],!M.every(e=>e<=0)){let e=((null==(i=s.flip)?void 0:i.index)||0)+1,t=F[e];if(t){let n="alignment"===R&&D!==g(t),r=(null==(l=V[0])?void 0:l.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:V},reset:{placement:t}}}let n=null==(o=V.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(P){case"bestFit":{let e=null==(u=V.filter(e=>{if(N){let t=g(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:u[0];e&&(n=e);break}case"initialPlacement":n=f}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},ex=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let i,u;let{placement:a,rects:s,platform:c,elements:f}=e,{apply:m=()=>{},...v}=d(n,e),y=await C(e,v),w=p(a),x=h(a),b="y"===g(a),{width:E,height:R}=s.floating;"top"===w||"bottom"===w?(i=w,u=x===(await (null==c.isRTL?void 0:c.isRTL(f.floating))?"start":"end")?"left":"right"):(u=w,i="end"===x?"top":"bottom");let A=R-y.top-y.bottom,P=E-y.left-y.right,S=o(R-y[i],A),L=o(E-y[u],P),O=!e.middlewareData.shift,T=S,D=L;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(D=P),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(T=A),O&&!x){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);b?D=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):T=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await m({...e,availableWidth:D,availableHeight:T});let W=await c.getDimensions(f.floating);return E!==W.width||R!==W.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},eb=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...i}=d(n,e);switch(r){case"referenceHidden":{let n=A(await C(e,{...i,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:P(n)}}}case"escaped":{let n=A(await C(e,{...i,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:P(n)}}}default:return{}}}}),options:[e,t]}},eE=(e,t)=>({...em(e),options:[e,t]});var eR=n(9586),eC=n(7437),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eC.jsx)(eR.WV.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eP=n(1266),eS=n(4104),eL=n(9830),eO=n(2618),eT=n(6769),eD="Popper",[eW,ej]=(0,eS.b)(eD),[ek,eN]=eW(eD),eF=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eC.jsx)(ek,{scope:t,anchor:i,onAnchorChange:o,children:n})};eF.displayName=eD;var eH="PopperAnchor",eM=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eN(eH,n),u=r.useRef(null),a=(0,eP.e)(t,u);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||u.current)}),i?null:(0,eC.jsx)(eR.WV.div,{...o,ref:a})});eM.displayName=eH;var eV="PopperContent",[e$,e_]=eW(eV),eB=r.forwardRef((e,t)=>{var n,i,u,s,c,f,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:C=!1,updatePositionStrategy:A="optimized",onPlaced:P,...S}=e,L=eN(eV,h),[O,T]=r.useState(null),W=(0,eP.e)(t,e=>T(e)),[j,k]=r.useState(null),N=(0,eT.t)(j),F=null!==(d=null==N?void 0:N.width)&&void 0!==d?d:0,H=null!==(p=null==N?void 0:N.height)&&void 0!==p?p:0,M="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},V=Array.isArray(b)?b:[b],$=V.length>0,_={padding:M,boundary:V.filter(eX),altBoundary:$},{refs:B,floatingStyles:z,placement:Y,isPositioned:X,middlewareData:q}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:u}={},transform:a=!0,whileElementsMounted:s,open:c}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);ef(p,i)||h(i);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),x=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),b=l||m,E=u||g,R=r.useRef(null),C=r.useRef(null),A=r.useRef(f),P=null!=s,S=eh(s),L=eh(o),O=eh(c),T=r.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),ea(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};D.current&&!ef(A.current,t)&&(A.current=t,es.flushSync(()=>{d(t)}))})},[p,t,n,L,O]);ec(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let D=r.useRef(!1);ec(()=>(D.current=!0,()=>{D.current=!1}),[]),ec(()=>{if(b&&(R.current=b),E&&(C.current=E),b&&E){if(S.current)return S.current(b,E,T);T()}},[b,E,T,S,P]);let W=r.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:x}),[w,x]),j=r.useMemo(()=>({reference:b,floating:E}),[b,E]),k=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=ep(j.floating,f.x),r=ep(j.floating,f.y);return a?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,a,j.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:T,refs:W,elements:j,floatingStyles:k}),[f,T,W,j,k])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=Z(e),h=u||s?[...p?I(p):[],...I(t)]:[];h.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let m=p&&f?function(e,t){let n,r=null,i=D(e);function u(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,f){void 0===c&&(c=!1),void 0===f&&(f=1),u();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=d;if(c||t(),!m||!v)return;let g=a(h),y=a(i.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-a(i.clientHeight-(h+v))+"px "+-a(p)+"px",threshold:l(0,o(1,f))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==f){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),u}(p,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?K(e):null;return d&&function t(){let r=K(e);y&&!el(y,r)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{u&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===A})},elements:{reference:L.anchor},middleware:[ev({mainAxis:v+H,alignmentAxis:y}),x&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?ey():void 0,..._}),x&&ew({..._}),ex({..._,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,u=t.floating.style;u.setProperty("--radix-popper-available-width","".concat(r,"px")),u.setProperty("--radix-popper-available-height","".concat(i,"px")),u.setProperty("--radix-popper-anchor-width","".concat(o,"px")),u.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&eE({element:j,padding:w}),eZ({arrowWidth:F,arrowHeight:H}),C&&eb({strategy:"referenceHidden",..._})]}),[G,J]=eq(Y),Q=(0,eL.W)(P);(0,eO.b)(()=>{X&&(null==Q||Q())},[X,Q]);let U=null===(n=q.arrow)||void 0===n?void 0:n.x,ee=null===(i=q.arrow)||void 0===i?void 0:i.y,et=(null===(u=q.arrow)||void 0===u?void 0:u.centerOffset)!==0,[en,er]=r.useState();return(0,eO.b)(()=>{O&&er(window.getComputedStyle(O).zIndex)},[O]),(0,eC.jsx)("div",{ref:B.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:X?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null===(s=q.transformOrigin)||void 0===s?void 0:s.x,null===(c=q.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(f=q.hide)||void 0===f?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(e$,{scope:h,placedSide:G,onArrowChange:k,arrowX:U,arrowY:ee,shouldHideArrow:et,children:(0,eC.jsx)(eR.WV.div,{"data-side":G,"data-align":J,...S,ref:W,style:{...S.style,animation:X?void 0:"none"}})})})});eB.displayName=eV;var ez="PopperArrow",eI={top:"bottom",right:"left",bottom:"top",left:"right"},eY=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=e_(ez,n),o=eI[i.placedSide];return(0,eC.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function eX(e){return null!==e}eY.displayName=ez;var eZ=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:u,rects:a,middlewareData:s}=t,c=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=eq(u),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!==(o=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+f/2,g=(null!==(l=null===(i=s.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,y="",w="";return"bottom"===p?(y=c?m:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=c?m:"".concat(v,"px"),w="".concat(a.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(a.floating.width+d,"px"),w=c?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function eq(e){let[t,n="center"]=e.split("-");return[t,n]}var eG=eF,eJ=eM,eK=eB,eQ=eY},7881:function(e,t,n){n.d(t,{h:function(){return a}});var r=n(2265),i=n(4887),o=n(9586),l=n(2618),u=n(7437),a=r.forwardRef((e,t)=>{var n,a;let{container:s,...c}=e,[f,d]=r.useState(!1);(0,l.b)(()=>d(!0),[]);let p=s||f&&(null===(a=globalThis)||void 0===a?void 0:null===(n=a.document)||void 0===n?void 0:n.body);return p?i.createPortal((0,u.jsx)(o.WV.div,{...c,ref:t}),p):null});a.displayName="Portal"},9586:function(e,t,n){n.d(t,{WV:function(){return u},jH:function(){return a}});var r=n(2265),i=n(4887),o=n(9143),l=n(7437),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.Z8)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e,u=i?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(u,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function a(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},9143:function(e,t,n){n.d(t,{Z8:function(){return l},g7:function(){return u},sA:function(){return s}});var r=n(2265),i=n(1266),o=n(7437);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,l;let u=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(a.ref=t?(0,i.F)(t,u):u),r.cloneElement(n,a)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...l}=e,u=r.Children.toArray(i),a=u.find(c);if(a){let e=a.props.children,i=u.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...l,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var u=l("Slot"),a=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},9830:function(e,t,n){n.d(t,{W:function(){return i}});var r=n(2265);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9310:function(e,t,n){n.d(t,{T:function(){return u}});var r,i=n(2265),o=n(2618),l=(r||(r=n.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.b;function u({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,u,a]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),u=i.useRef(t);return l(()=>{u.current=t},[t]),i.useEffect(()=>{o.current!==n&&(u.current?.(n),o.current=n)},[n,o]),[n,r,u]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,i.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else u(t)},[s,e,u,a])]}Symbol("RADIX:SYNC_STATE")},2618:function(e,t,n){n.d(t,{b:function(){return i}});var r=n(2265),i=globalThis?.document?r.useLayoutEffect:()=>{}},6769:function(e,t,n){n.d(t,{t:function(){return o}});var r=n(2265),i=n(2618);function o(e){let[t,n]=r.useState(void 0);return(0,i.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},9769:function(e,t,n){n.d(t,{j:function(){return l}});var r=n(3167);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.W,l=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:u}=t,a=Object.keys(l).map(e=>{let t=null==n?void 0:n[e],r=null==u?void 0:u[e];if(null===t)return null;let o=i(t)||i(r);return l[e][o]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,a,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...u,...s}[t]):({...u,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}}]);