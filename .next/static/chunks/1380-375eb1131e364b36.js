"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1380],{3277:function(e,a,s){s.d(a,{C:function(){return i}});var r=s(7437);s(2265);var l=s(9769),t=s(2169);let n=(0,l.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:a,variant:s,...l}=e;return(0,r.jsx)("div",{className:(0,t.cn)(n({variant:s}),a),...l})}},2632:function(e,a,s){s.d(a,{w:function(){return w}});var r=s(7437),l=s(2265),t=s(2169);let n=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:(0,t.cn)("w-full caption-bottom text-sm",s),...l})})});n.displayName="Table";let i=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("thead",{ref:a,className:(0,t.cn)("[&_tr]:border-b",s),...l})});i.displayName="TableHeader";let c=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("tbody",{ref:a,className:(0,t.cn)("[&_tr:last-child]:border-0",s),...l})});c.displayName="TableBody",l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("tfoot",{ref:a,className:(0,t.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...l})}).displayName="TableFooter";let d=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("tr",{ref:a,className:(0,t.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...l})});d.displayName="TableRow";let o=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("th",{ref:a,className:(0,t.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...l})});o.displayName="TableHead";let x=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("td",{ref:a,className:(0,t.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...l})});x.displayName="TableCell",l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,r.jsx)("caption",{ref:a,className:(0,t.cn)("mt-4 text-sm text-muted-foreground",s),...l})}).displayName="TableCaption";var m=s(575),u=s(2782),h=s(8641),f=s(5159),j=s(3441),g=s(4960),p=s(9108),v=s(7805),N=s(8306),b=s(8670),y=s(5404);function w(e){let{data:a,columns:s,loading:w=!1,pagination:k,searchable:C=!0,searchPlaceholder:S="Search...",onSearch:_,searchValue:Z,onSearchChange:z,onSearchSubmit:R,onSearchClear:T,showSearchButton:Q=!1,className:B,emptyMessage:E="No data available"}=e,[L,P]=(0,l.useState)(null),[M,V]=(0,l.useState)(null),[F,H]=(0,l.useState)(""),[I,O]=(0,l.useState)({}),A=e=>{L===e?(V("asc"===M?"desc":"desc"===M?null:"asc"),"desc"===M&&P(null)):(P(e),V("asc"))},K=e=>{H(e),_&&_(e)},q=(0,l.useMemo)(()=>{let e=[...a];return F&&!_&&(e=e.filter(e=>Object.values(e).some(e=>String(e).toLowerCase().includes(F.toLowerCase())))),Object.entries(I).forEach(a=>{let[s,r]=a;r&&"__all__"!==r&&(e=e.filter(e=>String(e[s]).toLowerCase().includes(r.toLowerCase())))}),L&&M&&e.sort((e,a)=>{let s=e[L],r=a[L];if(s===r)return 0;let l=s<r?-1:1;return"asc"===M?l:-l}),e},[a,F,I,L,M,_]),D=e=>{let s=a.map(a=>String(a[e])).filter(Boolean);return[...new Set(s)].sort()},G=e=>L!==e?(0,r.jsx)(f.Z,{className:"h-4 w-4 opacity-30"}):"asc"===M?(0,r.jsx)(f.Z,{className:"h-4 w-4"}):"desc"===M?(0,r.jsx)(j.Z,{className:"h-4 w-4"}):(0,r.jsx)(f.Z,{className:"h-4 w-4 opacity-30"}),J=(e,a,s)=>{let r=a[e.key];return e.render?e.render(r,a,s):String(r||"")};return(0,r.jsxs)("div",{className:(0,t.cn)("space-y-4",B),children:[(C||s.some(e=>e.filterable))&&(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[C&&(0,r.jsx)("div",{className:"relative flex-1",children:Q?(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(b.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(u.I,{placeholder:S,value:Z||"",onChange:e=>null==z?void 0:z(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(null==R||R())},className:"pl-10"})]}),(0,r.jsx)(m.z,{variant:"outline",onClick:R,className:"px-3",children:(0,r.jsx)(b.Z,{className:"h-4 w-4"})}),Z&&(0,r.jsx)(m.z,{variant:"outline",onClick:T,className:"px-3",title:"Clear search",children:"\xd7"})]}):(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(b.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(u.I,{placeholder:S,value:F,onChange:e=>K(e.target.value),className:"pl-10"})]})}),s.filter(e=>e.filterable).map(e=>(0,r.jsxs)(h.Ph,{value:I[String(e.key)]||"__all__",onValueChange:a=>O(s=>({...s,[String(e.key)]:"__all__"===a?"":a})),children:[(0,r.jsxs)(h.i4,{className:"w-48",children:[(0,r.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),(0,r.jsx)(h.ki,{placeholder:"Filter ".concat(e.title)})]}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsxs)(h.Ql,{value:"__all__",children:["All ",e.title]}),D(String(e.key)).map(e=>(0,r.jsx)(h.Ql,{value:e,children:e},e))]})]},String(e.key)))]}),(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(n,{children:[(0,r.jsx)(i,{children:(0,r.jsx)(d,{children:s.map(e=>(0,r.jsx)(o,{className:(0,t.cn)("font-medium",e.sortable&&"cursor-pointer hover:bg-gray-50","center"===e.align&&"text-center","right"===e.align&&"text-right",e.headerClassName),style:{width:e.width},onClick:()=>e.sortable&&A(String(e.key)),children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:e.title}),e.sortable&&G(String(e.key))]})},String(e.key)))})}),(0,r.jsx)(c,{children:w?[void 0,void 0,void 0,void 0,void 0].map((e,a)=>(0,r.jsx)(d,{children:s.map(e=>(0,r.jsx)(x,{children:(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"})},String(e.key)))},a)):0===q.length?(0,r.jsx)(d,{children:(0,r.jsx)(x,{colSpan:s.length,className:"h-24 text-center text-gray-500",children:E})}):q.map((e,a)=>(0,r.jsx)(d,{children:s.map(s=>(0,r.jsx)(x,{className:(0,t.cn)("center"===s.align&&"text-center","right"===s.align&&"text-right"),children:J(s,e,a)},String(s.key)))},a))})]})}),(()=>{if(!k)return null;let{page:e,limit:a,total:s,onPageChange:l,onLimitChange:t}=k,n=Math.ceil(s/a),i=Math.min(e*a,s);return(0,r.jsxs)("div",{className:"flex items-center justify-between px-2 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(e-1)*a+1," to ",i," of ",s," results"]}),(0,r.jsxs)(h.Ph,{value:String(a),onValueChange:e=>t(Number(e)),children:[(0,r.jsx)(h.i4,{className:"w-20",children:(0,r.jsx)(h.ki,{})}),(0,r.jsxs)(h.Bw,{children:[(0,r.jsx)(h.Ql,{value:"10",children:"10"}),(0,r.jsx)(h.Ql,{value:"25",children:"25"}),(0,r.jsx)(h.Ql,{value:"50",children:"50"}),(0,r.jsx)(h.Ql,{value:"100",children:"100"})]})]}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"per page"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.z,{variant:"outline",size:"sm",onClick:()=>l(1),disabled:1===e,children:(0,r.jsx)(g.Z,{className:"h-4 w-4"})}),(0,r.jsx)(m.z,{variant:"outline",size:"sm",onClick:()=>l(e-1),disabled:1===e,children:(0,r.jsx)(p.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"text-sm text-gray-700",children:["Page ",e," of ",n]}),(0,r.jsx)(m.z,{variant:"outline",size:"sm",onClick:()=>l(e+1),disabled:e===n,children:(0,r.jsx)(v.Z,{className:"h-4 w-4"})}),(0,r.jsx)(m.z,{variant:"outline",size:"sm",onClick:()=>l(n),disabled:e===n,children:(0,r.jsx)(N.Z,{className:"h-4 w-4"})})]})]})})()]})}}}]);