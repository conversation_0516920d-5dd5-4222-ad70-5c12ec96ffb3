"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9101],{7977:function(t,e,n){n.d(e,{Z:function(){return a}});var r=n(2265);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,n)=>n?n.toUpperCase():e.toLowerCase()),u=t=>{let e=o(t);return e.charAt(0).toUpperCase()+e.slice(1)},s=(...t)=>t.filter((t,e,n)=>!!t&&""!==t.trim()&&n.indexOf(t)===e).join(" ").trim(),l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,r.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:o="",children:u,iconNode:f,...a},h)=>(0,r.createElement)("svg",{ref:h,...c,width:e,height:e,stroke:t,strokeWidth:i?24*Number(n)/Number(e):n,className:s("lucide",o),...!u&&!l(a)&&{"aria-hidden":"true"},...a},[...f.map(([t,e])=>(0,r.createElement)(t,e)),...Array.isArray(u)?u:[u]])),a=(t,e)=>{let n=(0,r.forwardRef)(({className:n,...o},l)=>(0,r.createElement)(f,{ref:l,iconNode:e,className:s(`lucide-${i(u(t))}`,`lucide-${t}`,n),...o}));return n.displayName=u(t),n}},3597:function(t,e,n){n.d(e,{j:function(){return u}});var r=n(6761),i=n(1678);class o extends r.l{constructor(){super(),this.setup=t=>{if(!i.sk&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),window.addEventListener("focus",e,!1),()=>{window.removeEventListener("visibilitychange",e),window.removeEventListener("focus",e)}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){if(!this.hasListeners()){var t;null==(t=this.cleanup)||t.call(this),this.cleanup=void 0}}setEventListener(t){var e;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.focused!==t&&(this.focused=t,this.onFocus())}onFocus(){this.listeners.forEach(({listener:t})=>{t()})}isFocused(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)}}let u=new o},9522:function(t,e,n){n.d(e,{V:function(){return i}});var r=n(1678);let i=function(){let t=[],e=0,n=t=>{t()},i=t=>{t()},o=i=>{e?t.push(i):(0,r.A4)(()=>{n(i)})},u=()=>{let e=t;t=[],e.length&&(0,r.A4)(()=>{i(()=>{e.forEach(t=>{n(t)})})})};return{batch:t=>{let n;e++;try{n=t()}finally{--e||u()}return n},batchCalls:t=>(...e)=>{o(()=>{t(...e)})},schedule:o,setNotifyFunction:t=>{n=t},setBatchNotifyFunction:t=>{i=t}}}()},1507:function(t,e,n){n.d(e,{N:function(){return s}});var r=n(6761),i=n(1678);let o=["online","offline"];class u extends r.l{constructor(){super(),this.setup=t=>{if(!i.sk&&window.addEventListener){let e=()=>t();return o.forEach(t=>{window.addEventListener(t,e,!1)}),()=>{o.forEach(t=>{window.removeEventListener(t,e)})}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){if(!this.hasListeners()){var t;null==(t=this.cleanup)||t.call(this),this.cleanup=void 0}}setEventListener(t){var e;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(t=>{"boolean"==typeof t?this.setOnline(t):this.onOnline()})}setOnline(t){this.online!==t&&(this.online=t,this.onOnline())}onOnline(){this.listeners.forEach(({listener:t})=>{t()})}isOnline(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine}}let s=new u},4500:function(t,e,n){n.d(e,{DV:function(){return c},Kw:function(){return s},Mz:function(){return f}});var r=n(3597),i=n(1507),o=n(1678);function u(t){return Math.min(1e3*2**t,3e4)}function s(t){return(null!=t?t:"online")!=="online"||i.N.isOnline()}class l{constructor(t){this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}}function c(t){return t instanceof l}function f(t){let e,n,c,f=!1,a=0,h=!1,d=new Promise((t,e)=>{n=t,c=e}),y=()=>!r.j.isFocused()||"always"!==t.networkMode&&!i.N.isOnline(),p=r=>{h||(h=!0,null==t.onSuccess||t.onSuccess(r),null==e||e(),n(r))},v=n=>{h||(h=!0,null==t.onError||t.onError(n),null==e||e(),c(n))},w=()=>new Promise(n=>{e=t=>{let e=h||!y();return e&&n(t),e},null==t.onPause||t.onPause()}).then(()=>{e=void 0,h||null==t.onContinue||t.onContinue()}),b=()=>{let e;if(!h){try{e=t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(p).catch(e=>{var n,r;if(h)return;let i=null!=(n=t.retry)?n:3,s=null!=(r=t.retryDelay)?r:u,l="function"==typeof s?s(a,e):s,c=!0===i||"number"==typeof i&&a<i||"function"==typeof i&&i(a,e);if(f||!c){v(e);return}a++,null==t.onFail||t.onFail(a,e),(0,o.Gh)(l).then(()=>{if(y())return w()}).then(()=>{f?v(e):b()})})}};return s(t.networkMode)?b():w().then(b),{promise:d,cancel:e=>{h||(v(new l(e)),null==t.abort||t.abort())},continue:()=>(null==e?void 0:e())?d:Promise.resolve(),cancelRetry:()=>{f=!0},continueRetry:()=>{f=!1}}}},6761:function(t,e,n){n.d(e,{l:function(){return r}});class r{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){let e={listener:t};return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},1678:function(t,e,n){n.d(e,{A4:function(){return L},G9:function(){return k},Gh:function(){return C},I6:function(){return f},Kp:function(){return s},PN:function(){return u},Rm:function(){return d},SE:function(){return o},VS:function(){return w},X7:function(){return h},ZT:function(){return i},_v:function(){return l},_x:function(){return a},lV:function(){return c},oE:function(){return O},sk:function(){return r},to:function(){return p},yF:function(){return y}});let r="undefined"==typeof window||"Deno"in window;function i(){}function o(t,e){return"function"==typeof t?t(e):t}function u(t){return"number"==typeof t&&t>=0&&t!==1/0}function s(t,e){return Math.max(t+(e||0)-Date.now(),0)}function l(t,e,n){return g(t)?"function"==typeof e?{...n,queryKey:t,queryFn:e}:{...e,queryKey:t}:t}function c(t,e,n){return g(t)?"function"==typeof e?{...n,mutationKey:t,mutationFn:e}:{...e,mutationKey:t}:"function"==typeof t?{...e,mutationFn:t}:{...t}}function f(t,e,n){return g(t)?[{...e,queryKey:t},n]:[t||{},e]}function a(t,e){let{type:n="all",exact:r,fetchStatus:i,predicate:o,queryKey:u,stale:s}=t;if(g(u)){if(r){if(e.queryHash!==d(u,e.options))return!1}else{if(!v(e.queryKey,u))return!1}}if("all"!==n){let t=e.isActive();if("active"===n&&!t||"inactive"===n&&t)return!1}return("boolean"!=typeof s||e.isStale()===s)&&(void 0===i||i===e.state.fetchStatus)&&(!o||!!o(e))}function h(t,e){let{exact:n,fetching:r,predicate:i,mutationKey:o}=t;if(g(o)){if(!e.options.mutationKey)return!1;if(n){if(y(e.options.mutationKey)!==y(o))return!1}else{if(!v(e.options.mutationKey,o))return!1}}return("boolean"!=typeof r||"loading"===e.state.status===r)&&(!i||!!i(e))}function d(t,e){return((null==e?void 0:e.queryKeyHashFn)||y)(t)}function y(t){return JSON.stringify(t,(t,e)=>m(e)?Object.keys(e).sort().reduce((t,n)=>(t[n]=e[n],t),{}):e)}function p(t,e){return v(t,e)}function v(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&!Object.keys(e).some(n=>!v(t[n],e[n]))}function w(t,e){if(t&&!e||e&&!t)return!1;for(let n in t)if(t[n]!==e[n])return!1;return!0}function b(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function m(t){if(!E(t))return!1;let e=t.constructor;if(void 0===e)return!0;let n=e.prototype;return!!(E(n)&&n.hasOwnProperty("isPrototypeOf"))}function E(t){return"[object Object]"===Object.prototype.toString.call(t)}function g(t){return Array.isArray(t)}function C(t){return new Promise(e=>{setTimeout(e,t)})}function L(t){C(0).then(t)}function k(){if("function"==typeof AbortController)return new AbortController}function O(t,e,n){return null!=n.isDataEqual&&n.isDataEqual(t,e)?t:"function"==typeof n.structuralSharing?n.structuralSharing(t,e):!1!==n.structuralSharing?function t(e,n){if(e===n)return e;let r=b(e)&&b(n);if(r||m(e)&&m(n)){let i=r?e.length:Object.keys(e).length,o=r?n:Object.keys(n),u=o.length,s=r?[]:{},l=0;for(let i=0;i<u;i++){let u=r?i:o[i];s[u]=t(e[u],n[u]),s[u]===e[u]&&l++}return i===u&&l===i?e:s}return n}(t,e):e}},4095:function(t,e,n){n.d(e,{NL:function(){return s},aH:function(){return l}});var r=n(2265);let i=r.createContext(void 0),o=r.createContext(!1);function u(t,e){return t||(e&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=i),window.ReactQueryClientContext):i)}let s=function(){let{context:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=r.useContext(u(t,r.useContext(o)));if(!e)throw Error("No QueryClient set, use QueryClientProvider to set one");return e},l=t=>{let{client:e,children:n,context:i,contextSharing:s=!1}=t;r.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]);let l=u(i,s);return r.createElement(o.Provider,{value:!i&&s},r.createElement(l.Provider,{value:e},n))}}}]);