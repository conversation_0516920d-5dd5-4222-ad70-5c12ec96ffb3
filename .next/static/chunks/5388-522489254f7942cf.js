"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5388],{575:function(e,t,r){r.d(t,{d:function(){return l},z:function(){return c}});var a=r(7437),n=r(2265),i=r(9143),o=r(9769),s=r(2169);let l=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:r,variant:n,size:o,asChild:c=!1,...d}=e,u=c?i.g7:"button";return(0,a.jsx)(u,{className:(0,s.cn)(l({variant:n,size:o,className:r})),ref:t,...d})});c.displayName="Button"},2569:function(e,t,r){r.d(t,{U:function(){return f}});var a=r(7437),n=r(2265),i=r(2884),o=r(2169);let s=i.zt,l=i.fC,c=i.xz,d=n.forwardRef((e,t)=>{let{className:r,sideOffset:n=4,...s}=e;return(0,a.jsx)(i.VY,{ref:t,sideOffset:n,className:(0,o.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...s})});d.displayName=i.VY.displayName;var u=r(928);let f=e=>{let{dateTime:t,className:r="",showDate:n=!0,showTime:i=!0,format:o,onClick:f,isClickable:m=!1}=e,x=(0,u.Bv)(t,"dd/MM/yyyy"),h=(0,u.Bv)(t,"HH:mm"),g=(0,u.Yh)(t,"dd/MM/yyyy"),p=(0,u.Yh)(t,"HH:mm"),y=(0,u.vV)(),v="\n    text-center cursor-help transition-all duration-200\n    ".concat(m?"hover:bg-blue-50 hover:shadow-sm rounded-md p-2 cursor-pointer":"","\n    ").concat(r,"\n  ").trim(),b=(0,a.jsxs)("div",{className:v,onClick:m?f:void 0,children:[n&&(0,a.jsx)("div",{className:"font-semibold text-sm text-gray-900 leading-tight",children:x}),i&&(0,a.jsx)("div",{className:"text-gray-600 text-xs font-medium mt-1 leading-tight",children:h}),m&&(0,a.jsx)("div",{className:"text-xs text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity mt-1",children:"Click to filter"})]});return(0,a.jsx)(s,{children:(0,a.jsxs)(l,{children:[(0,a.jsx)(c,{asChild:!0,children:(0,a.jsx)("div",{className:m?"group":"",children:b})}),(0,a.jsx)(d,{side:"top",className:"max-w-xs bg-gray-900 text-white",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:"UTC Time:"}),(0,a.jsxs)("div",{className:"text-sm",children:[g," ",p," (GMT+0)"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-300 mt-2 border-t border-gray-700 pt-2",children:["Local: ",y]}),m&&(0,a.jsx)("div",{className:"text-xs text-blue-300 border-t border-gray-700 pt-2",children:"\uD83D\uDCA1 Click to filter by this date"})]})})]})})}},2782:function(e,t,r){r.d(t,{I:function(){return o}});var a=r(7437),n=r(2265),i=r(2169);let o=n.forwardRef((e,t)=>{let{className:r,type:n,...o}=e;return(0,a.jsx)("input",{type:n,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...o})});o.displayName="Input"},4133:function(e,t,r){r.d(t,{sm:function(){return f},uB:function(){return m},u_:function(){return u}});var a=r(7437),n=r(2265),i=r(5669),o=r(691),s=r(2235),l=r(575),c=r(2169);let d={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=e=>{let{isOpen:t,onClose:r,title:u,description:f,children:m,size:x="md",showCloseButton:h=!0,closeOnOverlayClick:g=!0,className:p}=e;return(0,a.jsx)(i.u,{appear:!0,show:t,as:n.Fragment,children:(0,a.jsxs)(o.Vq,{as:"div",className:"relative z-50",onClose:g?r:()=>{},children:[(0,a.jsx)(i.u.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,a.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,a.jsx)(i.u.Child,{as:n.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(o.Vq.Panel,{className:(0,c.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",d[x],p),children:[(u||h)&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[u&&(0,a.jsx)(o.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:u}),f&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:f})]}),h&&(0,a.jsx)(l.z,{variant:"ghost",size:"sm",onClick:r,className:"h-8 w-8 p-0",children:(0,a.jsx)(s.Z,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"mt-2",children:m})]})})})})]})})},f=e=>{let{isOpen:t,onClose:r,onConfirm:n,title:i="Confirm Action",message:o="Are you sure you want to proceed?",confirmText:s="Confirm",cancelText:c="Cancel",variant:d="default",loading:f=!1}=e;return(0,a.jsx)(u,{isOpen:t,onClose:r,title:i,size:"sm",closeOnOverlayClick:!f,children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:o}),(0,a.jsxs)("div",{className:"flex space-x-2 justify-end",children:[(0,a.jsx)(l.z,{variant:"outline",onClick:r,disabled:f,children:c}),(0,a.jsx)(l.z,{variant:"destructive"===d?"destructive":"default",onClick:n,disabled:f,children:f?"Processing...":s})]})]})})},m=e=>{let{isOpen:t,onClose:r,title:n,description:i,children:o,onSubmit:s,submitText:c="Save",cancelText:d="Cancel",loading:f=!1,size:m="md"}=e;return(0,a.jsx)(u,{isOpen:t,onClose:r,title:n,description:i,size:m,closeOnOverlayClick:!f,children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),null==s||s()},className:"space-y-4",children:[o,(0,a.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[(0,a.jsx)(l.z,{type:"button",variant:"outline",onClick:r,disabled:f,children:d}),s&&(0,a.jsx)(l.z,{type:"submit",disabled:f,children:f?"Saving...":c})]})]})})}},2975:function(e,t,r){r.d(t,{L:function(){return n}});var a=r(4921);let n={getFixtures:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;void 0!==a&&t.append(r,a.toString())});let r=await fetch("/api/fixtures?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error("Failed to fetch fixtures: ".concat(r.statusText));return await r.json()},getFixtureById:async e=>{let t=await fetch("/api/fixtures/".concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture: ".concat(t.statusText));return await t.json()},getUpcomingAndLive:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;void 0!==a&&t.append(r,a.toString())});let r=await fetch("/api/fixtures/live?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error("Failed to fetch live fixtures: ".concat(r.statusText));return await r.json()},getTeamSchedule:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,a]=e;void 0!==a&&r.append(t,a.toString())}),await a.x.get("/football/fixtures/schedules/".concat(e,"?").concat(r.toString()))},getFixtureStatistics:async e=>await a.x.get("/football/fixtures/statistics/".concat(e)),triggerSeasonSync:async()=>await a.x.get("/football/fixtures/sync/fixtures"),triggerDailySync:async()=>await a.x.get("/football/fixtures/sync/daily"),getSyncStatus:async()=>await a.x.get("/football/fixtures/sync/status"),createFixture:async e=>{let t=await fetch("/api/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create fixture: ".concat(t.statusText));return await t.json()},updateFixture:async(e,t)=>{let r=await fetch("/api/fixtures/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok)throw Error("Failed to update fixture: ".concat(r.statusText));return await r.json()},deleteFixture:async e=>{let t=(()=>{{try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t),a=null===(e=r.state)||void 0===e?void 0:e.accessToken;if(a)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",a.substring(0,20)+"..."),{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)}}}catch(e){console.warn("Failed to parse auth storage:",e)}let t=localStorage.getItem("accessToken");if(t)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}}return console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"}})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let r=await fetch("/api/fixtures/".concat(e),{method:"DELETE",headers:t});if(!r.ok){let e=await r.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",r.status,r.statusText,e),Error(e.message||"Failed to delete fixture: ".concat(r.statusText))}console.log("✅ Delete fixture successful:",e)},getFixture:async e=>(await n.getFixtureById(e)).data}},1546:function(e,t,r){r.d(t,{TE:function(){return c},a1:function(){return l}});var a=r(7437),n=r(2265),i=r(7907),o=r(7786),s=r(6146);let l=e=>{let{children:t,requiredRole:r,fallbackUrl:l="/auth/login"}=e,c=(0,i.useRouter)(),{isAuthenticated:d,user:u,isLoading:f}=(0,o.a)();if((0,n.useEffect)(()=>{if(!f){if(!d||!u){c.push(l);return}if(r&&!(Array.isArray(r)?r:[r]).includes(u.role)){c.push("/dashboard?error=unauthorized");return}}},[d,u,f,r,c,l]),f)return(0,a.jsx)(s.SX,{message:"Verifying authentication..."});if(!d||!u)return(0,a.jsx)(s.SX,{message:"Redirecting to login..."});if(r){let e=Array.isArray(r)?r:[r];if(!e.includes(u.role))return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,a.jsx)(a.Fragment,{children:t})},c=()=>{let{user:e}=(0,o.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),r=()=>t("admin"),a=()=>t(["admin","editor"]),n=()=>t(["admin","editor","moderator"]);return{user:e,hasRole:t,isAdmin:r,isEditor:a,isModerator:n,canManageUsers:()=>r(),canManageContent:()=>a(),canModerate:()=>n(),canSync:()=>r()}}},928:function(e,t,r){r.d(t,{Bv:function(){return l},PM:function(){return f},Yh:function(){return c},vV:function(){return u}});var a=r(7256),n=r(5544),i=r(8594),o=r(3617);let s=()=>Intl.DateTimeFormat().resolvedOptions().timeZone,l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"dd/MM/yyyy HH:mm";try{let r="string"==typeof e?(0,a.D)(e):e;if(!(0,n.J)(r))return"Invalid Date";let i=s();return(0,o.CV)(r,i,t)}catch(e){return console.error("Error formatting date to local time:",e),"Invalid Date"}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"dd/MM/yyyy HH:mm";try{let r="string"==typeof e?(0,a.D)(e):e;if(!(0,n.J)(r))return"Invalid Date";return(0,o.CV)(r,"UTC",t)}catch(e){return console.error("Error formatting date to UTC:",e),"Invalid Date"}},d=()=>{let e=new Date().getTimezoneOffset();return"GMT".concat(e<=0?"+":"-").concat(Math.floor(Math.abs(e)/60).toString().padStart(2,"0"),":").concat((Math.abs(e)%60).toString().padStart(2,"0"))},u=()=>{var e;let t=s(),r=d(),a=(null===(e=new Intl.DateTimeFormat("en",{timeZoneName:"short",timeZone:t}).formatToParts(new Date).find(e=>"timeZoneName"===e.type))||void 0===e?void 0:e.value)||"";return"".concat(a," (").concat(r,")")},f=e=>{try{return(0,i.WU)(e,"yyyy-MM-dd")}catch(t){return console.error("Error converting local date to UTC:",t),(0,i.WU)(e,"yyyy-MM-dd")}}}}]);