"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6123],{7977:function(e,r,t){t.d(r,{Z:function(){return u}});var n=t(2265);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=a(e);return r.charAt(0).toUpperCase()+r.slice(1)},s=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),o=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,n.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:l,className:a="",children:i,iconNode:c,...u},f)=>(0,n.createElement)("svg",{ref:f,...d,width:r,height:r,stroke:e,strokeWidth:l?24*Number(t)/Number(r):t,className:s("lucide",a),...!i&&!o(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,r])=>(0,n.createElement)(e,r)),...Array.isArray(i)?i:[i]])),u=(e,r)=>{let t=(0,n.forwardRef)(({className:t,...a},o)=>(0,n.createElement)(c,{ref:o,iconNode:r,className:s(`lucide-${l(i(e))}`,`lucide-${e}`,t),...a}));return t.displayName=i(e),t}},3879:function(e,r,t){t.d(r,{Z:function(){return n}});let n=(0,t(7977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9910:function(e,r,t){t.d(r,{Z:function(){return n}});let n=(0,t(7977).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6123:function(e,r,t){t.d(r,{G:function(){return f}});var n=t(7437),l=t(5671),a=t(575),i=t(6260),s=t(4059),o=t(9910);let d=(0,t(7977).Z)("construction",[["rect",{x:"2",y:"6",width:"20",height:"8",rx:"1",key:"1estib"}],["path",{d:"M17 14v7",key:"7m2elx"}],["path",{d:"M7 14v7",key:"1cm7wv"}],["path",{d:"M17 3v3",key:"1v4jwn"}],["path",{d:"M7 3v3",key:"7o6guu"}],["path",{d:"M10 14 2.3 6.3",key:"1023jk"}],["path",{d:"m14 6 7.7 7.7",key:"1s8pl2"}],["path",{d:"m8 6 8 8",key:"hl96qh"}]]);var c=t(3879),u=t(8792);let f=e=>{let{title:r,description:t="This page is under development and will be available soon.",iconName:f="construction",backUrl:m="/dashboard",backLabel:p="Back to Dashboard"}=e,h=(e=>{switch(e){case"trophy":return i.Z;case"users":return s.Z;case"settings":return o.Z;default:return d}})(f);return(0,n.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,n.jsxs)(l.Zb,{className:"w-full max-w-md text-center",children:[(0,n.jsxs)(l.Ol,{children:[(0,n.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20",children:(0,n.jsx)(h,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"})}),(0,n.jsx)(l.ll,{className:"text-xl",children:r}),(0,n.jsx)(l.SZ,{className:"text-base",children:t})]}),(0,n.jsx)(l.aY,{children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,n.jsx)("p",{children:"Features coming soon:"}),(0,n.jsxs)("ul",{className:"mt-2 space-y-1 text-left",children:[(0,n.jsx)("li",{children:"• Data management interface"}),(0,n.jsx)("li",{children:"• CRUD operations"}),(0,n.jsx)("li",{children:"• Advanced filtering"}),(0,n.jsx)("li",{children:"• Export functionality"})]})]}),(0,n.jsx)(a.z,{asChild:!0,className:"w-full",children:(0,n.jsxs)(u.default,{href:m,children:[(0,n.jsx)(c.Z,{className:"mr-2 h-4 w-4"}),p]})})]})})]})})}},575:function(e,r,t){t.d(r,{d:function(){return o},z:function(){return d}});var n=t(7437),l=t(2265),a=t(9143),i=t(9769),s=t(2169);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,r)=>{let{className:t,variant:l,size:i,asChild:d=!1,...c}=e,u=d?a.g7:"button";return(0,n.jsx)(u,{className:(0,s.cn)(o({variant:l,size:i,className:t})),ref:r,...c})});d.displayName="Button"},5671:function(e,r,t){t.d(r,{Ol:function(){return s},SZ:function(){return d},Zb:function(){return i},aY:function(){return c},ll:function(){return o}});var n=t(7437),l=t(2265),a=t(2169);let i=l.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...l})});i.displayName="Card";let s=l.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...l})});s.displayName="CardHeader";let o=l.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("font-semibold leading-none tracking-tight",t),...l})});o.displayName="CardTitle";let d=l.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",t),...l})});d.displayName="CardDescription";let c=l.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",t),...l})});c.displayName="CardContent",l.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",t),...l})}).displayName="CardFooter"},2169:function(e,r,t){t.d(r,{cn:function(){return a}});var n=t(3167),l=t(1367);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,l.m6)((0,n.W)(r))}},1266:function(e,r,t){t.d(r,{F:function(){return a},e:function(){return i}});var n=t(2265);function l(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,n=e.map(e=>{let n=l(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():l(e[r],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},9143:function(e,r,t){t.d(r,{Z8:function(){return i},g7:function(){return s},sA:function(){return d}});var n=t(2265),l=t(1266),a=t(7437);function i(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...a}=e;if(n.isValidElement(t)){let e,i;let s=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,o=function(e,r){let t={...r};for(let n in r){let l=e[n],a=r[n];/^on[A-Z]/.test(n)?l&&a?t[n]=(...e)=>{let r=a(...e);return l(...e),r}:l&&(t[n]=l):"style"===n?t[n]={...l,...a}:"className"===n&&(t[n]=[l,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==n.Fragment&&(o.ref=r?(0,l.F)(r,s):s),n.cloneElement(t,o)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:l,...i}=e,s=n.Children.toArray(l),o=s.find(c);if(o){let e=o.props.children,l=s.map(r=>r!==o?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,a.jsx)(r,{...i,ref:t,children:l})});return t.displayName=`${e}.Slot`,t}var s=i("Slot"),o=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=o,r}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},9769:function(e,r,t){t.d(r,{j:function(){return i}});var n=t(3167);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.W,i=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:s}=r,o=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],n=null==s?void 0:s[e];if(null===r)return null;let a=l(r)||l(n);return i[e][a]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return a(e,o,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...l}=r;return Object.entries(l).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...s,...d}[r]):({...s,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}}]);