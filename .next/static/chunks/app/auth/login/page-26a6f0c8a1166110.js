(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6716],{8152:function(e,s,r){Promise.resolve().then(r.bind(r,7952))},7952:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return j}});var t=r(7437),a=r(2265),n=r(7907),i=r(2670),d=r(1270),o=r(124),l=r(1930),c=r(7841),m=r(8790),u=r(575),x=r(2782),h=r(2647),p=r(5671),f=r(7786),g=r(6146);let b=o.z.object({username:o.z.string().min(1,"Username is required"),password:o.z.string().min(1,"Password is required")}),y=e=>{let{onSuccess:s,redirectTo:r="/dashboard"}=e,[n,o]=(0,a.useState)(!1),{login:y,isLoginLoading:j,loginError:v}=(0,f.a)(),{register:w,handleSubmit:N,formState:{errors:S},reset:_}=(0,i.cI)({resolver:(0,d.F)(b)}),z=async e=>{try{await y(e),_(),s?s():window.location.href=r}catch(e){console.error("Login failed:",e)}};return(0,t.jsxs)(p.Zb,{className:"w-full max-w-md mx-auto",children:[(0,t.jsxs)(p.Ol,{className:"space-y-1",children:[(0,t.jsx)(p.ll,{className:"text-2xl font-bold text-center",children:"Sign In"}),(0,t.jsx)(p.SZ,{className:"text-center",children:"Enter your credentials to access the CMS"})]}),(0,t.jsxs)(p.aY,{children:[(0,t.jsxs)("form",{onSubmit:N(z),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h._,{htmlFor:"username",children:"Username"}),(0,t.jsx)(x.I,{id:"username",type:"text",placeholder:"Enter your username",...w("username"),className:S.username?"border-red-500":""}),S.username&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:S.username.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h._,{htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(x.I,{id:"password",type:n?"text":"password",placeholder:"Enter your password",...w("password"),className:S.password?"border-red-500 pr-10":"pr-10"}),(0,t.jsx)(u.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>o(!n),children:n?(0,t.jsx)(l.Z,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(c.Z,{className:"h-4 w-4 text-gray-400"})})]}),S.password&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:S.password.message})]}),v&&(0,t.jsx)("div",{className:"rounded-md bg-red-50 p-3",children:(0,t.jsx)("p",{className:"text-sm text-red-800",children:v instanceof Error?v.message:"Login failed. Please try again."})}),(0,t.jsx)(u.z,{type:"submit",className:"w-full",disabled:j,children:j?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.TK,{size:"sm",className:"mr-2"}),"Signing in..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.Z,{className:"mr-2 h-4 w-4"}),"Sign In"]})})]}),(0,t.jsx)("div",{className:"mt-4 text-center text-sm text-gray-600",children:(0,t.jsx)("p",{children:"System Administrator Access Only"})})]})]})};function j(){let e=(0,n.useRouter)(),{isAuthenticated:s,isLoading:r}=(0,f.a)();return((0,a.useEffect)(()=>{s&&!r&&e.push("/dashboard")},[s,r,e]),r)?(0,t.jsx)(g.SX,{message:"Checking authentication..."}):s?(0,t.jsx)(g.SX,{message:"Redirecting to dashboard..."}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"APISportsGame CMS"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Content Management System"})]}),(0,t.jsx)(y,{onSuccess:()=>e.push("/dashboard")}),(0,t.jsx)("div",{className:"text-center text-sm text-gray-500",children:(0,t.jsx)("p",{children:"\xa9 2025 APISportsGame. All rights reserved."})})]})})}},575:function(e,s,r){"use strict";r.d(s,{d:function(){return o},z:function(){return l}});var t=r(7437),a=r(2265),n=r(9143),i=r(9769),d=r(2169);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,s)=>{let{className:r,variant:a,size:i,asChild:l=!1,...c}=e,m=l?n.g7:"button";return(0,t.jsx)(m,{className:(0,d.cn)(o({variant:a,size:i,className:r})),ref:s,...c})});l.displayName="Button"},2782:function(e,s,r){"use strict";r.d(s,{I:function(){return i}});var t=r(7437),a=r(2265),n=r(2169);let i=a.forwardRef((e,s)=>{let{className:r,type:a,...i}=e;return(0,t.jsx)("input",{type:a,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:s,...i})});i.displayName="Input"},2647:function(e,s,r){"use strict";r.d(s,{_:function(){return l}});var t=r(7437),a=r(2265),n=r(4602),i=r(9769),d=r(2169);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(n.f,{ref:s,className:(0,d.cn)(o(),r),...a})});l.displayName=n.f.displayName}},function(e){e.O(0,[2150,3107,9101,1346,41,1011,6877,2971,8069,1744],function(){return e(e.s=8152)}),_N_E=e.O()}]);