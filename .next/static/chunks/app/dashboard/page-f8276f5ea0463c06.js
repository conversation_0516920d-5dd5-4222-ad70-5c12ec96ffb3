(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7702],{694:function(e,t,a){Promise.resolve().then(a.bind(a,7736))},7307:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},6490:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5462:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("radio",[["path",{d:"M4.9 19.1C1 15.2 1 8.8 4.9 4.9",key:"1vaf9d"}],["path",{d:"M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5",key:"u1ii0m"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5",key:"1j5fej"}],["path",{d:"M19.1 4.9C23 8.8 23 15.1 19.1 19",key:"10b0cb"}]])},7326:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},7736:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return y}});var s=a(7437),n=a(7786),r=a(1546),i=a(1346),l=a(5671),o=a(3277),c=a(7307),d=a(5462),u=a(6260),x=a(6490),g=a(7326),h=a(4059),f=a(2975),m=a(7011),p=a(8792);function y(){var e,t,a,y,v,j,b;let{user:N}=(0,n.a)(),{isAdmin:w,isEditor:k,isModerator:S}=(0,r.TE)(),{data:L}=(0,i.a)({queryKey:["fixtures","live-upcoming"],queryFn:()=>f.L.getUpcomingAndLive({limit:5}),refetchInterval:3e4}),{data:T}=(0,i.a)({queryKey:["fixtures","all"],queryFn:()=>f.L.getFixtures({limit:1})}),{data:A}=(0,i.a)({queryKey:["leagues","all"],queryFn:()=>m.A.getLeagues({limit:1})}),C=[{title:"Total Fixtures",value:(null==T?void 0:null===(t=T.meta)||void 0===t?void 0:null===(e=t.totalItems)||void 0===e?void 0:e.toLocaleString())||"Loading...",icon:c.Z,description:"Active fixtures in database",loading:!T},{title:"Live Matches",value:(null==L?void 0:null===(a=L.data)||void 0===a?void 0:a.filter(e=>["1H","2H","HT"].includes(e.status)).length)||"0",icon:d.Z,description:"Currently live matches",loading:!L},{title:"Active Leagues",value:(null==A?void 0:null===(v=A.meta)||void 0===v?void 0:null===(y=v.totalItems)||void 0===y?void 0:y.toLocaleString())||"Loading...",icon:u.Z,description:"Currently active leagues",loading:!A},{title:"Upcoming Today",value:(null==L?void 0:null===(j=L.data)||void 0===j?void 0:j.filter(e=>"NS"===e.status).length)||"0",icon:x.Z,description:"Matches scheduled today",loading:!L}];return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg border p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[(()=>{let e=new Date().getHours();return e<12?"Good morning":e<18?"Good afternoon":"Good evening"})(),", ",(null==N?void 0:N.fullName)||(null==N?void 0:N.username),"!"]}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Welcome to the APISportsGame Content Management System"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)(o.C,{className:"".concat((e=>{switch(e){case"admin":return"bg-red-100 text-red-800";case"editor":return"bg-blue-100 text-blue-800";case"moderator":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})((null==N?void 0:N.role)||"")," flex items-center space-x-1"),children:[(0,s.jsx)(g.Z,{className:"h-3 w-3"}),(0,s.jsx)("span",{className:"capitalize",children:null==N?void 0:N.role})]}),(0,s.jsxs)("div",{className:"text-right text-sm text-gray-500",children:[(0,s.jsx)("p",{children:"Last login"}),(0,s.jsx)("p",{className:"font-medium",children:(null==N?void 0:N.lastLoginAt)?new Date(N.lastLoginAt).toLocaleDateString():"First time"})]})]})]})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:C.map((e,t)=>{let a=e.icon;return(0,s.jsx)(l.Zb,{children:(0,s.jsx)(l.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,s.jsx)("p",{className:"text-2xl font-bold ".concat(e.loading?"animate-pulse text-gray-400":"text-gray-900"),children:e.value}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),(0,s.jsx)("div",{className:"bg-blue-100 p-3 rounded-full",children:(0,s.jsx)(a,{className:"h-6 w-6 text-blue-600"})})]})})},t)})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(l.Zb,{children:[(0,s.jsxs)(l.Ol,{children:[(0,s.jsxs)(l.ll,{className:"flex items-center",children:[(0,s.jsx)(x.Z,{className:"mr-2 h-5 w-5"}),"Recent Activities"]}),(0,s.jsx)(l.SZ,{children:"Latest system activities and updates"})]}),(0,s.jsx)(l.aY,{children:(0,s.jsx)("div",{className:"space-y-4",children:[{action:"Fixture sync completed",time:"2 minutes ago",type:"sync"},{action:"New broadcast link added",time:"15 minutes ago",type:"broadcast"},{action:"League updated: Premier League",time:"1 hour ago",type:"league"},{action:"User tier upgraded",time:"2 hours ago",type:"user"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.action}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.time})]})]},t))})})]}),(0,s.jsxs)(l.Zb,{children:[(0,s.jsxs)(l.Ol,{children:[(0,s.jsx)(l.ll,{children:"Quick Actions"}),(0,s.jsx)(l.SZ,{children:"Common tasks and shortcuts"})]}),(0,s.jsx)(l.aY,{children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(p.default,{href:"/dashboard/fixtures/live",className:"block",children:(0,s.jsx)("div",{className:"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(c.Z,{className:"h-5 w-5 text-blue-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"View Live Fixtures"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Check ongoing matches"})]})]}),(null==L?void 0:null===(b=L.data)||void 0===b?void 0:b.filter(e=>["1H","2H","HT"].includes(e.status)).length)>0&&(0,s.jsxs)(o.C,{className:"bg-green-100 text-green-800",children:[L.data.filter(e=>["1H","2H","HT"].includes(e.status)).length," Live"]})]})})}),k()&&(0,s.jsx)("button",{className:"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(d.Z,{className:"h-5 w-5 text-green-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Add Broadcast Link"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Add new streaming link"})]})]})}),w()&&(0,s.jsx)("button",{className:"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(h.Z,{className:"h-5 w-5 text-purple-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Manage Users"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"User administration"})]})]})})]})})]})]})]})}},3277:function(e,t,a){"use strict";a.d(t,{C:function(){return l}});var s=a(7437);a(2265);var n=a(9769),r=a(2169);let i=(0,n.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,...n}=e;return(0,s.jsx)("div",{className:(0,r.cn)(i({variant:a}),t),...n})}},2975:function(e,t,a){"use strict";a.d(t,{L:function(){return n}});var s=a(4921);let n={getFixtures:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/fixtures?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch fixtures: ".concat(a.statusText));return await a.json()},getFixtureById:async e=>{let t=await fetch("/api/fixtures/".concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture: ".concat(t.statusText));return await t.json()},getUpcomingAndLive:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/fixtures/live?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch live fixtures: ".concat(a.statusText));return await a.json()},getTeamSchedule:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,s]=e;void 0!==s&&a.append(t,s.toString())}),await s.x.get("/football/fixtures/schedules/".concat(e,"?").concat(a.toString()))},getFixtureStatistics:async e=>await s.x.get("/football/fixtures/statistics/".concat(e)),triggerSeasonSync:async()=>await s.x.get("/football/fixtures/sync/fixtures"),triggerDailySync:async()=>await s.x.get("/football/fixtures/sync/daily"),getSyncStatus:async()=>await s.x.get("/football/fixtures/sync/status"),createFixture:async e=>{let t=await fetch("/api/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create fixture: ".concat(t.statusText));return await t.json()},updateFixture:async(e,t)=>{let a=await fetch("/api/fixtures/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok)throw Error("Failed to update fixture: ".concat(a.statusText));return await a.json()},deleteFixture:async e=>{let t=(()=>{{try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t),s=null===(e=a.state)||void 0===e?void 0:e.accessToken;if(s)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",s.substring(0,20)+"..."),{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)}}}catch(e){console.warn("Failed to parse auth storage:",e)}let t=localStorage.getItem("accessToken");if(t)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}}return console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"}})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let a=await fetch("/api/fixtures/".concat(e),{method:"DELETE",headers:t});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",a.status,a.statusText,e),Error(e.message||"Failed to delete fixture: ".concat(a.statusText))}console.log("✅ Delete fixture successful:",e)},getFixture:async e=>(await n.getFixtureById(e)).data}},7011:function(e,t,a){"use strict";a.d(t,{A:function(){return n}});var s=a(4921);let n={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>await s.x.get("/football/leagues/".concat(e).concat(t?"?season=".concat(t):"")),createLeague:async e=>await s.x.post("/football/leagues",e),updateLeague:async(e,t)=>await s.x.patch("/football/leagues/".concat(e),t),getActiveLeagues:async()=>n.getLeagues({active:!0}),getLeaguesByCountry:async e=>n.getLeagues({country:e}),toggleLeagueStatus:async(e,t)=>n.updateLeague(e,{active:t})}},1546:function(e,t,a){"use strict";a.d(t,{TE:function(){return c},a1:function(){return o}});var s=a(7437),n=a(2265),r=a(7907),i=a(7786),l=a(6146);let o=e=>{let{children:t,requiredRole:a,fallbackUrl:o="/auth/login"}=e,c=(0,r.useRouter)(),{isAuthenticated:d,user:u,isLoading:x}=(0,i.a)();if((0,n.useEffect)(()=>{if(!x){if(!d||!u){c.push(o);return}if(a&&!(Array.isArray(a)?a:[a]).includes(u.role)){c.push("/dashboard?error=unauthorized");return}}},[d,u,x,a,c,o]),x)return(0,s.jsx)(l.SX,{message:"Verifying authentication..."});if(!d||!u)return(0,s.jsx)(l.SX,{message:"Redirecting to login..."});if(a){let e=Array.isArray(a)?a:[a];if(!e.includes(u.role))return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,s.jsx)(s.Fragment,{children:t})},c=()=>{let{user:e}=(0,i.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),a=()=>t("admin"),s=()=>t(["admin","editor"]),n=()=>t(["admin","editor","moderator"]);return{user:e,hasRole:t,isAdmin:a,isEditor:s,isModerator:n,canManageUsers:()=>a(),canManageContent:()=>s(),canModerate:()=>n(),canSync:()=>a()}}},9769:function(e,t,a){"use strict";a.d(t,{j:function(){return i}});var s=a(3167);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,r=s.W,i=(e,t)=>a=>{var s;if((null==t?void 0:t.variants)==null)return r(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==a?void 0:a[e],s=null==l?void 0:l[e];if(null===t)return null;let r=n(t)||n(s);return i[e][r]}),c=a&&Object.entries(a).reduce((e,t)=>{let[a,s]=t;return void 0===s||(e[a]=s),e},{});return r(e,o,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:a,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...l,...c}[t]):({...l,...c})[t]===a})?[...e,a,s]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}}},function(e){e.O(0,[2150,3107,9101,1346,41,9459,6877,2971,8069,1744],function(){return e(e.s=694)}),_N_E=e.O()}]);