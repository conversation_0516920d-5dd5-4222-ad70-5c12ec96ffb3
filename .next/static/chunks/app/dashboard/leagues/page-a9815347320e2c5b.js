(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2175],{5567:function(e,t,a){Promise.resolve().then(a.bind(a,1374))},9108:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},7805:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4960:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},8306:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},7841:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5404:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7451:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},94:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},8670:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9295:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},489:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},6260:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},1374:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return b}});var s=a(7437),r=a(2265),n=a(7907),i=a(5671),l=a(575),c=a(2782),o=a(3277),d=a(2632),u=a(1546),h=a(4915),m=a(6260),x=a(7841),f=a(9295),g=a(489),p=a(94);let y=(0,a(7977).Z)("calendar-days",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]]);var v=a(7451),j=a(5404),N=a(8670);function b(){var e;let t=(0,n.useRouter)(),{isEditor:a,isAdmin:b}=(0,u.TE)(),[w,k]=(0,r.useState)({page:1,limit:20}),[Z,L]=(0,r.useState)(""),{leagues:M,leaguesMeta:C,isLoading:A,error:z}=(0,h.sF)(w),S=e=>{L(e),k(t=>({...t,search:e||void 0,page:1}))},_=e=>{k(t=>({...t,country:e||void 0,page:1}))},q=e=>{k(t=>({...t,active:e,page:1}))};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:"Leagues Management"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage football leagues, seasons, and configurations"})]}),a()&&(0,s.jsxs)(l.z,{onClick:()=>t.push("/dashboard/leagues/create"),children:[(0,s.jsx)(p.Z,{className:"w-4 h-4 mr-2"}),"Add League"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ll,{className:"text-sm font-medium",children:"Total Leagues"}),(0,s.jsx)(m.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:(null==C?void 0:null===(e=C.totalItems)||void 0===e?void 0:e.toLocaleString())||"Loading..."}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Across all countries"})]})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ll,{className:"text-sm font-medium",children:"Active Leagues"}),(0,s.jsx)(y,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:(null==M?void 0:M.filter(e=>e.active).length)||0}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently running seasons"})]})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ll,{className:"text-sm font-medium",children:"Countries"}),(0,s.jsx)(v.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:new Set(null==M?void 0:M.map(e=>e.country)).size||0}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Unique countries represented"})]})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ll,{className:"text-sm font-medium",children:"Hot Leagues"}),(0,s.jsx)(m.Z,{className:"h-4 w-4 text-red-500"})]}),(0,s.jsxs)(i.aY,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:(null==M?void 0:M.filter(e=>e.isHot).length)||0}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Popular leagues"})]})]})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsx)(i.Ol,{children:(0,s.jsxs)(i.ll,{className:"flex items-center space-x-2",children:[(0,s.jsx)(j.Z,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Filters & Search"})]})}),(0,s.jsx)(i.aY,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(N.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,s.jsx)(c.I,{placeholder:"Search leagues...",value:Z,onChange:e=>S(e.target.value),className:"pl-10"})]}),(0,s.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 text-sm",onChange:e=>_(e.target.value),value:w.country||"",children:[(0,s.jsx)("option",{value:"",children:"All Countries"}),Array.from(new Set(null==M?void 0:M.map(e=>e.country))).sort().map(e=>(0,s.jsx)("option",{value:e,children:e},e))]}),(0,s.jsxs)("select",{className:"border border-gray-300 rounded-md px-3 py-2 text-sm",onChange:e=>q(""===e.target.value?void 0:"true"===e.target.value),value:void 0===w.active?"":w.active.toString(),children:[(0,s.jsx)("option",{value:"",children:"All Status"}),(0,s.jsx)("option",{value:"true",children:"Active Only"}),(0,s.jsx)("option",{value:"false",children:"Inactive Only"})]}),(0,s.jsx)(l.z,{variant:"outline",onClick:()=>{L(""),k({page:1,limit:20})},children:"Clear Filters"})]})})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsx)(i.Ol,{children:(0,s.jsx)(i.ll,{children:"Leagues List"})}),(0,s.jsx)(i.aY,{children:(0,s.jsx)(d.w,{columns:[{key:"logo",title:"",render:(e,t)=>(0,s.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:t.logo?(0,s.jsx)("img",{src:t.logo,alt:t.name,className:"w-6 h-6 object-contain",onError:e=>{e.target.style.display="none"}}):(0,s.jsx)(m.Z,{className:"w-5 h-5 text-gray-400"})})},{key:"name",title:"League Name",render:(e,t)=>(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"font-medium",children:t.name}),t.type&&(0,s.jsx)("div",{className:"text-sm text-gray-500 capitalize",children:t.type})]})},{key:"country",title:"Country",render:(e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[t.countryFlag&&(0,s.jsx)("img",{src:t.countryFlag,alt:t.country,className:"w-4 h-4"}),(0,s.jsx)("span",{children:t.country})]})},{key:"season_detail",title:"Season",render:(e,t)=>{let a=t.season_detail;return a?(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"text-sm font-medium",children:a.year}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[a.start," - ",a.end]}),a.current&&(0,s.jsx)(o.C,{variant:"secondary",className:"text-xs",children:"Current"})]}):(0,s.jsx)("span",{className:"text-gray-400",children:"-"})}},{key:"isHot",title:"Status",render:(e,t)=>(0,s.jsxs)("div",{className:"space-y-1",children:[t.isHot&&(0,s.jsx)(o.C,{variant:"destructive",className:"text-xs",children:"Hot"}),(0,s.jsx)(o.C,{variant:t.active?"default":"secondary",className:"text-xs",children:t.active?"Active":"Inactive"})]})},{key:"actions",title:"Actions",render:(e,r)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.z,{variant:"ghost",size:"sm",onClick:()=>t.push("/dashboard/leagues/".concat(r.externalId)),children:(0,s.jsx)(x.Z,{className:"w-4 h-4"})}),a()&&(0,s.jsx)(l.z,{variant:"ghost",size:"sm",onClick:()=>t.push("/dashboard/leagues/".concat(r.externalId,"/edit")),children:(0,s.jsx)(f.Z,{className:"w-4 h-4"})}),b()&&(0,s.jsx)(l.z,{variant:"ghost",size:"sm",className:"text-red-600 hover:text-red-700",onClick:()=>{console.log("Delete league:",r.externalId)},children:(0,s.jsx)(g.Z,{className:"w-4 h-4"})})]})}],data:M||[],loading:A,pagination:{page:(null==C?void 0:C.currentPage)||1,limit:(null==C?void 0:C.limit)||20,total:(null==C?void 0:C.totalItems)||0,onPageChange:e=>{k(t=>({...t,page:e}))},onLimitChange:e=>{k(t=>({...t,limit:e,page:1}))}}})})]})]})}},575:function(e,t,a){"use strict";a.d(t,{d:function(){return c},z:function(){return o}});var s=a(7437),r=a(2265),n=a(9143),i=a(9769),l=a(2169);let c=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,t)=>{let{className:a,variant:r,size:i,asChild:o=!1,...d}=e,u=o?n.g7:"button";return(0,s.jsx)(u,{className:(0,l.cn)(c({variant:r,size:i,className:a})),ref:t,...d})});o.displayName="Button"},2782:function(e,t,a){"use strict";a.d(t,{I:function(){return i}});var s=a(7437),r=a(2265),n=a(2169);let i=r.forwardRef((e,t)=>{let{className:a,type:r,...i}=e;return(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...i})});i.displayName="Input"},8641:function(e,t,a){"use strict";a.d(t,{Bw:function(){return f},Ph:function(){return d},Ql:function(){return g},i4:function(){return h},ki:function(){return u}});var s=a(7437),r=a(2265),n=a(8178),i=a(3441),l=a(5159),c=a(9259),o=a(2169);let d=n.fC;n.ZA;let u=n.B4,h=r.forwardRef((e,t)=>{let{className:a,children:r,...l}=e;return(0,s.jsxs)(n.xz,{ref:t,className:(0,o.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...l,children:[r,(0,s.jsx)(n.JO,{asChild:!0,children:(0,s.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});h.displayName=n.xz.displayName;let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.u_,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,s.jsx)(l.Z,{className:"h-4 w-4"})})});m.displayName=n.u_.displayName;let x=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.$G,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,s.jsx)(i.Z,{className:"h-4 w-4"})})});x.displayName=n.$G.displayName;let f=r.forwardRef((e,t)=>{let{className:a,children:r,position:i="popper",...l}=e;return(0,s.jsx)(n.h_,{children:(0,s.jsxs)(n.VY,{ref:t,className:(0,o.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...l,children:[(0,s.jsx)(m,{}),(0,s.jsx)(n.l_,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,s.jsx)(x,{})]})})});f.displayName=n.VY.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.__,{ref:t,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",a),...r})}).displayName=n.__.displayName;let g=r.forwardRef((e,t)=>{let{className:a,children:r,...i}=e;return(0,s.jsxs)(n.ck,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.wU,{children:(0,s.jsx)(c.Z,{className:"h-4 w-4"})})}),(0,s.jsx)(n.eT,{children:r})]})});g.displayName=n.ck.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.Z0,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...r})}).displayName=n.Z0.displayName},7011:function(e,t,a){"use strict";a.d(t,{A:function(){return r}});var s=a(4921);let r={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>await s.x.get("/football/leagues/".concat(e).concat(t?"?season=".concat(t):"")),createLeague:async e=>await s.x.post("/football/leagues",e),updateLeague:async(e,t)=>await s.x.patch("/football/leagues/".concat(e),t),getActiveLeagues:async()=>r.getLeagues({active:!0}),getLeaguesByCountry:async e=>r.getLeagues({country:e}),toggleLeagueStatus:async(e,t)=>r.updateLeague(e,{active:t})}},4915:function(e,t,a){"use strict";a.d(t,{HK:function(){return c},My:function(){return o},sF:function(){return l}});var s=a(1346),r=a(4095),n=a(8186),i=a(7011);let l=function(){var e,t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=(0,s.a)({queryKey:["leagues",a],queryFn:()=>i.A.getLeagues(a),staleTime:6e5});return{leagues:(null===(e=r.data)||void 0===e?void 0:e.data)||[],leaguesMeta:null===(t=r.data)||void 0===t?void 0:t.meta,isLoading:r.isLoading,error:r.error,refetch:r.refetch}},c=(e,t)=>{let a=(0,s.a)({queryKey:["leagues",e,t],queryFn:()=>i.A.getLeagueById(e,t),enabled:!!e,staleTime:6e5});return{league:a.data,isLoading:a.isLoading,error:a.error,refetch:a.refetch}},o=()=>{let e=(0,r.NL)(),t=(0,n.D)({mutationFn:e=>i.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),a=(0,n.D)({mutationFn:e=>{let{id:t,data:a}=e;return i.A.updateLeague(t,a)},onSuccess:(t,a)=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),s=(0,n.D)({mutationFn:e=>{let{id:t,active:a}=e;return i.A.toggleLeagueStatus(t,a)},onSuccess:t=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:t.mutate,isCreateLoading:t.isPending,createError:t.error,createData:t.data,updateLeague:a.mutate,isUpdateLoading:a.isPending,updateError:a.error,updateData:a.data,toggleStatus:s.mutate,isToggleLoading:s.isPending,toggleError:s.error,toggleData:s.data}}},1546:function(e,t,a){"use strict";a.d(t,{TE:function(){return o},a1:function(){return c}});var s=a(7437),r=a(2265),n=a(7907),i=a(7786),l=a(6146);let c=e=>{let{children:t,requiredRole:a,fallbackUrl:c="/auth/login"}=e,o=(0,n.useRouter)(),{isAuthenticated:d,user:u,isLoading:h}=(0,i.a)();if((0,r.useEffect)(()=>{if(!h){if(!d||!u){o.push(c);return}if(a&&!(Array.isArray(a)?a:[a]).includes(u.role)){o.push("/dashboard?error=unauthorized");return}}},[d,u,h,a,o,c]),h)return(0,s.jsx)(l.SX,{message:"Verifying authentication..."});if(!d||!u)return(0,s.jsx)(l.SX,{message:"Redirecting to login..."});if(a){let e=Array.isArray(a)?a:[a];if(!e.includes(u.role))return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,s.jsx)(s.Fragment,{children:t})},o=()=>{let{user:e}=(0,i.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),a=()=>t("admin"),s=()=>t(["admin","editor"]),r=()=>t(["admin","editor","moderator"]);return{user:e,hasRole:t,isAdmin:a,isEditor:s,isModerator:r,canManageUsers:()=>a(),canManageContent:()=>s(),canModerate:()=>r(),canSync:()=>a()}}}},function(e){e.O(0,[2150,3107,9101,1346,1610,2608,41,1766,6877,1380,2971,8069,1744],function(){return e(e.s=5567)}),_N_E=e.O()}]);