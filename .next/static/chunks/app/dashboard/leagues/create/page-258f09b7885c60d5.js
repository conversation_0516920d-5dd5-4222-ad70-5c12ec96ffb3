(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5871],{1923:function(e,t,a){Promise.resolve().then(a.bind(a,3662))},3879:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},699:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},6260:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});let s=(0,a(7977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},7907:function(e,t,a){"use strict";var s=a(5313);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}})},3662:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return p}});var s=a(7437),i=a(2265),r=a(7907),n=a(5671),o=a(575),u=a(6803),l=a(4915),c=a(3879),h=a(6260),d=a(699),g=a(6288);function p(){let e=(0,r.useRouter)(),[t,a]=(0,i.useState)({name:"",country:"",type:"",active:!0,isHot:!1,logo:"",externalId:void 0}),[p,m]=(0,i.useState)({}),{createLeague:v,isCreateLoading:y}=(0,l.My)(),f=(e,t)=>{a(a=>({...a,[e]:t})),("name"===e||"country"===e||"type"===e||"logo"===e||"externalId"===e)&&m(t=>({...t,[e]:void 0}))},b=()=>{let e={};return t.name.trim()||(e.name="League name is required"),t.country.trim()||(e.country="Country is required"),t.type.trim()||(e.type="League type is required"),t.externalId&&(t.externalId<1||!Number.isInteger(t.externalId))&&(e.externalId="External ID must be a positive integer"),m(e),0===Object.keys(e).length};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>e.back(),children:[(0,s.jsx)(c.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:"Create League"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Add a new football league to the system"})]})]}),(0,s.jsx)("form",{onSubmit:a=>{if(a.preventDefault(),!b()){g.toast.error("Please fix the form errors");return}v({name:t.name.trim(),country:t.country.trim(),type:t.type.trim(),active:t.active,isHot:t.isHot,...t.logo&&{logo:t.logo.trim()},...t.externalId&&{externalId:t.externalId}},{onSuccess:t=>{g.toast.success("League created successfully"),e.push("/dashboard/leagues/".concat(t.externalId))},onError:e=>{g.toast.error(e.message||"Failed to create league")}})},children:(0,s.jsxs)(n.Zb,{children:[(0,s.jsxs)(n.Ol,{children:[(0,s.jsxs)(n.ll,{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.Z,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"League Information"})]}),(0,s.jsx)(n.SZ,{children:"Enter the league details and configuration settings."})]}),(0,s.jsxs)(n.aY,{className:"space-y-6",children:[(0,s.jsx)(u.hj,{title:"Basic Information",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(u.UP,{label:"League Name",placeholder:"e.g., Premier League, Champions League",required:!0,value:t.name,onChange:e=>f("name",e.target.value),error:p.name}),(0,s.jsx)(u.UP,{label:"Country",placeholder:"e.g., England, Spain, International",required:!0,value:t.country,onChange:e=>f("country",e.target.value),error:p.country}),(0,s.jsx)(u.mg,{label:"League Type",placeholder:"Select league type",required:!0,value:t.type,onValueChange:e=>f("type",e),options:[{value:"league",label:"League"},{value:"cup",label:"Cup"},{value:"playoffs",label:"Playoffs"},{value:"friendly",label:"Friendly"},{value:"qualification",label:"Qualification"}],error:p.type}),(0,s.jsx)(u.UP,{label:"External ID (Optional)",placeholder:"Enter external API ID",type:"number",value:t.externalId||"",onChange:e=>f("externalId",e.target.value?parseInt(e.target.value):void 0),error:p.externalId}),(0,s.jsx)(u.UP,{label:"Logo URL (Optional)",placeholder:"https://example.com/logo.png",value:t.logo,onChange:e=>f("logo",e.target.value)})]})}),(0,s.jsx)(u.hj,{title:"Settings",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(u.mg,{label:"Initial Status",value:t.active.toString(),onValueChange:e=>f("active","true"===e),options:[{value:"true",label:"Active"},{value:"false",label:"Inactive"}]}),(0,s.jsx)(u.mg,{label:"Hot League",description:"Mark as hot/popular league for prominence",value:t.isHot.toString(),onValueChange:e=>f("isHot","true"===e),options:[{value:"false",label:"Normal League"},{value:"true",label:"Hot League"}]})]})}),t.logo&&(0,s.jsx)(u.hj,{title:"Logo Preview",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)("img",{src:t.logo,alt:"Logo preview",className:"w-16 h-16 object-contain",onError:e=>{let t=e.target;t.src="",t.style.display="none",g.toast.error("Failed to load logo image")}}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Logo Preview"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 break-all",children:t.logo})]})]})}),(0,s.jsxs)(u.iN,{children:[(0,s.jsx)(o.z,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"}),(0,s.jsxs)(o.z,{type:"submit",disabled:y,children:[(0,s.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),y?"Creating...":"Create League"]})]})]})]})})]})}},4921:function(e,t,a){"use strict";a.d(t,{x:function(){return r}});var s=a(3107);class i{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async patch(e,t,a){return(await this.client.patch(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.baseURL="http://localhost:3000",this.client=s.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with baseURL:",this.baseURL)}}let r=new i},7011:function(e,t,a){"use strict";a.d(t,{A:function(){return i}});var s=a(4921);let i={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>await s.x.get("/football/leagues/".concat(e).concat(t?"?season=".concat(t):"")),createLeague:async e=>await s.x.post("/football/leagues",e),updateLeague:async(e,t)=>await s.x.patch("/football/leagues/".concat(e),t),getActiveLeagues:async()=>i.getLeagues({active:!0}),getLeaguesByCountry:async e=>i.getLeagues({country:e}),toggleLeagueStatus:async(e,t)=>i.updateLeague(e,{active:t})}},4915:function(e,t,a){"use strict";a.d(t,{HK:function(){return u},My:function(){return l},sF:function(){return o}});var s=a(1346),i=a(4095),r=a(8186),n=a(7011);let o=function(){var e,t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=(0,s.a)({queryKey:["leagues",a],queryFn:()=>n.A.getLeagues(a),staleTime:6e5});return{leagues:(null===(e=i.data)||void 0===e?void 0:e.data)||[],leaguesMeta:null===(t=i.data)||void 0===t?void 0:t.meta,isLoading:i.isLoading,error:i.error,refetch:i.refetch}},u=(e,t)=>{let a=(0,s.a)({queryKey:["leagues",e,t],queryFn:()=>n.A.getLeagueById(e,t),enabled:!!e,staleTime:6e5});return{league:a.data,isLoading:a.isLoading,error:a.error,refetch:a.refetch}},l=()=>{let e=(0,i.NL)(),t=(0,r.D)({mutationFn:e=>n.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),a=(0,r.D)({mutationFn:e=>{let{id:t,data:a}=e;return n.A.updateLeague(t,a)},onSuccess:(t,a)=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),s=(0,r.D)({mutationFn:e=>{let{id:t,active:a}=e;return n.A.toggleLeagueStatus(t,a)},onSuccess:t=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:t.mutate,isCreateLoading:t.isPending,createError:t.error,createData:t.data,updateLeague:a.mutate,isUpdateLoading:a.isPending,updateError:a.error,updateData:a.data,toggleStatus:s.mutate,isToggleLoading:s.isPending,toggleError:s.error,toggleData:s.data}}},5899:function(e,t,a){"use strict";a.d(t,{_:function(){return s}});let s=console},4654:function(e,t,a){"use strict";a.d(t,{R:function(){return u},m:function(){return o}});var s=a(5899),i=a(9522),r=a(3864),n=a(4500);class o extends r.F{constructor(e){super(),this.defaultOptions=e.defaultOptions,this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.logger=e.logger||s._,this.observers=[],this.state=e.state||u(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options={...this.defaultOptions,...e},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(e){this.dispatch({type:"setState",state:e})}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.observers=this.observers.filter(t=>t!==e),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.observers.length||("loading"===this.state.status?this.scheduleGc():this.mutationCache.remove(this))}continue(){var e,t;return null!=(e=null==(t=this.retryer)?void 0:t.continue())?e:this.execute()}async execute(){var e,t,a,s,i,r,o,u,l,c,h,d,g,p,m,v,y,f,b,x;let C="loading"===this.state.status;try{if(!C){this.dispatch({type:"loading",variables:this.options.variables}),await (null==(l=(c=this.mutationCache.config).onMutate)?void 0:l.call(c,this.state.variables,this));let e=await (null==(h=(d=this.options).onMutate)?void 0:h.call(d,this.state.variables));e!==this.state.context&&this.dispatch({type:"loading",context:e,variables:this.state.variables})}let g=await (()=>{var e;return this.retryer=(0,n.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(e,t)=>{this.dispatch({type:"failed",failureCount:e,error:t})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise})();return await (null==(e=(t=this.mutationCache.config).onSuccess)?void 0:e.call(t,g,this.state.variables,this.state.context,this)),await (null==(a=(s=this.options).onSuccess)?void 0:a.call(s,g,this.state.variables,this.state.context)),await (null==(i=(r=this.mutationCache.config).onSettled)?void 0:i.call(r,g,null,this.state.variables,this.state.context,this)),await (null==(o=(u=this.options).onSettled)?void 0:o.call(u,g,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:g}),g}catch(e){try{throw await (null==(g=(p=this.mutationCache.config).onError)?void 0:g.call(p,e,this.state.variables,this.state.context,this)),await (null==(m=(v=this.options).onError)?void 0:m.call(v,e,this.state.variables,this.state.context)),await (null==(y=(f=this.mutationCache.config).onSettled)?void 0:y.call(f,void 0,e,this.state.variables,this.state.context,this)),await (null==(b=(x=this.options).onSettled)?void 0:b.call(x,void 0,e,this.state.variables,this.state.context)),e}finally{this.dispatch({type:"error",error:e})}}}dispatch(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"loading":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,n.Kw)(this.options.networkMode),status:"loading",variables:e.variables};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"};case"setState":return{...t,...e.state}}})(this.state),i.V.batch(()=>{this.observers.forEach(t=>{t.onMutationUpdate(e)}),this.mutationCache.notify({mutation:this,type:"updated",action:e})})}}function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0}}},3864:function(e,t,a){"use strict";a.d(t,{F:function(){return i}});var s=a(1678);class i{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,s.PN)(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(e){this.cacheTime=Math.max(this.cacheTime||0,null!=e?e:s.sk?1/0:3e5)}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}},8186:function(e,t,a){"use strict";a.d(t,{D:function(){return d}});var s=a(2265),i=a(1678),r=a(4654),n=a(9522),o=a(6761);class u extends o.l{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;let a=this.options;this.options=this.client.defaultMutationOptions(e),(0,i.VS)(a,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,r.R)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){n.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var t,a,s,i,r,n,o,u;e.onSuccess?(null==(t=(a=this.mutateOptions).onSuccess)||t.call(a,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(s=(i=this.mutateOptions).onSettled)||s.call(i,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(r=(n=this.mutateOptions).onError)||r.call(n,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(o=(u=this.mutateOptions).onSettled)||o.call(u,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}var l=a(7536),c=a(4095),h=a(3439);function d(e,t,a){let r=(0,i.lV)(e,t,a),o=(0,c.NL)({context:r.context}),[d]=s.useState(()=>new u(o,r));s.useEffect(()=>{d.setOptions(r)},[d,r]);let p=(0,l.$)(s.useCallback(e=>d.subscribe(n.V.batchCalls(e)),[d]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),m=s.useCallback((e,t)=>{d.mutate(e,t).catch(g)},[d]);if(p.error&&(0,h.L)(d.options.useErrorBoundary,[p.error]))throw p.error;return{...p,mutate:m,mutateAsync:p.mutate}}function g(){}}},function(e){e.O(0,[2150,3107,9101,1346,1610,2608,1766,6288,1637,8140,2971,8069,1744],function(){return e(e.s=1923)}),_N_E=e.O()}]);