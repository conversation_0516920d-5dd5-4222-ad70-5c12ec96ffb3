(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2785],{1859:function(e,t,s){Promise.resolve().then(s.bind(s,4168))},3879:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7307:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},6490:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7451:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},9295:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},6260:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},7404:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},4168:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return y}});var a=s(7437),n=s(7907),r=s(5671),i=s(575),l=s(3277),c=s(7625),d=s(1546),o=s(4915),u=s(3879),m=s(6260),x=s(7451),g=s(9295),h=s(7404),f=s(7307),p=s(6490);function y(){var e;let t=(0,n.useParams)(),s=(0,n.useRouter)(),{isEditor:y}=(0,d.TE)(),j=parseInt(t.id),{league:v,isLoading:N,error:b}=(0,o.HK)(j);return N?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(c.Od,{className:"h-10 w-20"}),(0,a.jsx)(c.Od,{className:"h-8 w-48"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,a.jsx)(c.Od,{className:"h-96"})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(c.Od,{className:"h-64"}),(0,a.jsx)(c.Od,{className:"h-48"})]})]})]}):b||!v?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[(0,a.jsx)(u.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),(0,a.jsx)(r.Zb,{children:(0,a.jsx)(r.aY,{className:"flex items-center justify-center h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"League not found"}),(0,a.jsx)("p",{className:"text-gray-500",children:"The league you're looking for doesn't exist or you don't have permission to view it."})]})})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(i.z,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[(0,a.jsx)(u.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[v.logo?(0,a.jsx)("img",{src:v.logo,alt:v.name,className:"w-12 h-12 object-contain",onError:e=>{e.target.style.display="none"}}):(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(m.Z,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:v.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:v.country}),v.type&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:"•"}),(0,a.jsx)("span",{className:"capitalize",children:v.type})]})]})]})]})]}),y()&&(0,a.jsxs)(i.z,{onClick:()=>s.push("/dashboard/leagues/".concat(j,"/edit")),children:[(0,a.jsx)(g.Z,{className:"w-4 h-4 mr-2"}),"Edit League"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.C,{variant:v.active?"default":"secondary",children:v.active?"Active":"Inactive"}),v.isHot&&(0,a.jsxs)(l.C,{variant:"destructive",children:[(0,a.jsx)(h.Z,{className:"w-3 h-3 mr-1"}),"Hot League"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"League Information"})]})}),(0,a.jsx)(r.aY,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"League Name"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:v.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Country"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[v.countryFlag&&(0,a.jsx)("img",{src:v.countryFlag,alt:v.country,className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-lg font-medium",children:v.country})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"League Type"}),(0,a.jsx)("p",{className:"text-lg font-medium capitalize",children:v.type||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"External ID"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:v.externalId})]})]})})]}),v.season_detail&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsxs)(r.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Season Details"})]})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Season Year"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:v.season_detail.year})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Start Date"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:v.season_detail.start})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"End Date"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:v.season_detail.end})]})]}),v.season_detail.current&&(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)(l.C,{variant:"secondary",children:[(0,a.jsx)(p.Z,{className:"w-3 h-3 mr-1"}),"Current Season"]})}),v.season_detail.coverage&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Coverage Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat((null===(e=v.season_detail.coverage.fixtures)||void 0===e?void 0:e.events)?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{children:"Fixtures"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(v.season_detail.coverage.standings?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{children:"Standings"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(v.season_detail.coverage.players?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{children:"Players"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(v.season_detail.coverage.top_scorers?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{children:"Top Scorers"})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{className:"text-lg",children:"Quick Stats"})}),(0,a.jsxs)(r.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Status"}),(0,a.jsx)(l.C,{variant:v.active?"default":"secondary",children:v.active?"Active":"Inactive"})]}),v.isHot&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Popularity"}),(0,a.jsx)(l.C,{variant:"destructive",children:"Hot"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"League ID"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:["#",v.externalId]})]}),v.season_detail&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Current Season"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:v.season_detail.year})]})]})]}),v.logo&&(0,a.jsxs)(r.Zb,{children:[(0,a.jsx)(r.Ol,{children:(0,a.jsx)(r.ll,{className:"text-lg",children:"League Logo"})}),(0,a.jsx)(r.aY,{children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("img",{src:v.logo,alt:v.name,className:"w-32 h-32 object-contain"})})})]})]})]})]})}},3277:function(e,t,s){"use strict";s.d(t,{C:function(){return l}});var a=s(7437);s(2265);var n=s(9769),r=s(2169);let i=(0,n.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...n}=e;return(0,a.jsx)("div",{className:(0,r.cn)(i({variant:s}),t),...n})}},575:function(e,t,s){"use strict";s.d(t,{d:function(){return c},z:function(){return d}});var a=s(7437),n=s(2265),r=s(9143),i=s(9769),l=s(2169);let c=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:s,variant:n,size:i,asChild:d=!1,...o}=e,u=d?r.g7:"button";return(0,a.jsx)(u,{className:(0,l.cn)(c({variant:n,size:i,className:s})),ref:t,...o})});d.displayName="Button"},7011:function(e,t,s){"use strict";s.d(t,{A:function(){return n}});var a=s(4921);let n={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch leagues");return await s.json()},getLeagueById:async(e,t)=>await a.x.get("/football/leagues/".concat(e).concat(t?"?season=".concat(t):"")),createLeague:async e=>await a.x.post("/football/leagues",e),updateLeague:async(e,t)=>await a.x.patch("/football/leagues/".concat(e),t),getActiveLeagues:async()=>n.getLeagues({active:!0}),getLeaguesByCountry:async e=>n.getLeagues({country:e}),toggleLeagueStatus:async(e,t)=>n.updateLeague(e,{active:t})}},4915:function(e,t,s){"use strict";s.d(t,{HK:function(){return c},My:function(){return d},sF:function(){return l}});var a=s(1346),n=s(4095),r=s(8186),i=s(7011);let l=function(){var e,t;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,a.a)({queryKey:["leagues",s],queryFn:()=>i.A.getLeagues(s),staleTime:6e5});return{leagues:(null===(e=n.data)||void 0===e?void 0:e.data)||[],leaguesMeta:null===(t=n.data)||void 0===t?void 0:t.meta,isLoading:n.isLoading,error:n.error,refetch:n.refetch}},c=(e,t)=>{let s=(0,a.a)({queryKey:["leagues",e,t],queryFn:()=>i.A.getLeagueById(e,t),enabled:!!e,staleTime:6e5});return{league:s.data,isLoading:s.isLoading,error:s.error,refetch:s.refetch}},d=()=>{let e=(0,n.NL)(),t=(0,r.D)({mutationFn:e=>i.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),s=(0,r.D)({mutationFn:e=>{let{id:t,data:s}=e;return i.A.updateLeague(t,s)},onSuccess:(t,s)=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),a=(0,r.D)({mutationFn:e=>{let{id:t,active:s}=e;return i.A.toggleLeagueStatus(t,s)},onSuccess:t=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:t.mutate,isCreateLoading:t.isPending,createError:t.error,createData:t.data,updateLeague:s.mutate,isUpdateLoading:s.isPending,updateError:s.error,updateData:s.data,toggleStatus:a.mutate,isToggleLoading:a.isPending,toggleError:a.error,toggleData:a.data}}},1546:function(e,t,s){"use strict";s.d(t,{TE:function(){return d},a1:function(){return c}});var a=s(7437),n=s(2265),r=s(7907),i=s(7786),l=s(6146);let c=e=>{let{children:t,requiredRole:s,fallbackUrl:c="/auth/login"}=e,d=(0,r.useRouter)(),{isAuthenticated:o,user:u,isLoading:m}=(0,i.a)();if((0,n.useEffect)(()=>{if(!m){if(!o||!u){d.push(c);return}if(s&&!(Array.isArray(s)?s:[s]).includes(u.role)){d.push("/dashboard?error=unauthorized");return}}},[o,u,m,s,d,c]),m)return(0,a.jsx)(l.SX,{message:"Verifying authentication..."});if(!o||!u)return(0,a.jsx)(l.SX,{message:"Redirecting to login..."});if(s){let e=Array.isArray(s)?s:[s];if(!e.includes(u.role))return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",u.role]})]})})}return(0,a.jsx)(a.Fragment,{children:t})},d=()=>{let{user:e}=(0,i.a)(),t=t=>!!e&&(Array.isArray(t)?t:[t]).includes(e.role),s=()=>t("admin"),a=()=>t(["admin","editor"]),n=()=>t(["admin","editor","moderator"]);return{user:e,hasRole:t,isAdmin:s,isEditor:a,isModerator:n,canManageUsers:()=>s(),canManageContent:()=>a(),canModerate:()=>n(),canSync:()=>s()}}},1266:function(e,t,s){"use strict";s.d(t,{F:function(){return r},e:function(){return i}});var a=s(2265);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function r(...e){return t=>{let s=!1,a=e.map(e=>{let a=n(e,t);return s||"function"!=typeof a||(s=!0),a});if(s)return()=>{for(let t=0;t<a.length;t++){let s=a[t];"function"==typeof s?s():n(e[t],null)}}}}function i(...e){return a.useCallback(r(...e),e)}},9143:function(e,t,s){"use strict";s.d(t,{Z8:function(){return i},g7:function(){return l},sA:function(){return d}});var a=s(2265),n=s(1266),r=s(7437);function i(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:s,...r}=e;if(a.isValidElement(s)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?s.ref:(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?s.props.ref:s.props.ref||s.ref,c=function(e,t){let s={...t};for(let a in t){let n=e[a],r=t[a];/^on[A-Z]/.test(a)?n&&r?s[a]=(...e)=>{let t=r(...e);return n(...e),t}:n&&(s[a]=n):"style"===a?s[a]={...n,...r}:"className"===a&&(s[a]=[n,r].filter(Boolean).join(" "))}return{...e,...s}}(r,s.props);return s.type!==a.Fragment&&(c.ref=t?(0,n.F)(t,l):l),a.cloneElement(s,c)}return a.Children.count(s)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=a.forwardRef((e,s)=>{let{children:n,...i}=e,l=a.Children.toArray(n),c=l.find(o);if(c){let e=c.props.children,n=l.map(t=>t!==c?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,r.jsx)(t,{...i,ref:s,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,r.jsx)(t,{...i,ref:s,children:n})});return s.displayName=`${e}.Slot`,s}var l=i("Slot"),c=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,r.jsx)(r.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=c,t}function o(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}},9769:function(e,t,s){"use strict";s.d(t,{j:function(){return i}});var a=s(3167);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,r=a.W,i=(e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return r(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:i,defaultVariants:l}=t,c=Object.keys(i).map(e=>{let t=null==s?void 0:s[e],a=null==l?void 0:l[e];if(null===t)return null;let r=n(t)||n(a);return i[e][r]}),d=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return r(e,c,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:s,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...l,...d}[t]):({...l,...d})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}}},function(e){e.O(0,[2150,3107,9101,1346,41,6877,2971,8069,1744],function(){return e(e.s=1859)}),_N_E=e.O()}]);