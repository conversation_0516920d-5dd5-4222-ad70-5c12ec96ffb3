(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5026],{6353:function(e,t,s){Promise.resolve().then(s.bind(s,6109))},3879:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},699:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},6260:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(7977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},7907:function(e,t,s){"use strict";var a=s(5313);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},6109:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return v}});var a=s(7437),i=s(2265),r=s(7907),n=s(4095),o=s(5671),l=s(575),u=s(7625),c=s(6803),h=s(4915),d=s(3879),m=s(6260),g=s(699),p=s(6288);function v(){let e=(0,r.useParams)(),t=(0,r.useRouter)(),s=(0,n.NL)(),v=parseInt(e.id),[y,f]=(0,i.useState)({name:"",country:"",type:"",active:!0,isHot:!1,logo:""}),[x,b]=(0,i.useState)({}),{league:j,isLoading:N}=(0,h.HK)(v),{updateLeague:w,isUpdateLoading:L}=(0,h.My)();(0,i.useEffect)(()=>{if(j){var e,t;f({name:j.name||"",country:j.country||"",type:j.type||"",active:null===(e=j.active)||void 0===e||e,isHot:null!==(t=j.isHot)&&void 0!==t&&t,logo:j.logo||""})}},[j]);let C=(e,t)=>{f(s=>({...s,[e]:t})),x[e]&&b(t=>({...t,[e]:void 0}))},k=()=>{let e={};return y.name.trim()||(e.name="League name is required"),y.country.trim()||(e.country="Country is required"),y.type.trim()||(e.type="League type is required"),b(e),0===Object.keys(e).length};return N?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(u.Od,{className:"h-10 w-20"}),(0,a.jsx)(u.Od,{className:"h-8 w-48"})]}),(0,a.jsxs)(o.Zb,{children:[(0,a.jsxs)(o.Ol,{children:[(0,a.jsx)(u.Od,{className:"h-6 w-32"}),(0,a.jsx)(u.Od,{className:"h-4 w-64"})]}),(0,a.jsx)(o.aY,{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(u.Od,{className:"h-20"}),(0,a.jsx)(u.Od,{className:"h-20"}),(0,a.jsx)(u.Od,{className:"h-20"}),(0,a.jsx)(u.Od,{className:"h-20"})]})})]})]}):j?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,a.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:"Edit League"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Update league information and settings"})]})]}),(0,a.jsx)("form",{onSubmit:e=>{if(e.preventDefault(),!k()){p.toast.error("Please fix the form errors");return}w({id:v,data:{name:y.name.trim(),country:y.country.trim(),type:y.type.trim(),active:y.active,isHot:y.isHot,...y.logo&&{logo:y.logo.trim()}}},{onSuccess:()=>{s.invalidateQueries({queryKey:["leagues",v]}),s.invalidateQueries({queryKey:["leagues"]}),p.toast.success("League updated successfully"),t.push("/dashboard/leagues/".concat(v))},onError:e=>{p.toast.error(e.message||"Failed to update league")}})},children:(0,a.jsxs)(o.Zb,{children:[(0,a.jsxs)(o.Ol,{children:[(0,a.jsxs)(o.ll,{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"League Information"})]}),(0,a.jsx)(o.SZ,{children:"Edit the league details and configuration settings."})]}),(0,a.jsxs)(o.aY,{className:"space-y-6",children:[(0,a.jsx)(c.hj,{title:"Basic Information",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(c.UP,{label:"League Name",placeholder:"Enter league name",required:!0,value:y.name,onChange:e=>C("name",e.target.value),error:x.name}),(0,a.jsx)(c.UP,{label:"Country",placeholder:"Enter country",required:!0,value:y.country,onChange:e=>C("country",e.target.value),error:x.country}),(0,a.jsx)(c.mg,{label:"League Type",placeholder:"Select league type",required:!0,value:y.type,onValueChange:e=>C("type",e),options:[{value:"league",label:"League"},{value:"cup",label:"Cup"},{value:"playoffs",label:"Playoffs"},{value:"friendly",label:"Friendly"},{value:"qualification",label:"Qualification"}],error:x.type}),(0,a.jsx)(c.UP,{label:"Logo URL",placeholder:"Enter logo URL (optional)",value:y.logo,onChange:e=>C("logo",e.target.value)})]})}),(0,a.jsx)(c.hj,{title:"Settings",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(c.mg,{label:"Status",value:y.active.toString(),onValueChange:e=>C("active","true"===e),options:[{value:"true",label:"Active"},{value:"false",label:"Inactive"}]}),(0,a.jsx)(c.mg,{label:"Hot League",description:"Mark as hot/popular league",value:y.isHot.toString(),onValueChange:e=>C("isHot","true"===e),options:[{value:"false",label:"Normal"},{value:"true",label:"Hot League"}]})]})}),y.logo&&(0,a.jsx)(c.hj,{title:"Logo Preview",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("img",{src:y.logo,alt:"Logo preview",className:"w-16 h-16 object-contain",onError:e=>{let t=e.target;t.src="",t.style.display="none"}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Logo Preview"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:y.logo})]})]})}),(0,a.jsxs)(c.iN,{children:[(0,a.jsx)(l.z,{type:"button",variant:"outline",onClick:()=>t.back(),children:"Cancel"}),(0,a.jsxs)(l.z,{type:"submit",disabled:L,children:[(0,a.jsx)(g.Z,{className:"w-4 h-4 mr-2"}),L?"Updating...":"Update League"]})]})]})]})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>t.back(),children:[(0,a.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),"Back"]})}),(0,a.jsx)(o.Zb,{children:(0,a.jsx)(o.aY,{className:"flex items-center justify-center h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"League not found"}),(0,a.jsx)("p",{className:"text-gray-500",children:"The league you're trying to edit doesn't exist or you don't have permission to edit it."})]})})})]})}},7625:function(e,t,s){"use strict";s.d(t,{Od:function(){return r},hM:function(){return o},q4:function(){return n}});var a=s(7437),i=s(2169);function r(e){let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",t),...s})}let n=e=>{let{className:t}=e;return(0,a.jsxs)("div",{className:(0,i.cn)("border rounded-lg p-6 space-y-4",t),children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(r,{className:"h-4 w-3/4"}),(0,a.jsx)(r,{className:"h-4 w-1/2"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(r,{className:"h-3 w-full"}),(0,a.jsx)(r,{className:"h-3 w-full"}),(0,a.jsx)(r,{className:"h-3 w-2/3"})]})]})},o=e=>{let{rows:t=5,columns:s=4,className:n}=e;return(0,a.jsx)("div",{className:(0,i.cn)("space-y-4",n),children:(0,a.jsxs)("div",{className:"border rounded-lg",children:[(0,a.jsx)("div",{className:"border-b p-4",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(s,", 1fr)")},children:Array.from({length:s}).map((e,t)=>(0,a.jsx)(r,{className:"h-4 w-20"},t))})}),Array.from({length:t}).map((e,t)=>(0,a.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(s,", 1fr)")},children:Array.from({length:s}).map((e,t)=>(0,a.jsx)(r,{className:"h-4 w-full"},t))})},t))]})})}},4921:function(e,t,s){"use strict";s.d(t,{x:function(){return r}});var a=s(3107);class i{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let s=JSON.parse(t);return(null===(e=s.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.baseURL="http://localhost:3000",this.client=a.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with baseURL:",this.baseURL)}}let r=new i},7011:function(e,t,s){"use strict";s.d(t,{A:function(){return i}});var a=s(4921);let i={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch leagues");return await s.json()},getLeagueById:async(e,t)=>await a.x.get("/football/leagues/".concat(e).concat(t?"?season=".concat(t):"")),createLeague:async e=>await a.x.post("/football/leagues",e),updateLeague:async(e,t)=>await a.x.patch("/football/leagues/".concat(e),t),getActiveLeagues:async()=>i.getLeagues({active:!0}),getLeaguesByCountry:async e=>i.getLeagues({country:e}),toggleLeagueStatus:async(e,t)=>i.updateLeague(e,{active:t})}},4915:function(e,t,s){"use strict";s.d(t,{HK:function(){return l},My:function(){return u},sF:function(){return o}});var a=s(1346),i=s(4095),r=s(8186),n=s(7011);let o=function(){var e,t;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=(0,a.a)({queryKey:["leagues",s],queryFn:()=>n.A.getLeagues(s),staleTime:6e5});return{leagues:(null===(e=i.data)||void 0===e?void 0:e.data)||[],leaguesMeta:null===(t=i.data)||void 0===t?void 0:t.meta,isLoading:i.isLoading,error:i.error,refetch:i.refetch}},l=(e,t)=>{let s=(0,a.a)({queryKey:["leagues",e,t],queryFn:()=>n.A.getLeagueById(e,t),enabled:!!e,staleTime:6e5});return{league:s.data,isLoading:s.isLoading,error:s.error,refetch:s.refetch}},u=()=>{let e=(0,i.NL)(),t=(0,r.D)({mutationFn:e=>n.A.createLeague(e),onSuccess:()=>{e.invalidateQueries({queryKey:["leagues"]})}}),s=(0,r.D)({mutationFn:e=>{let{id:t,data:s}=e;return n.A.updateLeague(t,s)},onSuccess:(t,s)=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}}),a=(0,r.D)({mutationFn:e=>{let{id:t,active:s}=e;return n.A.toggleLeagueStatus(t,s)},onSuccess:t=>{e.setQueryData(["leagues",t.externalId],t),e.invalidateQueries({queryKey:["leagues"],exact:!1})}});return{createLeague:t.mutate,isCreateLoading:t.isPending,createError:t.error,createData:t.data,updateLeague:s.mutate,isUpdateLoading:s.isPending,updateError:s.error,updateData:s.data,toggleStatus:a.mutate,isToggleLoading:a.isPending,toggleError:a.error,toggleData:a.data}}},5899:function(e,t,s){"use strict";s.d(t,{_:function(){return a}});let a=console},4654:function(e,t,s){"use strict";s.d(t,{R:function(){return l},m:function(){return o}});var a=s(5899),i=s(9522),r=s(3864),n=s(4500);class o extends r.F{constructor(e){super(),this.defaultOptions=e.defaultOptions,this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.logger=e.logger||a._,this.observers=[],this.state=e.state||l(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options={...this.defaultOptions,...e},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(e){this.dispatch({type:"setState",state:e})}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.observers=this.observers.filter(t=>t!==e),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.observers.length||("loading"===this.state.status?this.scheduleGc():this.mutationCache.remove(this))}continue(){var e,t;return null!=(e=null==(t=this.retryer)?void 0:t.continue())?e:this.execute()}async execute(){var e,t,s,a,i,r,o,l,u,c,h,d,m,g,p,v,y,f,x,b;let j="loading"===this.state.status;try{if(!j){this.dispatch({type:"loading",variables:this.options.variables}),await (null==(u=(c=this.mutationCache.config).onMutate)?void 0:u.call(c,this.state.variables,this));let e=await (null==(h=(d=this.options).onMutate)?void 0:h.call(d,this.state.variables));e!==this.state.context&&this.dispatch({type:"loading",context:e,variables:this.state.variables})}let m=await (()=>{var e;return this.retryer=(0,n.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(e,t)=>{this.dispatch({type:"failed",failureCount:e,error:t})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise})();return await (null==(e=(t=this.mutationCache.config).onSuccess)?void 0:e.call(t,m,this.state.variables,this.state.context,this)),await (null==(s=(a=this.options).onSuccess)?void 0:s.call(a,m,this.state.variables,this.state.context)),await (null==(i=(r=this.mutationCache.config).onSettled)?void 0:i.call(r,m,null,this.state.variables,this.state.context,this)),await (null==(o=(l=this.options).onSettled)?void 0:o.call(l,m,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:m}),m}catch(e){try{throw await (null==(m=(g=this.mutationCache.config).onError)?void 0:m.call(g,e,this.state.variables,this.state.context,this)),await (null==(p=(v=this.options).onError)?void 0:p.call(v,e,this.state.variables,this.state.context)),await (null==(y=(f=this.mutationCache.config).onSettled)?void 0:y.call(f,void 0,e,this.state.variables,this.state.context,this)),await (null==(x=(b=this.options).onSettled)?void 0:x.call(b,void 0,e,this.state.variables,this.state.context)),e}finally{this.dispatch({type:"error",error:e})}}}dispatch(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"loading":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,n.Kw)(this.options.networkMode),status:"loading",variables:e.variables};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"};case"setState":return{...t,...e.state}}})(this.state),i.V.batch(()=>{this.observers.forEach(t=>{t.onMutationUpdate(e)}),this.mutationCache.notify({mutation:this,type:"updated",action:e})})}}function l(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0}}},3864:function(e,t,s){"use strict";s.d(t,{F:function(){return i}});var a=s(1678);class i{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,a.PN)(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(e){this.cacheTime=Math.max(this.cacheTime||0,null!=e?e:a.sk?1/0:3e5)}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}},8186:function(e,t,s){"use strict";s.d(t,{D:function(){return d}});var a=s(2265),i=s(1678),r=s(4654),n=s(9522),o=s(6761);class l extends o.l{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;let s=this.options;this.options=this.client.defaultMutationOptions(e),(0,i.VS)(s,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,r.R)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){n.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var t,s,a,i,r,n,o,l;e.onSuccess?(null==(t=(s=this.mutateOptions).onSuccess)||t.call(s,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(a=(i=this.mutateOptions).onSettled)||a.call(i,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(r=(n=this.mutateOptions).onError)||r.call(n,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(o=(l=this.mutateOptions).onSettled)||o.call(l,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}var u=s(7536),c=s(4095),h=s(3439);function d(e,t,s){let r=(0,i.lV)(e,t,s),o=(0,c.NL)({context:r.context}),[d]=a.useState(()=>new l(o,r));a.useEffect(()=>{d.setOptions(r)},[d,r]);let g=(0,u.$)(a.useCallback(e=>d.subscribe(n.V.batchCalls(e)),[d]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),p=a.useCallback((e,t)=>{d.mutate(e,t).catch(m)},[d]);if(g.error&&(0,h.L)(d.options.useErrorBoundary,[g.error]))throw g.error;return{...g,mutate:p,mutateAsync:g.mutate}}function m(){}}},function(e){e.O(0,[2150,3107,9101,1346,1610,2608,1766,6288,1637,8140,2971,8069,1744],function(){return e(e.s=6353)}),_N_E=e.O()}]);