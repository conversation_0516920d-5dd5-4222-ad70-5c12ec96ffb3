(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[683],{2020:function(e,t,s){Promise.resolve().then(s.bind(s,9312))},7977:function(e,t,s){"use strict";s.d(t,{Z:function(){return u}});var n=s(2265);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:r,className:a="",children:i,iconNode:d,...u},m)=>(0,n.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:r?24*Number(s)/Number(t):s,className:l("lucide",a),...!i&&!o(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let s=(0,n.forwardRef)(({className:s,...a},o)=>(0,n.createElement)(d,{ref:o,iconNode:t,className:l(`lucide-${r(i(e))}`,`lucide-${e}`,s),...a}));return s.displayName=i(e),s}},9108:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(7977).Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},7805:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(7977).Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4960:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(7977).Z)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},8306:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(7977).Z)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},7841:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(7977).Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5404:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(7977).Z)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},8670:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(7977).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9079:function(e,t,s){"use strict";var n,r;e.exports=(null==(n=s.g.process)?void 0:n.env)&&"object"==typeof(null==(r=s.g.process)?void 0:r.env)?s.g.process:s(3127)},3127:function(e){!function(){var t={229:function(e){var t,s,n,r=e.exports={};function a(){throw Error("setTimeout has not been defined")}function i(){throw Error("clearTimeout has not been defined")}function l(e){if(t===setTimeout)return setTimeout(e,0);if((t===a||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(s){try{return t.call(null,e,0)}catch(s){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:a}catch(e){t=a}try{s="function"==typeof clearTimeout?clearTimeout:i}catch(e){s=i}}();var o=[],c=!1,d=-1;function u(){c&&n&&(c=!1,n.length?o=n.concat(o):d=-1,o.length&&m())}function m(){if(!c){var e=l(u);c=!0;for(var t=o.length;t;){for(n=o,o=[];++d<t;)n&&n[d].run();d=-1,t=o.length}n=null,c=!1,function(e){if(s===clearTimeout)return clearTimeout(e);if((s===i||!s)&&clearTimeout)return s=clearTimeout,clearTimeout(e);try{s(e)}catch(t){try{return s.call(null,e)}catch(t){return s.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function x(){}r.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var s=1;s<arguments.length;s++)t[s-1]=arguments[s];o.push(new h(e,t)),1!==o.length||c||l(m)},h.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=x,r.addListener=x,r.once=x,r.off=x,r.removeListener=x,r.removeAllListeners=x,r.emit=x,r.prependListener=x,r.prependOnceListener=x,r.listeners=function(e){return[]},r.binding=function(e){throw Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw Error("process.chdir is not supported")},r.umask=function(){return 0}}},s={};function n(e){var r=s[e];if(void 0!==r)return r.exports;var a=s[e]={exports:{}},i=!0;try{t[e](a,a.exports,n),i=!1}finally{i&&delete s[e]}return a.exports}n.ab="//";var r=n(229);e.exports=r}()},9312:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return p}});var n=s(7437),r=s(2265),a=s(5671),i=s(575),l=s(3277),o=s(2632),c=s(6803),d=s(4133),u=s(7625),m=s(7841),h=s(9295),x=s(489);let f=[{id:1,name:"John Doe",email:"<EMAIL>",role:"Admin",status:"active",lastLogin:"2025-05-24"},{id:2,name:"Jane Smith",email:"<EMAIL>",role:"Editor",status:"active",lastLogin:"2025-05-23"},{id:3,name:"Bob Johnson",email:"<EMAIL>",role:"Moderator",status:"inactive",lastLogin:"2025-05-20"},{id:4,name:"Alice Brown",email:"<EMAIL>",role:"Editor",status:"active",lastLogin:"2025-05-24"},{id:5,name:"Charlie Wilson",email:"<EMAIL>",role:"Moderator",status:"active",lastLogin:"2025-05-22"}];function p(){let[e,t]=(0,r.useState)(!1),[s,p]=(0,r.useState)(!1),[j,v]=(0,r.useState)(!1),[y,g]=(0,r.useState)(!1),[b,N]=(0,r.useState)({name:"",email:"",role:"",bio:"",notifications:!1});return(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reusable Components Demo"}),(0,n.jsx)("p",{className:"text-gray-600 mt-1",children:"Showcase of all reusable components built for the CMS"})]}),(0,n.jsxs)(a.Zb,{children:[(0,n.jsxs)(a.Ol,{children:[(0,n.jsx)(a.ll,{children:"DataTable Component"}),(0,n.jsx)(a.SZ,{children:"Advanced data table with sorting, filtering, and pagination"})]}),(0,n.jsx)(a.aY,{children:(0,n.jsx)(o.w,{data:f,columns:[{key:"name",title:"Name",sortable:!0,render:e=>(0,n.jsx)("span",{className:"font-medium",children:e})},{key:"email",title:"Email",sortable:!0},{key:"role",title:"Role",sortable:!0,filterable:!0,render:e=>(0,n.jsx)(l.C,{variant:"Admin"===e?"default":"secondary",children:e})},{key:"status",title:"Status",sortable:!0,filterable:!0,render:e=>(0,n.jsx)(l.C,{variant:"active"===e?"default":"secondary",children:e})},{key:"lastLogin",title:"Last Login",sortable:!0},{key:"actions",title:"Actions",render:(e,t)=>(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)(i.z,{size:"sm",variant:"outline",children:(0,n.jsx)(m.Z,{className:"h-4 w-4"})}),(0,n.jsx)(i.z,{size:"sm",variant:"outline",children:(0,n.jsx)(h.Z,{className:"h-4 w-4"})}),(0,n.jsx)(i.z,{size:"sm",variant:"outline",children:(0,n.jsx)(x.Z,{className:"h-4 w-4"})})]})}],searchable:!0,searchPlaceholder:"Search users...",emptyMessage:"No users found"})})]}),(0,n.jsxs)(a.Zb,{children:[(0,n.jsxs)(a.Ol,{children:[(0,n.jsx)(a.ll,{children:"Form Components"}),(0,n.jsx)(a.SZ,{children:"Various form fields with validation and styling"})]}),(0,n.jsxs)(a.aY,{children:[(0,n.jsxs)(c.hj,{title:"User Information",description:"Basic user details",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsx)(c.UP,{label:"Full Name",placeholder:"Enter full name",required:!0,value:b.name,onChange:e=>N(t=>({...t,name:e.target.value}))}),(0,n.jsx)(c.UP,{label:"Email Address",type:"email",placeholder:"Enter email",required:!0,value:b.email,onChange:e=>N(t=>({...t,email:e.target.value}))})]}),(0,n.jsx)(c.mg,{label:"Role",placeholder:"Select a role",required:!0,value:b.role,onValueChange:e=>N(t=>({...t,role:e})),options:[{value:"admin",label:"Administrator"},{value:"editor",label:"Editor"},{value:"moderator",label:"Moderator"}]}),(0,n.jsx)(c.XL,{label:"Bio",placeholder:"Tell us about yourself...",description:"Optional bio information",value:b.bio,onChange:e=>N(t=>({...t,bio:e.target.value}))}),(0,n.jsx)(c.ji,{label:"Enable email notifications",checked:b.notifications,onCheckedChange:e=>N(t=>({...t,notifications:e}))})]}),(0,n.jsxs)(c.iN,{children:[(0,n.jsx)(i.z,{variant:"outline",children:"Cancel"}),(0,n.jsx)(i.z,{children:"Save Changes"})]})]})]}),(0,n.jsxs)(a.Zb,{children:[(0,n.jsxs)(a.Ol,{children:[(0,n.jsx)(a.ll,{children:"Modal Components"}),(0,n.jsx)(a.SZ,{children:"Different types of modal dialogs"})]}),(0,n.jsx)(a.aY,{children:(0,n.jsxs)("div",{className:"flex space-x-4",children:[(0,n.jsx)(i.z,{onClick:()=>t(!0),children:"Basic Modal"}),(0,n.jsx)(i.z,{onClick:()=>p(!0),variant:"outline",children:"Confirm Modal"}),(0,n.jsx)(i.z,{onClick:()=>v(!0),variant:"outline",children:"Form Modal"})]})})]}),(0,n.jsxs)(a.Zb,{children:[(0,n.jsxs)(a.Ol,{children:[(0,n.jsx)(a.ll,{children:"Loading Skeletons"}),(0,n.jsx)(a.SZ,{children:"Loading states for different components"})]}),(0,n.jsx)(a.aY,{children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)(i.z,{onClick:()=>g(!y),variant:"outline",children:[y?"Hide":"Show"," Skeletons"]}),y&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Card Skeleton"}),(0,n.jsx)(u.q4,{})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Table Skeleton"}),(0,n.jsx)(u.hM,{rows:3,columns:4})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Basic Skeletons"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.Od,{className:"h-4 w-3/4"}),(0,n.jsx)(u.Od,{className:"h-4 w-1/2"}),(0,n.jsx)(u.Od,{className:"h-8 w-1/4"})]})]})]})]})})]}),(0,n.jsx)(d.u_,{isOpen:e,onClose:()=>t(!1),title:"Basic Modal",description:"This is a basic modal dialog example",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("p",{children:"This is the content of the modal. You can put any content here."}),(0,n.jsx)("div",{className:"flex justify-end space-x-2",children:(0,n.jsx)(i.z,{variant:"outline",onClick:()=>t(!1),children:"Close"})})]})}),(0,n.jsx)(d.sm,{isOpen:s,onClose:()=>p(!1),onConfirm:()=>{console.log("Confirmed!"),p(!1)},title:"Confirm Action",message:"Are you sure you want to proceed with this action?",confirmText:"Yes, proceed",variant:"destructive"}),(0,n.jsx)(d.uB,{isOpen:j,onClose:()=>v(!1),onSubmit:()=>{console.log("Form submitted:",b),v(!1)},title:"Create New User",description:"Fill in the details to create a new user",submitText:"Create User",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(c.UP,{label:"Username",placeholder:"Enter username",required:!0}),(0,n.jsx)(c.UP,{label:"Email",type:"email",placeholder:"Enter email",required:!0}),(0,n.jsx)(c.mg,{label:"Role",placeholder:"Select role",required:!0,options:[{value:"admin",label:"Administrator"},{value:"editor",label:"Editor"},{value:"moderator",label:"Moderator"}]})]})})]})}},4133:function(e,t,s){"use strict";s.d(t,{sm:function(){return m},uB:function(){return h},u_:function(){return u}});var n=s(7437),r=s(2265),a=s(5669),i=s(691),l=s(2235),o=s(575),c=s(2169);let d={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl","2xl":"max-w-6xl",full:"max-w-full mx-4"},u=e=>{let{isOpen:t,onClose:s,title:u,description:m,children:h,size:x="md",showCloseButton:f=!0,closeOnOverlayClick:p=!0,className:j}=e;return(0,n.jsx)(a.u,{appear:!0,show:t,as:r.Fragment,children:(0,n.jsxs)(i.Vq,{as:"div",className:"relative z-50",onClose:p?s:()=>{},children:[(0,n.jsx)(a.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,n.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,n.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,n.jsx)(a.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,n.jsxs)(i.Vq.Panel,{className:(0,c.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",d[x],j),children:[(u||f)&&(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("div",{children:[u&&(0,n.jsx)(i.Vq.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:u}),m&&(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:m})]}),f&&(0,n.jsx)(o.z,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:(0,n.jsx)(l.Z,{className:"h-4 w-4"})})]}),(0,n.jsx)("div",{className:"mt-2",children:h})]})})})})]})})},m=e=>{let{isOpen:t,onClose:s,onConfirm:r,title:a="Confirm Action",message:i="Are you sure you want to proceed?",confirmText:l="Confirm",cancelText:c="Cancel",variant:d="default",loading:m=!1}=e;return(0,n.jsx)(u,{isOpen:t,onClose:s,title:a,size:"sm",closeOnOverlayClick:!m,children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:i}),(0,n.jsxs)("div",{className:"flex space-x-2 justify-end",children:[(0,n.jsx)(o.z,{variant:"outline",onClick:s,disabled:m,children:c}),(0,n.jsx)(o.z,{variant:"destructive"===d?"destructive":"default",onClick:r,disabled:m,children:m?"Processing...":l})]})]})})},h=e=>{let{isOpen:t,onClose:s,title:r,description:a,children:i,onSubmit:l,submitText:c="Save",cancelText:d="Cancel",loading:m=!1,size:h="md"}=e;return(0,n.jsx)(u,{isOpen:t,onClose:s,title:r,description:a,size:h,closeOnOverlayClick:!m,children:(0,n.jsxs)("form",{onSubmit:e=>{e.preventDefault(),null==l||l()},className:"space-y-4",children:[i,(0,n.jsxs)("div",{className:"flex space-x-2 justify-end pt-4 border-t",children:[(0,n.jsx)(o.z,{type:"button",variant:"outline",onClick:s,disabled:m,children:d}),l&&(0,n.jsx)(o.z,{type:"submit",disabled:m,children:m?"Saving...":c})]})]})})}},7625:function(e,t,s){"use strict";s.d(t,{Od:function(){return a},hM:function(){return l},q4:function(){return i}});var n=s(7437),r=s(2169);function a(e){let{className:t,...s}=e;return(0,n.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",t),...s})}let i=e=>{let{className:t}=e;return(0,n.jsxs)("div",{className:(0,r.cn)("border rounded-lg p-6 space-y-4",t),children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(a,{className:"h-4 w-3/4"}),(0,n.jsx)(a,{className:"h-4 w-1/2"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(a,{className:"h-3 w-full"}),(0,n.jsx)(a,{className:"h-3 w-full"}),(0,n.jsx)(a,{className:"h-3 w-2/3"})]})]})},l=e=>{let{rows:t=5,columns:s=4,className:i}=e;return(0,n.jsx)("div",{className:(0,r.cn)("space-y-4",i),children:(0,n.jsxs)("div",{className:"border rounded-lg",children:[(0,n.jsx)("div",{className:"border-b p-4",children:(0,n.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(s,", 1fr)")},children:Array.from({length:s}).map((e,t)=>(0,n.jsx)(a,{className:"h-4 w-20"},t))})}),Array.from({length:t}).map((e,t)=>(0,n.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,n.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(s,", 1fr)")},children:Array.from({length:s}).map((e,t)=>(0,n.jsx)(a,{className:"h-4 w-full"},t))})},t))]})})}}},function(e){e.O(0,[2150,1610,2608,1766,1637,1953,8140,1380,2971,8069,1744],function(){return e(e.s=2020)}),_N_E=e.O()}]);