(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7663],{1622:function(e,s,t){Promise.resolve().then(t.bind(t,1118))},1118:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return et}});var r=t(7437),a=t(2265),n=t(1546),i=t(9744),l=t(8670),o=t(3345),d=t(575),c=t(2782),m=t(7326),u=t(1213),h=t(9910),f=t(1049),x=t(3613),g=t(7805),p=t(9259),j=t(7501),b=t(2169);let v=x.fC,N=x.xz;x.ZA,x.Uv,x.Tr,x.Ee,a.forwardRef((e,s)=>{let{className:t,inset:a,children:n,...i}=e;return(0,r.jsxs)(x.fF,{ref:s,className:(0,b.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",t),...i,children:[n,(0,r.jsx)(g.Z,{className:"ml-auto"})]})}).displayName=x.fF.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(x.tu,{ref:s,className:(0,b.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...a})}).displayName=x.tu.displayName;let y=a.forwardRef((e,s)=>{let{className:t,sideOffset:a=4,...n}=e;return(0,r.jsx)(x.Uv,{children:(0,r.jsx)(x.VY,{ref:s,sideOffset:a,className:(0,b.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...n})})});y.displayName=x.VY.displayName;let w=a.forwardRef((e,s)=>{let{className:t,inset:a,...n}=e;return(0,r.jsx)(x.ck,{ref:s,className:(0,b.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",a&&"pl-8",t),...n})});w.displayName=x.ck.displayName,a.forwardRef((e,s)=>{let{className:t,children:a,checked:n,...i}=e;return(0,r.jsxs)(x.oC,{ref:s,className:(0,b.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:n,...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(x.wU,{children:(0,r.jsx)(p.Z,{className:"h-4 w-4"})})}),a]})}).displayName=x.oC.displayName,a.forwardRef((e,s)=>{let{className:t,children:a,...n}=e;return(0,r.jsxs)(x.Rk,{ref:s,className:(0,b.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(x.wU,{children:(0,r.jsx)(j.Z,{className:"h-2 w-2 fill-current"})})}),a]})}).displayName=x.Rk.displayName;let Z=a.forwardRef((e,s)=>{let{className:t,inset:a,...n}=e;return(0,r.jsx)(x.__,{ref:s,className:(0,b.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",t),...n})});Z.displayName=x.__.displayName;let k=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(x.Z0,{ref:s,className:(0,b.cn)("-mx-1 my-1 h-px bg-muted",t),...a})});k.displayName=x.Z0.displayName;var C=t(7733);let z=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(C.fC,{ref:s,className:(0,b.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...a})});z.displayName=C.fC.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(C.Ee,{ref:s,className:(0,b.cn)("aspect-square h-full w-full",t),...a})}).displayName=C.Ee.displayName;let R=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(C.NY,{ref:s,className:(0,b.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...a})});R.displayName=C.NY.displayName;var S=t(3277),L=t(7786),E=t(8763),T=t(6146);let U=()=>{var e,s;let{user:t,logout:a,isLogoutLoading:n}=(0,L.a)(),{refreshToken:i}=(0,E.t)();return t?(0,r.jsxs)(v,{children:[(0,r.jsx)(N,{asChild:!0,children:(0,r.jsx)(d.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,r.jsx)(z,{className:"h-8 w-8",children:(0,r.jsx)(R,{className:"text-xs",children:(e=t.fullName,s=t.username,e?e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2):(null==s?void 0:s.slice(0,2).toUpperCase())||"U")})})})}),(0,r.jsxs)(y,{className:"w-56",align:"end",forceMount:!0,children:[(0,r.jsx)(Z,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:t.fullName||t.username}),(0,r.jsx)(S.C,{className:"text-xs ".concat((e=>{switch(e){case"admin":return"bg-red-100 text-red-800";case"editor":return"bg-blue-100 text-blue-800";case"moderator":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(t.role)),children:(0,r.jsxs)("span",{className:"flex items-center space-x-1",children:[(e=>{switch(e){case"admin":return(0,r.jsx)(m.Z,{className:"h-3 w-3"});case"editor":default:return(0,r.jsx)(u.Z,{className:"h-3 w-3"});case"moderator":return(0,r.jsx)(h.Z,{className:"h-3 w-3"})}})(t.role),(0,r.jsx)("span",{children:t.role})]})})]}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:t.email}),t.lastLoginAt&&(0,r.jsxs)("p",{className:"text-xs leading-none text-muted-foreground",children:["Last login: ",new Date(t.lastLoginAt).toLocaleDateString()]})]})}),(0,r.jsx)(k,{}),(0,r.jsxs)(w,{className:"cursor-pointer",children:[(0,r.jsx)(u.Z,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Profile"})]}),(0,r.jsxs)(w,{className:"cursor-pointer",children:[(0,r.jsx)(h.Z,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]}),(0,r.jsx)(k,{}),(0,r.jsx)(w,{className:"cursor-pointer text-red-600 focus:text-red-600",onClick:()=>{if(console.log("\uD83D\uDD04 Logout clicked, refreshToken:",i?"Present":"Missing"),i)a(i);else{console.log("⚠️ No refresh token, forcing logout...");let{clearAuth:e}=E.t.getState();e(),window.location.href="/auth/login"}},disabled:n,children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(T.TK,{size:"sm",className:"mr-2"}),(0,r.jsx)("span",{children:"Signing out..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.Z,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Log out"})]})})]})]}):null};var _=t(7907),A=t(8792),D=t(2527);let M={dashboard:"Dashboard",fixtures:"Fixtures",leagues:"Leagues",teams:"Teams",users:"Users",settings:"Settings",broadcast:"Broadcast Links",system:"System Users",registered:"Registered Users",tiers:"Tier Statistics",live:"Live & Upcoming",sync:"Data Sync",auth:"Authentication",login:"Login"},P=e=>{let{items:s,className:t}=e,a=(0,_.usePathname)(),n=s||function(e){let s=e.split("/").filter(Boolean),t=[];t.push({label:"Dashboard",href:"/dashboard",icon:D.Z});let r="";return s.forEach((e,a)=>{if(r+="/".concat(e),"dashboard"===e)return;let n=M[e]||e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),i=a===s.length-1;t.push({label:n,href:i?void 0:r})}),t}(a);return n.length<=1?null:(0,r.jsx)("nav",{className:(0,b.cn)("flex items-center space-x-1 text-sm text-gray-500",t),children:n.map((e,s)=>{let t=s===n.length-1,a=e.icon;return(0,r.jsxs)("div",{className:"flex items-center",children:[s>0&&(0,r.jsx)(g.Z,{className:"h-4 w-4 mx-1 text-gray-400"}),t?(0,r.jsxs)("span",{className:"font-medium text-gray-900 flex items-center",children:[a&&(0,r.jsx)(a,{className:"h-4 w-4 mr-1"}),e.label]}):(0,r.jsxs)(A.default,{href:e.href||"#",className:"hover:text-gray-700 transition-colors flex items-center",children:[a&&(0,r.jsx)(a,{className:"h-4 w-4 mr-1"}),e.label]})]},s)})})};var q=t(1047),F=t(2891),I=t(3013),Y=t(7457);let V=()=>{let{setTheme:e,theme:s}=(0,Y.useTheme)();return(0,r.jsxs)(v,{children:[(0,r.jsx)(N,{asChild:!0,children:(0,r.jsxs)(d.z,{variant:"ghost",size:"sm",className:"h-8 w-8 px-0",children:[(0,r.jsx)(q.Z,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,r.jsx)(F.Z,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,r.jsxs)(y,{align:"end",children:[(0,r.jsxs)(w,{onClick:()=>e("light"),className:"light"===s?"bg-accent":"",children:[(0,r.jsx)(q.Z,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Light"})]}),(0,r.jsxs)(w,{onClick:()=>e("dark"),className:"dark"===s?"bg-accent":"",children:[(0,r.jsx)(F.Z,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Dark"})]}),(0,r.jsxs)(w,{onClick:()=>e("system"),className:"system"===s?"bg-accent":"",children:[(0,r.jsx)(I.Z,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"System"})]})]})]})},B=e=>{let{onMenuClick:s,showBreadcrumb:t=!0}=e;return(0,r.jsxs)("header",{className:"bg-white border-b border-gray-200",children:[(0,r.jsx)("div",{className:"px-4 sm:px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(d.z,{variant:"ghost",size:"sm",className:"md:hidden",onClick:s,children:(0,r.jsx)(i.Z,{className:"h-5 w-5"})}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"APISportsGame CMS"})]}),(0,r.jsx)("div",{className:"hidden sm:flex flex-1 max-w-md mx-8",children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(c.I,{type:"text",placeholder:"Search...",className:"pl-10 pr-4 w-full"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,r.jsx)(d.z,{variant:"ghost",size:"sm",className:"sm:hidden",children:(0,r.jsx)(l.Z,{className:"h-5 w-5"})}),(0,r.jsx)(V,{}),(0,r.jsxs)(d.z,{variant:"ghost",size:"sm",className:"relative",children:[(0,r.jsx)(o.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:"3"})]}),(0,r.jsx)(U,{})]})]})}),t&&(0,r.jsx)("div",{className:"px-4 sm:px-6 py-2 bg-gray-50 border-t border-gray-100",children:(0,r.jsx)(P,{})})]})};var O=t(5423),W=t(7307),X=t(5462),G=t(834),K=t(6260),H=t(4059),J=t(5497),Q=t(3441),$=t(2235);let ee=[{title:"Dashboard",href:"/dashboard",icon:O.Z},{title:"Fixtures",icon:W.Z,children:[{title:"All Fixtures",href:"/dashboard/fixtures",icon:W.Z},{title:"Live & Upcoming",href:"/dashboard/fixtures/live",icon:X.Z},{title:"Sync Data",href:"/dashboard/fixtures/sync",icon:G.Z,requiredRole:"admin"}]},{title:"Leagues",href:"/dashboard/leagues",icon:K.Z},{title:"Teams",href:"/dashboard/teams",icon:H.Z},{title:"Broadcast Links",href:"/dashboard/broadcast",icon:X.Z,requiredRole:"editor"},{title:"User Management",icon:J.Z,requiredRole:"admin",children:[{title:"System Users",href:"/dashboard/users/system",icon:J.Z},{title:"Registered Users",href:"/dashboard/users/registered",icon:H.Z},{title:"Tier Statistics",href:"/dashboard/users/tiers",icon:K.Z}]},{title:"Settings",href:"/dashboard/settings",icon:h.Z},{title:"API Test",href:"/dashboard/api-test",icon:G.Z,requiredRole:"admin"},{title:"Components Demo",href:"/dashboard/components-demo",icon:h.Z,requiredRole:"admin"}],es=e=>{let{isOpen:s=!0,onClose:t,className:i}=e,l=(0,_.usePathname)(),{hasRole:o}=(0,n.TE)(),[c,m]=(0,a.useState)([]),[u,h]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=()=>{h(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,a.useEffect)(()=>{u&&t&&t()},[l,u,t]);let f=e=>{m(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},x=e=>l===e||l.startsWith(e+"/"),p=e=>!e.requiredRole||o(e.requiredRole),j=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!p(e))return null;let t=e.children&&e.children.length>0,a=c.includes(e.title),n=e.icon;if(t){var i;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("button",{onClick:()=>f(e.title),className:(0,b.cn)("w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors","text-gray-700 hover:text-gray-900 hover:bg-gray-100",s>0&&"ml-4"),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n,{className:"mr-3 h-4 w-4"}),(0,r.jsx)("span",{children:e.title}),e.badge&&(0,r.jsx)(S.C,{variant:"secondary",className:"ml-2 text-xs",children:e.badge})]}),a?(0,r.jsx)(Q.Z,{className:"h-4 w-4"}):(0,r.jsx)(g.Z,{className:"h-4 w-4"})]}),a&&(0,r.jsx)("div",{className:"mt-1 space-y-1",children:null===(i=e.children)||void 0===i?void 0:i.map(e=>j(e,s+1))})]},e.title)}return(0,r.jsxs)(A.default,{href:e.href,className:(0,b.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s>0&&"ml-4",x(e.href)?"bg-blue-100 text-blue-700":"text-gray-700 hover:text-gray-900 hover:bg-gray-100"),children:[(0,r.jsx)(n,{className:"mr-3 h-4 w-4"}),(0,r.jsx)("span",{children:e.title}),e.badge&&(0,r.jsx)(S.C,{variant:"secondary",className:"ml-2 text-xs",children:e.badge})]},e.title)};return u?(0,r.jsxs)(r.Fragment,{children:[s&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:t}),(0,r.jsxs)("aside",{className:(0,b.cn)("fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out md:hidden",s?"translate-x-0":"-translate-x-full",i),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Menu"}),(0,r.jsx)(d.z,{variant:"ghost",size:"sm",onClick:t,className:"h-8 w-8 p-0",children:(0,r.jsx)($.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("nav",{className:"p-4 space-y-2 overflow-y-auto h-full pb-20",children:ee.map(e=>j(e))})]})]}):(0,r.jsx)("aside",{className:(0,b.cn)("w-64 bg-white border-r border-gray-200 min-h-screen",i),children:(0,r.jsx)("nav",{className:"p-4 space-y-2",children:ee.map(e=>j(e))})})};function et(e){let{children:s}=e,[t,i]=(0,a.useState)(!1);return(0,r.jsx)(n.a1,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(B,{onMenuClick:()=>{i(!0)}}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(es,{isOpen:t,onClose:()=>{i(!1)}}),(0,r.jsx)("main",{className:"flex-1 p-4 sm:p-6 md:ml-0",children:s})]})]})})}},3277:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var r=t(7437);t(2265);var a=t(9769),n=t(2169);let i=(0,a.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:t}),s),...a})}},575:function(e,s,t){"use strict";t.d(s,{d:function(){return o},z:function(){return d}});var r=t(7437),a=t(2265),n=t(9143),i=t(9769),l=t(2169);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:t,variant:a,size:i,asChild:d=!1,...c}=e,m=d?n.g7:"button";return(0,r.jsx)(m,{className:(0,l.cn)(o({variant:a,size:i,className:t})),ref:s,...c})});d.displayName="Button"},2782:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var r=t(7437),a=t(2265),n=t(2169);let i=a.forwardRef((e,s)=>{let{className:t,type:a,...i}=e;return(0,r.jsx)("input",{type:a,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...i})});i.displayName="Input"},1546:function(e,s,t){"use strict";t.d(s,{TE:function(){return d},a1:function(){return o}});var r=t(7437),a=t(2265),n=t(7907),i=t(7786),l=t(6146);let o=e=>{let{children:s,requiredRole:t,fallbackUrl:o="/auth/login"}=e,d=(0,n.useRouter)(),{isAuthenticated:c,user:m,isLoading:u}=(0,i.a)();if((0,a.useEffect)(()=>{if(!u){if(!c||!m){d.push(o);return}if(t&&!(Array.isArray(t)?t:[t]).includes(m.role)){d.push("/dashboard?error=unauthorized");return}}},[c,m,u,t,d,o]),u)return(0,r.jsx)(l.SX,{message:"Verifying authentication..."});if(!c||!m)return(0,r.jsx)(l.SX,{message:"Redirecting to login..."});if(t){let e=Array.isArray(t)?t:[t];if(!e.includes(m.role))return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this page."}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Required role: ",e.join(" or ")," | Your role: ",m.role]})]})})}return(0,r.jsx)(r.Fragment,{children:s})},d=()=>{let{user:e}=(0,i.a)(),s=s=>!!e&&(Array.isArray(s)?s:[s]).includes(e.role),t=()=>s("admin"),r=()=>s(["admin","editor"]),a=()=>s(["admin","editor","moderator"]);return{user:e,hasRole:s,isAdmin:t,isEditor:r,isModerator:a,canManageUsers:()=>t(),canManageContent:()=>r(),canModerate:()=>a(),canSync:()=>t()}}},7457:function(e,s,t){"use strict";t.r(s),t.d(s,{ThemeProvider:function(){return i},useTheme:function(){return l}});var r=t(7437),a=t(2265);let n=(0,a.createContext)({theme:"system",setTheme:()=>null});function i(e){let{children:s,defaultTheme:t="system",storageKey:i="cms-theme",...l}=e,[o,d]=(0,a.useState)(t),[c,m]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{var e;m(!0);let s=null===(e=localStorage)||void 0===e?void 0:e.getItem(i);s&&d(s)},[i]),(0,a.useEffect)(()=>{let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===o){let s=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(s);return}e.classList.add(o)},[o]),c)?(0,r.jsx)(n.Provider,{...l,value:{theme:o,setTheme:e=>{var s;null===(s=localStorage)||void 0===s||s.setItem(i,e),d(e)}},children:s}):null}let l=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}}},function(e){e.O(0,[2150,3107,9101,1346,1610,2608,41,9459,8157,6877,2971,8069,1744],function(){return e(e.s=1622)}),_N_E=e.O()}]);