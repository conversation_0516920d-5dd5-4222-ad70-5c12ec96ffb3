(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2622],{1514:function(e,a,t){Promise.resolve().then(t.bind(t,160))},160:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return j}});var l=t(7437),r=t(2265),i=t(7907),s=t(1346),n=t(8186),d=t(5671),u=t(575),m=t(6803),o=t(2975),c=t(7011),h=t(3016),g=t(3879),x=t(7307),v=t(699),y=t(6288);function j(){var e,a;let t=(0,i.useRouter)(),[j,p]=(0,r.useState)({homeTeamId:"",awayTeamId:"",leagueId:"",date:"",time:"",venueName:"",venueCity:"",round:""}),[I,f]=(0,r.useState)({}),{data:T}=(0,s.a)({queryKey:["leagues","all"],queryFn:()=>c.A.getLeagues({limit:100})}),{data:b}=(0,s.a)({queryKey:["teams","all"],queryFn:()=>h.k.getTeams({limit:100})}),C=(0,n.D)({mutationFn:e=>o.L.createFixture(e),onSuccess:()=>{y.toast.success("Fixture created successfully"),t.push("/dashboard/fixtures")},onError:e=>{y.toast.error(e.message||"Failed to create fixture")}}),N=()=>{let e={};return j.homeTeamId||(e.homeTeamId="Home team is required"),j.awayTeamId||(e.awayTeamId="Away team is required"),j.leagueId||(e.leagueId="League is required"),j.date||(e.date="Date is required"),j.time||(e.time="Time is required"),j.homeTeamId===j.awayTeamId&&(e.awayTeamId="Away team must be different from home team"),f(e),0===Object.keys(e).length},w=(e,a)=>{p(t=>({...t,[e]:a})),I[e]&&f(a=>({...a,[e]:void 0}))},S=(null==T?void 0:null===(e=T.data)||void 0===e?void 0:e.map(e=>({value:e.id.toString(),label:e.name})))||[],q=(null==b?void 0:null===(a=b.data)||void 0===a?void 0:a.map(e=>({value:e.id.toString(),label:e.name})))||[];return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)(u.z,{variant:"outline",onClick:()=>t.back(),children:[(0,l.jsx)(g.Z,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Create New Fixture"}),(0,l.jsx)("p",{className:"text-gray-600 mt-1",children:"Add a new football fixture to the system"})]})]}),(0,l.jsxs)(d.Zb,{children:[(0,l.jsxs)(d.Ol,{children:[(0,l.jsxs)(d.ll,{className:"flex items-center",children:[(0,l.jsx)(x.Z,{className:"mr-2 h-5 w-5"}),"Fixture Details"]}),(0,l.jsx)(d.SZ,{children:"Fill in the details for the new fixture"})]}),(0,l.jsx)(d.aY,{children:(0,l.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!N()){y.toast.error("Please fix the form errors");return}let a=new Date("".concat(j.date,"T").concat(j.time)),t={homeTeamId:parseInt(j.homeTeamId),awayTeamId:parseInt(j.awayTeamId),leagueId:parseInt(j.leagueId),date:a.toISOString(),venueName:j.venueName||null,venueCity:j.venueCity||null,round:j.round||null,status:"NS"};C.mutate(t)},className:"space-y-6",children:[(0,l.jsxs)(m.hj,{title:"Teams & Competition",description:"Select the teams and league",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.mg,{label:"Home Team",placeholder:"Select home team",required:!0,value:j.homeTeamId,onValueChange:e=>w("homeTeamId",e),options:q,error:I.homeTeamId}),(0,l.jsx)(m.mg,{label:"Away Team",placeholder:"Select away team",required:!0,value:j.awayTeamId,onValueChange:e=>w("awayTeamId",e),options:q.filter(e=>e.value!==j.homeTeamId),error:I.awayTeamId})]}),(0,l.jsx)(m.mg,{label:"League",placeholder:"Select league",required:!0,value:j.leagueId,onValueChange:e=>w("leagueId",e),options:S,error:I.leagueId})]}),(0,l.jsx)(m.hj,{title:"Schedule",description:"Set the date and time",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.UP,{label:"Date",type:"date",required:!0,value:j.date,onChange:e=>w("date",e.target.value),error:I.date}),(0,l.jsx)(m.UP,{label:"Time",type:"time",required:!0,value:j.time,onChange:e=>w("time",e.target.value),error:I.time})]})}),(0,l.jsxs)(m.hj,{title:"Venue Information",description:"Optional venue details",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(m.UP,{label:"Venue Name",placeholder:"Stadium name",value:j.venueName,onChange:e=>w("venueName",e.target.value)}),(0,l.jsx)(m.UP,{label:"Venue City",placeholder:"City",value:j.venueCity,onChange:e=>w("venueCity",e.target.value)})]}),(0,l.jsx)(m.UP,{label:"Round",placeholder:"e.g., Matchday 1, Quarter-final",value:j.round,onChange:e=>w("round",e.target.value)})]}),(0,l.jsxs)(m.iN,{children:[(0,l.jsx)(u.z,{type:"button",variant:"outline",onClick:()=>t.back(),disabled:C.isPending,children:"Cancel"}),(0,l.jsxs)(u.z,{type:"submit",disabled:C.isPending,children:[(0,l.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),C.isPending?"Creating...":"Create Fixture"]})]})]})})]})]})}}},function(e){e.O(0,[2150,3107,9101,1346,1610,2608,1766,6288,1637,8140,1880,2971,8069,1744],function(){return e(e.s=1514)}),_N_E=e.O()}]);