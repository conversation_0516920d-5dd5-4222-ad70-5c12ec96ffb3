(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8815],{6399:function(e,t,r){Promise.resolve().then(r.bind(r,3333))},6490:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(7977).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},2457:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(7977).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},834:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(7977).Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4059:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(7977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},3333:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return m}});var a=r(7437),n=r(2265),s=r(1346),i=r(5671),l=r(3277),o=r(575),c=r(4059),d=r(2457),u=r(834),f=r(6490),x=r(2975);function m(){var e,t,r;let[m,h]=(0,n.useState)(!0),{data:g,isLoading:p,error:v,refetch:y}=(0,s.a)({queryKey:["fixtures","live-upcoming"],queryFn:()=>x.L.getUpcomingAndLive({limit:20}),refetchInterval:!!m&&3e4,staleTime:1e4}),j=e=>{switch(e){case"1H":case"2H":case"HT":return"bg-green-100 text-green-800 animate-pulse";case"FT":return"bg-gray-100 text-gray-800";case"NS":return"bg-blue-100 text-blue-800";case"CANC":case"PST":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}},N=(e,t)=>{switch(e){case"1H":case"2H":return"".concat(t,"'");case"HT":return"Half Time";case"FT":return"Full Time";case"NS":return"Not Started";case"CANC":return"Cancelled";case"PST":return"Postponed";default:return e}},b=e=>{let t=new Date(e);return{date:t.toLocaleDateString(),time:t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}},w=e=>{var t,r;let{fixture:n}=e,{date:s,time:o}=b(n.date),u=["1H","2H","HT"].includes(n.status);return(0,a.jsxs)(i.Zb,{className:"transition-all duration-200 ".concat(u?"ring-2 ring-green-500":""),children:[(0,a.jsxs)(i.Ol,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.C,{className:j(n.status),children:N(n.status,n.elapsed)}),u&&(0,a.jsx)(l.C,{variant:"outline",className:"text-green-600 border-green-600",children:"LIVE"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[s," • ",o]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[n.leagueName," • ",n.round]})]}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:(0,a.jsx)(c.Z,{className:"h-4 w-4 text-gray-600"})}),(0,a.jsx)("span",{className:"font-medium",children:n.homeTeamName})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:(0,a.jsx)(c.Z,{className:"h-4 w-4 text-gray-600"})}),(0,a.jsx)("span",{className:"font-medium",children:n.awayTeamName})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:null!==(t=n.goalsHome)&&void 0!==t?t:"-"}),(0,a.jsx)("div",{className:"text-2xl font-bold",children:null!==(r=n.goalsAway)&&void 0!==r?r:"-"})]})]}),n.venue&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(d.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[n.venue.name,", ",n.venue.city]})]}),null!==n.scoreHalftimeHome&&null!==n.scoreHalftimeAway&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Half-time: ",n.scoreHalftimeHome," - ",n.scoreHalftimeAway]})]})})]})};return v?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Live & Upcoming Fixtures"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Real-time football match updates"})]}),(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load fixtures"}),(0,a.jsxs)(o.z,{onClick:()=>y(),children:[(0,a.jsx)(u.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Live & Upcoming Fixtures"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Real-time football match updates"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(o.z,{variant:m?"default":"outline",size:"sm",onClick:()=>h(!m),children:[(0,a.jsx)(f.Z,{className:"mr-2 h-4 w-4"}),"Auto Refresh ",m?"ON":"OFF"]}),(0,a.jsxs)(o.z,{variant:"outline",size:"sm",onClick:()=>y(),disabled:p,children:[(0,a.jsx)(u.Z,{className:"mr-2 h-4 w-4 ".concat(p?"animate-spin":"")}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(null==g?void 0:null===(e=g.data)||void 0===e?void 0:e.filter(e=>["1H","2H","HT"].includes(e.status)).length)||0}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Live Matches"})]})}),(0,a.jsx)(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:(null==g?void 0:null===(t=g.data)||void 0===t?void 0:t.filter(e=>"NS"===e.status).length)||0}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Upcoming"})]})}),(0,a.jsx)(i.Zb,{children:(0,a.jsxs)(i.aY,{className:"p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:(null==g?void 0:null===(r=g.data)||void 0===r?void 0:r.length)||0}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Fixtures"})]})})]}),p?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[...Array(6)].map((e,t)=>(0,a.jsx)(i.Zb,{className:"animate-pulse",children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded"})]})})},t))}):(null==g?void 0:g.data)&&g.data.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:g.data.map(e=>(0,a.jsx)(w,{fixture:e},e.id))}):(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6 text-center",children:(0,a.jsx)("p",{className:"text-gray-600",children:"No live or upcoming fixtures found"})})}),(null==g?void 0:g.meta)&&g.meta.totalPages>1&&(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Page ",g.meta.currentPage," of ",g.meta.totalPages,"(",g.meta.totalItems," total fixtures)"]})})]})}},3277:function(e,t,r){"use strict";r.d(t,{C:function(){return l}});var a=r(7437);r(2265);var n=r(9769),s=r(2169);let i=(0,n.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{className:(0,s.cn)(i({variant:r}),t),...n})}},575:function(e,t,r){"use strict";r.d(t,{d:function(){return o},z:function(){return c}});var a=r(7437),n=r(2265),s=r(9143),i=r(9769),l=r(2169);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:r,variant:n,size:i,asChild:c=!1,...d}=e,u=c?s.g7:"button";return(0,a.jsx)(u,{className:(0,l.cn)(o({variant:n,size:i,className:r})),ref:t,...d})});c.displayName="Button"},5671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return l},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},ll:function(){return o}});var a=r(7437),n=r(2265),s=r(2169);let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...n})});i.displayName="Card";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...n})});l.displayName="CardHeader";let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("font-semibold leading-none tracking-tight",r),...n})});o.displayName="CardTitle";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...n})});c.displayName="CardDescription";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...n})});d.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...n})}).displayName="CardFooter"},4921:function(e,t,r){"use strict";r.d(t,{x:function(){return s}});var a=r(3107);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t);return(null===(e=r.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.baseURL="http://localhost:3000",this.client=a.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with baseURL:",this.baseURL)}}let s=new n},2975:function(e,t,r){"use strict";r.d(t,{L:function(){return n}});var a=r(4921);let n={getFixtures:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;void 0!==a&&t.append(r,a.toString())});let r=await fetch("/api/fixtures?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error("Failed to fetch fixtures: ".concat(r.statusText));return await r.json()},getFixtureById:async e=>{let t=await fetch("/api/fixtures/".concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture: ".concat(t.statusText));return await t.json()},getUpcomingAndLive:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;void 0!==a&&t.append(r,a.toString())});let r=await fetch("/api/fixtures/live?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error("Failed to fetch live fixtures: ".concat(r.statusText));return await r.json()},getTeamSchedule:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,a]=e;void 0!==a&&r.append(t,a.toString())}),await a.x.get("/football/fixtures/schedules/".concat(e,"?").concat(r.toString()))},getFixtureStatistics:async e=>await a.x.get("/football/fixtures/statistics/".concat(e)),triggerSeasonSync:async()=>await a.x.get("/football/fixtures/sync/fixtures"),triggerDailySync:async()=>await a.x.get("/football/fixtures/sync/daily"),getSyncStatus:async()=>await a.x.get("/football/fixtures/sync/status"),createFixture:async e=>{let t=await fetch("/api/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create fixture: ".concat(t.statusText));return await t.json()},updateFixture:async(e,t)=>{let r=await fetch("/api/fixtures/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok)throw Error("Failed to update fixture: ".concat(r.statusText));return await r.json()},deleteFixture:async e=>{let t=(()=>{{try{let t=localStorage.getItem("auth-storage");if(t){var e;let r=JSON.parse(t),a=null===(e=r.state)||void 0===e?void 0:e.accessToken;if(a)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",a.substring(0,20)+"..."),{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)}}}catch(e){console.warn("Failed to parse auth storage:",e)}let t=localStorage.getItem("accessToken");if(t)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}}return console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"}})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let r=await fetch("/api/fixtures/".concat(e),{method:"DELETE",headers:t});if(!r.ok){let e=await r.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",r.status,r.statusText,e),Error(e.message||"Failed to delete fixture: ".concat(r.statusText))}console.log("✅ Delete fixture successful:",e)},getFixture:async e=>(await n.getFixtureById(e)).data}},2169:function(e,t,r){"use strict";r.d(t,{cn:function(){return s}});var a=r(3167),n=r(1367);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.m6)((0,a.W)(t))}},1266:function(e,t,r){"use strict";r.d(t,{F:function(){return s},e:function(){return i}});var a=r(2265);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,a=e.map(e=>{let a=n(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return a.useCallback(s(...e),e)}},9143:function(e,t,r){"use strict";r.d(t,{Z8:function(){return i},g7:function(){return l},sA:function(){return c}});var a=r(2265),n=r(1266),s=r(7437);function i(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...s}=e;if(a.isValidElement(r)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let a in t){let n=e[a],s=t[a];/^on[A-Z]/.test(a)?n&&s?r[a]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(r[a]=n):"style"===a?r[a]={...n,...s}:"className"===a&&(r[a]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==a.Fragment&&(o.ref=t?(0,n.F)(t,l):l),a.cloneElement(r,o)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:n,...i}=e,l=a.Children.toArray(n),o=l.find(d);if(o){let e=o.props.children,n=l.map(t=>t!==o?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,s.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),o=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function d(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},9769:function(e,t,r){"use strict";r.d(t,{j:function(){return i}});var a=r(3167);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=a.W,i=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],a=null==l?void 0:l[e];if(null===t)return null;let s=n(t)||n(a);return i[e][s]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return s(e,o,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[2150,3107,9101,1346,2971,8069,1744],function(){return e(e.s=6399)}),_N_E=e.O()}]);