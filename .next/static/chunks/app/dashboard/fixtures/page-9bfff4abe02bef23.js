(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[266],{4240:function(e,a,t){Promise.resolve().then(t.bind(t,1100))},1100:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return J}});var s=t(7437),l=t(2265),r=t(4095),n=t(1346),i=t(8186),o=t(5671),d=t(575),c=t(3277),m=t(2632),u=t(4133),x=t(2975),h=t(7307),f=t(7841),g=t(5462),p=t(9925),N=t(9295),y=t(489),b=t(834),j=t(632),w=t(4715),v=t(94),T=t(2235),C=t(7625),k=t(1546),S=t(8594),z=t(2169),Z=t(9108),L=t(7805),_=t(9485);function D(e){let{className:a,classNames:t,showOutsideDays:l=!0,...r}=e,n=new Date;return n.setHours(0,0,0,0),(0,s.jsx)(_._,{showOutsideDays:l,className:(0,z.cn)("p-3",a),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,z.cn)((0,d.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,z.cn)((0,d.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-red-500 text-white font-bold hover:bg-red-600",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},modifiers:{past:e=>{let a=new Date(e);return a.setHours(0,0,0,0),a<n},future:e=>{let a=new Date(e);return a.setHours(0,0,0,0),a>n}},modifiersClassNames:{past:"text-black bg-gray-200 hover:bg-gray-300",future:"text-blue-600 bg-blue-50 hover:bg-blue-100"},components:{IconLeft:e=>{let{...a}=e;return(0,s.jsx)(Z.Z,{className:"h-4 w-4"})},IconRight:e=>{let{...a}=e;return(0,s.jsx)(L.Z,{className:"h-4 w-4"})}},...r})}D.displayName="Calendar";var F=t(7427);let P=F.fC,E=F.xz,H=l.forwardRef((e,a)=>{let{className:t,align:l="center",sideOffset:r=4,...n}=e;return(0,s.jsx)(F.h_,{children:(0,s.jsx)(F.VY,{ref:a,align:l,sideOffset:r,className:(0,z.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})})});function M(e){let{date:a,onDateChange:t,placeholder:l="Pick a date",className:r}=e;return(0,s.jsxs)(P,{children:[(0,s.jsx)(E,{asChild:!0,children:(0,s.jsxs)(d.z,{variant:"outline",className:(0,z.cn)("w-[280px] justify-start text-left font-normal",!a&&"text-muted-foreground",r),children:[(0,s.jsx)(h.Z,{className:"mr-2 h-4 w-4"}),a?(0,S.WU)(a,"PPP"):(0,s.jsx)("span",{children:l})]})}),(0,s.jsx)(H,{className:"w-auto p-0",children:(0,s.jsx)(D,{mode:"single",selected:a,onSelect:t,initialFocus:!0})})]})}H.displayName=F.VY.displayName;var A=t(2569),R=t(3348),I=t(9259),V=t(2936);let Y=V.fC;V.xz;let O=V.h_;V.x8;let B=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(V.aV,{ref:a,className:(0,z.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l})});B.displayName=V.aV.displayName;let U=l.forwardRef((e,a)=>{let{className:t,children:l,...r}=e;return(0,s.jsxs)(O,{children:[(0,s.jsx)(B,{}),(0,s.jsxs)(V.VY,{ref:a,className:(0,z.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...r,children:[l,(0,s.jsxs)(V.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(T.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});U.displayName=V.VY.displayName;let q=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,z.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};q.displayName="DialogHeader";let G=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,z.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};G.displayName="DialogFooter";let Q=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(V.Dx,{ref:a,className:(0,z.cn)("text-lg font-semibold leading-none tracking-tight",t),...l})});Q.displayName=V.Dx.displayName;let W=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(V.dk,{ref:a,className:(0,z.cn)("text-sm text-muted-foreground",t),...l})});function K(e){let{isOpen:a,onClose:t,selectedDate:r,onDateSelect:n,onApplyFilter:i,onResetFilter:o}=e,[c,m]=l.useState(r);l.useEffect(()=>{m(r)},[r,a]);let u=e=>{try{m(e)}catch(e){console.error("Error selecting date:",e)}};return(0,s.jsx)(Y,{open:a,onOpenChange:t,children:(0,s.jsxs)(U,{className:"sm:max-w-[480px] p-0 overflow-hidden bg-white rounded-xl shadow-2xl",children:[(0,s.jsxs)(q,{className:"px-6 py-5 bg-gradient-to-r from-blue-600 to-blue-700 text-white",children:[(0,s.jsxs)(Q,{className:"flex items-center gap-3 text-xl font-bold",children:[(0,s.jsx)(h.Z,{className:"h-6 w-6"}),"Select Date Filter"]}),(0,s.jsx)(W,{className:"text-blue-100 mt-2 text-sm",children:"Choose a specific date to filter fixtures, or use quick actions below."})]}),(0,s.jsxs)("div",{className:"px-6 py-6",children:[c&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-1",children:"Selected Date:"}),(0,s.jsx)("div",{className:"text-xl font-bold text-blue-900",children:(0,S.WU)(c,"EEEE, MMMM d, yyyy")}),(0,s.jsx)("div",{className:"text-xs text-blue-600 mt-1",children:c<new Date?"Past date":c.toDateString()===new Date().toDateString()?"Today":"Future date"})]}),(0,s.jsx)(d.z,{variant:"ghost",size:"sm",onClick:()=>u(void 0),className:"text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-lg",children:(0,s.jsx)(T.Z,{className:"h-5 w-5"})})]})}),(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)("div",{className:"bg-white rounded-xl border border-gray-200 shadow-lg p-2",children:(0,s.jsx)(D,{mode:"single",selected:c,onSelect:u,initialFocus:!0,className:"rounded-lg"})})}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 border border-gray-200",children:[(0,s.jsxs)("div",{className:"text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Quick Actions"]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,s.jsx)(d.z,{variant:"outline",size:"sm",onClick:()=>u(new Date),className:"text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200",children:"Today"}),(0,s.jsx)(d.z,{variant:"outline",size:"sm",onClick:()=>{let e=new Date;e.setDate(e.getDate()+1),u(e)},className:"text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200",children:"Tomorrow"}),(0,s.jsx)(d.z,{variant:"outline",size:"sm",onClick:()=>{let e=new Date;e.setDate(e.getDate()+7),u(e)},className:"text-xs font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200",children:"Next Week"})]})]})]}),(0,s.jsxs)(G,{className:"px-6 py-5 bg-gray-50 border-t border-gray-200 flex-col sm:flex-row gap-3",children:[(0,s.jsxs)("div",{className:"flex gap-3 w-full sm:w-auto",children:[(0,s.jsxs)(d.z,{variant:"outline",onClick:()=>{try{m(void 0),o(),n(void 0),t()}catch(e){console.error("Error resetting date filter:",e)}},className:"flex-1 sm:flex-none hover:bg-gray-100 border-gray-300",children:[(0,s.jsx)(R.Z,{className:"h-4 w-4 mr-2"}),"Reset Filter"]}),(0,s.jsx)(d.z,{variant:"outline",onClick:()=>{try{m(r),t()}catch(e){console.error("Error canceling date filter:",e),t()}},className:"flex-1 sm:flex-none hover:bg-gray-100 border-gray-300",children:"Cancel"})]}),(0,s.jsxs)(d.z,{onClick:()=>{try{i(c),n(c),t()}catch(e){console.error("Error applying date filter:",e)}},disabled:!c,className:"w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)(I.Z,{className:"h-4 w-4 mr-2"}),"Apply Filter"]})]})]})})}W.displayName=V.dk.displayName;var $=t(928);function J(){var e,a,t,S,z,Z;let[L,_]=(0,l.useState)(1),[D,F]=(0,l.useState)(25),[P,E]=(0,l.useState)(""),[H,R]=(0,l.useState)(""),[I,V]=(0,l.useState)(""),[Y,O]=(0,l.useState)(""),[B,U]=(0,l.useState)(!1),[q,G]=(0,l.useState)(null),[Q,W]=(0,l.useState)(!1),[J,X]=(0,l.useState)(null),[ee,ea]=(0,l.useState)(void 0),[et,es]=(0,l.useState)(!1),{isAdmin:el,isEditor:er}=(0,k.TE)(),en=(0,r.NL)(),{data:ei,isLoading:eo,error:ed,refetch:ec}=(0,n.a)({queryKey:["fixtures","all",L,D,H,I,Y,ee],queryFn:()=>{let e={page:L,limit:D};return H&&H.trim()&&(e.search=H.trim()),I&&(e.status=I),Y&&(e.league=Y),ee&&(e.date=(0,$.PM)(ee)),x.L.getFixtures(e)},staleTime:3e4,retry:!1,onError:e=>{console.log("API is down, using mock data:",e.message)}}),em=ei||{data:[{id:1,homeTeamName:"Manchester United",awayTeamName:"Liverpool",homeTeamLogo:"/images/teams/1.png",awayTeamLogo:"/images/teams/2.png",date:"2024-12-19T14:30:00Z",status:"NS",leagueName:"Premier League",venue:"Old Trafford"},{id:2,homeTeamName:"Arsenal",awayTeamName:"Chelsea",homeTeamLogo:"/images/teams/3.png",awayTeamLogo:"/images/teams/4.png",date:"2024-12-20T16:00:00Z",status:"NS",leagueName:"Premier League",venue:"Emirates Stadium"},{id:3,homeTeamName:"Barcelona",awayTeamName:"Real Madrid",homeTeamLogo:"/images/teams/5.png",awayTeamLogo:"/images/teams/6.png",date:"2024-12-21T20:00:00Z",status:"LIVE",leagueName:"La Liga",venue:"Camp Nou"},{id:4,homeTeamName:"Bayern Munich",awayTeamName:"Borussia Dortmund",homeTeamLogo:"/images/teams/7.png",awayTeamLogo:"/images/teams/8.png",date:"2024-12-18T18:30:00Z",status:"FT",leagueName:"Bundesliga",venue:"Allianz Arena"},{id:5,homeTeamName:"PSG",awayTeamName:"Marseille",homeTeamLogo:"/images/teams/9.png",awayTeamLogo:"/images/teams/10.png",date:"2024-12-22T21:00:00Z",status:"NS",leagueName:"Ligue 1",venue:"Parc des Princes"}],totalItems:5,totalPages:1,currentPage:1,limit:25},eu=!ei,ex=l.useMemo(()=>{if(!eu||!H.trim())return em;let e=em.data.filter(e=>{var a,t,s,l,r;let n=H.toLowerCase();return(null===(a=e.homeTeamName)||void 0===a?void 0:a.toLowerCase().includes(n))||(null===(t=e.awayTeamName)||void 0===t?void 0:t.toLowerCase().includes(n))||(null===(s=e.leagueName)||void 0===s?void 0:s.toLowerCase().includes(n))||(null===(l=e.venue)||void 0===l?void 0:l.toLowerCase().includes(n))||(null===(r=e.status)||void 0===r?void 0:r.toLowerCase().includes(n))});return{...em,data:e,meta:{...em.meta,totalItems:e.length,totalPages:Math.ceil(e.length/D)},totalItems:e.length,totalPages:Math.ceil(e.length/D)}},[em,H,eu,D]),eh=(0,i.D)({mutationFn:e=>{let a=e.externalId||e.id;return x.L.deleteFixture(a)},onSuccess:()=>{en.invalidateQueries({queryKey:["fixtures"]}),console.log("Fixture deleted successfully"),U(!1),G(null)},onError:e=>{console.error("Failed to delete fixture:",e.message)}}),ef=[{key:"date",title:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{children:"Date & Time"}),(0,s.jsx)(h.Z,{className:"h-4 w-4 text-gray-400"})]}),sortable:!0,render:e=>(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(A.U,{dateTime:e,showDate:!0,showTime:!0,isClickable:!0,onClick:()=>{ea(new Date(e)),es(!0)},className:"min-w-[100px]"})})},{key:"match",title:"Match",sortable:!1,headerClassName:"text-center",render:(e,a)=>(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-4 py-3",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2 min-w-[80px]",children:[a.homeTeamLogo&&(0,s.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(a.homeTeamLogo),alt:a.homeTeamName,className:"w-8 h-8 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsx)("span",{className:"text-xs font-medium text-center leading-tight max-w-[80px] break-words",children:a.homeTeamName})]}),(0,s.jsx)("div",{className:"px-2",children:(0,s.jsx)("span",{className:"text-gray-500 font-bold text-sm",children:"VS"})}),(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2 min-w-[80px]",children:[a.awayTeamLogo&&(0,s.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(a.awayTeamLogo),alt:a.awayTeamName,className:"w-8 h-8 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsx)("span",{className:"text-xs font-medium text-center leading-tight max-w-[80px] break-words",children:a.awayTeamName})]})]})},{key:"score",title:"Score",align:"center",render:(e,a)=>{var t,l;return(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"font-bold text-lg",children:[null!==(t=a.goalsHome)&&void 0!==t?t:"-"," - ",null!==(l=a.goalsAway)&&void 0!==l?l:"-"]}),null!==a.scoreHalftimeHome&&null!==a.scoreHalftimeAway&&(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["HT: ",a.scoreHalftimeHome," - ",a.scoreHalftimeAway]})]})}},{key:"status",title:"Status",sortable:!0,filterable:!0,render:(e,a)=>(0,s.jsx)(c.C,{className:(e=>{switch(e){case"1H":case"2H":case"HT":return"bg-green-100 text-green-800";case"FT":return"bg-gray-100 text-gray-800";case"NS":return"bg-blue-100 text-blue-800";case"CANC":case"PST":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}})(e),children:((e,a)=>{switch(e){case"1H":case"2H":return"".concat(a,"'");case"HT":return"Half Time";case"FT":return"Full Time";case"NS":return"Not Started";case"CANC":return"Cancelled";case"PST":return"Postponed";default:return e}})(e,a.elapsed)})},{key:"leagueName",title:"League",sortable:!0,filterable:!0,render:e=>(0,s.jsx)("span",{className:"text-sm text-gray-600",children:e})},{key:"actions",title:"Actions",render:(e,a)=>(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)(d.z,{size:"sm",variant:"outline",title:"View Details",onClick:()=>eg(a),children:(0,s.jsx)(f.Z,{className:"h-4 w-4"})}),(0,s.jsx)(d.z,{size:"sm",variant:"outline",title:"Broadcast Links",onClick:()=>ey(a),children:(0,s.jsx)(g.Z,{className:"h-4 w-4"})}),(0,s.jsx)(d.z,{size:"sm",variant:"outline",title:"View Broadcast Links",onClick:()=>eb(a),children:(0,s.jsx)(p.Z,{className:"h-4 w-4"})}),er()&&(0,s.jsx)(d.z,{size:"sm",variant:"outline",title:"Edit",onClick:()=>ep(a),children:(0,s.jsx)(N.Z,{className:"h-4 w-4"})}),el()&&(0,s.jsx)(d.z,{size:"sm",variant:"outline",title:"Delete",onClick:()=>eN(a),children:(0,s.jsx)(y.Z,{className:"h-4 w-4"})})]})}],eg=e=>{let a=e.externalId||e.id;window.open("/dashboard/fixtures/".concat(a),"_blank")},ep=e=>{let a=e.externalId||e.id;window.open("/dashboard/fixtures/".concat(a,"/edit"),"_blank")},eN=e=>{G(e),U(!0)},ey=e=>{X(e),W(!0)},eb=e=>{X(e),W(!0)},ej=async()=>{try{console.log("Starting fixtures sync..."),console.log("Fixtures sync completed"),ec()}catch(e){console.error("Sync failed:",e.message)}};return ed?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Fixtures Management"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage football fixtures and match data"})]}),(0,s.jsx)(o.Zb,{children:(0,s.jsx)(o.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load fixtures"}),(0,s.jsxs)(d.z,{onClick:()=>ec(),children:[(0,s.jsx)(b.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})})})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Fixtures Management"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage football fixtures and match data"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(d.z,{variant:"outline",onClick:()=>ec(),disabled:eo,children:[(0,s.jsx)(b.Z,{className:"mr-2 h-4 w-4 ".concat(eo?"animate-spin":"")}),"Refresh"]}),el()&&(0,s.jsxs)(d.z,{variant:"outline",onClick:ej,disabled:eo,children:[(0,s.jsx)(j.Z,{className:"mr-2 h-4 w-4"}),"Sync Data"]}),(0,s.jsxs)(d.z,{variant:"outline",onClick:()=>{console.log("Export feature coming soon")},children:[(0,s.jsx)(w.Z,{className:"mr-2 h-4 w-4"}),"Export"]}),er()&&(0,s.jsxs)(d.z,{onClick:()=>window.open("/dashboard/fixtures/create","_blank"),children:[(0,s.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Add Fixture"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)(o.Zb,{children:(0,s.jsxs)(o.aY,{className:"p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:(null==ei?void 0:null===(a=ei.meta)||void 0===a?void 0:null===(e=a.totalItems)||void 0===e?void 0:e.toLocaleString())||"Loading..."}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Total Fixtures"})]})}),(0,s.jsx)(o.Zb,{children:(0,s.jsxs)(o.aY,{className:"p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(null==ei?void 0:null===(t=ei.data)||void 0===t?void 0:t.filter(e=>["1H","2H","HT"].includes(e.status)).length)||0}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Live Matches"})]})}),(0,s.jsx)(o.Zb,{children:(0,s.jsxs)(o.aY,{className:"p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:(null==ei?void 0:null===(S=ei.data)||void 0===S?void 0:S.filter(e=>"NS"===e.status).length)||0}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Upcoming"})]})}),(0,s.jsx)(o.Zb,{children:(0,s.jsxs)(o.aY,{className:"p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:(null==ei?void 0:null===(z=ei.data)||void 0===z?void 0:z.filter(e=>"FT"===e.status).length)||0}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Completed"})]})})]}),(0,s.jsxs)(o.Zb,{children:[(0,s.jsx)(o.Ol,{children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(o.ll,{className:"flex items-center gap-2",children:[(0,s.jsx)(h.Z,{className:"mr-2 h-5 w-5"}),"All Fixtures",eu&&(0,s.jsx)("span",{className:"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-normal",children:"Demo Mode"})]}),(0,s.jsx)(o.SZ,{children:eu?"Showing demo data - API backend is not available":"Complete list of football fixtures with real-time updates"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(M,{date:ee,onDateChange:ea,placeholder:"Filter by date",className:"w-[200px]"}),ee&&(0,s.jsx)(d.z,{variant:"outline",size:"sm",onClick:()=>ea(void 0),className:"px-2",title:"Clear date filter",children:(0,s.jsx)(T.Z,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)(o.aY,{children:eo?(0,s.jsx)(C.hM,{rows:10,columns:7}):(0,s.jsx)(m.w,{data:(null==ex?void 0:ex.data)||[],columns:ef,loading:eo&&!eu,searchable:!0,searchPlaceholder:"Search fixtures...",showSearchButton:!0,searchValue:P,onSearchChange:E,onSearchSubmit:()=>{R(P.trim()),_(1)},onSearchClear:()=>{E(""),R(""),_(1)},pagination:{page:L,limit:D,total:(null==ex?void 0:null===(Z=ex.meta)||void 0===Z?void 0:Z.totalItems)||(null==ex?void 0:ex.totalItems)||0,onPageChange:_,onLimitChange:e=>{F(e),_(1)}},emptyMessage:"No fixtures found",error:ed&&!eu?ed:null})})]}),(0,s.jsx)(u.sm,{isOpen:B,onClose:()=>{U(!1),G(null)},onConfirm:()=>{q&&eh.mutate(q)},title:"Delete Fixture",message:q?'Are you sure you want to delete the fixture "'.concat(q.homeTeamName," vs ").concat(q.awayTeamName,'"? This action cannot be undone.'):"Are you sure you want to delete this fixture?",confirmText:"Delete",cancelText:"Cancel",variant:"destructive",loading:eh.isPending}),(0,s.jsx)(K,{isOpen:et,onClose:()=>es(!1),selectedDate:ee,onDateSelect:ea,onApplyFilter:e=>{ea(e),_(1)},onResetFilter:()=>{ea(void 0),_(1)}})]})}},8641:function(e,a,t){"use strict";t.d(a,{Bw:function(){return f},Ph:function(){return c},Ql:function(){return g},i4:function(){return u},ki:function(){return m}});var s=t(7437),l=t(2265),r=t(8178),n=t(3441),i=t(5159),o=t(9259),d=t(2169);let c=r.fC;r.ZA;let m=r.B4,u=l.forwardRef((e,a)=>{let{className:t,children:l,...i}=e;return(0,s.jsxs)(r.xz,{ref:a,className:(0,d.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[l,(0,s.jsx)(r.JO,{asChild:!0,children:(0,s.jsx)(n.Z,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=r.xz.displayName;let x=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.u_,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,s.jsx)(i.Z,{className:"h-4 w-4"})})});x.displayName=r.u_.displayName;let h=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.$G,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,s.jsx)(n.Z,{className:"h-4 w-4"})})});h.displayName=r.$G.displayName;let f=l.forwardRef((e,a)=>{let{className:t,children:l,position:n="popper",...i}=e;return(0,s.jsx)(r.h_,{children:(0,s.jsxs)(r.VY,{ref:a,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,s.jsx)(x,{}),(0,s.jsx)(r.l_,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,s.jsx)(h,{})]})})});f.displayName=r.VY.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.__,{ref:a,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",t),...l})}).displayName=r.__.displayName;let g=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsxs)(r.ck,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.wU,{children:(0,s.jsx)(o.Z,{className:"h-4 w-4"})})}),(0,s.jsx)(r.eT,{children:l})]})});g.displayName=r.ck.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.Z0,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...l})}).displayName=r.Z0.displayName}},function(e){e.O(0,[2150,3107,9101,1346,1610,2608,41,1766,1953,8926,7300,6877,1380,5388,2971,8069,1744],function(){return e(e.s=4240)}),_N_E=e.O()}]);