(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3660],{340:function(e,a,l){Promise.resolve().then(l.bind(l,914))},9580:function(e,a,l){"use strict";l.d(a,{Z:function(){return s}});let s=(0,l(7977).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8670:function(e,a,l){"use strict";l.d(a,{Z:function(){return s}});let s=(0,l(7977).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},914:function(e,a,l){"use strict";l.r(a),l.d(a,{default:function(){return S}});var s=l(7437),t=l(2265),r=l(7907),n=l(4095),d=l(1346),i=l(8186),o=l(5671),c=l(575),u=l(6803),m=l(3441),h=l(8670),g=l(9580),x=l(2169);let p=t.forwardRef((e,a)=>{let{label:l,placeholder:r="Select option",value:n,onValueChange:d,options:i=[],error:o,disabled:c=!1,required:u=!1,onSearch:p,isLoading:v=!1,searchPlaceholder:y="Search...",...j}=e,[f,b]=(0,t.useState)(!1),[N,w]=(0,t.useState)(""),T=(0,t.useRef)(null),I=(0,t.useRef)(null),S="http://172.31.213.61";(0,t.useEffect)(()=>{console.log("\uD83D\uDD0D SearchableSelectField state change:",{isOpen:f,searchQuery:N,optionsLength:i.length,placeholder:r})},[f,N,i.length,r]),(0,t.useEffect)(()=>{console.log("\uD83D\uDCCB Options changed for:",{placeholder:r,newOptionsLength:i.length,firstFewOptions:i.slice(0,3).map(e=>e.label)})},[i,r]);let[C,D]=(0,t.useState)(!1);(0,t.useEffect)(()=>{N.trim()&&i.length>0&&(console.log("\uD83D\uDD04 Keeping dropdown open due to search results:",{searchQuery:N,optionsLength:i.length,currentIsOpen:f,placeholder:r}),D(!0),f||b(!0))},[i.length,N,f,r]),(0,t.useEffect)(()=>{let e=e=>{T.current&&!T.current.contains(e.target)&&(console.log("\uD83D\uDDB1️ Click outside detected - closing dropdown"),b(!1),D(!1))};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,t.useEffect)(()=>{f&&I.current&&I.current.focus()},[f]),(0,t.useEffect)(()=>{p&&(N.trim()?p(N):p(""))},[N,p]);let k=i.find(e=>e.value===n),O=e=>{console.log("✅ Option selected - closing dropdown:",e),null==d||d(e),b(!1),w(""),D(!1)};return(0,s.jsxs)("div",{className:"space-y-2",ref:a,children:[l&&(0,s.jsxs)("label",{className:"text-sm font-medium text-gray-700",children:[l,u&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",ref:T,children:[(0,s.jsxs)("button",{type:"button",onClick:()=>{c||(console.log("\uD83D\uDD18 Dropdown button clicked:",{currentIsOpen:f,willBeOpen:!f}),b(!f))},disabled:c,className:(0,x.cn)("w-full flex items-center justify-between px-3 py-2 text-left bg-white border rounded-md shadow-sm","focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",c&&"bg-gray-50 text-gray-500 cursor-not-allowed",o&&"border-red-500 focus:ring-red-500 focus:border-red-500",!o&&!c&&"border-gray-300 hover:border-gray-400"),children:[(0,s.jsx)("div",{className:"flex items-center space-x-2 min-w-0 flex-1",children:k?(0,s.jsxs)(s.Fragment,{children:[k.logo&&(0,s.jsx)("img",{src:"".concat(S,"/").concat(k.logo),alt:k.label,className:"w-5 h-5 object-contain rounded flex-shrink-0",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsx)("span",{className:"truncate",children:k.label})]}):(0,s.jsx)("span",{className:"text-gray-500",children:r})}),(0,s.jsx)(m.Z,{className:(0,x.cn)("w-4 h-4 text-gray-400 transition-transform",f&&"transform rotate-180")})]}),f&&(0,s.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden",children:[(0,s.jsx)("div",{className:"p-2 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.Z,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,s.jsx)("input",{ref:I,type:"text",placeholder:y,value:N,onChange:e=>w(e.target.value),className:"w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]})}),(0,s.jsx)("div",{className:"max-h-60 overflow-y-auto",children:0!==i.length||v?(0,s.jsxs)(s.Fragment,{children:[i.map((e,a)=>(0,s.jsxs)("button",{type:"button",onClick:()=>O(e.value),className:(0,x.cn)("w-full flex items-center space-x-2 px-3 py-2 text-left text-sm hover:bg-gray-50",n===e.value&&"bg-blue-50 text-blue-700"),children:[e.logo&&(0,s.jsx)("img",{src:"".concat(S,"/").concat(e.logo),alt:e.label,className:"w-5 h-5 object-contain rounded flex-shrink-0",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsx)("span",{className:"truncate",children:e.label})]},e.uniqueKey||"".concat(e.value,"-").concat(a))),v&&(0,s.jsxs)("div",{className:"w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm text-gray-500",children:[(0,s.jsx)(g.Z,{className:"w-4 h-4 animate-spin"}),(0,s.jsx)("span",{children:"Searching..."})]})]}):(0,s.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500 text-center",children:"No options found"})})]})]}),o&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o})]})});p.displayName="SearchableSelectField";let v=t.memo(p);var y=l(7625),j=l(2975),f=l(7011),b=l(3016),N=l(3879),w=l(7307),T=l(699),I=l(6288);function S(){let e=(0,r.useParams)(),a=(0,r.useRouter)(),l=(0,n.NL)(),m=parseInt(e.id),[h,g]=(0,t.useState)({homeTeamId:"",awayTeamId:"",leagueId:"",date:"",time:"",venueName:"",venueCity:"",round:"",status:"",goalsHome:"",goalsAway:"",elapsed:""}),[x,p]=(0,t.useState)({}),{data:S,isLoading:C}=(0,d.a)({queryKey:["fixture",m],queryFn:()=>j.L.getFixture(m),enabled:!!m}),[D,k]=(0,t.useState)(""),[O,E]=(0,t.useState)(""),[F,A]=(0,t.useState)([]),[L,q]=(0,t.useState)(""),[P,H]=(0,t.useState)([]),{data:M,isLoading:U,error:Z}=(0,d.a)({queryKey:["leagues"],queryFn:()=>f.A.getLeagues({limit:100})}),{data:K,isLoading:z,error:B}=(0,d.a)({queryKey:["teams"],queryFn:()=>b.k.getTeams({limit:100})});(0,t.useEffect)(()=>{if(!O.trim()){A([]);return}let e=setTimeout(async()=>{try{let e=await b.k.getTeams({limit:100,search:O});(null==e?void 0:e.data)&&A(e.data)}catch(e){console.error("❌ Home team search error:",e),A([])}},3e3);return()=>clearTimeout(e)},[O]),(0,t.useEffect)(()=>{if(!L.trim()){H([]);return}let e=setTimeout(async()=>{try{let e=await b.k.getTeams({limit:100,search:L});(null==e?void 0:e.data)&&H(e.data)}catch(e){console.error("❌ Away team search error:",e),H([])}},3e3);return()=>clearTimeout(e)},[L]);let R=(0,i.D)({mutationFn:e=>j.L.updateFixture(m,e),onSuccess:()=>{l.invalidateQueries({queryKey:["fixture",m]}),l.invalidateQueries({queryKey:["fixtures"]}),I.toast.success("Fixture updated successfully"),a.push("/dashboard/fixtures/".concat(m))},onError:e=>{I.toast.error(e.message||"Failed to update fixture")}});(0,t.useEffect)(()=>{if(S){var e,a,l,s,t,r,n,d,i,o;let c=new Date(S.date);console.log("\uD83D\uDD04 Populating form with fixture data:",{homeTeamId:S.homeTeamId,awayTeamId:S.awayTeamId,leagueId:S.leagueId,venue:S.venue,venueName:S.venueName,venueCity:S.venueCity}),console.log("\uD83D\uDD0D FIXTURE ID ANALYSIS:",{"fixture.homeTeamId":S.homeTeamId,"fixture.awayTeamId":S.awayTeamId,"fixture.leagueId":S.leagueId,"fixture object":S}),g({homeTeamId:(null===(e=S.homeTeamId)||void 0===e?void 0:e.toString())||"",awayTeamId:(null===(a=S.awayTeamId)||void 0===a?void 0:a.toString())||"",leagueId:(null===(l=S.leagueId)||void 0===l?void 0:l.toString())||"",date:c.toISOString().split("T")[0],time:c.toTimeString().slice(0,5),venueName:(null===(s=S.venue)||void 0===s?void 0:s.name)||S.venueName||"",venueCity:(null===(t=S.venue)||void 0===t?void 0:t.city)||S.venueCity||"",round:S.round||"",status:S.status||"",goalsHome:(null===(r=S.goalsHome)||void 0===r?void 0:r.toString())||"",goalsAway:(null===(n=S.goalsAway)||void 0===n?void 0:n.toString())||"",elapsed:(null===(d=S.elapsed)||void 0===d?void 0:d.toString())||"",referee:S.referee||"",temperature:(null===(i=S.temperature)||void 0===i?void 0:i.toString())||"",weather:S.weather||"",attendance:(null===(o=S.attendance)||void 0===o?void 0:o.toString())||""})}},[S]);let V=()=>{let e={};return h.homeTeamId||(e.homeTeamId="Home team is required"),h.awayTeamId||(e.awayTeamId="Away team is required"),h.leagueId||(e.leagueId="League is required"),h.date||(e.date="Date is required"),h.time||(e.time="Time is required"),h.status||(e.status="Status is required"),h.homeTeamId===h.awayTeamId&&(e.awayTeamId="Away team must be different from home team"),p(e),0===Object.keys(e).length},_=(e,a)=>{g(l=>({...l,[e]:a})),x[e]&&p(a=>({...a,[e]:void 0}))},W=(0,t.useCallback)(e=>{k(e)},[]),Y=(0,t.useCallback)(e=>{E(e)},[]),Q=(0,t.useCallback)(e=>{q(e)},[]),G=(0,t.useMemo)(()=>{var e;return(null==M?void 0:null===(e=M.data)||void 0===e?void 0:e.map((e,a)=>({value:e.externalId.toString(),label:"".concat(e.name).concat(e.season?" (".concat(e.season,")"):""),logo:e.logo,season:e.season,uniqueKey:"league-".concat(e.id||e.externalId,"-").concat(a)})))||[]},[null==M?void 0:M.data]),X=(0,t.useMemo)(()=>{var e;return(null==K?void 0:null===(e=K.data)||void 0===e?void 0:e.map((e,a)=>({value:e.externalId.toString(),label:e.name,logo:e.logo,uniqueKey:"team-".concat(e.id||e.externalId,"-").concat(a)})))||[]},[null==K?void 0:K.data]),J=(0,t.useMemo)(()=>F.length>0?F.map((e,a)=>({value:e.externalId.toString(),label:e.name,logo:e.logo,uniqueKey:"search-home-team-".concat(e.id||e.externalId,"-").concat(a)})):X,[X,F]),$=(0,t.useMemo)(()=>P.length>0?P.map((e,a)=>({value:e.externalId.toString(),label:e.name,logo:e.logo,uniqueKey:"search-away-team-".concat(e.id||e.externalId,"-").concat(a)})):X,[X,P]),{league:ee,homeTeam:ea,awayTeam:el}=(()=>{if(!S)return{league:null,homeTeam:null,awayTeam:null};let e=G.find(e=>e.value===h.leagueId),a=J.find(e=>e.value===h.homeTeamId),l=$.find(e=>e.value===h.awayTeamId);return{league:e||{value:h.leagueId,label:S.leagueName,logo:""},homeTeam:a||{value:h.homeTeamId,label:S.homeTeamName,logo:S.homeTeamLogo},awayTeam:l||{value:h.awayTeamId,label:S.awayTeamName,logo:S.awayTeamLogo}}})(),[es,et]=(0,t.useState)(0);(0,t.useEffect)(()=>{J.length>0&&$.length>0&&G.length>0&&h.homeTeamId&&et(e=>e+1)},[J.length,$.length,G.length,h.homeTeamId,h.awayTeamId,h.leagueId]);let er=e=>{let{label:a,selectedOption:l,placeholder:t="Not selected"}=e;return(0,s.jsxs)("div",{className:"mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-2",children:a}),l?(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[l.logo&&(0,s.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(l.logo),alt:l.label,className:"w-8 h-8 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsx)("span",{className:"text-lg font-semibold text-gray-900",children:l.label})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-gray-400 text-xs",children:"?"})}),(0,s.jsx)("span",{className:"text-gray-500 italic",children:t})]})]})};return C||U||z?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(y.Od,{className:"h-10 w-20"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(y.Od,{className:"h-8 w-64"}),(0,s.jsx)(y.Od,{className:"h-4 w-48"})]})]}),(0,s.jsxs)(o.Zb,{children:[(0,s.jsxs)(o.Ol,{children:[(0,s.jsx)(y.Od,{className:"h-6 w-48"}),(0,s.jsx)(y.Od,{className:"h-4 w-64"})]}),(0,s.jsxs)(o.aY,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(y.Od,{className:"h-4 w-32"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(y.Od,{className:"h-10"}),(0,s.jsx)(y.Od,{className:"h-10"})]}),(0,s.jsx)(y.Od,{className:"h-10"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(y.Od,{className:"h-4 w-24"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(y.Od,{className:"h-10"}),(0,s.jsx)(y.Od,{className:"h-10"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)(y.Od,{className:"h-10 w-20"}),(0,s.jsx)(y.Od,{className:"h-10 w-32"})]})]})]})]}):!S||Z||B?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(c.z,{variant:"outline",onClick:()=>a.back(),children:[(0,s.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"Back"]})}),(0,s.jsx)(o.Zb,{children:(0,s.jsx)(o.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[!S&&(0,s.jsx)("p",{className:"text-red-600 mb-4",children:"Fixture not found"}),Z&&(0,s.jsxs)("p",{className:"text-red-600 mb-4",children:["Failed to load leagues: ",Z.message]}),homeTeamsError&&(0,s.jsxs)("p",{className:"text-red-600 mb-4",children:["Failed to load home teams: ",homeTeamsError.message]}),awayTeamsError&&(0,s.jsxs)("p",{className:"text-red-600 mb-4",children:["Failed to load away teams: ",awayTeamsError.message]}),(0,s.jsx)(c.z,{onClick:()=>a.push("/dashboard/fixtures"),children:"Return to Fixtures"})]})})})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(c.z,{variant:"outline",onClick:()=>a.back(),children:[(0,s.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Edit Fixture: ",S.homeTeamName," vs ",S.awayTeamName]}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Update fixture details and match information"})]})]}),(0,s.jsxs)(o.Zb,{children:[(0,s.jsxs)(o.Ol,{children:[(0,s.jsxs)(o.ll,{className:"flex items-center",children:[(0,s.jsx)(w.Z,{className:"mr-2 h-5 w-5"}),"Fixture Details"]}),(0,s.jsx)(o.SZ,{children:"Update the fixture information"})]}),(0,s.jsx)(o.aY,{children:(0,s.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!V()){I.toast.error("Please fix the form errors");return}let a=new Date("".concat(h.date,"T").concat(h.time)),l={homeTeamId:parseInt(h.homeTeamId),awayTeamId:parseInt(h.awayTeamId),leagueId:parseInt(h.leagueId),date:a.toISOString(),venueName:h.venueName||null,venueCity:h.venueCity||null,round:h.round||null,status:h.status,goalsHome:h.goalsHome?parseInt(h.goalsHome):null,goalsAway:h.goalsAway?parseInt(h.goalsAway):null,elapsed:h.elapsed?parseInt(h.elapsed):null,referee:h.referee||null,temperature:h.temperature?parseInt(h.temperature):null,weather:h.weather||null,attendance:h.attendance?parseInt(h.attendance):null};R.mutate(l)},className:"space-y-6",children:[(0,s.jsxs)(u.hj,{title:"Teams & Competition",description:"Select the teams and league",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(er,{label:"Selected Home Team",selectedOption:ea,placeholder:"No home team selected"},"home-".concat(es)),(0,s.jsx)(v,{label:"Home Team",placeholder:z?"Loading teams...":"Select home team",searchPlaceholder:"Search teams... (3s delay)",required:!0,value:h.homeTeamId,onValueChange:e=>_("homeTeamId",e),options:J,error:x.homeTeamId,disabled:z,onSearch:Y,isLoading:z},"home-team-search-stable")]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(er,{label:"Selected Away Team",selectedOption:el,placeholder:"No away team selected"},"away-".concat(es)),(0,s.jsx)(v,{label:"Away Team",placeholder:z?"Loading teams...":"Select away team",searchPlaceholder:"Search teams... (3s delay)",required:!0,value:h.awayTeamId,onValueChange:e=>_("awayTeamId",e),options:$.filter(e=>e.value!==h.homeTeamId),error:x.awayTeamId,disabled:z,onSearch:Q,isLoading:z},"away-team-search-stable")]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(()=>(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"flex items-center space-x-3 min-w-0 flex-1",children:ee?(0,s.jsxs)(s.Fragment,{children:[ee.logo&&(0,s.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(ee.logo),alt:ee.label,className:"w-8 h-8 object-contain rounded flex-shrink-0",onError:e=>{e.currentTarget.style.display="none"}}),(0,s.jsx)("span",{className:"text-lg font-semibold text-gray-900 truncate",children:ee.label})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0",children:(0,s.jsx)("span",{className:"text-gray-400 text-xs",children:"?"})}),(0,s.jsx)("span",{className:"text-gray-500 italic",children:"No league selected"})]})}),(0,s.jsxs)("div",{className:"flex-shrink-0 w-64",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-2",children:"League*"}),(0,s.jsx)(v,{placeholder:U?"Loading leagues...":"Select league",searchPlaceholder:"Search leagues...",required:!0,value:h.leagueId,onValueChange:e=>_("leagueId",e),options:G,error:x.leagueId,disabled:U,onSearch:W,isLoading:U},"league-search-stable")]})]}),{})})]}),(0,s.jsxs)(u.hj,{title:"Schedule",description:"Set the date and time (local timezone)",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(u.UP,{label:"Date *",type:"date",required:!0,value:h.date,onChange:e=>_("date",e.target.value),error:x.date,description:"Match date"}),(0,s.jsx)(u.UP,{label:"Time *",type:"time",required:!0,value:h.time,onChange:e=>_("time",e.target.value),error:x.time,description:"Local time (".concat(Intl.DateTimeFormat().resolvedOptions().timeZone,")")})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200",children:(0,s.jsxs)("p",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-blue-600 mr-2",children:"ℹ️"}),(0,s.jsx)("strong",{children:"Timezone Info:"})," Times are displayed in your local timezone (",Intl.DateTimeFormat().resolvedOptions().timeZone,"). The asterisk (*) indicates required fields."]})})]}),(0,s.jsxs)(u.hj,{title:"Match Status",description:"Update match status and score",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsx)(u.mg,{label:"Status",placeholder:"Select status",required:!0,value:h.status,onValueChange:e=>_("status",e),options:[{value:"TBD",label:"Time To Be Defined"},{value:"NS",label:"Not Started"},{value:"1H",label:"First Half"},{value:"HT",label:"Halftime"},{value:"2H",label:"Second Half"},{value:"ET",label:"Extra Time"},{value:"BT",label:"Break Time"},{value:"P",label:"Penalty In Progress"},{value:"SUSP",label:"Match Suspended"},{value:"INT",label:"Match Interrupted"},{value:"FT",label:"Match Finished"},{value:"AET",label:"Match Finished After Extra Time"},{value:"PEN",label:"Match Finished After Penalty"},{value:"PST",label:"Match Postponed"},{value:"CANC",label:"Match Cancelled"},{value:"ABD",label:"Match Abandoned"},{value:"AWD",label:"Technical Loss"},{value:"WO",label:"WalkOver"}],error:x.status}),(0,s.jsx)(u.UP,{label:"Home Goals",type:"number",min:"0",value:h.goalsHome,onChange:e=>_("goalsHome",e.target.value)}),(0,s.jsx)(u.UP,{label:"Away Goals",type:"number",min:"0",value:h.goalsAway,onChange:e=>_("goalsAway",e.target.value)})]}),(0,s.jsx)(u.UP,{label:"Elapsed Time (minutes)",type:"number",min:"0",max:"120",value:h.elapsed,onChange:e=>_("elapsed",e.target.value),description:"Minutes played in the match"})]}),(0,s.jsxs)(u.hj,{title:"Venue & Match Information",description:"Venue details and match context",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(u.UP,{label:"Venue Name",placeholder:"Stadium name",value:h.venueName,onChange:e=>_("venueName",e.target.value)}),(0,s.jsx)(u.UP,{label:"Venue City",placeholder:"City",value:h.venueCity,onChange:e=>_("venueCity",e.target.value)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(u.UP,{label:"Round",placeholder:"e.g., Matchday 1, Quarter-final",value:h.round,onChange:e=>_("round",e.target.value)}),(0,s.jsx)(u.UP,{label:"Referee",placeholder:"Referee name",value:h.referee||"",onChange:e=>_("referee",e.target.value)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsx)(u.UP,{label:"Temperature (\xb0C)",type:"number",placeholder:"e.g., 22",value:h.temperature||"",onChange:e=>_("temperature",e.target.value)}),(0,s.jsx)(u.UP,{label:"Weather",placeholder:"e.g., Sunny, Rainy",value:h.weather||"",onChange:e=>_("weather",e.target.value)}),(0,s.jsx)(u.UP,{label:"Attendance",type:"number",placeholder:"Number of spectators",value:h.attendance||"",onChange:e=>_("attendance",e.target.value)})]})]}),(0,s.jsxs)(u.iN,{children:[(0,s.jsx)(c.z,{type:"button",variant:"outline",onClick:()=>a.back(),disabled:R.isLoading,children:"Cancel"}),(0,s.jsxs)(c.z,{type:"submit",disabled:R.isLoading,children:[(0,s.jsx)(T.Z,{className:"mr-2 h-4 w-4"}),R.isLoading?"Updating...":"Update Fixture"]})]})]})})]})]})}},7625:function(e,a,l){"use strict";l.d(a,{Od:function(){return r},hM:function(){return d},q4:function(){return n}});var s=l(7437),t=l(2169);function r(e){let{className:a,...l}=e;return(0,s.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",a),...l})}let n=e=>{let{className:a}=e;return(0,s.jsxs)("div",{className:(0,t.cn)("border rounded-lg p-6 space-y-4",a),children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(r,{className:"h-4 w-3/4"}),(0,s.jsx)(r,{className:"h-4 w-1/2"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(r,{className:"h-3 w-full"}),(0,s.jsx)(r,{className:"h-3 w-full"}),(0,s.jsx)(r,{className:"h-3 w-2/3"})]})]})},d=e=>{let{rows:a=5,columns:l=4,className:n}=e;return(0,s.jsx)("div",{className:(0,t.cn)("space-y-4",n),children:(0,s.jsxs)("div",{className:"border rounded-lg",children:[(0,s.jsx)("div",{className:"border-b p-4",children:(0,s.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(l,", 1fr)")},children:Array.from({length:l}).map((e,a)=>(0,s.jsx)(r,{className:"h-4 w-20"},a))})}),Array.from({length:a}).map((e,a)=>(0,s.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,s.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(l,", 1fr)")},children:Array.from({length:l}).map((e,a)=>(0,s.jsx)(r,{className:"h-4 w-full"},a))})},a))]})})}}},function(e){e.O(0,[2150,3107,9101,1346,1610,2608,1766,6288,1637,8140,1880,2971,8069,1744],function(){return e(e.s=340)}),_N_E=e.O()}]);