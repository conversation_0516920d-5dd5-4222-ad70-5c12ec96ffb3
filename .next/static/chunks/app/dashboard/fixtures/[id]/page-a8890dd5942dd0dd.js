(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9447],{2073:function(e,s,a){Promise.resolve().then(a.bind(a,416))},3879:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(7977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6490:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(7977).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},2457:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(7977).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},699:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(7977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9724:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(7977).Z)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6260:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(7977).Z)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},1213:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(7977).Z)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4059:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(7977).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7404:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(7977).Z)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},416:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return X}});var t=a(7437),l=a(2265),r=a(4095),n=a(1346),i=a(8186),c=a(7907),d=a(5671),o=a(575),x=a(7625),u=a(2975),m=a(3879),h=a(834),j=a(5462),y=a(9295),p=a(489),f=a(7307),g=a(6490),N=a(6260),v=a(2457),w=a(4059),b=a(9724),k=a(1546),D=a(3277),C=a(1213),Z=a(2569);function T(e){var s,a;let{fixture:l,className:r}=e;return(0,t.jsx)(d.Zb,{className:r,children:(0,t.jsxs)(d.aY,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-600",children:l.leagueName}),l.round&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["• ",l.round]})]}),(0,t.jsx)(D.C,{className:(e=>{switch(e){case"1H":case"2H":case"HT":return"bg-green-100 text-green-800";case"FT":return"bg-gray-100 text-gray-800";case"NS":return"bg-blue-100 text-blue-800";case"CANC":case"PST":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}})(l.status),children:((e,s)=>{switch(e){case"1H":case"2H":return"".concat(s,"'");case"HT":return"Half Time";case"FT":return"Full Time";case"NS":return"Not Started";case"CANC":return"Cancelled";case"PST":return"Postponed";default:return e}})(l.status,l.elapsed)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-3 flex-1",children:[l.homeTeamLogo&&(0,t.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(l.homeTeamLogo),alt:l.homeTeamName,className:"w-16 h-16 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:l.homeTeamName}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Home"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsxs)("div",{className:"text-4xl font-bold text-gray-900",children:[null!==(s=l.goalsHome)&&void 0!==s?s:"-"," - ",null!==(a=l.goalsAway)&&void 0!==a?a:"-"]}),null!==l.scoreHalftimeHome&&null!==l.scoreHalftimeAway&&(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["HT: ",l.scoreHalftimeHome," - ",l.scoreHalftimeAway]}),(0,t.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"VS"})]}),(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-3 flex-1",children:[l.awayTeamLogo&&(0,t.jsx)("img",{src:"".concat("http://172.31.213.61","/").concat(l.awayTeamLogo),alt:l.awayTeamName,className:"w-16 h-16 object-contain",onError:e=>{e.currentTarget.style.display="none"}}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:l.awayTeamName}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Away"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(f.Z,{className:"h-4 w-4"}),(0,t.jsx)(Z.U,{dateTime:l.date,showDate:!0,showTime:!0,className:"font-medium"})]}),l.venue&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["string"==typeof l.venue?l.venue:l.venue.name,"object"==typeof l.venue&&l.venue.city&&(0,t.jsxs)("span",{className:"text-gray-500",children:[", ",l.venue.city]})]})]}),l.referee&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(C.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Referee: ",l.referee]})]}),l.elapsed&&"NS"!==l.status&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.Z,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[l.elapsed,"' minutes played"]})]})]})]})})}var F=a(7977);let E=(0,F.Z)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),M=(0,F.Z)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),S=(0,F.Z)("flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);function L(e){let{fixture:s}=e,a={possession:{home:65,away:35},shots:{home:12,away:8},shotsOnTarget:{home:6,away:3},corners:{home:7,away:4},fouls:{home:11,away:14},yellowCards:{home:2,away:3},redCards:{home:0,away:1},offsides:{home:3,away:2}},l=e=>{let{label:s,homeValue:a,awayValue:l,icon:r,isPercentage:n=!1}=e,i=a+l,c=i>0?a/i*100:50,d=i>0?l/i*100:50;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"font-medium text-right w-12",children:[a,n?"%":""]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-1 justify-center",children:[(0,t.jsx)(r,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:s})]}),(0,t.jsxs)("span",{className:"font-medium text-left w-12",children:[l,n?"%":""]})]}),(0,t.jsxs)("div",{className:"flex h-2 bg-gray-200 rounded-full overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-blue-500 transition-all duration-300",style:{width:"".concat(c,"%")}}),(0,t.jsx)("div",{className:"bg-red-500 transition-all duration-300",style:{width:"".concat(d,"%")}})]})]})};return(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(E,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Match Statistics"})]})}),(0,t.jsxs)(d.aY,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm font-medium text-gray-600",children:[(0,t.jsx)("span",{className:"w-12 text-right",children:s.homeTeamName}),(0,t.jsx)("span",{className:"flex-1 text-center",children:"Statistics"}),(0,t.jsx)("span",{className:"w-12 text-left",children:s.awayTeamName})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(l,{label:"Possession",homeValue:a.possession.home,awayValue:a.possession.away,icon:E,isPercentage:!0}),(0,t.jsx)(l,{label:"Shots",homeValue:a.shots.home,awayValue:a.shots.away,icon:M}),(0,t.jsx)(l,{label:"Shots on Target",homeValue:a.shotsOnTarget.home,awayValue:a.shotsOnTarget.away,icon:M}),(0,t.jsx)(l,{label:"Corners",homeValue:a.corners.home,awayValue:a.corners.away,icon:S}),(0,t.jsx)(l,{label:"Fouls",homeValue:a.fouls.home,awayValue:a.fouls.away,icon:g.Z}),(0,t.jsx)(l,{label:"Yellow Cards",homeValue:a.yellowCards.home,awayValue:a.yellowCards.away,icon:S}),(0,t.jsx)(l,{label:"Red Cards",homeValue:a.redCards.home,awayValue:a.redCards.away,icon:S}),(0,t.jsx)(l,{label:"Offsides",homeValue:a.offsides.home,awayValue:a.offsides.away,icon:S})]}),(0,t.jsx)("div",{className:"text-xs text-gray-500 text-center pt-4 border-t",children:"* Statistics are updated in real-time during the match"})]})]})}let H=(0,F.Z)("goal",[["path",{d:"M12 13V2l8 4-8 4",key:"5wlwwj"}],["path",{d:"M20.561 10.222a9 9 0 1 1-12.55-5.29",key:"1c0wjv"}],["path",{d:"M8.002 9.997a5 5 0 1 0 8.9 2.02",key:"gb1g7m"}]]),z=(0,F.Z)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var A=a(3348);function V(e){let{fixture:s}=e,a=[{id:1,minute:15,type:"goal",team:"home",player:"Marcus Rashford",description:"Goal",additionalInfo:"Assist: Bruno Fernandes"},{id:2,minute:23,type:"yellow_card",team:"away",player:"Virgil van Dijk",description:"Yellow Card",additionalInfo:"Foul"},{id:3,minute:45,type:"substitution",team:"home",player:"Anthony Martial",description:"Substitution",additionalInfo:"Out: Jadon Sancho"},{id:4,minute:67,type:"goal",team:"away",player:"Mohamed Salah",description:"Goal",additionalInfo:"Assist: Sadio Man\xe9"},{id:5,minute:89,type:"goal",team:"home",player:"Mason Greenwood",description:"Goal",additionalInfo:"Penalty"}],l=e=>{switch(e){case"goal":case"penalty":case"own_goal":return(0,t.jsx)(H,{className:"h-4 w-4"});case"yellow_card":return(0,t.jsx)(b.Z,{className:"h-4 w-4 text-yellow-500"});case"red_card":return(0,t.jsx)(z,{className:"h-4 w-4 text-red-500"});case"substitution":return(0,t.jsx)(A.Z,{className:"h-4 w-4 text-blue-500"});default:return(0,t.jsx)(g.Z,{className:"h-4 w-4"})}},r=e=>{switch(e){case"goal":case"penalty":return"bg-green-100 text-green-800";case"own_goal":return"bg-orange-100 text-orange-800";case"yellow_card":return"bg-yellow-100 text-yellow-800";case"red_card":return"bg-red-100 text-red-800";case"substitution":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},n=e=>{switch(e){case"goal":return"Goal";case"penalty":return"Penalty Goal";case"own_goal":return"Own Goal";case"yellow_card":return"Yellow Card";case"red_card":return"Red Card";case"substitution":return"Substitution";default:return e}};return 0===a.length?(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.Z,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Match Timeline"})]})}),(0,t.jsx)(d.aY,{children:(0,t.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,t.jsx)(g.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No events recorded for this match yet."})]})})]}):(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.Z,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Match Timeline"})]})}),(0,t.jsxs)(d.aY,{children:[(0,t.jsx)("div",{className:"space-y-4",children:a.map((e,i)=>(0,t.jsxs)("div",{className:"relative",children:[i<a.length-1&&(0,t.jsx)("div",{className:"absolute left-6 top-12 w-0.5 h-8 bg-gray-200"}),(0,t.jsxs)("div",{className:"flex items-start space-x-4 ".concat("away"===e.team?"flex-row-reverse space-x-reverse":""),children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsxs)(D.C,{variant:"outline",className:"font-mono",children:[e.minute,"'"]})}),(0,t.jsx)("div",{className:"flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ".concat("home"===e.team?"bg-blue-100":"bg-red-100"),children:l(e.type)}),(0,t.jsxs)("div",{className:"flex-1 ".concat("away"===e.team?"text-right":""),children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(D.C,{className:r(e.type),children:n(e.type)}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"home"===e.team?s.homeTeamName:s.awayTeamName})]}),(0,t.jsx)("p",{className:"font-medium text-gray-900 mt-1",children:e.player}),e.additionalInfo&&(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.additionalInfo})]})]})]},e.id))}),(0,t.jsx)("div",{className:"mt-8 pt-6 border-t",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-8 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsx)("span",{children:"1st Half: 0-45'"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,t.jsx)("span",{children:"2nd Half: 45-90'"})]}),s.elapsed&&s.elapsed>90&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),(0,t.jsx)("span",{children:"Extra Time: 90'+"})]})]})})]})]})}var q=a(2782),O=a(2647),_=a(2235),B=a(94),I=a(699);let P=(0,F.Z)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);var R=a(7404);let Y=(0,F.Z)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var G=a(8763);let U=()=>{let e=G.t.getState(),s=e.accessToken;if(console.log("\uD83D\uDD11 Auth Debug:",{isAuthenticated:e.isAuthenticated,hasToken:!!s,tokenLength:(null==s?void 0:s.length)||0,tokenPreview:(null==s?void 0:s.substring(0,20))+"..."}),!s){console.error("❌ No access token found in auth store!");let e=localStorage.getItem("accessToken");if(e)return console.log("\uD83D\uDD04 Using fallback token from localStorage"),{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}}return{"Content-Type":"application/json",...s&&{Authorization:"Bearer ".concat(s)}}},K={getBroadcastLinks:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,t]=e;void 0!==t&&s.append(a,t.toString())});let a=await fetch("/api/broadcast-links?".concat(s.toString()),{method:"GET",headers:U()});if(!a.ok)throw Error("Failed to fetch broadcast links: ".concat(a.statusText));return await a.json()},getBroadcastLinksByFixture:async e=>{let s=await fetch("/api/broadcast-links/fixture/".concat(e),{method:"GET",headers:U()});if(!s.ok)throw Error("Failed to fetch broadcast links for fixture: ".concat(s.statusText));return await s.json()},getBroadcastLinkById:async e=>{let s=await fetch("/api/broadcast-links/".concat(e),{method:"GET",headers:U()});if(!s.ok)throw Error("Failed to fetch broadcast link: ".concat(s.statusText));return await s.json()},createBroadcastLink:async e=>{let s=await fetch("/api/broadcast-links",{method:"POST",headers:U(),body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to create broadcast link: ".concat(s.statusText));return await s.json()},updateBroadcastLink:async(e,s)=>{let a=await fetch("/api/broadcast-links/".concat(e),{method:"PUT",headers:U(),body:JSON.stringify(s)});if(!a.ok)throw Error("Failed to update broadcast link: ".concat(a.statusText));return await a.json()},deleteBroadcastLink:async e=>{let s=await fetch("/api/broadcast-links/".concat(e),{method:"DELETE",headers:U()});if(!s.ok)throw Error("Failed to delete broadcast link: ".concat(s.statusText))}},Q=e=>{let{isOpen:s,onClose:a,fixture:c}=e,[u,m]=(0,l.useState)(!1),[h,f]=(0,l.useState)(null),[g,N]=(0,l.useState)({title:"",url:"",comment:"",language:"English",quality:"HD"}),v=(0,r.NL)(),{data:w,isLoading:b,error:k}=(0,n.a)({queryKey:["broadcast-links",c.externalId||c.id],queryFn:()=>K.getBroadcastLinksByFixture(c.externalId||c.id),enabled:s}),C=(null==w?void 0:w.data)||[],Z=(0,i.D)({mutationFn:e=>K.createBroadcastLink(e),onSuccess:()=>{v.invalidateQueries({queryKey:["broadcast-links"]}),m(!1),E()},onError:e=>{console.error("Failed to create broadcast link:",e.message)}}),T=(0,i.D)({mutationFn:e=>{let{id:s,data:a}=e;return K.updateBroadcastLink(s,a)},onSuccess:()=>{v.invalidateQueries({queryKey:["broadcast-links"]}),f(null),E()},onError:e=>{console.error("Failed to update broadcast link:",e.message)}}),F=(0,i.D)({mutationFn:e=>K.deleteBroadcastLink(e),onSuccess:()=>{v.invalidateQueries({queryKey:["broadcast-links"]})},onError:e=>{console.error("Failed to delete broadcast link:",e.message)}}),E=()=>{N({title:"",url:"",comment:"",language:"English",quality:"HD"})},M=e=>{f(e),N({title:e.linkName,url:e.linkUrl,comment:e.linkComment||"",language:e.language||"English",quality:e.quality||"HD"}),m(!0)},S=e=>{confirm('Are you sure you want to delete "'.concat(e.linkName,'"?'))&&F.mutate(e.id)},L=()=>{m(!1),f(null),E()},H=e=>{switch(e.toLowerCase()){case"4k":case"uhd":return"bg-purple-100 text-purple-800";case"hd":case"1080p":return"bg-blue-100 text-blue-800";case"sd":case"720p":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},z=e=>({en:"\uD83C\uDDFA\uD83C\uDDF8",es:"\uD83C\uDDEA\uD83C\uDDF8",fr:"\uD83C\uDDEB\uD83C\uDDF7",de:"\uD83C\uDDE9\uD83C\uDDEA",it:"\uD83C\uDDEE\uD83C\uDDF9",pt:"\uD83C\uDDF5\uD83C\uDDF9",ar:"\uD83C\uDDF8\uD83C\uDDE6"})[e]||"\uD83C\uDF10";return s?(0,t.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto",children:[(0,t.jsx)("div",{className:"p-6 border-b",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold flex items-center",children:[(0,t.jsx)(j.Z,{className:"mr-2 h-5 w-5"}),"Broadcast Links - ",c.homeTeamName," vs ",c.awayTeamName]}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage streaming links for this fixture"})]}),(0,t.jsx)(o.z,{variant:"outline",size:"sm",onClick:a,children:(0,t.jsx)(_.Z,{className:"h-4 w-4"})})]})}),(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[!u&&(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[C.length," broadcast link",1!==C.length?"s":""," available"]}),(0,t.jsxs)(o.z,{onClick:()=>m(!0),children:[(0,t.jsx)(B.Z,{className:"h-4 w-4 mr-2"}),"Add Link"]})]}),u&&(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(d.ll,{className:"text-lg",children:h?"Edit Broadcast Link":"Add New Broadcast Link"}),(0,t.jsx)(o.z,{type:"button",variant:"outline",size:"sm",onClick:L,children:(0,t.jsx)(_.Z,{className:"h-4 w-4"})})]})}),(0,t.jsx)(d.aY,{children:(0,t.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!g.title.trim()||!g.url.trim()||!g.comment.trim())return;let s={fixtureId:c.externalId||c.id,linkName:g.title.trim(),linkUrl:g.url.trim(),linkComment:g.comment.trim(),language:g.language,quality:g.quality};h?T.mutate({id:h.id,data:s}):Z.mutate(s)},className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(O._,{htmlFor:"title",children:"Title"}),(0,t.jsx)(q.I,{id:"title",value:g.title,onChange:e=>N({...g,title:e.target.value}),placeholder:"e.g., ESPN HD Stream",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(O._,{htmlFor:"url",children:"URL"}),(0,t.jsx)(q.I,{id:"url",type:"url",value:g.url,onChange:e=>N({...g,url:e.target.value}),placeholder:"https://...",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(O._,{htmlFor:"language",children:"Language"}),(0,t.jsxs)("select",{id:"language",value:g.language,onChange:e=>N({...g,language:e.target.value}),className:"w-full p-2 border rounded",children:[(0,t.jsx)("option",{value:"en",children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,t.jsx)("option",{value:"es",children:"\uD83C\uDDEA\uD83C\uDDF8 Spanish"}),(0,t.jsx)("option",{value:"fr",children:"\uD83C\uDDEB\uD83C\uDDF7 French"}),(0,t.jsx)("option",{value:"de",children:"\uD83C\uDDE9\uD83C\uDDEA German"}),(0,t.jsx)("option",{value:"it",children:"\uD83C\uDDEE\uD83C\uDDF9 Italian"}),(0,t.jsx)("option",{value:"pt",children:"\uD83C\uDDF5\uD83C\uDDF9 Portuguese"}),(0,t.jsx)("option",{value:"ar",children:"\uD83C\uDDF8\uD83C\uDDE6 Arabic"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(O._,{htmlFor:"quality",children:"Quality"}),(0,t.jsxs)("select",{id:"quality",value:g.quality,onChange:e=>N({...g,quality:e.target.value}),className:"w-full p-2 border rounded",children:[(0,t.jsx)("option",{value:"4K",children:"4K Ultra HD"}),(0,t.jsx)("option",{value:"HD",children:"HD (1080p)"}),(0,t.jsx)("option",{value:"720p",children:"HD (720p)"}),(0,t.jsx)("option",{value:"SD",children:"SD (480p)"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(O._,{htmlFor:"comment",children:"Comment"}),(0,t.jsx)(q.I,{id:"comment",value:g.comment,onChange:e=>N({...g,comment:e.target.value}),placeholder:"e.g., Official HD stream with English commentary",required:!0})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,t.jsx)(o.z,{type:"button",variant:"outline",onClick:L,children:"Cancel"}),(0,t.jsxs)(o.z,{type:"submit",disabled:Z.isLoading||T.isLoading,children:[(0,t.jsx)(I.Z,{className:"h-4 w-4 mr-2"}),h?"Update":"Add"," Link"]})]})]})})]}),(0,t.jsx)("div",{className:"space-y-4",children:b?(0,t.jsx)(x.hM,{rows:3,columns:1}):k?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load broadcast links"}),(0,t.jsx)(o.z,{onClick:()=>console.log("Mock: Retry loading"),children:"Try Again"})]}):0===C.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(j.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"No broadcast links added yet"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Add a link to get started"}),!u&&(0,t.jsxs)(o.z,{onClick:()=>m(!0),className:"mt-4",children:[(0,t.jsx)(B.Z,{className:"h-4 w-4 mr-2"}),"Add First Link"]})]}):C.map(e=>(0,t.jsx)(d.Zb,{children:(0,t.jsx)(d.aY,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[e.linkName.toLowerCase().includes("comment")||e.linkName.toLowerCase().includes("chat")?(0,t.jsx)(P,{className:"h-4 w-4 text-blue-600"}):(0,t.jsx)(j.Z,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)("span",{className:"font-medium",children:e.linkName})]}),(0,t.jsxs)(D.C,{className:H(e.quality||"HD"),children:[(0,t.jsx)(R.Z,{className:"mr-1 h-3 w-3"}),e.quality||"HD"]}),(0,t.jsxs)(D.C,{variant:"outline",children:[z(e.language||"English")," ",e.language||"English"]}),(0,t.jsx)(D.C,{variant:"outline",className:"bg-green-50 text-green-700",children:"Active"})]}),(0,t.jsx)("div",{className:"text-sm text-gray-600 flex items-center space-x-2",children:(0,t.jsxs)("a",{href:e.linkUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 flex items-center space-x-1 truncate max-w-[300px]",children:[(0,t.jsx)("span",{className:"truncate",children:e.linkUrl}),(0,t.jsx)(Y,{className:"h-3 w-3 flex-shrink-0"})]})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(o.z,{size:"sm",variant:"outline",onClick:()=>M(e),disabled:(null==h?void 0:h.id)===e.id,children:(0,t.jsx)(y.Z,{className:"h-4 w-4"})}),(0,t.jsx)(o.z,{size:"sm",variant:"outline",onClick:()=>S(e),disabled:F.isLoading,children:(0,t.jsx)(p.Z,{className:"h-4 w-4"})})]})]})})},e.id))})]})]})}):null};var J=a(4133),W=a(6288);function X(){let e=(0,c.useParams)(),s=(0,c.useRouter)(),a=(0,r.NL)(),D=parseInt(e.id),[C,Z]=(0,l.useState)(!1),[F,E]=(0,l.useState)(!1),{isEditor:M,isAdmin:S}=(0,k.TE)(),{data:H,isLoading:z,error:A,refetch:q}=(0,n.a)({queryKey:["fixture",D],queryFn:()=>u.L.getFixture(D),enabled:!!D}),O=(0,i.D)({mutationFn:()=>u.L.deleteFixture(D),onSuccess:()=>{a.invalidateQueries({queryKey:["fixtures"]}),W.toast.success("Fixture deleted successfully"),E(!1),s.push("/dashboard/fixtures")},onError:e=>{W.toast.error(e.message||"Failed to delete fixture"),E(!1)}});return z?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(x.Od,{className:"h-10 w-10"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.Od,{className:"h-8 w-64"}),(0,t.jsx)(x.Od,{className:"h-4 w-48"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)(x.Od,{className:"h-64"}),(0,t.jsx)(x.Od,{className:"h-48"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(x.Od,{className:"h-32"}),(0,t.jsx)(x.Od,{className:"h-48"})]})]})]}):A||!H?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>s.back(),children:[(0,t.jsx)(m.Z,{className:"mr-2 h-4 w-4"}),"Back"]})}),(0,t.jsx)(d.Zb,{children:(0,t.jsx)(d.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-red-600 mb-4",children:"Failed to load fixture details"}),(0,t.jsx)(o.z,{onClick:()=>s.push("/dashboard/fixtures"),children:"Return to Fixtures"})]})})})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>s.back(),children:[(0,t.jsx)(m.Z,{className:"mr-2 h-4 w-4"}),"Back to Fixtures"]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[H.homeTeamName," vs ",H.awayTeamName]}),(0,t.jsxs)("p",{className:"text-gray-600 mt-1",children:[H.leagueName," • Fixture Details"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>q(),children:[(0,t.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]}),M()&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>{Z(!0)},children:[(0,t.jsx)(j.Z,{className:"h-4 w-4 mr-2"}),"Broadcast Links"]}),(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>{s.push("/dashboard/fixtures/".concat(D,"/edit"))},children:[(0,t.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Edit"]})]}),S()&&(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>{E(!0)},children:[(0,t.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)(T,{fixture:H}),(0,t.jsx)(V,{fixture:H})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(L,{fixture:H}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsxs)(d.ll,{className:"flex items-center",children:[(0,t.jsx)(f.Z,{className:"mr-2 h-5 w-5"}),"Match Information"]})}),(0,t.jsxs)(d.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(f.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Date"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:new Date(H.date).toLocaleDateString()})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(g.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Time"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:new Date(H.date).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(N.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"League"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:H.leagueName})]})]}),H.venueName&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(v.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"Venue"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:H.venueName})]})]})]})]}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsx)(d.Ol,{children:(0,t.jsx)(d.ll,{children:"Quick Actions"})}),(0,t.jsxs)(d.aY,{className:"space-y-2",children:[(0,t.jsxs)(o.z,{variant:"outline",className:"w-full justify-start",onClick:()=>window.open("/dashboard/leagues/".concat(H.leagueId),"_blank"),children:[(0,t.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"View League"]}),(0,t.jsxs)(o.z,{variant:"outline",className:"w-full justify-start",onClick:()=>window.open("/dashboard/teams/".concat(H.homeTeamId),"_blank"),children:[(0,t.jsx)(w.Z,{className:"mr-2 h-4 w-4"}),"View Home Team"]}),(0,t.jsxs)(o.z,{variant:"outline",className:"w-full justify-start",onClick:()=>window.open("/dashboard/teams/".concat(H.awayTeamId),"_blank"),children:[(0,t.jsx)(w.Z,{className:"mr-2 h-4 w-4"}),"View Away Team"]})]})]})]})]}),(0,t.jsx)(Q,{isOpen:C,onClose:()=>Z(!1),fixture:H}),(0,t.jsx)(J.u_,{isOpen:F,onClose:()=>E(!1),title:"Delete Fixture",description:"Are you sure you want to delete this fixture? This action cannot be undone.",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[(0,t.jsx)(b.Z,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-red-800",children:"This will permanently delete the fixture:"}),(0,t.jsx)("p",{className:"text-sm text-red-700 mt-1",children:(0,t.jsxs)("strong",{children:[H.homeTeamName," vs ",H.awayTeamName]})}),(0,t.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:[new Date(H.date).toLocaleDateString()," • ",H.leagueName]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)(o.z,{variant:"outline",onClick:()=>E(!1),disabled:O.isPending,children:"Cancel"}),(0,t.jsx)(o.z,{variant:"destructive",onClick:()=>{O.mutate()},disabled:O.isPending,children:O.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.Z,{className:"mr-2 h-4 w-4"}),"Delete Fixture"]})})]})]})})]})}},3277:function(e,s,a){"use strict";a.d(s,{C:function(){return i}});var t=a(7437);a(2265);var l=a(9769),r=a(2169);let n=(0,l.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:a,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)(n({variant:a}),s),...l})}},2647:function(e,s,a){"use strict";a.d(s,{_:function(){return d}});var t=a(7437),l=a(2265),r=a(4602),n=a(9769),i=a(2169);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.f,{ref:s,className:(0,i.cn)(c(),a),...l})});d.displayName=r.f.displayName},4602:function(e,s,a){"use strict";a.d(s,{f:function(){return i}});var t=a(2265),l=a(9586),r=a(7437),n=t.forwardRef((e,s)=>(0,r.jsx)(l.WV.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var i=n},1780:function(e,s,a){"use strict";a.d(s,{C2:function(){return n},fC:function(){return c}});var t=a(2265),l=a(9586),r=a(7437),n=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=t.forwardRef((e,s)=>(0,r.jsx)(l.WV.span,{...e,ref:s,style:{...n,...e.style}}));i.displayName="VisuallyHidden";var c=i}},function(e){e.O(0,[2150,3107,9101,1346,1610,41,6288,1953,8926,6877,5388,2971,8069,1744],function(){return e(e.s=2073)}),_N_E=e.O()}]);