(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4171],{2982:function(e,t,a){Promise.resolve().then(a.bind(a,9233))},9233:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return h}});var s=a(7437),n=a(2265),r=a(5671),o=a(575),i=a(3277),c=a(4921),l=a(773),u=a(2975),d=a(7011);function h(){let[e,t]=(0,n.useState)([]),[a,h]=(0,n.useState)(!1),f=(e,a)=>{t(t=>t.find(t=>t.endpoint===e)?t.map(t=>t.endpoint===e?{...t,...a}:t):[...t,{endpoint:e,status:"pending",...a}])},g=async(e,t)=>{let a=Date.now();f(e,{status:"pending"});try{let s=await t(),n=Date.now()-a;f(e,{status:"success",data:s,duration:n})}catch(s){let t=Date.now()-a;f(e,{status:"error",error:s.message||"Unknown error",duration:t})}},m=async()=>{h(!0),t([]),await g("API Documentation",async()=>{var e,t;let a=await c.x.get("/api-docs-json");return{title:(null===(e=a.info)||void 0===e?void 0:e.title)||"APISportsGame API",version:(null===(t=a.info)||void 0===t?void 0:t.version)||"1.0.0",endpoints:Object.keys(a.paths||{}).length}}),await g("Public Fixtures",async()=>{var e,t,a,s,n,r,o;let i=await u.L.getUpcomingAndLive({limit:3});return{totalFixtures:(null===(e=i.data)||void 0===e?void 0:e.length)||0,liveMatches:(null===(t=i.data)||void 0===t?void 0:t.filter(e=>["1H","2H","HT"].includes(e.status)).length)||0,upcomingMatches:(null===(a=i.data)||void 0===a?void 0:a.filter(e=>"NS"===e.status).length)||0,sampleFixture:(null===(n=i.data)||void 0===n?void 0:null===(s=n[0])||void 0===s?void 0:s.homeTeamName)+" vs "+(null===(o=i.data)||void 0===o?void 0:null===(r=o[0])||void 0===r?void 0:r.awayTeamName)||"No fixtures"}}),await g("Public Leagues",async()=>{var e,t,a,s;let n=await d.A.getLeagues({limit:3});return{totalLeagues:(null===(e=n.meta)||void 0===e?void 0:e.totalItems)||0,currentPage:(null===(t=n.meta)||void 0===t?void 0:t.currentPage)||1,sampleLeague:(null===(s=n.data)||void 0===s?void 0:null===(a=s[0])||void 0===a?void 0:a.name)||"No leagues"}}),await g("System Auth Login",async()=>{let e=await l.i.login({username:"admin",password:"admin123456"});return{username:e.user.username,role:e.user.role,email:e.user.email,tokenLength:e.accessToken.length}}),h(!1)},p=e=>{switch(e){case"success":return"bg-green-100 text-green-800";case"error":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},x=e=>{switch(e){case"success":return"✅";case"error":return"❌";case"pending":return"⏳";default:return"⚪"}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"API Connection Test"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Test connection to APISportsGame API endpoints"})]}),(0,s.jsxs)(r.Zb,{children:[(0,s.jsxs)(r.Ol,{children:[(0,s.jsx)(r.ll,{children:"API Configuration"}),(0,s.jsx)(r.SZ,{children:"Current API settings and connection details"})]}),(0,s.jsx)(r.aY,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"font-medium",children:"Base URL:"}),(0,s.jsx)("span",{className:"text-gray-600",children:"http://localhost:3000"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"font-medium",children:"Timeout:"}),(0,s.jsx)("span",{className:"text-gray-600",children:"30 seconds"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"font-medium",children:"Auth Token:"}),(0,s.jsx)("span",{className:"text-gray-600",children:localStorage.getItem("accessToken")?"✅ Present":"❌ Not found"})]})]})})]}),(0,s.jsxs)(r.Zb,{children:[(0,s.jsxs)(r.Ol,{children:[(0,s.jsx)(r.ll,{children:"Test Results"}),(0,s.jsx)(r.SZ,{children:"API endpoint connectivity tests"})]}),(0,s.jsx)(r.aY,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(o.z,{onClick:m,disabled:a,className:"w-full",children:a?"Running Tests...":"Run All Tests"}),e.length>0&&(0,s.jsx)("div",{className:"space-y-3",children:e.map((e,t)=>(0,s.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-lg",children:x(e.status)}),(0,s.jsx)("span",{className:"font-medium",children:e.endpoint})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.C,{className:p(e.status),children:e.status}),e.duration&&(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[e.duration,"ms"]})]})]}),e.error&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded p-2 mt-2",children:[(0,s.jsx)("p",{className:"text-red-800 text-sm font-medium",children:"Error:"}),(0,s.jsx)("p",{className:"text-red-700 text-sm",children:e.error})]}),e.data&&"success"===e.status&&(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded p-2 mt-2",children:[(0,s.jsx)("p",{className:"text-green-800 text-sm font-medium",children:"Response:"}),(0,s.jsxs)("pre",{className:"text-green-700 text-xs mt-1 overflow-x-auto",children:[JSON.stringify(e.data,null,2).substring(0,200),JSON.stringify(e.data,null,2).length>200?"...":""]})]})]},t))})]})})]})]})}},3277:function(e,t,a){"use strict";a.d(t,{C:function(){return i}});var s=a(7437);a(2265);var n=a(9769),r=a(2169);let o=(0,n.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...n}=e;return(0,s.jsx)("div",{className:(0,r.cn)(o({variant:a}),t),...n})}},575:function(e,t,a){"use strict";a.d(t,{d:function(){return c},z:function(){return l}});var s=a(7437),n=a(2265),r=a(9143),o=a(9769),i=a(2169);let c=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef((e,t)=>{let{className:a,variant:n,size:o,asChild:l=!1,...u}=e,d=l?r.g7:"button";return(0,s.jsx)(d,{className:(0,i.cn)(c({variant:n,size:o,className:a})),ref:t,...u})});l.displayName="Button"},5671:function(e,t,a){"use strict";a.d(t,{Ol:function(){return i},SZ:function(){return l},Zb:function(){return o},aY:function(){return u},ll:function(){return c}});var s=a(7437),n=a(2265),r=a(2169);let o=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,r.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...n})});o.displayName="Card";let i=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",a),...n})});i.displayName="CardHeader";let c=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,r.cn)("font-semibold leading-none tracking-tight",a),...n})});c.displayName="CardTitle";let l=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",a),...n})});l.displayName="CardDescription";let u=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",a),...n})});u.displayName="CardContent",n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",a),...n})}).displayName="CardFooter"},773:function(e,t,a){"use strict";a.d(t,{i:function(){return r}});var s=a(4921),n=a(8763);let r={login:async e=>{console.log("\uD83D\uDD10 Attempting login via proxy...");try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.message||"Login failed")}let a=await t.json();console.log("✅ Login successful via proxy");let s=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.accessToken)}});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to fetch profile")}return{user:await s.json(),accessToken:a.accessToken,refreshToken:a.refreshToken}}catch(t){if(console.error("❌ Login failed via proxy:",t.message),(t.message.includes("fetch")||t.message.includes("network"))&&(console.warn("⚠️ Network error, using mock data"),"admin"===e.username&&"admin123456"===e.password)){let e={user:{id:1,username:"admin",email:"<EMAIL>",fullName:"System Administrator",role:"admin",isActive:!0,lastLoginAt:new Date().toISOString(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},accessToken:"mock-access-token-"+Date.now(),refreshToken:"mock-refresh-token-"+Date.now()};return await new Promise(e=>setTimeout(e,500)),e}throw t}},logout:async e=>{let t=await fetch("/api/auth/logout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error((await t.json()).message||"Logout failed");return await t.json()},logoutFromAllDevices:async()=>await s.x.post("/system-auth/logout-all"),refreshToken:async e=>await s.x.post("/system-auth/refresh",{refreshToken:e}),getProfile:async()=>{let e=n.t.getState(),t=e.accessToken,a=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}});if(!a.ok){if(401===a.status)throw console.warn("⚠️ Token expired, forcing logout..."),e.clearAuth(),window.location.href="/auth/login",Error("Token expired, please login again");throw Error((await a.json()).message||"Failed to fetch profile")}return await a.json()},updateProfile:async e=>await s.x.put("/system-auth/profile",e),changePassword:async e=>await s.x.post("/system-auth/change-password",e),createUser:async e=>await s.x.post("/system-auth/users",e),updateUser:async(e,t)=>await s.x.put("/system-auth/users/".concat(e),t)}},4921:function(e,t,a){"use strict";a.d(t,{x:function(){return r}});var s=a(3107);class n{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async patch(e,t,a){return(await this.client.patch(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.baseURL="http://localhost:3000",this.client=s.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with baseURL:",this.baseURL)}}let r=new n},2975:function(e,t,a){"use strict";a.d(t,{L:function(){return n}});var s=a(4921);let n={getFixtures:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/fixtures?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch fixtures: ".concat(a.statusText));return await a.json()},getFixtureById:async e=>{let t=await fetch("/api/fixtures/".concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch fixture: ".concat(t.statusText));return await t.json()},getUpcomingAndLive:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/fixtures/live?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch live fixtures: ".concat(a.statusText));return await a.json()},getTeamSchedule:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,s]=e;void 0!==s&&a.append(t,s.toString())}),await s.x.get("/football/fixtures/schedules/".concat(e,"?").concat(a.toString()))},getFixtureStatistics:async e=>await s.x.get("/football/fixtures/statistics/".concat(e)),triggerSeasonSync:async()=>await s.x.get("/football/fixtures/sync/fixtures"),triggerDailySync:async()=>await s.x.get("/football/fixtures/sync/daily"),getSyncStatus:async()=>await s.x.get("/football/fixtures/sync/status"),createFixture:async e=>{let t=await fetch("/api/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create fixture: ".concat(t.statusText));return await t.json()},updateFixture:async(e,t)=>{let a=await fetch("/api/fixtures/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok)throw Error("Failed to update fixture: ".concat(a.statusText));return await a.json()},deleteFixture:async e=>{let t=(()=>{{try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t),s=null===(e=a.state)||void 0===e?void 0:e.accessToken;if(s)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",s.substring(0,20)+"..."),{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)}}}catch(e){console.warn("Failed to parse auth storage:",e)}let t=localStorage.getItem("accessToken");if(t)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}}return console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"}})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:e,hasAuth:!!t.Authorization});let a=await fetch("/api/fixtures/".concat(e),{method:"DELETE",headers:t});if(!a.ok){let e=await a.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",a.status,a.statusText,e),Error(e.message||"Failed to delete fixture: ".concat(a.statusText))}console.log("✅ Delete fixture successful:",e)},getFixture:async e=>(await n.getFixtureById(e)).data}},7011:function(e,t,a){"use strict";a.d(t,{A:function(){return n}});var s=a(4921);let n={getLeagues:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,s]=e;void 0!==s&&t.append(a,s.toString())});let a=await fetch("/api/leagues?".concat(t.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(e,t)=>await s.x.get("/football/leagues/".concat(e).concat(t?"?season=".concat(t):"")),createLeague:async e=>await s.x.post("/football/leagues",e),updateLeague:async(e,t)=>await s.x.patch("/football/leagues/".concat(e),t),getActiveLeagues:async()=>n.getLeagues({active:!0}),getLeaguesByCountry:async e=>n.getLeagues({country:e}),toggleLeagueStatus:async(e,t)=>n.updateLeague(e,{active:t})}},8763:function(e,t,a){"use strict";a.d(t,{t:function(){return o}});var s=a(2574),n=a(5249);let r={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},o=(0,s.U)()((0,n.tJ)((e,t)=>({...r,setAuth:(t,a,s)=>{e({user:t,accessToken:a,refreshToken:s,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(r)},setLoading:t=>{e({isLoading:t})},updateUser:a=>{let s=t().user;s&&e({user:{...s,...a}})}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},2169:function(e,t,a){"use strict";a.d(t,{cn:function(){return r}});var s=a(3167),n=a(1367);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.m6)((0,s.W)(t))}}},function(e){e.O(0,[2150,3107,7591,2971,8069,1744],function(){return e(e.s=2982)}),_N_E=e.O()}]);