(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3455],{9800:function(n,e,t){Promise.resolve().then(t.bind(t,6938))},6938:function(n,e,t){"use strict";t.r(e),t.d(e,{default:function(){return r}});var i=t(7437),s=t(6123);function r(){return(0,i.jsx)(s.G,{title:"System Settings",description:"Configure application settings and preferences.",iconName:"settings"})}}},function(n){n.O(0,[2150,9459,6123,2971,8069,1744],function(){return n(n.s=9800)}),_N_E=n.O()}]);