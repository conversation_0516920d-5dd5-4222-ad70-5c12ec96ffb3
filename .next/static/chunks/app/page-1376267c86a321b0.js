(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1931],{5700:function(e,n,u){Promise.resolve().then(u.bind(u,8836))},8836:function(e,n,u){"use strict";u.r(n),u.d(n,{default:function(){return f}});var t=u(7437),s=u(2265),r=u(7907),i=u(7786),c=u(6146);function f(){let e=(0,r.useRouter)(),{isAuthenticated:n,isLoading:u}=(0,i.a)();return(0,s.useEffect)(()=>{u||(n?e.push("/dashboard"):e.push("/auth/login"))},[n,u,e]),(0,t.jsx)(c.SX,{message:"Redirecting..."})}}},function(e){e.O(0,[2150,3107,9101,1346,41,6877,2971,8069,1744],function(){return e(e.s=5700)}),_N_E=e.O()}]);