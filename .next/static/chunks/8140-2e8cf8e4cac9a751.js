"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8140],{575:function(e,r,t){t.d(r,{d:function(){return i},z:function(){return o}});var a=t(7437),s=t(2265),n=t(9143),l=t(9769),d=t(2169);let i=(0,l.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,r)=>{let{className:t,variant:s,size:l,asChild:o=!1,...c}=e,f=o?n.g7:"button";return(0,a.jsx)(f,{className:(0,d.cn)(i({variant:s,size:l,className:t})),ref:r,...c})});o.displayName="Button"},5671:function(e,r,t){t.d(r,{Ol:function(){return d},SZ:function(){return o},Zb:function(){return l},aY:function(){return c},ll:function(){return i}});var a=t(7437),s=t(2265),n=t(2169);let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...s})});l.displayName="Card";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...s})});d.displayName="CardHeader";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("font-semibold leading-none tracking-tight",t),...s})});i.displayName="CardTitle";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...s})}).displayName="CardFooter"},6803:function(e,r,t){t.d(r,{ji:function(){return j},iN:function(){return w},hj:function(){return v},UP:function(){return N},mg:function(){return y},XL:function(){return g}});var a=t(7437),s=t(2265),n=t(2647),l=t(2782),d=t(2169);let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("textarea",{className:(0,d.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...s})});i.displayName="Textarea";var o=t(8641),c=t(6969),f=t(9259);let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(c.fC,{ref:r,className:(0,d.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...s,children:(0,a.jsx)(c.z$,{className:(0,d.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(f.Z,{className:"h-4 w-4"})})})});u.displayName=c.fC.displayName;var m=t(8928),x=t(7501);let p=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(m.fC,{className:(0,d.cn)("grid gap-2",t),...s,ref:r})});p.displayName=m.fC.displayName;let h=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(m.ck,{ref:r,className:(0,d.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),...s,children:(0,a.jsx)(m.z$,{className:"flex items-center justify-center",children:(0,a.jsx)(x.Z,{className:"h-3.5 w-3.5 fill-primary"})})})});h.displayName=m.ck.displayName;let b=(0,s.forwardRef)((e,r)=>{let{label:t,description:s,error:l,required:i,className:o,children:c}=e;return(0,a.jsxs)("div",{ref:r,className:(0,d.cn)("space-y-2",o),children:[t&&(0,a.jsxs)(n._,{className:(0,d.cn)("text-sm font-medium",l&&"text-red-600"),children:[t,i&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),c,s&&!l&&(0,a.jsx)("p",{className:"text-sm text-gray-500",children:s}),l&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:l})]})});b.displayName="FormField";let N=(0,s.forwardRef)((e,r)=>{let{label:t,description:s,error:n,required:i,className:o,...c}=e;return(0,a.jsx)(b,{label:t,description:s,error:n,required:i,children:(0,a.jsx)(l.I,{ref:r,className:(0,d.cn)(n&&"border-red-500 focus:border-red-500",o),...c})})});N.displayName="InputField";let g=(0,s.forwardRef)((e,r)=>{let{label:t,description:s,error:n,required:l,className:o,...c}=e;return(0,a.jsx)(b,{label:t,description:s,error:n,required:l,children:(0,a.jsx)(i,{ref:r,className:(0,d.cn)(n&&"border-red-500 focus:border-red-500",o),...c})})});g.displayName="TextareaField";let y=(0,s.forwardRef)((e,r)=>{let{label:t,description:s,error:n,required:l,placeholder:i,value:c,onValueChange:f,options:u,className:m,disabled:x}=e,p=u.find(e=>e.value===c),h="http://*************";return(0,a.jsx)(b,{label:t,description:s,error:n,required:l,children:(0,a.jsxs)(o.Ph,{value:c,onValueChange:f,disabled:x,children:[(0,a.jsx)(o.i4,{ref:r,className:(0,d.cn)(n&&"border-red-500 focus:border-red-500",m),children:(0,a.jsx)("div",{className:"flex items-center justify-between w-full",children:(0,a.jsx)("div",{className:"flex items-center space-x-2 flex-1",children:p?p?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[p.logo&&(0,a.jsx)("img",{src:"".concat(h,"/").concat(p.logo),alt:p.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,a.jsx)("span",{children:p.label})]}):i:(0,a.jsx)("span",{className:"text-muted-foreground",children:i})})})}),(0,a.jsx)(o.Bw,{children:u.map(e=>(0,a.jsx)(o.Ql,{value:e.value,disabled:e.disabled,children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.logo&&(0,a.jsx)("img",{src:"".concat(h,"/").concat(e.logo),alt:e.label,className:"w-5 h-5 object-contain rounded",onError:e=>{e.currentTarget.style.display="none"}}),(0,a.jsx)("span",{children:e.label})]})},e.value))})]})})});y.displayName="SelectField";let j=(0,s.forwardRef)((e,r)=>{let{label:t,description:s,error:l,checked:i,onCheckedChange:o,className:c}=e;return(0,a.jsx)(b,{description:s,error:l,className:c,children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u,{ref:r,checked:i,onCheckedChange:o,className:(0,d.cn)(l&&"border-red-500")}),t&&(0,a.jsx)(n._,{className:(0,d.cn)("text-sm font-normal cursor-pointer",l&&"text-red-600"),children:t})]})})});j.displayName="CheckboxField",(0,s.forwardRef)((e,r)=>{let{label:t,description:s,error:l,required:i,value:o,onValueChange:c,options:f,orientation:u="vertical",className:m}=e;return(0,a.jsx)(b,{label:t,description:s,error:l,required:i,className:m,children:(0,a.jsx)(p,{ref:r,value:o,onValueChange:c,className:(0,d.cn)("horizontal"===u?"flex flex-row space-x-4":"space-y-2"),children:f.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h,{value:e.value,disabled:e.disabled,className:(0,d.cn)(l&&"border-red-500")}),(0,a.jsx)(n._,{className:"text-sm font-normal cursor-pointer",children:e.label})]},e.value))})})}).displayName="RadioField";let v=e=>{let{title:r,description:t,children:s,className:n}=e;return(0,a.jsxs)("div",{className:(0,d.cn)("space-y-4",n),children:[(r||t)&&(0,a.jsxs)("div",{className:"space-y-1",children:[r&&(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:r}),t&&(0,a.jsx)("p",{className:"text-sm text-gray-600",children:t})]}),(0,a.jsx)("div",{className:"space-y-4",children:s})]})},w=e=>{let{children:r,className:t,align:s="right"}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex space-x-2 pt-4 border-t","left"===s&&"justify-start","center"===s&&"justify-center","right"===s&&"justify-end",t),children:r})}},2782:function(e,r,t){t.d(r,{I:function(){return l}});var a=t(7437),s=t(2265),n=t(2169);let l=s.forwardRef((e,r)=>{let{className:t,type:s,...l}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...l})});l.displayName="Input"},2647:function(e,r,t){t.d(r,{_:function(){return o}});var a=t(7437),s=t(2265),n=t(4602),l=t(9769),d=t(2169);let i=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.f,{ref:r,className:(0,d.cn)(i(),t),...s})});o.displayName=n.f.displayName},8641:function(e,r,t){t.d(r,{Bw:function(){return p},Ph:function(){return c},Ql:function(){return h},i4:function(){return u},ki:function(){return f}});var a=t(7437),s=t(2265),n=t(8178),l=t(3441),d=t(5159),i=t(9259),o=t(2169);let c=n.fC;n.ZA;let f=n.B4,u=s.forwardRef((e,r)=>{let{className:t,children:s,...d}=e;return(0,a.jsxs)(n.xz,{ref:r,className:(0,o.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...d,children:[s,(0,a.jsx)(n.JO,{asChild:!0,children:(0,a.jsx)(l.Z,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=n.xz.displayName;let m=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.u_,{ref:r,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(d.Z,{className:"h-4 w-4"})})});m.displayName=n.u_.displayName;let x=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.$G,{ref:r,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(l.Z,{className:"h-4 w-4"})})});x.displayName=n.$G.displayName;let p=s.forwardRef((e,r)=>{let{className:t,children:s,position:l="popper",...d}=e;return(0,a.jsx)(n.h_,{children:(0,a.jsxs)(n.VY,{ref:r,className:(0,o.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...d,children:[(0,a.jsx)(m,{}),(0,a.jsx)(n.l_,{className:(0,o.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(x,{})]})})});p.displayName=n.VY.displayName,s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.__,{ref:r,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",t),...s})}).displayName=n.__.displayName;let h=s.forwardRef((e,r)=>{let{className:t,children:s,...l}=e;return(0,a.jsxs)(n.ck,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...l,children:[(0,a.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.wU,{children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})}),(0,a.jsx)(n.eT,{children:s})]})});h.displayName=n.ck.displayName,s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.Z0,{ref:r,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...s})}).displayName=n.Z0.displayName},2169:function(e,r,t){t.d(r,{cn:function(){return n}});var a=t(3167),s=t(1367);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.m6)((0,a.W)(r))}}}]);