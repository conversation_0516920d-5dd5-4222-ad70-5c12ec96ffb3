"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1880],{3879:function(t,e,a){a.d(e,{Z:function(){return s}});let s=(0,a(7977).Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7307:function(t,e,a){a.d(e,{Z:function(){return s}});let s=(0,a(7977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},699:function(t,e,a){a.d(e,{Z:function(){return s}});let s=(0,a(7977).Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},7907:function(t,e,a){var s=a(5313);a.o(s,"useParams")&&a.d(e,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(e,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(e,{useRouter:function(){return s.useRouter}})},4921:function(t,e,a){a.d(e,{x:function(){return o}});var s=a(3107);class i{setupInterceptors(){this.client.interceptors.request.use(t=>{let e=this.getAuthToken();return e&&(t.headers.Authorization="Bearer ".concat(e)),t},t=>Promise.reject(t)),this.client.interceptors.response.use(t=>t,t=>{var e;return(null===(e=t.response)||void 0===e?void 0:e.status)===401&&this.handleUnauthorized(),Promise.reject(t)})}getAuthToken(){try{let e=localStorage.getItem("auth-storage");if(e){var t;let a=JSON.parse(e);return(null===(t=a.state)||void 0===t?void 0:t.accessToken)||null}}catch(t){console.warn("Failed to parse auth storage:",t)}return localStorage.getItem("accessToken")}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}setAuthToken(t){localStorage.setItem("accessToken",t)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(t,e){return(await this.client.get(t,e)).data}async post(t,e,a){return(await this.client.post(t,e,a)).data}async put(t,e,a){return(await this.client.put(t,e,a)).data}async patch(t,e,a){return(await this.client.patch(t,e,a)).data}async delete(t,e){return(await this.client.delete(t,e)).data}constructor(){this.baseURL="http://localhost:3000",this.client=s.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with baseURL:",this.baseURL)}}let o=new i},2975:function(t,e,a){a.d(e,{L:function(){return i}});var s=a(4921);let i={getFixtures:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,s]=t;void 0!==s&&e.append(a,s.toString())});let a=await fetch("/api/fixtures?".concat(e.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch fixtures: ".concat(a.statusText));return await a.json()},getFixtureById:async t=>{let e=await fetch("/api/fixtures/".concat(t),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to fetch fixture: ".concat(e.statusText));return await e.json()},getUpcomingAndLive:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,s]=t;void 0!==s&&e.append(a,s.toString())});let a=await fetch("/api/fixtures/live?".concat(e.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch live fixtures: ".concat(a.statusText));return await a.json()},getTeamSchedule:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;return Object.entries(e).forEach(t=>{let[e,s]=t;void 0!==s&&a.append(e,s.toString())}),await s.x.get("/football/fixtures/schedules/".concat(t,"?").concat(a.toString()))},getFixtureStatistics:async t=>await s.x.get("/football/fixtures/statistics/".concat(t)),triggerSeasonSync:async()=>await s.x.get("/football/fixtures/sync/fixtures"),triggerDailySync:async()=>await s.x.get("/football/fixtures/sync/daily"),getSyncStatus:async()=>await s.x.get("/football/fixtures/sync/status"),createFixture:async t=>{let e=await fetch("/api/fixtures",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!e.ok)throw Error("Failed to create fixture: ".concat(e.statusText));return await e.json()},updateFixture:async(t,e)=>{let a=await fetch("/api/fixtures/".concat(t),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to update fixture: ".concat(a.statusText));return await a.json()},deleteFixture:async t=>{let e=(()=>{{try{let e=localStorage.getItem("auth-storage");if(e){var t;let a=JSON.parse(e),s=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(s)return console.log("\uD83D\uDD11 Delete fixture - Using token from auth store:",s.substring(0,20)+"..."),{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)}}}catch(t){console.warn("Failed to parse auth storage:",t)}let e=localStorage.getItem("accessToken");if(e)return console.log("\uD83D\uDD11 Delete fixture - Using fallback token from localStorage"),{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}}return console.warn("❌ Delete fixture - No token found!"),{"Content-Type":"application/json"}})();console.log("\uD83D\uDD04 Delete fixture request:",{externalId:t,hasAuth:!!e.Authorization});let a=await fetch("/api/fixtures/".concat(t),{method:"DELETE",headers:e});if(!a.ok){let t=await a.json().catch(()=>({}));throw console.error("❌ Delete fixture failed:",a.status,a.statusText,t),Error(t.message||"Failed to delete fixture: ".concat(a.statusText))}console.log("✅ Delete fixture successful:",t)},getFixture:async t=>(await i.getFixtureById(t)).data}},7011:function(t,e,a){a.d(e,{A:function(){return i}});var s=a(4921);let i={getLeagues:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,s]=t;void 0!==s&&e.append(a,s.toString())});let a=await fetch("/api/leagues?".concat(e.toString()),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch leagues");return await a.json()},getLeagueById:async(t,e)=>await s.x.get("/football/leagues/".concat(t).concat(e?"?season=".concat(e):"")),createLeague:async t=>await s.x.post("/football/leagues",t),updateLeague:async(t,e)=>await s.x.patch("/football/leagues/".concat(t),e),getActiveLeagues:async()=>i.getLeagues({active:!0}),getLeaguesByCountry:async t=>i.getLeagues({country:t}),toggleLeagueStatus:async(t,e)=>i.updateLeague(t,{active:e})}},3016:function(t,e,a){a.d(e,{k:function(){return i}});var s=a(4921);let i={getTeams:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,s]=t;void 0!==s&&e.append(a,s.toString())});let a=await fetch("/api/teams?".concat(e.toString()),{method:"GET",headers:(()=>{{try{let e=localStorage.getItem("auth-storage");if(e){var t;let a=JSON.parse(e),s=null===(t=a.state)||void 0===t?void 0:t.accessToken;if(s)return{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)}}}catch(t){console.warn("Failed to parse auth storage:",t)}let e=localStorage.getItem("accessToken");if(e)return{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}}return{"Content-Type":"application/json"}})()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch teams");return await a.json()},getTeamById:async t=>await s.x.get("/football/teams/".concat(t)),getTeamStatistics:async(t,e,a)=>{let i=new URLSearchParams({league:t.toString(),season:e.toString(),team:a.toString()});return await s.x.get("/football/teams/statistics?".concat(i.toString()))},getTeamsByLeague:async(t,e)=>{let a={league:t};return e&&(a.season=e),i.getTeams(a)},getTeamsByCountry:async t=>i.getTeams({country:t}),searchTeams:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=await i.getTeams(e),s=a.data.filter(e=>{var a;return e.name.toLowerCase().includes(t.toLowerCase())||(null===(a=e.code)||void 0===a?void 0:a.toLowerCase().includes(t.toLowerCase()))});return{data:s,meta:{...a.meta,totalItems:s.length,totalPages:Math.ceil(s.length/(e.limit||10))}}}}},5899:function(t,e,a){a.d(e,{_:function(){return s}});let s=console},4654:function(t,e,a){a.d(e,{R:function(){return u},m:function(){return r}});var s=a(5899),i=a(9522),o=a(3864),n=a(4500);class r extends o.F{constructor(t){super(),this.defaultOptions=t.defaultOptions,this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.logger=t.logger||s._,this.observers=[],this.state=t.state||u(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options={...this.defaultOptions,...t},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(t){this.dispatch({type:"setState",state:t})}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.observers=this.observers.filter(e=>e!==t),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.observers.length||("loading"===this.state.status?this.scheduleGc():this.mutationCache.remove(this))}continue(){var t,e;return null!=(t=null==(e=this.retryer)?void 0:e.continue())?t:this.execute()}async execute(){var t,e,a,s,i,o,r,u,c,l,h,d,p,f,g,m,v,y,w,b;let x="loading"===this.state.status;try{if(!x){this.dispatch({type:"loading",variables:this.options.variables}),await (null==(c=(l=this.mutationCache.config).onMutate)?void 0:c.call(l,this.state.variables,this));let t=await (null==(h=(d=this.options).onMutate)?void 0:h.call(d,this.state.variables));t!==this.state.context&&this.dispatch({type:"loading",context:t,variables:this.state.variables})}let p=await (()=>{var t;return this.retryer=(0,n.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(t,e)=>{this.dispatch({type:"failed",failureCount:t,error:e})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise})();return await (null==(t=(e=this.mutationCache.config).onSuccess)?void 0:t.call(e,p,this.state.variables,this.state.context,this)),await (null==(a=(s=this.options).onSuccess)?void 0:a.call(s,p,this.state.variables,this.state.context)),await (null==(i=(o=this.mutationCache.config).onSettled)?void 0:i.call(o,p,null,this.state.variables,this.state.context,this)),await (null==(r=(u=this.options).onSettled)?void 0:r.call(u,p,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:p}),p}catch(t){try{throw await (null==(p=(f=this.mutationCache.config).onError)?void 0:p.call(f,t,this.state.variables,this.state.context,this)),await (null==(g=(m=this.options).onError)?void 0:g.call(m,t,this.state.variables,this.state.context)),await (null==(v=(y=this.mutationCache.config).onSettled)?void 0:v.call(y,void 0,t,this.state.variables,this.state.context,this)),await (null==(w=(b=this.options).onSettled)?void 0:w.call(b,void 0,t,this.state.variables,this.state.context)),t}finally{this.dispatch({type:"error",error:t})}}}dispatch(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"loading":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,n.Kw)(this.options.networkMode),status:"loading",variables:t.variables};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"};case"setState":return{...e,...t.state}}})(this.state),i.V.batch(()=>{this.observers.forEach(e=>{e.onMutationUpdate(t)}),this.mutationCache.notify({mutation:this,type:"updated",action:t})})}}function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0}}},3864:function(t,e,a){a.d(e,{F:function(){return i}});var s=a(1678);class i{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,s.PN)(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(t){this.cacheTime=Math.max(this.cacheTime||0,null!=t?t:s.sk?1/0:3e5)}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}},8186:function(t,e,a){a.d(e,{D:function(){return d}});var s=a(2265),i=a(1678),o=a(4654),n=a(9522),r=a(6761);class u extends r.l{constructor(t,e){super(),this.client=t,this.setOptions(e),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var e;let a=this.options;this.options=this.client.defaultMutationOptions(t),(0,i.VS)(a,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(e=this.currentMutation)||e.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var t;null==(t=this.currentMutation)||t.removeObserver(this)}}onMutationUpdate(t){this.updateResult();let e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==t?t:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let t=this.currentMutation?this.currentMutation.state:(0,o.R)(),e={...t,isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset};this.currentResult=e}notify(t){n.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var e,a,s,i,o,n,r,u;t.onSuccess?(null==(e=(a=this.mutateOptions).onSuccess)||e.call(a,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(s=(i=this.mutateOptions).onSettled)||s.call(i,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):t.onError&&(null==(o=(n=this.mutateOptions).onError)||o.call(n,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(r=(u=this.mutateOptions).onSettled)||r.call(u,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}t.listeners&&this.listeners.forEach(({listener:t})=>{t(this.currentResult)})})}}var c=a(7536),l=a(4095),h=a(3439);function d(t,e,a){let o=(0,i.lV)(t,e,a),r=(0,l.NL)({context:o.context}),[d]=s.useState(()=>new u(r,o));s.useEffect(()=>{d.setOptions(o)},[d,o]);let f=(0,c.$)(s.useCallback(t=>d.subscribe(n.V.batchCalls(t)),[d]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),g=s.useCallback((t,e)=>{d.mutate(t,e).catch(p)},[d]);if(f.error&&(0,h.L)(d.options.useErrorBoundary,[f.error]))throw f.error;return{...f,mutate:g,mutateAsync:f.mutate}}function p(){}}}]);