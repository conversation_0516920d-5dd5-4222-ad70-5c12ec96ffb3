"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6877],{5671:function(e,t,a){a.d(t,{Ol:function(){return i},SZ:function(){return l},Zb:function(){return o},aY:function(){return u},ll:function(){return c}});var r=a(7437),s=a(2265),n=a(2169);let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...s})});o.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...s})});i.displayName="CardHeader";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",a),...s})});c.displayName="CardTitle";let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...s})});l.displayName="CardDescription";let u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",a),...s})});u.displayName="CardContent",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",a),...s})}).displayName="CardFooter"},6146:function(e,t,a){a.d(t,{SX:function(){return o},TK:function(){return n}});var r=a(7437);a(2265),a(7625),a(5671);var s=a(9580);let n=e=>{let{size:t="md",className:a=""}=e;return(0,r.jsx)(s.Z,{className:"animate-spin ".concat({sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[t]," ").concat(a)})},o=e=>{let{message:t="Loading..."}=e;return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[(0,r.jsx)(n,{size:"lg"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:t})]})}},7625:function(e,t,a){a.d(t,{Od:function(){return n},hM:function(){return i},q4:function(){return o}});var r=a(7437),s=a(2169);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",t),...a})}let o=e=>{let{className:t}=e;return(0,r.jsxs)("div",{className:(0,s.cn)("border rounded-lg p-6 space-y-4",t),children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n,{className:"h-4 w-3/4"}),(0,r.jsx)(n,{className:"h-4 w-1/2"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n,{className:"h-3 w-full"}),(0,r.jsx)(n,{className:"h-3 w-full"}),(0,r.jsx)(n,{className:"h-3 w-2/3"})]})]})},i=e=>{let{rows:t=5,columns:a=4,className:o}=e;return(0,r.jsx)("div",{className:(0,s.cn)("space-y-4",o),children:(0,r.jsxs)("div",{className:"border rounded-lg",children:[(0,r.jsx)("div",{className:"border-b p-4",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,t)=>(0,r.jsx)(n,{className:"h-4 w-20"},t))})}),Array.from({length:t}).map((e,t)=>(0,r.jsx)("div",{className:"border-b last:border-b-0 p-4",children:(0,r.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,t)=>(0,r.jsx)(n,{className:"h-4 w-full"},t))})},t))]})})}},773:function(e,t,a){a.d(t,{i:function(){return n}});var r=a(4921),s=a(8763);let n={login:async e=>{console.log("\uD83D\uDD10 Attempting login via proxy...");try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.message||"Login failed")}let a=await t.json();console.log("✅ Login successful via proxy");let r=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.accessToken)}});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to fetch profile")}return{user:await r.json(),accessToken:a.accessToken,refreshToken:a.refreshToken}}catch(t){if(console.error("❌ Login failed via proxy:",t.message),(t.message.includes("fetch")||t.message.includes("network"))&&(console.warn("⚠️ Network error, using mock data"),"admin"===e.username&&"admin123456"===e.password)){let e={user:{id:1,username:"admin",email:"<EMAIL>",fullName:"System Administrator",role:"admin",isActive:!0,lastLoginAt:new Date().toISOString(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},accessToken:"mock-access-token-"+Date.now(),refreshToken:"mock-refresh-token-"+Date.now()};return await new Promise(e=>setTimeout(e,500)),e}throw t}},logout:async e=>{let t=await fetch("/api/auth/logout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error((await t.json()).message||"Logout failed");return await t.json()},logoutFromAllDevices:async()=>await r.x.post("/system-auth/logout-all"),refreshToken:async e=>await r.x.post("/system-auth/refresh",{refreshToken:e}),getProfile:async()=>{let e=s.t.getState(),t=e.accessToken,a=await fetch("/api/auth/profile",{method:"GET",headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}});if(!a.ok){if(401===a.status)throw console.warn("⚠️ Token expired, forcing logout..."),e.clearAuth(),window.location.href="/auth/login",Error("Token expired, please login again");throw Error((await a.json()).message||"Failed to fetch profile")}return await a.json()},updateProfile:async e=>await r.x.put("/system-auth/profile",e),changePassword:async e=>await r.x.post("/system-auth/change-password",e),createUser:async e=>await r.x.post("/system-auth/users",e),updateUser:async(e,t)=>await r.x.put("/system-auth/users/".concat(e),t)}},4921:function(e,t,a){a.d(t,{x:function(){return n}});var r=a(3107);class s{setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.getAuthToken();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(e)})}getAuthToken(){try{let t=localStorage.getItem("auth-storage");if(t){var e;let a=JSON.parse(t);return(null===(e=a.state)||void 0===e?void 0:e.accessToken)||null}}catch(e){console.warn("Failed to parse auth storage:",e)}return localStorage.getItem("accessToken")}handleUnauthorized(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.href="/auth/login"}setAuthToken(e){localStorage.setItem("accessToken",e)}removeAuthToken(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth-storage")}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async patch(e,t,a){return(await this.client.patch(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(){this.baseURL="http://localhost:3000",this.client=r.Z.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),console.log("\uD83D\uDD17 API Client initialized with baseURL:",this.baseURL)}}let n=new s},7786:function(e,t,a){a.d(t,{a:function(){return l}});var r=a(4095),s=a(8186),n=a(1346),o=a(773),i=a(8763),c=a(4921);let l=()=>{let e=(0,r.NL)(),{setAuth:t,clearAuth:a,setLoading:l,user:u,isAuthenticated:d}=(0,i.t)(),h=(0,s.D)({mutationFn:o.i.login,onMutate:()=>{l(!0)},onSuccess:a=>{t(a.user,a.accessToken,a.refreshToken),c.x.setAuthToken(a.accessToken),e.invalidateQueries({queryKey:["auth","profile"]})},onError:e=>{console.error("Login failed:",e),l(!1)}}),m=(0,s.D)({mutationFn:e=>o.i.logout(e),onSuccess:()=>{a(),c.x.removeAuthToken(),e.clear()},onError:()=>{a(),c.x.removeAuthToken(),e.clear()}}),f=(0,s.D)({mutationFn:o.i.logoutFromAllDevices,onSuccess:()=>{a(),c.x.removeAuthToken(),e.clear()}}),g=(0,n.a)({queryKey:["auth","profile"],queryFn:o.i.getProfile,enabled:d,staleTime:6e5}),p=(0,s.D)({mutationFn:e=>o.i.updateProfile(e),onSuccess:t=>{e.setQueryData(["auth","profile"],t),i.t.getState().updateUser(t)}}),w=(0,s.D)({mutationFn:o.i.changePassword,onSuccess:()=>{}}),y=(0,s.D)({mutationFn:e=>o.i.refreshToken(e),onSuccess:e=>{let a=i.t.getState().user,r=i.t.getState().refreshToken;a&&r&&(t(a,e.accessToken,r),c.x.setAuthToken(e.accessToken))},onError:()=>{a(),c.x.removeAuthToken(),e.clear()}});return{user:u,isAuthenticated:d,isLoading:(0,i.t)(e=>e.isLoading),profile:g.data,isProfileLoading:g.isLoading,profileError:g.error,login:h.mutate,logout:e=>m.mutate(e),logoutAll:f.mutate,updateProfile:p.mutate,changePassword:w.mutate,refreshToken:y.mutate,isLoginLoading:h.isPending,loginError:h.error,isLogoutLoading:m.isPending,isUpdateProfileLoading:p.isPending,updateProfileError:p.error,isChangePasswordLoading:w.isPending,changePasswordError:w.error}}},8763:function(e,t,a){a.d(t,{t:function(){return o}});var r=a(2574),s=a(5249);let n={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1},o=(0,r.U)()((0,s.tJ)((e,t)=>({...n,setAuth:(t,a,r)=>{e({user:t,accessToken:a,refreshToken:r,isAuthenticated:!0,isLoading:!1})},clearAuth:()=>{e(n)},setLoading:t=>{e({isLoading:t})},updateUser:a=>{let r=t().user;r&&e({user:{...r,...a}})}}),{name:"auth-storage",partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated})}))},2169:function(e,t,a){a.d(t,{cn:function(){return n}});var r=a(3167),s=a(1367);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.m6)((0,r.W)(t))}}}]);