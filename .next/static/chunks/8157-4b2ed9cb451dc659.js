"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8157],{3345:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},7307:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},7805:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7501:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},2527:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},5423:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},1049:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},9744:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},3013:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},2891:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},5462:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("radio",[["path",{d:"M4.9 19.1C1 15.2 1 8.8 4.9 4.9",key:"1vaf9d"}],["path",{d:"M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5",key:"u1ii0m"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5",key:"1j5fej"}],["path",{d:"M19.1 4.9C23 8.8 23 15.1 19.1 19",key:"10b0cb"}]])},834:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},8670:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9910:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},7326:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},1047:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},5497:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},1213:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2235:function(e,n,r){r.d(n,{Z:function(){return t}});let t=(0,r(7977).Z)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},7733:function(e,n,r){r.d(n,{NY:function(){return C},Ee:function(){return b},fC:function(){return k}});var t=r(2265),o=r(4104),a=r(9830),u=r(2618),i=r(9586),l=r(2362);function d(){return()=>{}}var c=r(7437),s="Avatar",[f,p]=(0,o.b)(s),[v,h]=f(s),m=t.forwardRef((e,n)=>{let{__scopeAvatar:r,...o}=e,[a,u]=t.useState("idle");return(0,c.jsx)(v,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:u,children:(0,c.jsx)(i.WV.span,{...o,ref:n})})});m.displayName=s;var y="AvatarImage",g=t.forwardRef((e,n)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:s=()=>{},...f}=e,p=h(y,r),v=function(e,n){let{referrerPolicy:r,crossOrigin:o}=n,a=(0,l.useSyncExternalStore)(d,()=>!0,()=>!1),i=t.useRef(null),c=a?(i.current||(i.current=new window.Image),i.current):null,[s,f]=t.useState(()=>x(c,e));return(0,u.b)(()=>{f(x(c,e))},[c,e]),(0,u.b)(()=>{let e=e=>()=>{f(e)};if(!c)return;let n=e("loaded"),t=e("error");return c.addEventListener("load",n),c.addEventListener("error",t),r&&(c.referrerPolicy=r),"string"==typeof o&&(c.crossOrigin=o),()=>{c.removeEventListener("load",n),c.removeEventListener("error",t)}},[c,o,r]),s}(o,f),m=(0,a.W)(e=>{s(e),p.onImageLoadingStatusChange(e)});return(0,u.b)(()=>{"idle"!==v&&m(v)},[v,m]),"loaded"===v?(0,c.jsx)(i.WV.img,{...f,ref:n,src:o}):null});g.displayName=y;var w="AvatarFallback",M=t.forwardRef((e,n)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,u=h(w,r),[l,d]=t.useState(void 0===o);return t.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>d(!0),o);return()=>window.clearTimeout(e)}},[o]),l&&"loaded"!==u.imageLoadingStatus?(0,c.jsx)(i.WV.span,{...a,ref:n}):null});function x(e,n){return e?n?(e.src!==n&&(e.src=n),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}M.displayName=w;var k=m,b=g,C=M},3613:function(e,n,r){r.d(n,{oC:function(){return e5},VY:function(){return e7},ZA:function(){return e4},ck:function(){return e3},wU:function(){return ne},__:function(){return e9},Uv:function(){return e2},Ee:function(){return e6},Rk:function(){return e8},fC:function(){return e0},Z0:function(){return nn},Tr:function(){return nr},tu:function(){return no},fF:function(){return nt},xz:function(){return e1}});var t=r(2265),o=r(4991),a=r(1266),u=r(4104),i=r(9310),l=r(9586),d=r(5528),c=r(3876),s=r(1260),f=r(6007),p=r(8082),v=r(8687),h=r(2338),m=r(7881),y=r(2642),g=r(3715),w=r(9143),M=r(9830),x=r(6674),k=r(7225),b=r(7437),C=["Enter"," "],j=["ArrowUp","PageDown","End"],R=["ArrowDown","PageUp","Home",...j],D={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},E={ltr:["ArrowLeft"],rtl:["ArrowRight"]},I="Menu",[_,N,T]=(0,d.B)(I),[P,S]=(0,u.b)(I,[T,h.D7,g.Pc]),O=(0,h.D7)(),A=(0,g.Pc)(),[F,L]=P(I),[Z,K]=P(I),V=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:u,modal:i=!0}=e,l=O(n),[d,s]=t.useState(null),f=t.useRef(!1),p=(0,M.W)(u),v=(0,c.gm)(a);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,b.jsx)(h.fC,{...l,children:(0,b.jsx)(F,{scope:n,open:r,onOpenChange:p,content:d,onContentChange:s,children:(0,b.jsx)(Z,{scope:n,onClose:t.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:i,children:o})})})};V.displayName=I;var U=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=O(r);return(0,b.jsx)(h.ee,{...o,...t,ref:n})});U.displayName="MenuAnchor";var W="MenuPortal",[z,G]=P(W,{forceMount:void 0}),B=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=L(W,n);return(0,b.jsx)(z,{scope:n,forceMount:r,children:(0,b.jsx)(y.z,{present:r||a.open,children:(0,b.jsx)(m.h,{asChild:!0,container:o,children:t})})})};B.displayName=W;var H="MenuContent",[q,X]=P(H),Y=t.forwardRef((e,n)=>{let r=G(H,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=L(H,e.__scopeMenu),u=K(H,e.__scopeMenu);return(0,b.jsx)(_.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(y.z,{present:t||a.open,children:(0,b.jsx)(_.Slot,{scope:e.__scopeMenu,children:u.modal?(0,b.jsx)(J,{...o,ref:n}):(0,b.jsx)(Q,{...o,ref:n})})})})}),J=t.forwardRef((e,n)=>{let r=L(H,e.__scopeMenu),u=t.useRef(null),i=(0,a.e)(n,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,x.Ry)(e)},[]),(0,b.jsx)(ee,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=t.forwardRef((e,n)=>{let r=L(H,e.__scopeMenu);return(0,b.jsx)(ee,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),$=(0,w.Z8)("MenuContent.ScrollLock"),ee=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:u=!1,trapFocus:i,onOpenAutoFocus:l,onCloseAutoFocus:d,disableOutsidePointerEvents:c,onEntryFocus:v,onEscapeKeyDown:m,onPointerDownOutside:y,onFocusOutside:w,onInteractOutside:M,onDismiss:x,disableOutsideScroll:C,...D}=e,E=L(H,r),I=K(H,r),_=O(r),T=A(r),P=N(r),[S,F]=t.useState(null),Z=t.useRef(null),V=(0,a.e)(n,Z,E.onContentChange),U=t.useRef(0),W=t.useRef(""),z=t.useRef(0),G=t.useRef(null),B=t.useRef("right"),X=t.useRef(0),Y=C?k.Z:t.Fragment,J=e=>{var n,r;let t=W.current+e,o=P().filter(e=>!e.disabled),a=document.activeElement,u=null===(n=o.find(e=>e.ref.current===a))||void 0===n?void 0:n.textValue,i=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,a=(t=Math.max(r?e.indexOf(r):-1,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let u=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==r?u:void 0}(o.map(e=>e.textValue),t,u),l=null===(r=o.find(e=>e.textValue===i))||void 0===r?void 0:r.ref.current;!function e(n){W.current=n,window.clearTimeout(U.current),""!==n&&(U.current=window.setTimeout(()=>e(""),1e3))}(t),l&&setTimeout(()=>l.focus())};t.useEffect(()=>()=>window.clearTimeout(U.current),[]),(0,f.EW)();let Q=t.useCallback(e=>{var n,r,t;return B.current===(null===(n=G.current)||void 0===n?void 0:n.side)&&!!(t=null===(r=G.current)||void 0===r?void 0:r.area)&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,a=n.length-1;e<n.length;a=e++){let u=n[e],i=n[a],l=u.x,d=u.y,c=i.x,s=i.y;d>t!=s>t&&r<(c-l)*(t-d)/(s-d)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)},[]);return(0,b.jsx)(q,{scope:r,searchRef:W,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var n;Q(e)||(null===(n=Z.current)||void 0===n||n.focus(),F(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:z,onPointerGraceIntentChange:t.useCallback(e=>{G.current=e},[]),children:(0,b.jsx)(Y,{...C?{as:$,allowPinchZoom:!0}:void 0,children:(0,b.jsx)(p.M,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.M)(l,e=>{var n;e.preventDefault(),null===(n=Z.current)||void 0===n||n.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,b.jsx)(s.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:m,onPointerDownOutside:y,onFocusOutside:w,onInteractOutside:M,onDismiss:x,children:(0,b.jsx)(g.fC,{asChild:!0,...T,dir:I.dir,orientation:"vertical",loop:u,currentTabStopId:S,onCurrentTabStopIdChange:F,onEntryFocus:(0,o.M)(v,e=>{I.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,b.jsx)(h.VY,{role:"menu","aria-orientation":"vertical","data-state":eE(E.open),"data-radix-menu-content":"",dir:I.dir,..._,...D,ref:V,style:{outline:"none",...D.style},onKeyDown:(0,o.M)(D.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&J(e.key));let o=Z.current;if(e.target!==o||!R.includes(e.key))return;e.preventDefault();let a=P().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(U.current),W.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eN(e=>{let n=e.target,r=X.current!==e.clientX;if(e.currentTarget.contains(n)&&r){let n=e.clientX>X.current?"right":"left";B.current=n,X.current=e.clientX}}))})})})})})})});Y.displayName=H;var en=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,b.jsx)(l.WV.div,{role:"group",...t,ref:n})});en.displayName="MenuGroup";var er=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,b.jsx)(l.WV.div,{...t,ref:n})});er.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:u,...i}=e,d=t.useRef(null),c=K(et,e.__scopeMenu),s=X(et,e.__scopeMenu),f=(0,a.e)(n,d),p=t.useRef(!1);return(0,b.jsx)(eu,{...i,ref:f,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=d.current;if(!r&&e){let n=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==u?void 0:u(e),{once:!0}),(0,l.jH)(e,n),n.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:n=>{var r;null===(r=e.onPointerDown)||void 0===r||r.call(e,n),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{var n;p.current||null===(n=e.currentTarget)||void 0===n||n.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let n=""!==s.searchRef.current;!r&&(!n||" "!==e.key)&&C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var eu=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:u=!1,textValue:i,...d}=e,c=X(et,r),s=A(r),f=t.useRef(null),p=(0,a.e)(n,f),[v,h]=t.useState(!1),[m,y]=t.useState("");return t.useEffect(()=>{let e=f.current;if(e){var n;y((null!==(n=e.textContent)&&void 0!==n?n:"").trim())}},[d.children]),(0,b.jsx)(_.ItemSlot,{scope:r,disabled:u,textValue:null!=i?i:m,children:(0,b.jsx)(g.ck,{asChild:!0,...s,focusable:!u,children:(0,b.jsx)(l.WV.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...d,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eN(e=>{u?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eN(e=>c.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>h(!0)),onBlur:(0,o.M)(e.onBlur,()=>h(!1))})})})}),ei=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...a}=e;return(0,b.jsx)(eh,{scope:e.__scopeMenu,checked:r,children:(0,b.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eI(r)?"mixed":r,...a,ref:n,"data-state":e_(r),onSelect:(0,o.M)(a.onSelect,()=>null==t?void 0:t(!!eI(r)||!r),{checkForDefaultPrevented:!1})})})});ei.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[ed,ec]=P(el,{value:void 0,onValueChange:()=>{}}),es=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,a=(0,M.W)(t);return(0,b.jsx)(ed,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,b.jsx)(en,{...o,ref:n})})});es.displayName=el;var ef="MenuRadioItem",ep=t.forwardRef((e,n)=>{let{value:r,...t}=e,a=ec(ef,e.__scopeMenu),u=r===a.value;return(0,b.jsx)(eh,{scope:e.__scopeMenu,checked:u,children:(0,b.jsx)(ea,{role:"menuitemradio","aria-checked":u,...t,ref:n,"data-state":e_(u),onSelect:(0,o.M)(t.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var ev="MenuItemIndicator",[eh,em]=P(ev,{checked:!1}),ey=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,a=em(ev,r);return(0,b.jsx)(y.z,{present:t||eI(a.checked)||!0===a.checked,children:(0,b.jsx)(l.WV.span,{...o,ref:n,"data-state":e_(a.checked)})})});ey.displayName=ev;var eg=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,b.jsx)(l.WV.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});eg.displayName="MenuSeparator";var ew=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=O(r);return(0,b.jsx)(h.Eh,{...o,...t,ref:n})});ew.displayName="MenuArrow";var eM="MenuSub",[ex,ek]=P(eM),eb=e=>{let{__scopeMenu:n,children:r,open:o=!1,onOpenChange:a}=e,u=L(eM,n),i=O(n),[l,d]=t.useState(null),[c,s]=t.useState(null),f=(0,M.W)(a);return t.useEffect(()=>(!1===u.open&&f(!1),()=>f(!1)),[u.open,f]),(0,b.jsx)(h.fC,{...i,children:(0,b.jsx)(F,{scope:n,open:o,onOpenChange:f,content:c,onContentChange:s,children:(0,b.jsx)(ex,{scope:n,contentId:(0,v.M)(),triggerId:(0,v.M)(),trigger:l,onTriggerChange:d,children:r})})})};eb.displayName=eM;var eC="MenuSubTrigger",ej=t.forwardRef((e,n)=>{let r=L(eC,e.__scopeMenu),u=K(eC,e.__scopeMenu),i=ek(eC,e.__scopeMenu),l=X(eC,e.__scopeMenu),d=t.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:s}=l,f={__scopeMenu:e.__scopeMenu},p=t.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return t.useEffect(()=>p,[p]),t.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),s(null)}},[c,s]),(0,b.jsx)(U,{asChild:!0,...f,children:(0,b.jsx)(eu,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":eE(r.open),...e,ref:(0,a.F)(n,i.onTriggerChange),onClick:n=>{var t;null===(t=e.onClick)||void 0===t||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eN(n=>{l.onItemEnter(n),n.defaultPrevented||e.disabled||r.open||d.current||(l.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eN(e=>{var n,t;p();let o=null===(n=r.content)||void 0===n?void 0:n.getBoundingClientRect();if(o){let n=null===(t=r.content)||void 0===t?void 0:t.dataset.side,a="right"===n,u=o[a?"left":"right"],i=o[a?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,n=>{let t=""!==l.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&D[u.dir].includes(n.key)){var o;r.onOpenChange(!0),null===(o=r.content)||void 0===o||o.focus(),n.preventDefault()}})})})});ej.displayName=eC;var eR="MenuSubContent",eD=t.forwardRef((e,n)=>{let r=G(H,e.__scopeMenu),{forceMount:u=r.forceMount,...i}=e,l=L(H,e.__scopeMenu),d=K(H,e.__scopeMenu),c=ek(eR,e.__scopeMenu),s=t.useRef(null),f=(0,a.e)(n,s);return(0,b.jsx)(_.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(y.z,{present:u||l.open,children:(0,b.jsx)(_.Slot,{scope:e.__scopeMenu,children:(0,b.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...i,ref:f,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;d.isUsingKeyboardRef.current&&(null===(n=s.current)||void 0===n||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=E[d.dir].includes(e.key);if(n&&r){var t;l.onOpenChange(!1),null===(t=c.trigger)||void 0===t||t.focus(),e.preventDefault()}})})})})})});function eE(e){return e?"open":"closed"}function eI(e){return"indeterminate"===e}function e_(e){return eI(e)?"indeterminate":e?"checked":"unchecked"}function eN(e){return n=>"mouse"===n.pointerType?e(n):void 0}eD.displayName=eR;var eT="DropdownMenu",[eP,eS]=(0,u.b)(eT,[S]),eO=S(),[eA,eF]=eP(eT),eL=e=>{let{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:u,onOpenChange:l,modal:d=!0}=e,c=eO(n),s=t.useRef(null),[f,p]=(0,i.T)({prop:a,defaultProp:null!=u&&u,onChange:l,caller:eT});return(0,b.jsx)(eA,{scope:n,triggerId:(0,v.M)(),triggerRef:s,contentId:(0,v.M)(),open:f,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),modal:d,children:(0,b.jsx)(V,{...c,open:f,onOpenChange:p,dir:o,modal:d,children:r})})};eL.displayName=eT;var eZ="DropdownMenuTrigger",eK=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,disabled:t=!1,...u}=e,i=eF(eZ,r),d=eO(r);return(0,b.jsx)(U,{asChild:!0,...d,children:(0,b.jsx)(l.WV.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.F)(n,i.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eK.displayName=eZ;var eV=e=>{let{__scopeDropdownMenu:n,...r}=e,t=eO(n);return(0,b.jsx)(B,{...t,...r})};eV.displayName="DropdownMenuPortal";var eU="DropdownMenuContent",eW=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...a}=e,u=eF(eU,r),i=eO(r),l=t.useRef(!1);return(0,b.jsx)(Y,{id:u.contentId,"aria-labelledby":u.triggerId,...i,...a,ref:n,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var n;l.current||null===(n=u.triggerRef.current)||void 0===n||n.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;(!u.modal||t)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eW.displayName=eU;var ez=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(en,{...o,...t,ref:n})});ez.displayName="DropdownMenuGroup";var eG=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(er,{...o,...t,ref:n})});eG.displayName="DropdownMenuLabel";var eB=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(ea,{...o,...t,ref:n})});eB.displayName="DropdownMenuItem";var eH=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(ei,{...o,...t,ref:n})});eH.displayName="DropdownMenuCheckboxItem";var eq=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(es,{...o,...t,ref:n})});eq.displayName="DropdownMenuRadioGroup";var eX=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(ep,{...o,...t,ref:n})});eX.displayName="DropdownMenuRadioItem";var eY=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(ey,{...o,...t,ref:n})});eY.displayName="DropdownMenuItemIndicator";var eJ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(eg,{...o,...t,ref:n})});eJ.displayName="DropdownMenuSeparator",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(ew,{...o,...t,ref:n})}).displayName="DropdownMenuArrow";var eQ=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(ej,{...o,...t,ref:n})});eQ.displayName="DropdownMenuSubTrigger";var e$=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eO(r);return(0,b.jsx)(eD,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var e0=eL,e1=eK,e2=eV,e7=eW,e4=ez,e9=eG,e3=eB,e5=eH,e6=eq,e8=eX,ne=eY,nn=eJ,nr=e=>{let{__scopeDropdownMenu:n,children:r,open:t,onOpenChange:o,defaultOpen:a}=e,u=eO(n),[l,d]=(0,i.T)({prop:t,defaultProp:null!=a&&a,onChange:o,caller:"DropdownMenuSub"});return(0,b.jsx)(eb,{...u,open:l,onOpenChange:d,children:r})},nt=eQ,no=e$},2642:function(e,n,r){r.d(n,{z:function(){return u}});var t=r(2265),o=r(1266),a=r(2618),u=e=>{var n,r;let u,l;let{present:d,children:c}=e,s=function(e){var n,r;let[o,u]=t.useState(),l=t.useRef(null),d=t.useRef(e),c=t.useRef("none"),[s,f]=(n=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,n)=>{let t=r[e][n];return null!=t?t:e},n));return t.useEffect(()=>{let e=i(l.current);c.current="mounted"===s?e:"none"},[s]),(0,a.b)(()=>{let n=l.current,r=d.current;if(r!==e){let t=c.current,o=i(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):r&&t!==o?f("ANIMATION_OUT"):f("UNMOUNT"),d.current=e}},[e,f]),(0,a.b)(()=>{if(o){var e;let n;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,t=e=>{let t=i(l.current).includes(e.animationName);if(e.target===o&&t&&(f("ANIMATION_END"),!d.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=i(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:t.useCallback(e=>{l.current=e?getComputedStyle(e):null,u(e)},[])}}(d),f="function"==typeof c?c({present:s.isPresent}):t.Children.only(c),p=(0,o.e)(s.ref,(u=null===(n=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in u&&u.isReactWarning?f.ref:(u=null===(r=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in u&&u.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof c||s.isPresent?t.cloneElement(f,{ref:p}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"},3715:function(e,n,r){r.d(n,{Pc:function(){return x},ck:function(){return N},fC:function(){return _}});var t=r(2265),o=r(4991),a=r(5528),u=r(1266),i=r(4104),l=r(8687),d=r(9586),c=r(9830),s=r(9310),f=r(3876),p=r(7437),v="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[y,g,w]=(0,a.B)(m),[M,x]=(0,i.b)(m,[w]),[k,b]=M(m),C=t.forwardRef((e,n)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:n})})}));C.displayName=m;var j=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:l,currentTabStopId:y,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:M,onEntryFocus:x,preventScrollOnEntryFocus:b=!1,...C}=e,j=t.useRef(null),R=(0,u.e)(n,j),D=(0,f.gm)(l),[E,_]=(0,s.T)({prop:y,defaultProp:null!=w?w:null,onChange:M,caller:m}),[N,T]=t.useState(!1),P=(0,c.W)(x),S=g(r),O=t.useRef(!1),[A,F]=t.useState(0);return t.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(v,P),()=>e.removeEventListener(v,P)},[P]),(0,p.jsx)(k,{scope:r,orientation:a,dir:D,loop:i,currentTabStopId:E,onItemFocus:t.useCallback(e=>_(e),[_]),onItemShiftTab:t.useCallback(()=>T(!0),[]),onFocusableItemAdd:t.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>F(e=>e-1),[]),children:(0,p.jsx)(d.WV.div,{tabIndex:N||0===A?-1:0,"data-orientation":a,...C,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let n=!O.current;if(e.target===e.currentTarget&&n&&!N){let n=new CustomEvent(v,h);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=S().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),b)}}O.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>T(!1))})})}),R="RovingFocusGroupItem",D=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:u=!1,tabStopId:i,children:c,...s}=e,f=(0,l.M)(),v=i||f,h=b(R,r),m=h.currentTabStopId===v,w=g(r),{onFocusableItemAdd:M,onFocusableItemRemove:x,currentTabStopId:k}=h;return t.useEffect(()=>{if(a)return M(),()=>x()},[a,M,x]),(0,p.jsx)(y.ItemSlot,{scope:r,id:v,focusable:a,active:u,children:(0,p.jsx)(d.WV.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...s,ref:n,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?h.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>h.onItemFocus(v)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let n=function(e,n,r){var t;let o=(t=e.key,"rtl"!==r?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,h.orientation,h.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)o.reverse();else if("prev"===n||"next"===n){var r,t;"prev"===n&&o.reverse();let a=o.indexOf(e.currentTarget);o=h.loop?(r=o,t=a+1,r.map((e,n)=>r[(t+n)%r.length])):o.slice(a+1)}setTimeout(()=>I(o))}}),children:"function"==typeof c?c({isCurrentTabStop:m,hasTabStop:null!=k}):c})})});D.displayName=R;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let t of e)if(t===r||(t.focus({preventScroll:n}),document.activeElement!==r))return}var _=C,N=D}}]);