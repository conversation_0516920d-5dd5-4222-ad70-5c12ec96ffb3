{"/api/fixtures/route": "app/api/fixtures/route.js", "/api/auth/profile/route": "app/api/auth/profile/route.js", "/api/fixtures/[id]/route": "app/api/fixtures/[id]/route.js", "/api/broadcast-links/fixture/[fixtureId]/route": "app/api/broadcast-links/fixture/[fixtureId]/route.js", "/dashboard/teams/page": "app/dashboard/teams/page.js", "/dashboard/fixtures/page": "app/dashboard/fixtures/page.js", "/dashboard/users/registered/page": "app/dashboard/users/registered/page.js", "/dashboard/users/tiers/page": "app/dashboard/users/tiers/page.js", "/dashboard/settings/page": "app/dashboard/settings/page.js", "/dashboard/api-test/page": "app/dashboard/api-test/page.js", "/dashboard/fixtures/[id]/page": "app/dashboard/fixtures/[id]/page.js"}