globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/fixtures/[id]/edit/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/error-boundary.tsx":{"*":{"id":"(ssr)/./src/components/ui/error-boundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/providers/query-provider.tsx":{"*":{"id":"(ssr)/./src/lib/providers/query-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/providers/theme-provider.tsx":{"*":{"id":"(ssr)/./src/lib/providers/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/fixtures/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/fixtures/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/fixtures/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/fixtures/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/home/<USER>/FECMS-sport/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/FECMS-sport/node_modules/sonner/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/FECMS-sport/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx":{"id":"(app-pages-browser)/./src/components/ui/error-boundary.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx":{"id":"(app-pages-browser)/./src/lib/providers/query-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/FECMS-sport/src/lib/providers/theme-provider.tsx":{"id":"(app-pages-browser)/./src/lib/providers/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/FECMS-sport/src/app/dashboard/layout.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/layout.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx","name":"*","chunks":["app/dashboard/fixtures/page","static/chunks/app/dashboard/fixtures/page.js"],"async":false},"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/fixtures/[id]/page.tsx","name":"*","chunks":["app/dashboard/fixtures/[id]/page","static/chunks/app/dashboard/fixtures/%5Bid%5D/page.js"],"async":false},"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/fixtures/[id]/edit/page.tsx","name":"*","chunks":["app/dashboard/fixtures/[id]/edit/page","static/chunks/app/dashboard/fixtures/%5Bid%5D/edit/page.js"],"async":false}},"entryCSSFiles":{"/home/<USER>/FECMS-sport/src/app/layout":["static/css/app/layout.css"],"/home/<USER>/FECMS-sport/src/app/not-found":[],"/home/<USER>/FECMS-sport/src/app/dashboard/layout":[],"/home/<USER>/FECMS-sport/src/app/dashboard/page":[],"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page":[],"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page":[],"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/edit/page":[]}}