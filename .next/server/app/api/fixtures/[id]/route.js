"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/fixtures/[id]/route";
exports.ids = ["app/api/fixtures/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_fixtures_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/fixtures/[id]/route.ts */ \"(rsc)/./src/app/api/fixtures/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/fixtures/[id]/route\",\n        pathname: \"/api/fixtures/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/fixtures/[id]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/fixtures/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_fixtures_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/fixtures/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/fixtures/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/fixtures/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const fixtureId = params.id;\n        console.log(\"\\uD83D\\uDD04 Proxying fixture detail request:\", `${API_BASE_URL}/football/fixtures/${fixtureId}`);\n        const response = await fetch(`${API_BASE_URL}/football/fixtures/${fixtureId}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch fixture\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ Fixture detail fetched successfully:\", data.data?.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request, { params }) {\n    try {\n        const fixtureId = params.id;\n        const body = await request.json();\n        console.log(\"\\uD83D\\uDD04 Proxying fixture update request:\", `${API_BASE_URL}/football/fixtures/${fixtureId}`);\n        const response = await fetch(`${API_BASE_URL}/football/fixtures/${fixtureId}`, {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to update fixture\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ Fixture updated successfully:\", data.data?.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const fixtureId = params.id;\n        console.log(\"\\uD83D\\uDD04 Proxying fixture delete request:\", `${API_BASE_URL}/football/fixtures/${fixtureId}`);\n        const response = await fetch(`${API_BASE_URL}/football/fixtures/${fixtureId}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to delete fixture\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        console.log(\"✅ Fixture deleted successfully:\", fixtureId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Fixture deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9maXh0dXJlcy9baWRdL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0Q7QUFFeEQsTUFBTUMsZUFBZUMsdUJBQW9DLElBQUk7QUFFdEQsZUFBZUcsSUFDcEJDLE9BQW9CLEVBQ3BCLEVBQUVDLE1BQU0sRUFBOEI7SUFFdEMsSUFBSTtRQUNGLE1BQU1DLFlBQVlELE9BQU9FLEVBQUU7UUFFM0JDLFFBQVFDLEdBQUcsQ0FBQyxpREFBdUMsQ0FBQyxFQUFFVixhQUFhLG1CQUFtQixFQUFFTyxVQUFVLENBQUM7UUFFbkcsTUFBTUksV0FBVyxNQUFNQyxNQUFNLENBQUMsRUFBRVosYUFBYSxtQkFBbUIsRUFBRU8sVUFBVSxDQUFDLEVBQUU7WUFDN0VNLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7Z0JBQ2hCLEdBQUlULFFBQVFTLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQjtvQkFDMUMsaUJBQWlCVixRQUFRUyxPQUFPLENBQUNDLEdBQUcsQ0FBQztnQkFDdkMsQ0FBQztZQUNIO1FBQ0Y7UUFFQSxJQUFJLENBQUNKLFNBQVNLLEVBQUUsRUFBRTtZQUNoQlAsUUFBUVEsS0FBSyxDQUFDLGdCQUFnQk4sU0FBU08sTUFBTSxFQUFFUCxTQUFTUSxVQUFVO1lBQ2xFLE9BQU9wQixxREFBWUEsQ0FBQ3FCLElBQUksQ0FDdEI7Z0JBQ0VILE9BQU87Z0JBQ1BDLFFBQVFQLFNBQVNPLE1BQU07Z0JBQ3ZCRyxTQUFTVixTQUFTUSxVQUFVO1lBQzlCLEdBQ0E7Z0JBQUVELFFBQVFQLFNBQVNPLE1BQU07WUFBQztRQUU5QjtRQUVBLE1BQU1JLE9BQU8sTUFBTVgsU0FBU1MsSUFBSTtRQUNoQ1gsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ1ksS0FBS0EsSUFBSSxFQUFFZDtRQUVqRSxPQUFPVCxxREFBWUEsQ0FBQ3FCLElBQUksQ0FBQ0U7SUFDM0IsRUFBRSxPQUFPTCxPQUFZO1FBQ25CUixRQUFRUSxLQUFLLENBQUMsa0JBQWtCQTtRQUNoQyxPQUFPbEIscURBQVlBLENBQUNxQixJQUFJLENBQ3RCO1lBQ0VILE9BQU87WUFDUEksU0FBU0osTUFBTUksT0FBTztRQUN4QixHQUNBO1lBQUVILFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRU8sZUFBZUssSUFDcEJsQixPQUFvQixFQUNwQixFQUFFQyxNQUFNLEVBQThCO0lBRXRDLElBQUk7UUFDRixNQUFNQyxZQUFZRCxPQUFPRSxFQUFFO1FBQzNCLE1BQU1nQixPQUFPLE1BQU1uQixRQUFRZSxJQUFJO1FBRS9CWCxRQUFRQyxHQUFHLENBQUMsaURBQXVDLENBQUMsRUFBRVYsYUFBYSxtQkFBbUIsRUFBRU8sVUFBVSxDQUFDO1FBRW5HLE1BQU1JLFdBQVcsTUFBTUMsTUFBTSxDQUFDLEVBQUVaLGFBQWEsbUJBQW1CLEVBQUVPLFVBQVUsQ0FBQyxFQUFFO1lBQzdFTSxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixHQUFJVCxRQUFRUyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxvQkFBb0I7b0JBQzFDLGlCQUFpQlYsUUFBUVMsT0FBTyxDQUFDQyxHQUFHLENBQUM7Z0JBQ3ZDLENBQUM7WUFDSDtZQUNBUyxNQUFNQyxLQUFLQyxTQUFTLENBQUNGO1FBQ3ZCO1FBRUEsSUFBSSxDQUFDYixTQUFTSyxFQUFFLEVBQUU7WUFDaEJQLFFBQVFRLEtBQUssQ0FBQyxnQkFBZ0JOLFNBQVNPLE1BQU0sRUFBRVAsU0FBU1EsVUFBVTtZQUNsRSxPQUFPcEIscURBQVlBLENBQUNxQixJQUFJLENBQ3RCO2dCQUNFSCxPQUFPO2dCQUNQQyxRQUFRUCxTQUFTTyxNQUFNO2dCQUN2QkcsU0FBU1YsU0FBU1EsVUFBVTtZQUM5QixHQUNBO2dCQUFFRCxRQUFRUCxTQUFTTyxNQUFNO1lBQUM7UUFFOUI7UUFFQSxNQUFNSSxPQUFPLE1BQU1YLFNBQVNTLElBQUk7UUFDaENYLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNZLEtBQUtBLElBQUksRUFBRWQ7UUFFMUQsT0FBT1QscURBQVlBLENBQUNxQixJQUFJLENBQUNFO0lBQzNCLEVBQUUsT0FBT0wsT0FBWTtRQUNuQlIsUUFBUVEsS0FBSyxDQUFDLGtCQUFrQkE7UUFDaEMsT0FBT2xCLHFEQUFZQSxDQUFDcUIsSUFBSSxDQUN0QjtZQUNFSCxPQUFPO1lBQ1BJLFNBQVNKLE1BQU1JLE9BQU87UUFDeEIsR0FDQTtZQUFFSCxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVPLGVBQWVTLE9BQ3BCdEIsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUE4QjtJQUV0QyxJQUFJO1FBQ0YsTUFBTUMsWUFBWUQsT0FBT0UsRUFBRTtRQUUzQkMsUUFBUUMsR0FBRyxDQUFDLGlEQUF1QyxDQUFDLEVBQUVWLGFBQWEsbUJBQW1CLEVBQUVPLFVBQVUsQ0FBQztRQUVuRyxNQUFNSSxXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFWixhQUFhLG1CQUFtQixFQUFFTyxVQUFVLENBQUMsRUFBRTtZQUM3RU0sUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtnQkFDaEIsR0FBSVQsUUFBUVMsT0FBTyxDQUFDQyxHQUFHLENBQUMsb0JBQW9CO29CQUMxQyxpQkFBaUJWLFFBQVFTLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDO2dCQUN2QyxDQUFDO1lBQ0g7UUFDRjtRQUVBLElBQUksQ0FBQ0osU0FBU0ssRUFBRSxFQUFFO1lBQ2hCUCxRQUFRUSxLQUFLLENBQUMsZ0JBQWdCTixTQUFTTyxNQUFNLEVBQUVQLFNBQVNRLFVBQVU7WUFDbEUsT0FBT3BCLHFEQUFZQSxDQUFDcUIsSUFBSSxDQUN0QjtnQkFDRUgsT0FBTztnQkFDUEMsUUFBUVAsU0FBU08sTUFBTTtnQkFDdkJHLFNBQVNWLFNBQVNRLFVBQVU7WUFDOUIsR0FDQTtnQkFBRUQsUUFBUVAsU0FBU08sTUFBTTtZQUFDO1FBRTlCO1FBRUFULFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNIO1FBRS9DLE9BQU9SLHFEQUFZQSxDQUFDcUIsSUFBSSxDQUFDO1lBQUVRLFNBQVM7WUFBTVAsU0FBUztRQUErQjtJQUNwRixFQUFFLE9BQU9KLE9BQVk7UUFDbkJSLFFBQVFRLEtBQUssQ0FBQyxrQkFBa0JBO1FBQ2hDLE9BQU9sQixxREFBWUEsQ0FBQ3FCLElBQUksQ0FDdEI7WUFDRUgsT0FBTztZQUNQSSxTQUFTSixNQUFNSSxPQUFPO1FBQ3hCLEdBQ0E7WUFBRUgsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbXMtYXBpc3BvcnRzZ2FtZS8uL3NyYy9hcHAvYXBpL2ZpeHR1cmVzL1tpZF0vcm91dGUudHM/YTBiMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuXG5jb25zdCBBUElfQkFTRV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMCc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgZml4dHVyZUlkID0gcGFyYW1zLmlkO1xuICAgIFxuICAgIGNvbnNvbGUubG9nKCfwn5SEIFByb3h5aW5nIGZpeHR1cmUgZGV0YWlsIHJlcXVlc3Q6JywgYCR7QVBJX0JBU0VfVVJMfS9mb290YmFsbC9maXh0dXJlcy8ke2ZpeHR1cmVJZH1gKTtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS9mb290YmFsbC9maXh0dXJlcy8ke2ZpeHR1cmVJZH1gLCB7XG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAuLi4ocmVxdWVzdC5oZWFkZXJzLmdldCgnYXV0aG9yaXphdGlvbicpICYmIHtcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IHJlcXVlc3QuaGVhZGVycy5nZXQoJ2F1dGhvcml6YXRpb24nKSFcbiAgICAgICAgfSlcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgQVBJIEVycm9yOicsIHJlc3BvbnNlLnN0YXR1cywgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgXG4gICAgICAgICAgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggZml4dHVyZScsXG4gICAgICAgICAgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2Uuc3RhdHVzVGV4dCBcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgY29uc29sZS5sb2coJ+KchSBGaXh0dXJlIGRldGFpbCBmZXRjaGVkIHN1Y2Nlc3NmdWxseTonLCBkYXRhLmRhdGE/LmlkKTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihkYXRhKTtcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBQcm94eSBFcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBcbiAgICAgICAgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InLFxuICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlIFxuICAgICAgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBVVChcbiAgcmVxdWVzdDogTmV4dFJlcXVlc3QsXG4gIHsgcGFyYW1zIH06IHsgcGFyYW1zOiB7IGlkOiBzdHJpbmcgfSB9XG4pIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBmaXh0dXJlSWQgPSBwYXJhbXMuaWQ7XG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuICAgIFxuICAgIGNvbnNvbGUubG9nKCfwn5SEIFByb3h5aW5nIGZpeHR1cmUgdXBkYXRlIHJlcXVlc3Q6JywgYCR7QVBJX0JBU0VfVVJMfS9mb290YmFsbC9maXh0dXJlcy8ke2ZpeHR1cmVJZH1gKTtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS9mb290YmFsbC9maXh0dXJlcy8ke2ZpeHR1cmVJZH1gLCB7XG4gICAgICBtZXRob2Q6ICdQQVRDSCcsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIC4uLihyZXF1ZXN0LmhlYWRlcnMuZ2V0KCdhdXRob3JpemF0aW9uJykgJiYge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogcmVxdWVzdC5oZWFkZXJzLmdldCgnYXV0aG9yaXphdGlvbicpIVxuICAgICAgICB9KVxuICAgICAgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGJvZHkpLFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEFQSSBFcnJvcjonLCByZXNwb25zZS5zdGF0dXMsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IFxuICAgICAgICAgIGVycm9yOiAnRmFpbGVkIHRvIHVwZGF0ZSBmaXh0dXJlJyxcbiAgICAgICAgICBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cyxcbiAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5zdGF0dXNUZXh0IFxuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICBjb25zb2xlLmxvZygn4pyFIEZpeHR1cmUgdXBkYXRlZCBzdWNjZXNzZnVsbHk6JywgZGF0YS5kYXRhPy5pZCk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oZGF0YSk7XG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCfinYwgUHJveHkgRXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgXG4gICAgICAgIGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyxcbiAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSBcbiAgICAgIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBERUxFVEUoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgZml4dHVyZUlkID0gcGFyYW1zLmlkO1xuICAgIFxuICAgIGNvbnNvbGUubG9nKCfwn5SEIFByb3h5aW5nIGZpeHR1cmUgZGVsZXRlIHJlcXVlc3Q6JywgYCR7QVBJX0JBU0VfVVJMfS9mb290YmFsbC9maXh0dXJlcy8ke2ZpeHR1cmVJZH1gKTtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS9mb290YmFsbC9maXh0dXJlcy8ke2ZpeHR1cmVJZH1gLCB7XG4gICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAuLi4ocmVxdWVzdC5oZWFkZXJzLmdldCgnYXV0aG9yaXphdGlvbicpICYmIHtcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IHJlcXVlc3QuaGVhZGVycy5nZXQoJ2F1dGhvcml6YXRpb24nKSFcbiAgICAgICAgfSlcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgQVBJIEVycm9yOicsIHJlc3BvbnNlLnN0YXR1cywgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgXG4gICAgICAgICAgZXJyb3I6ICdGYWlsZWQgdG8gZGVsZXRlIGZpeHR1cmUnLFxuICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgIG1lc3NhZ2U6IHJlc3BvbnNlLnN0YXR1c1RleHQgXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygn4pyFIEZpeHR1cmUgZGVsZXRlZCBzdWNjZXNzZnVsbHk6JywgZml4dHVyZUlkKTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IHN1Y2Nlc3M6IHRydWUsIG1lc3NhZ2U6ICdGaXh0dXJlIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyB9KTtcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBQcm94eSBFcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBcbiAgICAgICAgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InLFxuICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlIFxuICAgICAgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJBUElfQkFTRV9VUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMIiwiR0VUIiwicmVxdWVzdCIsInBhcmFtcyIsImZpeHR1cmVJZCIsImlkIiwiY29uc29sZSIsImxvZyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiZ2V0Iiwib2siLCJlcnJvciIsInN0YXR1cyIsInN0YXR1c1RleHQiLCJqc29uIiwibWVzc2FnZSIsImRhdGEiLCJQVVQiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIkRFTEVURSIsInN1Y2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/fixtures/[id]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();