# 🔧 Module 13: Fixtures Edit Dropdown Value Display Fixes
**Date**: 26/05/2025  
**Status**: 🔄 In Progress  
**Priority**: 🔥 High  
**Duration**: 30 minutes  

## 🎯 **Module Overview**
Sửa các vấn đề với dropdown values không hiển thị trong edit form và venue name mapping từ API response.

## 📊 **Issues Identified**

### **❌ Current Issues (Reported by User)**
1. **Home Team* và Away Team* và League***: Hiện chỉ có dropdown, cần hiển thị giá trị đã chọn để cải thiện UX
2. **Venue Name**: Không thấy giá trị
3. **API Data Mapping**: Cần check endpoint http://localhost:3000/football/fixtures/1274455

### **🔍 Root Cause Analysis**

#### **API Response Structure**
```json
{
  "data": {
    "id": 1922,
    "externalId": 1274455,
    "leagueId": 570,
    "leagueName": "Premier League",
    "homeTeamId": 22782,
    "homeTeamName": "Vision",
    "homeTeamLogo": "public/images/teams/22782.png",
    "awayTeamId": 12244,
    "awayTeamName": "Berekum Chelsea",
    "awayTeamLogo": "public/images/teams/12244.png",
    "venue": {
      "id": 21453,
      "name": "Nii Adjei Kraku II Sports Complex",
      "city": "Tema"
    },
    "referee": "",
    "status": "FT",
    // ... other fields
  }
}
```

#### **Issues Found**
1. **Venue Mapping**: API returns `venue.name` và `venue.city`, không phải `venueName` và `venueCity`
2. **SelectField Display**: Radix UI SelectValue không support custom content properly
3. **Form Data Population**: Cần đảm bảo form data được populate đúng với API response

## 🧩 **Implementation Details**

### **1. Fixed Venue Data Mapping**
```typescript
// Updated: src/app/dashboard/fixtures/[id]/edit/page.tsx
useEffect(() => {
  if (fixture) {
    const fixtureDate = new Date(fixture.date);
    console.log('🔄 Populating form with fixture data:', {
      homeTeamId: fixture.homeTeamId,
      awayTeamId: fixture.awayTeamId,
      leagueId: fixture.leagueId,
      venue: fixture.venue,
      venueName: fixture.venueName,
      venueCity: fixture.venueCity
    });
    
    setFormData({
      homeTeamId: fixture.homeTeamId?.toString() || '',
      awayTeamId: fixture.awayTeamId?.toString() || '',
      leagueId: fixture.leagueId?.toString() || '',
      date: fixtureDate.toISOString().split('T')[0],
      time: fixtureDate.toTimeString().slice(0, 5),
      // ✅ FIX: API returns venue.name and venue.city
      venueName: fixture.venue?.name || fixture.venueName || '',
      venueCity: fixture.venue?.city || fixture.venueCity || '',
      round: fixture.round || '',
      status: fixture.status || '',
      goalsHome: fixture.goalsHome?.toString() || '',
      goalsAway: fixture.goalsAway?.toString() || '',
      elapsed: fixture.elapsed?.toString() || '',
      referee: fixture.referee || '',
      temperature: fixture.temperature?.toString() || '',
      weather: fixture.weather || '',
      attendance: fixture.attendance?.toString() || '',
    });
  }
}, [fixture]);
```

### **2. Enhanced SelectField với Custom Display**
```typescript
// Updated: src/components/ui/form-field.tsx
export const SelectField = forwardRef<HTMLButtonElement, SelectFieldProps>(
  ({ label, description, error, required, placeholder, value, onValueChange, options, className, disabled }, ref) => {
    const selectedOption = options.find(option => option.value === value);
    const CDN_URL = process.env.NEXT_PUBLIC_DOMAIN_CDN_PICTURE || 'http://**************';

    // Custom display for selected value
    const renderSelectedValue = () => {
      if (!selectedOption) return placeholder;
      
      return (
        <div className="flex items-center space-x-2">
          {selectedOption.logo && (
            <img
              src={`${CDN_URL}/${selectedOption.logo}`}
              alt={selectedOption.label}
              className="w-5 h-5 object-contain rounded"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
          )}
          <span>{selectedOption.label}</span>
        </div>
      );
    };

    return (
      <FormField label={label} description={description} error={error} required={required}>
        <Select value={value} onValueChange={onValueChange} disabled={disabled}>
          <SelectTrigger
            ref={ref}
            className={cn(error && 'border-red-500 focus:border-red-500', className)}
          >
            {/* ✅ FIX: Custom display instead of SelectValue */}
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center space-x-2 flex-1">
                {selectedOption ? renderSelectedValue() : (
                  <span className="text-muted-foreground">{placeholder}</span>
                )}
              </div>
            </div>
          </SelectTrigger>
          <SelectContent>
            {options.map((option) => (
              <SelectItem key={option.value} value={option.value} disabled={option.disabled}>
                <div className="flex items-center space-x-2">
                  {option.logo && (
                    <img
                      src={`${CDN_URL}/${option.logo}`}
                      alt={option.label}
                      className="w-5 h-5 object-contain rounded"
                      onError={(e) => { e.currentTarget.style.display = 'none'; }}
                    />
                  )}
                  <span>{option.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </FormField>
    );
  }
);
```

### **3. Added Debug Logging**
```typescript
// Added comprehensive debug logging
console.log('🔍 Form Debug:', {
  formData,
  leagueOptionsCount: leagueOptions.length,
  teamOptionsCount: teamOptions.length,
  selectedLeague: leagueOptions.find(l => l.value === formData.leagueId),
  selectedHomeTeam: teamOptions.find(t => t.value === formData.homeTeamId),
  selectedAwayTeam: teamOptions.find(t => t.value === formData.awayTeamId),
});
```

## 📁 **Files Modified**

```
src/app/dashboard/fixtures/[id]/edit/
└── page.tsx                   # ✅ FIXED - Venue data mapping và debug logging

src/components/ui/
└── form-field.tsx             # ✅ ENHANCED - SelectField custom display
```

## 🔄 **Expected Results**

### **After Fixes**
1. **Home Team Dropdown**: Should display "Vision" với logo
2. **Away Team Dropdown**: Should display "Berekum Chelsea" với logo  
3. **League Dropdown**: Should display "Premier League" với logo
4. **Venue Name Field**: Should display "Nii Adjei Kraku II Sports Complex"
5. **Venue City Field**: Should display "Tema"

### **API Data Mapping**
- **homeTeamId**: `22782` → Should find team trong options
- **awayTeamId**: `12244` → Should find team trong options  
- **leagueId**: `570` → Should find league trong options
- **venue.name**: `"Nii Adjei Kraku II Sports Complex"` → venueName field
- **venue.city**: `"Tema"` → venueCity field

## 🚨 **Potential Issues & Solutions**

### **Issue 1: SelectField Display**
**Problem**: Radix UI SelectValue không support custom JSX content
**Solution**: Replace SelectValue với custom div display

### **Issue 2: Data Type Mismatch**
**Problem**: API returns numbers, form expects strings
**Solution**: Convert với `.toString()` trong form population

### **Issue 3: Async Data Loading**
**Problem**: Options chưa load khi form data được set
**Solution**: Debug logging để verify timing

## ✅ **Testing Checklist**

### **Functional Testing**
- [ ] 🔄 Home Team dropdown shows selected value với logo
- [ ] 🔄 Away Team dropdown shows selected value với logo
- [ ] 🔄 League dropdown shows selected value với logo
- [ ] 🔄 Venue Name field shows correct value
- [ ] 🔄 Venue City field shows correct value
- [ ] 🔄 All other fields populate correctly

### **Debug Verification**
- [ ] 🔄 Console logs show correct form data
- [ ] 🔄 Console logs show options are loaded
- [ ] 🔄 Console logs show selected options found
- [ ] 🔄 No JavaScript errors trong console

### **UI/UX Testing**
- [ ] 🔄 Dropdowns look professional với logos
- [ ] 🔄 Selected values clearly visible
- [ ] 🔄 Loading states work correctly
- [ ] 🔄 Error handling for missing logos

## 🎯 **Next Steps**

### **Immediate Actions**
1. **Test edit page** tại http://localhost:3001/dashboard/fixtures/1274455/edit
2. **Check browser console** cho debug logs
3. **Verify dropdown displays** show selected values
4. **Test form submission** để ensure data integrity

### **If Issues Persist**
1. **Check API response** format vs expected format
2. **Verify options loading** timing vs form population
3. **Debug SelectField** component behavior
4. **Consider alternative** SelectField implementation

## 📈 **Expected Impact**

### **User Experience**
- **Clear Visual Feedback**: Users see selected values immediately
- **Professional Appearance**: Logos make interface more polished
- **Better Usability**: No confusion about current selections
- **Complete Data**: All venue information properly displayed

### **Developer Experience**
- **Better Debugging**: Comprehensive logging for troubleshooting
- **Maintainable Code**: Clear separation of concerns
- **Reusable Components**: Enhanced SelectField can be used elsewhere
- **Type Safety**: Proper data type handling

---

## 🎯 **Current Status**

**Module 13 in progress...** 

✅ **Venue Data Mapping**: Fixed API response mapping  
✅ **SelectField Enhancement**: Custom display implementation  
✅ **Debug Logging**: Comprehensive debugging added  
🔄 **Testing**: Waiting for browser verification  

**Next**: Verify fixes work correctly và complete testing checklist.
