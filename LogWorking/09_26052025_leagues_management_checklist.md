# 🏆 Module 9: Leagues Management - Detailed Checklist
**Date**: 26/05/2025  
**Status**: 🚧 Ready to Start  
**Priority**: 🔥 High  
**Estimated Duration**: 2-3 days  

## 🎯 **Module Overview**
Create comprehensive Leagues Management system following established patterns from Fixtures module, với full CRUD operations, filtering, search, và country-based organization.

## 📊 **API Endpoints Available (From Swagger)**
```typescript
✅ GET /football/leagues - Public, with filters
✅ GET /football/leagues/{externalId} - Requires auth 
✅ POST /football/leagues - Editor+ only
✅ PATCH /football/leagues/{id} - Editor+ only
```

## 🧩 **Detailed Implementation Checklist**

### **📦 Phase 1: Foundation & Types (Day 1 Morning)**

#### **1.1 TypeScript Interfaces** ⏳
- [ ] **1.1.1** Create League interface in `/src/lib/types/api.ts`
  - [ ] id, externalId, name, country, logo, flag 
  - [ ] season, active, type, createdAt, updatedAt
  - [ ] Extend existing types or create new League interface
- [ ] **1.1.2** Create LeagueFilters interface
  - [ ] page, limit, country, active, season, search
- [ ] **1.1.3** Create CreateLeagueDto và UpdateLeagueDto
- [ ] **1.1.4** Validate against swagger schemas

#### **1.2 API Client Setup** ⏳  
- [ ] **1.2.1** Create `/src/lib/api/leagues.ts` (similar to fixtures.ts)
  - [ ] getLeagues() với filtering support
  - [ ] getLeagueById() with auth headers
  - [ ] createLeague() for Editor+ roles
  - [ ] updateLeague() for Editor+ roles
  - [ ] Use Next.js proxy pattern like fixtures
- [ ] **1.2.2** Create API proxy routes in `/src/app/api/leagues/`
  - [ ] `/api/leagues/route.ts` (GET, POST)
  - [ ] `/api/leagues/[id]/route.ts` (GET, PATCH)
  - [ ] Forward auth headers properly
- [ ] **1.2.3** Error handling và response formatting

#### **1.3 Custom Hooks** ⏳
- [ ] **1.3.1** Create `/src/lib/hooks/useLeagues.ts`
  - [ ] useLeagues() for listing với filters
  - [ ] useLeague() for single league detail
  - [ ] useCreateLeague() mutation
  - [ ] useUpdateLeague() mutation
  - [ ] Follow useFixtures.ts patterns
- [ ] **1.3.2** React Query cache management
- [ ] **1.3.3** Loading and error states

---

### **📦 Phase 2: Core Components (Day 1 Afternoon)**

#### **2.1 Leagues Listing Page** ⏳
- [ ] **2.1.1** Create `/src/app/dashboard/leagues/page.tsx`
  - [ ] Follow fixtures page.tsx structure
  - [ ] DataTable với League columns
  - [ ] Search functionality (name, country)
  - [ ] Country filter dropdown
  - [ ] Active/Inactive status filter
  - [ ] Pagination support
- [ ] **2.1.2** Define table columns
  - [ ] Logo (with fallback handling)
  - [ ] Name với country flag
  - [ ] Season và Type
  - [ ] Active status badge
  - [ ] Actions (View, Edit, Delete for role-based)
- [ ] **2.1.3** Role-based action buttons
  - [ ] Editor+: Create League button
  - [ ] Editor+: Edit/Delete actions
  - [ ] All roles: View details

#### **2.2 League Detail View** ⏳
- [ ] **2.2.1** Create `/src/app/dashboard/leagues/[id]/page.tsx`
  - [ ] League information display
  - [ ] Team listings for the league (if available)
  - [ ] Season statistics
  - [ ] Fixtures count per league
- [ ] **2.2.2** League detail components
  - [ ] LeagueInfo card với logo, country, details
  - [ ] LeagueStats component (fixtures count, teams count)
  - [ ] Related teams list
- [ ] **2.2.3** Navigation breadcrumbs
- [ ] **2.2.4** Responsive design

---

### **📦 Phase 3: CRUD Operations (Day 2 Morning)**

#### **3.1 Create League Form** ⏳
- [ ] **3.1.1** Create `/src/app/dashboard/leagues/create/page.tsx`
  - [ ] React Hook Form + Zod validation
  - [ ] Fields: name, country, logo URL, flag URL, season, active, type
  - [ ] Logo preview functionality
  - [ ] Submit handler với error handling
- [ ] **3.1.2** League form component `/src/components/leagues/LeagueForm.tsx`
  - [ ] Reusable form cho Create và Edit
  - [ ] Validation schema với Zod
  - [ ] Auto-suggestions for countries
  - [ ] Image URL validation và preview

#### **3.2 Edit League Form** ⏳  
- [ ] **3.2.1** Create `/src/app/dashboard/leagues/[id]/edit/page.tsx`
  - [ ] Pre-populate form với existing data
  - [ ] Use shared LeagueForm component
  - [ ] Update mutation integration
  - [ ] Success/error handling với toast
- [ ] **3.2.2** Form validation
  - [ ] Required fields validation
  - [ ] URL format validation for logos
  - [ ] Season year validation

#### **3.3 Delete Operations** ⏳
- [ ] **3.3.1** Delete confirmation modal
  - [ ] Reuse existing Modal component
  - [ ] Warning về impact (fixtures affected)
  - [ ] Role-based permission check
- [ ] **3.3.2** Delete mutation với optimistic updates

---

### **📦 Phase 4: Advanced Features (Day 2 Afternoon)**

#### **4.1 Filtering & Search** ⏳
- [ ] **4.1.1** Country-based filtering
  - [ ] Country dropdown với popular countries first
  - [ ] Flag display in dropdown options
  - [ ] Multiple country selection support
- [ ] **4.1.2** Search functionality
  - [ ] Real-time search trong league names
  - [ ] Debounced search input
  - [ ] Search highlighting in results
- [ ] **4.1.3** Status filtering (Active/Inactive)
- [ ] **4.1.4** Season filtering

#### **4.2 League Statistics** ⏳
- [ ] **4.2.1** League stats component
  - [ ] Fixtures count for league
  - [ ] Teams count (if data available)
  - [ ] Current season information
  - [ ] Activity indicators
- [ ] **4.2.2** Quick stats dashboard
  - [ ] Most popular leagues
  - [ ] Countries representation
  - [ ] Active vs inactive leagues

#### **4.3 Image Management** ⏳
- [ ] **4.3.1** Logo upload functionality (if needed)
  - [ ] Integration với upload API
  - [ ] Image preview và validation
  - [ ] Fallback logo handling
- [ ] **4.3.2** Country flag integration
  - [ ] Auto-loading flags based on country
  - [ ] Flag display trong tables và forms

---

### **📦 Phase 5: UI/UX Polish (Day 3)**

#### **5.1 Responsive Design** ⏳
- [ ] **5.1.1** Mobile-first approach
  - [ ] Table responsiveness on mobile
  - [ ] Touch-friendly buttons và interactions
  - [ ] Mobile navigation patterns
- [ ] **5.1.2** Tablet optimizations
- [ ] **5.1.3** Desktop layout improvements

#### **5.2 Loading States & Errors** ⏳
- [ ] **5.2.1** Skeleton loading components
  - [ ] League card skeletons
  - [ ] Table loading states
  - [ ] Form loading indicators
- [ ] **5.2.2** Error boundaries
  - [ ] API error handling
  - [ ] Network error recovery
  - [ ] User-friendly error messages
- [ ] **5.2.3** Empty states
  - [ ] No leagues found
  - [ ] No search results
  - [ ] Country with no leagues

#### **5.3 Accessibility & Performance** ⏳
- [ ] **5.3.1** ARIA labels và keyboard navigation
- [ ] **5.3.2** Focus management
- [ ] **5.3.3** Performance optimizations
  - [ ] Image lazy loading
  - [ ] Query optimization
  - [ ] Cache management

---

## 📁 **File Structure to Create**

```
src/app/dashboard/leagues/
├── page.tsx                    # Main leagues listing
├── [id]/
│   ├── page.tsx               # League detail view
│   └── edit/
│       └── page.tsx           # Edit league form
└── create/
    └── page.tsx               # Create league form

src/components/leagues/
├── LeagueForm.tsx             # Reusable league form
├── LeagueCard.tsx             # League display card
├── LeagueStats.tsx            # League statistics
└── LeagueCountryFilter.tsx    # Country filtering component

src/lib/api/
└── leagues.ts                 # League API client

src/lib/hooks/
└── useLeagues.ts              # League React Query hooks

src/app/api/leagues/
├── route.ts                   # GET, POST endpoints
└── [id]/
    └── route.ts               # GET, PATCH endpoints
```

## 🔄 **Integration Patterns (From Fixtures)**

### **1. DataTable Pattern**
```typescript
// Follow fixtures page columns structure
const columns: Column<League>[] = [
  {
    key: 'logo',
    title: 'Logo',
    render: (value) => <img src={value} fallback="/default-league.png" />
  },
  {
    key: 'name',
    title: 'League',
    sortable: true,
    filterable: true,
  },
  // ... more columns
];
```

### **2. API Integration Pattern**
```typescript
// Use same proxy pattern as fixtures
export const leaguesApi = {
  getLeagues: async (filters: LeagueFilters = {}) => {
    const response = await fetch('/api/leagues?...');
    return response.json();
  },
  // ... other methods
};
```

### **3. Form Pattern**
```typescript
// Follow fixture form patterns với React Hook Form
const form = useForm<LeagueFormData>({
  resolver: zodResolver(leagueSchema),
  defaultValues: { ... }
});
```

## 🚨 **Potential Challenges & Solutions**

### **Challenge 1: Country Data**
- **Problem**: Need comprehensive country list với flags
- **Solution**: Use existing country data from API or create static country mapping

### **Challenge 2: Logo Management** 
- **Problem**: League logos might be missing or broken
- **Solution**: Implement fallback logo system, use image proxy

### **Challenge 3: Relationship Management**
- **Problem**: Leagues có many teams và fixtures
- **Solution**: Implement lazy loading, show counts instead of full lists

## ✅ **Definition of Done**

### **Functional Requirements**
- [ ] ✅ Complete CRUD operations for leagues
- [ ] ✅ Country-based filtering và search
- [ ] ✅ Role-based permissions (Editor+ for modifications)
- [ ] ✅ Responsive design (mobile, tablet, desktop)
- [ ] ✅ Error handling và loading states

### **Technical Requirements**  
- [ ] ✅ TypeScript strict compliance
- [ ] ✅ Follows established patterns from fixtures
- [ ] ✅ React Query caching và optimization
- [ ] ✅ Accessibility compliance (ARIA, keyboard nav)
- [ ] ✅ Performance optimization (lazy loading, debouncing)

### **Code Quality**
- [ ] ✅ DRY principle compliance
- [ ] ✅ Component reusability
- [ ] ✅ Comprehensive error boundaries
- [ ] ✅ Descriptive variable và function names
- [ ] ✅ No TODO comments or placeholders

---

## 🎯 **Next Steps**
1. **Review this checklist** với user/team
2. **Start với Phase 1** - Foundation & Types
3. **Update LogWorking** after each phase completion
4. **Test thoroughly** before moving to next phase

**Ready to begin implementation! 🚀**
