const { execSync } = require('child_process');

// Load .env.local file
require('dotenv').config({ path: '.env.local' });

// Get port from environment or default
const port = process.env.PORT || '3001';

console.log(`🚀 Starting development server on port ${port}...`);

// Execute next dev with the port
try {
      execSync(`npx next dev -p ${port}`, { stdio: 'inherit' });
} catch (error) {
      console.error('Error starting development server:', error.message);
      process.exit(1);
}
