const { execSync } = require('child_process');

// Load .env.local file
require('dotenv').config({ path: '.env.local' });

// Get port from environment or default
const port = process.env.PORT || '3001';

console.log(`🚀 Starting production server on port ${port}...`);

// Execute next start with the port
try {
      execSync(`npx next start -p ${port}`, { stdio: 'inherit' });
} catch (error) {
      console.error('Error starting production server:', error.message);
      process.exit(1);
}
