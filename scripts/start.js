#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env.local
const envPath = path.join(__dirname, '..', '.env.local');
if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const envLines = envContent.split('\n');

      envLines.forEach(line => {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('#')) {
                  const [key, ...valueParts] = trimmedLine.split('=');
                  if (key && valueParts.length > 0) {
                        const value = valueParts.join('=');
                        process.env[key] = value;
                  }
            }
      });
}

// Get port from environment or default to 3001
const port = process.env.PORT || '3001';

// Start Next.js production server
const nextStart = spawn('npx', ['next', 'start', '-p', port], {
      stdio: 'inherit',
      shell: true
});

nextStart.on('close', (code) => {
      process.exit(code);
});

process.on('SIGINT', () => {
      nextStart.kill('SIGINT');
});

process.on('SIGTERM', () => {
      nextStart.kill('SIGTERM');
});
