# Port Configuration Guide

## 🎯 Simple & Clean Configuration

This project uses a **single port configuration** for development - keeping it simple!

```bash
# .env.local
PORT=3001
```

## 🚀 How it works

1. **Next.js automatically reads** the `PORT` environment variable from `.env.local`
2. **Auto-fallback**: If port 3001 is busy, Next.js automatically tries next available port (3002, 3003, etc.)
3. **No custom scripts needed** - standard npm commands work perfectly

## 📋 Available Commands

### Development
```bash
# Start development server (reads PORT from .env.local)
npm run dev

# Temporary port override
PORT=3002 npm run dev
```

### Production
```bash
# Build the application
npm run build

# Start production server (reads PORT from .env.local)
npm run start

# Temporary port override
PORT=3002 npm run start
```

## ⚙️ Changing Port

### Method 1: Edit .env.local (Recommended)
```bash
# Edit .env.local file
PORT=3002
```

### Method 2: Temporary override
```bash
PORT=3002 npm run dev
```

## 🔗 URL Structure

### Frontend (Next.js)
- **Development**: `http://localhost:3001` (or auto-assigned port)
- **Production**: `http://localhost:3001` (or configured port)

### Backend API (External)
- **All Environments**: `http://localhost:3000`
- **Configured in**: `NEXT_PUBLIC_API_BASE_URL=http://localhost:3000`

## ✅ Benefits of This Approach

1. **Simple**: Only one port to configure
2. **Flexible**: Auto-fallback to available ports
3. **Clean**: No custom scripts needed
4. **Standard**: Uses Next.js built-in port handling

## 🔄 Migration from Complex Setup

We simplified from:
```bash
# OLD - Complex setup
PORT=3001
NEXT_PUBLIC_FRONTEND_PORT=3001  # ❌ Duplicate
NEXT_PUBLIC_BACKEND_PORT=3000   # ❌ Not needed
# Custom scripts required
```

To:
```bash
# NEW - Simple setup  
PORT=3001  # ✅ Single source of truth
# Standard npm scripts work
```

## 🔗 URL Endpoints

Với port 3001 (default):
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:3000
- **CDN**: http://*************

## 📁 Files Quản Lý Port

- `.env.local` - Environment variables
- `package.json` - NPM scripts
- `scripts/dev.js` - Development wrapper
- `scripts/start.js` - Production wrapper

## 🚨 Lưu Ý

1. **NEXTAUTH_URL** trong `.env.local` cũng cần được cập nhật khi thay đổi port
2. Các URL trong client-side code sử dụng `NEXT_PUBLIC_FRONTEND_PORT`
3. API calls sử dụng `NEXT_PUBLIC_BACKEND_PORT`
