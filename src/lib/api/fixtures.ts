import { apiClient } from './client';
import { Fixture, PaginatedResponse } from '@/lib/types/api';

export interface FixtureFilters {
  page?: number;
  limit?: number;
  league?: number;
  season?: number;
  team?: number;
  venue?: number;
  date?: string;
  status?: string;
  timezone?: string;
  from?: string;
  to?: string;
}

export interface SyncResponse {
  status: string;
  message: string;
  fixturesUpserted?: number;
  details?: {
    seasonsProcessed: number[];
    leaguesProcessed: number;
    totalFixtures: number;
    duration: string;
    timestamp: string;
  };
}

export interface SyncStatus {
  lastSync: string;
  fixtures: number;
  errors: any[];
}

export const fixturesApi = {
  // Public endpoints - Using Next.js API proxy
  getFixtures: async (filters: FixtureFilters = {}): Promise<PaginatedResponse<Fixture>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    // Use Next.js API proxy instead of direct API call
    const response = await fetch(`/api/fixtures?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch fixtures: ${response.statusText}`);
    }

    return await response.json();
  },

  getFixtureById: async (externalId: number): Promise<{ data: Fixture; status: number }> => {
    // Use Next.js API proxy instead of direct API call
    const response = await fetch(`/api/fixtures/${externalId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch fixture: ${response.statusText}`);
    }

    return await response.json();
  },

  // Upcoming and Live fixtures (Public) - Using Next.js API proxy
  getUpcomingAndLive: async (filters: {
    leagueId?: number;
    limit?: number;
    page?: number;
  } = {}): Promise<PaginatedResponse<Fixture>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    // Use Next.js API proxy instead of direct API call
    const response = await fetch(`/api/fixtures/live?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch live fixtures: ${response.statusText}`);
    }

    return await response.json();
  },

  // Team schedule (Requires auth)
  getTeamSchedule: async (
    teamId: number,
    filters: {
      from?: string;
      to?: string;
      limit?: number;
      page?: number;
    } = {}
  ): Promise<PaginatedResponse<Fixture>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<Fixture>>(
      `/football/fixtures/schedules/${teamId}?${params.toString()}`
    );
    return response;
  },

  // Fixture statistics (Requires auth)
  getFixtureStatistics: async (externalId: number): Promise<{
    data: Array<{
      team: { id: number; name: string };
      statistics: Array<{ type: string; value: string | number }>;
    }>;
    status: number;
  }> => {
    const response = await apiClient.get<{
      data: Array<{
        team: { id: number; name: string };
        statistics: Array<{ type: string; value: string | number }>;
      }>;
      status: number;
    }>(`/football/fixtures/statistics/${externalId}`);
    return response;
  },

  // Admin only - Sync operations - Using Next.js API proxy
  triggerSeasonSync: async (): Promise<SyncResponse> => {
    // Get token from auth store (same pattern as other operations)
    const getAuthHeaders = (): Record<string, string> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (typeof window !== 'undefined') {
        // Try to get from Zustand store first
        try {
          const authStorage = localStorage.getItem('auth-storage');
          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            const token = parsed.state?.accessToken;
            if (token) {
              console.log('🔑 Season sync - Using token from auth store:', token.substring(0, 20) + '...');
              headers.Authorization = `Bearer ${token}`;
              return headers;
            }
          }
        } catch (error) {
          console.warn('Failed to parse auth storage:', error);
        }

        // Fallback to direct localStorage access
        const fallbackToken = localStorage.getItem('accessToken');
        if (fallbackToken) {
          console.log('🔑 Season sync - Using fallback token from localStorage');
          headers.Authorization = `Bearer ${fallbackToken}`;
          return headers;
        }
      }

      console.warn('❌ Season sync - No token found!');
      return headers;
    };

    const headers = getAuthHeaders();
    console.log('🔄 Season sync request via proxy');

    const response = await fetch('/api/fixtures/sync', {
      method: 'POST',
      headers,
      body: JSON.stringify({ type: 'season' }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Season sync failed:', response.status, response.statusText, errorData);
      throw new Error(errorData.message || `Failed to trigger season sync: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Season sync successful');
    return result;
  },

  triggerDailySync: async (): Promise<SyncResponse> => {
    // Get token from auth store (same pattern as other operations)
    const getAuthHeaders = (): Record<string, string> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (typeof window !== 'undefined') {
        // Try to get from Zustand store first
        try {
          const authStorage = localStorage.getItem('auth-storage');
          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            const token = parsed.state?.accessToken;
            if (token) {
              console.log('🔑 Daily sync - Using token from auth store:', token.substring(0, 20) + '...');
              headers.Authorization = `Bearer ${token}`;
              return headers;
            }
          }
        } catch (error) {
          console.warn('Failed to parse auth storage:', error);
        }

        // Fallback to direct localStorage access
        const fallbackToken = localStorage.getItem('accessToken');
        if (fallbackToken) {
          console.log('🔑 Daily sync - Using fallback token from localStorage');
          headers.Authorization = `Bearer ${fallbackToken}`;
          return headers;
        }
      }

      console.warn('❌ Daily sync - No token found!');
      return headers;
    };

    const headers = getAuthHeaders();
    console.log('🔄 Daily sync request via proxy');

    const response = await fetch('/api/fixtures/sync', {
      method: 'POST',
      headers,
      body: JSON.stringify({ type: 'daily' }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Daily sync failed:', response.status, response.statusText, errorData);
      throw new Error(errorData.message || `Failed to trigger daily sync: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Daily sync successful');
    return result;
  },

  // Editor+ - Sync status - Using Next.js API proxy
  getSyncStatus: async (): Promise<SyncStatus> => {
    // Get token from auth store (same pattern as other operations)
    const getAuthHeaders = (): Record<string, string> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (typeof window !== 'undefined') {
        // Try to get from Zustand store first
        try {
          const authStorage = localStorage.getItem('auth-storage');
          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            const token = parsed.state?.accessToken;
            if (token) {
              console.log('🔑 Sync status - Using token from auth store:', token.substring(0, 20) + '...');
              headers.Authorization = `Bearer ${token}`;
              return headers;
            }
          }
        } catch (error) {
          console.warn('Failed to parse auth storage:', error);
        }

        // Fallback to direct localStorage access
        const fallbackToken = localStorage.getItem('accessToken');
        if (fallbackToken) {
          console.log('🔑 Sync status - Using fallback token from localStorage');
          headers.Authorization = `Bearer ${fallbackToken}`;
          return headers;
        }
      }

      console.warn('❌ Sync status - No token found!');
      return headers;
    };

    const headers = getAuthHeaders();
    console.log('🔄 Sync status request via proxy');

    const response = await fetch('/api/fixtures/sync', {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Sync status failed:', response.status, response.statusText, errorData);
      throw new Error(errorData.message || `Failed to get sync status: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Sync status successful');
    return result;
  },

  // CRUD operations - Using Next.js API proxy
  createFixture: async (data: any): Promise<Fixture> => {
    // Get token from auth store (same pattern as update/delete)
    const getAuthHeaders = (): Record<string, string> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (typeof window !== 'undefined') {
        // Try to get from Zustand store first
        try {
          const authStorage = localStorage.getItem('auth-storage');
          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            const token = parsed.state?.accessToken;
            if (token) {
              console.log('🔑 Create fixture - Using token from auth store:', token.substring(0, 20) + '...');
              headers.Authorization = `Bearer ${token}`;
              return headers;
            }
          }
        } catch (error) {
          console.warn('Failed to parse auth storage:', error);
        }

        // Fallback to direct localStorage access
        const fallbackToken = localStorage.getItem('accessToken');
        if (fallbackToken) {
          console.log('🔑 Create fixture - Using fallback token from localStorage');
          headers.Authorization = `Bearer ${fallbackToken}`;
          return headers;
        }
      }

      console.warn('❌ Create fixture - No token found!');
      return headers;
    };

    const headers = getAuthHeaders();
    console.log('🔄 Create fixture request:', { hasAuth: !!headers.Authorization, data });

    const response = await fetch('/api/fixtures', {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Create fixture failed:', response.status, response.statusText, errorData);
      throw new Error(errorData.message || `Failed to create fixture: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Create fixture successful:', result.data?.id);
    return result.data || result;
  },

  updateFixture: async (externalId: number, data: any): Promise<Fixture> => {
    // Get token from auth store (same pattern as delete)
    const getAuthHeaders = (): Record<string, string> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (typeof window !== 'undefined') {
        // Try to get from Zustand store first
        try {
          const authStorage = localStorage.getItem('auth-storage');
          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            const token = parsed.state?.accessToken;
            if (token) {
              console.log('🔑 Update fixture - Using token from auth store:', token.substring(0, 20) + '...');
              headers.Authorization = `Bearer ${token}`;
              return headers;
            }
          }
        } catch (error) {
          console.warn('Failed to parse auth storage:', error);
        }

        // Fallback to direct localStorage access
        const fallbackToken = localStorage.getItem('accessToken');
        if (fallbackToken) {
          console.log('🔑 Update fixture - Using fallback token from localStorage');
          headers.Authorization = `Bearer ${fallbackToken}`;
          return headers;
        }
      }

      console.warn('❌ Update fixture - No token found!');
      return headers;
    };

    const headers = getAuthHeaders();
    console.log('🔄 Update fixture request:', { externalId, hasAuth: !!headers.Authorization, data });

    const response = await fetch(`/api/fixtures/${externalId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Update fixture failed:', response.status, response.statusText, errorData);
      throw new Error(errorData.message || `Failed to update fixture: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Update fixture successful:', externalId);
    return result.data || result;
  },

  deleteFixture: async (externalId: number): Promise<void> => {
    // Get token from auth store (same pattern as broadcast-links.ts)
    const getAuthHeaders = (): Record<string, string> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (typeof window !== 'undefined') {
        // Try to get from Zustand store first
        try {
          const authStorage = localStorage.getItem('auth-storage');
          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            const token = parsed.state?.accessToken;
            if (token) {
              console.log('🔑 Delete fixture - Using token from auth store:', token.substring(0, 20) + '...');
              headers.Authorization = `Bearer ${token}`;
              return headers;
            }
          }
        } catch (error) {
          console.warn('Failed to parse auth storage:', error);
        }

        // Fallback to direct localStorage access
        const fallbackToken = localStorage.getItem('accessToken');
        if (fallbackToken) {
          console.log('🔑 Delete fixture - Using fallback token from localStorage');
          headers.Authorization = `Bearer ${fallbackToken}`;
          return headers;
        }
      }

      console.warn('❌ Delete fixture - No token found!');
      return headers;
    };

    const headers = getAuthHeaders();
    console.log('🔄 Delete fixture request:', { externalId, hasAuth: !!headers.Authorization });

    const response = await fetch(`/api/fixtures/${externalId}`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Delete fixture failed:', response.status, response.statusText, errorData);
      throw new Error(errorData.message || `Failed to delete fixture: ${response.statusText}`);
    }

    console.log('✅ Delete fixture successful:', externalId);
  },

  // Aliases for consistency
  getFixture: async (externalId: number): Promise<Fixture> => {
    const response = await fixturesApi.getFixtureById(externalId);
    return response.data;
  },
};
