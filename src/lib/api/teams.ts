import { apiClient } from './client';
import { Team, PaginatedResponse } from '@/lib/types/api';

export interface TeamFilters {
  page?: number;
  limit?: number;
  league?: number;
  season?: number;
  country?: string;
  search?: string;
}

export interface TeamStatistics {
  teamId: number;
  leagueId: number;
  season: number;
  fixtures: {
    played: { home: number; away: number; total: number };
    wins: { home: number; away: number; total: number };
    draws: { home: number; away: number; total: number };
    loses: { home: number; away: number; total: number };
  };
  goals: {
    for: { total: { home: number; away: number; total: number } };
    against: { total: { home: number; away: number; total: number } };
  };
}

export const teamsApi = {
  // Use Next.js API proxy (similar to leagues)
  getTeams: async (filters: TeamFilters = {}): Promise<PaginatedResponse<Team>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    // Get token from auth store for authorization
    const getAuthHeaders = (): Record<string, string> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (typeof window !== 'undefined') {
        try {
          const authStorage = localStorage.getItem('auth-storage');
          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            const token = parsed.state?.accessToken;
            if (token) {
              headers.Authorization = `Bearer ${token}`;
              return headers;
            }
          }
        } catch (error) {
          console.warn('Failed to parse auth storage:', error);
        }

        // Fallback to direct localStorage access
        const fallbackToken = localStorage.getItem('accessToken');
        if (fallbackToken) {
          headers.Authorization = `Bearer ${fallbackToken}`;
          return headers;
        }
      }

      return headers;
    };

    const response = await fetch(`/api/teams?${params.toString()}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch teams');
    }

    return await response.json();
  },

  // Requires authentication
  getTeamById: async (externalId: number): Promise<Team> => {
    const response = await apiClient.get<Team>(`/football/teams/${externalId}`);
    return response;
  },

  // Requires authentication
  getTeamStatistics: async (
    league: number,
    season: number,
    team: number
  ): Promise<{ data: TeamStatistics; status: number }> => {
    const params = new URLSearchParams({
      league: league.toString(),
      season: season.toString(),
      team: team.toString(),
    });

    const response = await apiClient.get<{ data: TeamStatistics; status: number }>(
      `/football/teams/statistics?${params.toString()}`
    );
    return response;
  },

  // Helper methods for common operations
  getTeamsByLeague: async (league: number, season?: number): Promise<PaginatedResponse<Team>> => {
    const filters: TeamFilters = { league };
    if (season) filters.season = season;
    return teamsApi.getTeams(filters);
  },

  getTeamsByCountry: async (country: string): Promise<PaginatedResponse<Team>> => {
    return teamsApi.getTeams({ country });
  },

  searchTeams: async (query: string, filters: TeamFilters = {}): Promise<PaginatedResponse<Team>> => {
    // Note: This would need to be implemented on the backend
    // For now, we'll get all teams and filter client-side (not ideal for production)
    const teams = await teamsApi.getTeams(filters);
    const filteredTeams = teams.data.filter(team =>
      team.name.toLowerCase().includes(query.toLowerCase()) ||
      team.code?.toLowerCase().includes(query.toLowerCase())
    );

    return {
      data: filteredTeams,
      meta: {
        ...teams.meta,
        totalItems: filteredTeams.length,
        totalPages: Math.ceil(filteredTeams.length / (filters.limit || 10)),
      },
    };
  },

  // Delete team (requires admin access)
  deleteTeam: async (externalId: number): Promise<void> => {
    await apiClient.delete(`/football/teams/${externalId}`);
  },
};
