import { apiClient } from './client';
import { League, PaginatedResponse } from '@/lib/types/api';

export interface LeagueFilters {
  page?: number;
  limit?: number;
  country?: string;
  active?: boolean;
  search?: string;
}

export interface CreateLeagueData {
  externalId?: number;
  name: string;
  country: string;
  logo?: string;
  flag?: string;
  season: number;
  active: boolean;
  type: string;
  isHot?: boolean;
}

export interface UpdateLeagueData {
  name?: string;
  country?: string;
  logo?: string;
  flag?: string;
  season?: number;
  active?: boolean;
  type?: string;
  isHot?: boolean;
}

export const leaguesApi = {
  // Public endpoint via proxy
  getLeagues: async (filters: LeagueFilters = {}): Promise<PaginatedResponse<League>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await fetch(`/api/leagues?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch leagues');
    }

    return await response.json();
  },

  // Requires authentication
  getLeagueById: async (externalId: number, season?: number): Promise<League> => {
    const params = season ? `?season=${season}` : '';
    const response = await apiClient.get<League>(`/football/leagues/${externalId}${params}`);
    return response;
  },

  // Editor+ access required
  createLeague: async (data: CreateLeagueData): Promise<League> => {
    const response = await apiClient.post<League>('/football/leagues', data);
    return response;
  },

  // Editor+ access required
  updateLeague: async (id: number, data: UpdateLeagueData): Promise<League> => {
    const response = await apiClient.patch<League>(`/football/leagues/${id}`, data);
    return response;
  },

  // Admin access required
  deleteLeague: async (id: number): Promise<void> => {
    await apiClient.delete(`/football/leagues/${id}`);
  },

  // Helper methods for common operations
  getActiveLeagues: async (): Promise<PaginatedResponse<League>> => {
    return leaguesApi.getLeagues({ active: true });
  },

  getLeaguesByCountry: async (country: string): Promise<PaginatedResponse<League>> => {
    return leaguesApi.getLeagues({ country });
  },

  toggleLeagueStatus: async (id: number, active: boolean): Promise<League> => {
    return leaguesApi.updateLeague(id, { active });
  },
};
