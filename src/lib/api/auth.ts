import { apiClient } from './client';
import { AuthResponse, LoginCredentials, SystemUser } from '@/lib/types/api';
import { useAuthStore } from '@/lib/stores/auth';

export const authApi = {
  // System Authentication
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    console.log('🔐 Attempting login via proxy...');

    try {
      // Use proxy endpoint instead of direct API call
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Login failed');
      }

      const loginData = await response.json();
      console.log('✅ Login successful via proxy');

      // Get user profile with the token via proxy
      const profileResponse = await fetch('/api/auth/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${loginData.accessToken}`
        }
      });

      if (!profileResponse.ok) {
        const errorData = await profileResponse.json();
        throw new Error(errorData.message || 'Failed to fetch profile');
      }

      const userProfile = await profileResponse.json();

      return {
        user: userProfile,
        accessToken: loginData.accessToken,
        refreshToken: loginData.refreshToken,
      };
    } catch (error: any) {
      console.error('❌ Login failed via proxy:', error.message);

      // Only use mock as absolute fallback for network errors
      if (error.message.includes('fetch') || error.message.includes('network')) {
        console.warn('⚠️ Network error, using mock data');

        if (credentials.username === 'admin' && credentials.password === 'admin123456') {
          const mockResponse: AuthResponse = {
            user: {
              id: 1,
              username: 'admin',
              email: '<EMAIL>',
              fullName: 'System Administrator',
              role: 'admin',
              isActive: true,
              lastLoginAt: new Date().toISOString(),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            accessToken: 'mock-access-token-' + Date.now(),
            refreshToken: 'mock-refresh-token-' + Date.now(),
          };

          await new Promise(resolve => setTimeout(resolve, 500));
          return mockResponse;
        }
      }

      // Re-throw API errors (invalid credentials, etc.)
      throw error;
    }
  },

  logout: async (refreshToken: string): Promise<{ message: string }> => {
    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Logout failed');
    }

    return await response.json();
  },

  logoutFromAllDevices: async (): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/system-auth/logout-all');
    return response;
  },

  refreshToken: async (refreshToken: string): Promise<{ accessToken: string }> => {
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Token refresh failed');
    }

    return await response.json();
  },

  getProfile: async (): Promise<SystemUser> => {
    const authStore = useAuthStore.getState();
    const token = authStore.accessToken;

    const response = await fetch('/api/auth/profile', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      }
    });

    if (!response.ok) {
      // If 401, token might be expired - force logout
      if (response.status === 401) {
        console.warn('⚠️ Token expired, forcing logout...');
        authStore.clearAuth();
        window.location.href = '/auth/login';
        throw new Error('Token expired, please login again');
      }

      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch profile');
    }

    return await response.json();
  },

  updateProfile: async (data: Partial<SystemUser>): Promise<SystemUser> => {
    const response = await apiClient.put<SystemUser>('/system-auth/profile', data);
    return response;
  },

  changePassword: async (data: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/system-auth/change-password', data);
    return response;
  },

  // System User Management (Admin only)
  createUser: async (userData: {
    username: string;
    email: string;
    password: string;
    role: 'admin' | 'editor' | 'moderator';
    fullName?: string;
  }): Promise<SystemUser> => {
    const response = await apiClient.post<SystemUser>('/system-auth/users', userData);
    return response;
  },

  updateUser: async (id: number, data: Partial<SystemUser>): Promise<SystemUser> => {
    const response = await apiClient.put<SystemUser>(`/system-auth/users/${id}`, data);
    return response;
  },
};
