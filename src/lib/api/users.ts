import { apiClient } from './client';
import { RegisteredUser, PaginatedResponse } from '@/lib/types/api';

export interface UserFilters {
  page?: number;
  limit?: number;
  tier?: 'free' | 'premium' | 'enterprise';
  isActive?: boolean;
}

export interface TierStatistics {
  free: number;
  premium: number;
  enterprise: number;
  total: number;
}

export interface ApiUsageStats {
  tier: 'free' | 'premium' | 'enterprise';
  apiCallsUsed: number;
  apiCallsLimit: number | null;
  apiCallsRemaining: number | null;
  lastApiCallAt: string | null;
  resetDate: string;
}

export interface SubscriptionInfo {
  userId: number;
  tier: string;
  subscriptionEndDate: string | null;
  hasActiveSubscription: boolean;
  apiCallsUsed: number;
  apiCallsLimit: number | null;
}

export const usersApi = {
  // Registered Users Authentication (Public)
  register: async (data: {
    username: string;
    email: string;
    password: string;
    fullName?: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
  }): Promise<{ message: string; user: RegisteredUser }> => {
    const response = await apiClient.post<{ message: string; user: RegisteredUser }>('/users/register', data);
    return response;
  },

  login: async (data: {
    usernameOrEmail: string;
    password: string;
  }): Promise<{
    accessToken: string;
    refreshToken: string;
    user: RegisteredUser;
  }> => {
    const response = await apiClient.post<{
      accessToken: string;
      refreshToken: string;
      user: RegisteredUser;
    }>('/users/login', data);
    return response;
  },

  verifyEmail: async (token: string): Promise<{ message: string; user: RegisteredUser }> => {
    const response = await apiClient.post<{ message: string; user: RegisteredUser }>('/users/verify-email', { token });
    return response;
  },

  resendVerification: async (email: string): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/users/resend-verification', { email });
    return response;
  },

  forgotPassword: async (email: string): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/users/forgot-password', { email });
    return response;
  },

  resetPassword: async (token: string, newPassword: string): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/users/reset-password', { token, newPassword });
    return response;
  },

  // Authenticated User Operations
  getProfile: async (): Promise<RegisteredUser> => {
    const response = await apiClient.get<RegisteredUser>('/users/profile');
    return response;
  },

  updateProfile: async (data: {
    fullName?: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
  }): Promise<RegisteredUser> => {
    const response = await apiClient.put<RegisteredUser>('/users/profile', data);
    return response;
  },

  changePassword: async (data: {
    currentPassword: string;
    newPassword: string;
  }): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/users/change-password', data);
    return response;
  },

  getApiUsage: async (): Promise<ApiUsageStats> => {
    const response = await apiClient.get<ApiUsageStats>('/users/api-usage');
    return response;
  },

  // Admin Operations
  getAllUsers: async (filters: UserFilters = {}): Promise<PaginatedResponse<RegisteredUser>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<RegisteredUser>>(
      `/admin/users?${params.toString()}`
    );
    return response;
  },

  getTierStatistics: async (): Promise<TierStatistics> => {
    const response = await apiClient.get<TierStatistics>('/admin/tiers/statistics');
    return response;
  },

  getUsersApproachingLimits: async (threshold: number = 80): Promise<RegisteredUser[]> => {
    const response = await apiClient.get<RegisteredUser[]>(
      `/admin/users/approaching-limits?threshold=${threshold}`
    );
    return response;
  },

  upgradeTier: async (
    userId: number,
    newTier: 'premium' | 'enterprise',
    subscriptionMonths?: number
  ): Promise<{ message: string }> => {
    const data: any = { newTier };
    if (subscriptionMonths) data.subscriptionMonths = subscriptionMonths;

    const response = await apiClient.post<{ message: string }>(`/admin/users/${userId}/upgrade-tier`, data);
    return response;
  },

  downgradeTier: async (
    userId: number,
    newTier: 'free' | 'premium'
  ): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>(`/admin/users/${userId}/downgrade-tier`, { newTier });
    return response;
  },

  extendSubscription: async (
    userId: number,
    additionalMonths: number
  ): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>(`/admin/users/${userId}/extend-subscription`, {
      additionalMonths,
    });
    return response;
  },

  getSubscriptionInfo: async (userId: number): Promise<SubscriptionInfo> => {
    const response = await apiClient.get<SubscriptionInfo>(`/admin/users/${userId}/subscription`);
    return response;
  },

  resetMonthlyApiUsage: async (): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/admin/reset-api-usage');
    return response;
  },

  checkApiUsageWarnings: async (): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/admin/check-usage-warnings');
    return response;
  },
};
