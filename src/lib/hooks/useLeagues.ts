import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { leaguesApi, LeagueFilters, CreateLeagueData, UpdateLeagueData } from '@/lib/api/leagues';

export const useLeagues = (filters: LeagueFilters = {}) => {
  const leaguesQuery = useQuery({
    queryKey: ['leagues', filters],
    queryFn: () => leaguesApi.getLeagues(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes - leagues don't change often
  });

  return {
    leagues: leaguesQuery.data?.data || [],
    leaguesMeta: leaguesQuery.data?.meta,
    isLoading: leaguesQuery.isLoading,
    error: leaguesQuery.error,
    refetch: leaguesQuery.refetch,
  };
};

export const useLeague = (externalId: number, season?: number) => {
  const leagueQuery = useQuery({
    queryKey: ['leagues', externalId, season],
    queryFn: () => leaguesApi.getLeagueById(externalId, season),
    enabled: !!externalId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    league: leagueQuery.data,
    isLoading: leagueQuery.isLoading,
    error: leagueQuery.error,
    refetch: leagueQuery.refetch,
  };
};

export const useActiveLeagues = () => {
  const activeLeaguesQuery = useQuery({
    queryKey: ['leagues', { active: true }],
    queryFn: () => leaguesApi.getActiveLeagues(),
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  return {
    activeLeagues: activeLeaguesQuery.data?.data || [],
    isLoading: activeLeaguesQuery.isLoading,
    error: activeLeaguesQuery.error,
    refetch: activeLeaguesQuery.refetch,
  };
};

export const useLeaguesByCountry = (country: string) => {
  const leaguesByCountryQuery = useQuery({
    queryKey: ['leagues', 'by-country', country],
    queryFn: () => leaguesApi.getLeaguesByCountry(country),
    enabled: !!country,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  return {
    leagues: leaguesByCountryQuery.data?.data || [],
    leaguesMeta: leaguesByCountryQuery.data?.meta,
    isLoading: leaguesByCountryQuery.isLoading,
    error: leaguesByCountryQuery.error,
    refetch: leaguesByCountryQuery.refetch,
  };
};

// League mutations (Editor+ access required)
export const useLeagueMutations = () => {
  const queryClient = useQueryClient();

  // Create league mutation
  const createLeagueMutation = useMutation({
    mutationFn: (data: CreateLeagueData) => leaguesApi.createLeague(data),
    onSuccess: () => {
      // Invalidate all league queries
      queryClient.invalidateQueries({ queryKey: ['leagues'] });
    },
  });

  // Update league mutation
  const updateLeagueMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateLeagueData }) =>
      leaguesApi.updateLeague(id, data),
    onSuccess: (updatedLeague, variables) => {
      // Update specific league in cache
      queryClient.setQueryData(['leagues', updatedLeague.externalId], updatedLeague);
      // Invalidate league lists
      queryClient.invalidateQueries({ queryKey: ['leagues'], exact: false });
    },
  });

  // Toggle league status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: ({ id, active }: { id: number; active: boolean }) =>
      leaguesApi.toggleLeagueStatus(id, active),
    onSuccess: (updatedLeague) => {
      // Update specific league in cache
      queryClient.setQueryData(['leagues', updatedLeague.externalId], updatedLeague);
      // Invalidate league lists
      queryClient.invalidateQueries({ queryKey: ['leagues'], exact: false });
    },
  });

  return {
    // Create league
    createLeague: createLeagueMutation.mutate,
    isCreateLoading: createLeagueMutation.isLoading,
    createError: createLeagueMutation.error,
    createData: createLeagueMutation.data,

    // Update league
    updateLeague: updateLeagueMutation.mutate,
    isUpdateLoading: updateLeagueMutation.isLoading,
    updateError: updateLeagueMutation.error,
    updateData: updateLeagueMutation.data,

    // Toggle status
    toggleStatus: toggleStatusMutation.mutate,
    isToggleLoading: toggleStatusMutation.isLoading,
    toggleError: toggleStatusMutation.error,
    toggleData: toggleStatusMutation.data,
  };
};
