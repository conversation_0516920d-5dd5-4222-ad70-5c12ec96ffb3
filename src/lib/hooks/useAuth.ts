import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authApi } from '@/lib/api/auth';
import { useAuthStore } from '@/lib/stores/auth';
import { LoginCredentials, SystemUser } from '@/lib/types/api';
import { apiClient } from '@/lib/api/client';

export const useAuth = () => {
  const queryClient = useQueryClient();
  const { setAuth, clearAuth, setLoading, user, isAuthenticated } = useAuthStore();

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: authApi.login,
    onMutate: () => {
      setLoading(true);
    },
    onSuccess: (data) => {
      setAuth(data.user, data.accessToken, data.refreshToken);
      apiClient.setAuthToken(data.accessToken);
      queryClient.invalidateQueries({ queryKey: ['auth', 'profile'] });
    },
    onError: (error) => {
      console.error('Login failed:', error);
      setLoading(false);
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: (refreshToken: string) => authApi.logout(refreshToken),
    onSuccess: () => {
      clearAuth();
      apiClient.removeAuthToken();
      queryClient.clear();
    },
    onError: () => {
      // Even if logout fails on server, clear local state
      clearAuth();
      apiClient.removeAuthToken();
      queryClient.clear();
    },
  });

  // Logout from all devices
  const logoutAllMutation = useMutation({
    mutationFn: authApi.logoutFromAllDevices,
    onSuccess: () => {
      clearAuth();
      apiClient.removeAuthToken();
      queryClient.clear();
    },
  });

  // Get profile query
  const profileQuery = useQuery({
    queryKey: ['auth', 'profile'],
    queryFn: authApi.getProfile,
    enabled: isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: (data: Partial<SystemUser>) => authApi.updateProfile(data),
    onSuccess: (updatedUser) => {
      queryClient.setQueryData(['auth', 'profile'], updatedUser);
      // Update auth store
      useAuthStore.getState().updateUser(updatedUser);
    },
  });

  // Change password mutation
  const changePasswordMutation = useMutation({
    mutationFn: authApi.changePassword,
    onSuccess: () => {
      // Optionally logout user after password change
      // logoutMutation.mutate();
    },
  });

  // Refresh token mutation
  const refreshTokenMutation = useMutation({
    mutationFn: (refreshToken: string) => authApi.refreshToken(refreshToken),
    onSuccess: (data) => {
      const currentUser = useAuthStore.getState().user;
      const currentRefreshToken = useAuthStore.getState().refreshToken;

      if (currentUser && currentRefreshToken) {
        setAuth(currentUser, data.accessToken, currentRefreshToken);
        apiClient.setAuthToken(data.accessToken);
      }
    },
    onError: () => {
      // If refresh fails, logout user
      clearAuth();
      apiClient.removeAuthToken();
      queryClient.clear();
    },
  });

  return {
    // State
    user,
    isAuthenticated,
    isLoading: useAuthStore((state) => state.isLoading),

    // Queries
    profile: profileQuery.data,
    isProfileLoading: profileQuery.isLoading,
    profileError: profileQuery.error,

    // Mutations
    login: loginMutation.mutate,
    logout: (refreshToken: string) => logoutMutation.mutate(refreshToken),
    logoutAll: logoutAllMutation.mutate,
    updateProfile: updateProfileMutation.mutate,
    changePassword: changePasswordMutation.mutate,
    refreshToken: refreshTokenMutation.mutate,

    // Mutation states
    isLoginLoading: loginMutation.isLoading,
    loginError: loginMutation.error,
    isLogoutLoading: logoutMutation.isLoading,
    isUpdateProfileLoading: updateProfileMutation.isLoading,
    updateProfileError: updateProfileMutation.error,
    isChangePasswordLoading: changePasswordMutation.isLoading,
    changePasswordError: changePasswordMutation.error,
  };
};
