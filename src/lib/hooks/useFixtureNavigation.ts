import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

interface UseFixtureNavigationProps {
  fixtureId?: string | number;
}

export const useFixtureNavigation = ({ fixtureId }: UseFixtureNavigationProps = {}) => {
  const router = useRouter();

  const navigateToFixtures = useCallback(() => {
    router.push('/dashboard/fixtures');
  }, [router]);

  const navigateToDetail = useCallback(() => {
    if (fixtureId) {
      router.push(`/dashboard/fixtures/${fixtureId}`);
    } else {
      router.back();
    }
  }, [router, fixtureId]);

  const navigateToEdit = useCallback(() => {
    if (fixtureId) {
      router.push(`/dashboard/fixtures/${fixtureId}/edit`);
    }
  }, [router, fixtureId]);

  const navigateToCreate = useCallback(() => {
    router.push('/dashboard/fixtures/create');
  }, [router]);

  const navigateBack = useCallback(() => {
    router.back();
  }, [router]);

  return {
    navigateToFixtures,
    navigateToDetail,
    navigateToEdit,
    navigateToCreate,
    navigateBack,
  };
};
