import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { usersApi, UserFilters } from '@/lib/api/users';
import { RegisteredUser } from '@/lib/types/api';
import { toast } from 'sonner';

// Get all registered users with filtering
export const useRegisteredUsersList = (filters: UserFilters = {}) => {
      return useQuery({
            queryKey: ['registered-users', filters],
            queryFn: () => usersApi.getAllUsers(filters),
            staleTime: 5 * 60 * 1000, // 5 minutes
      });
};

// Get single registered user by ID
export const useRegisteredUser = (userId: number | string) => {
      return useQuery({
            queryKey: ['registered-user', userId],
            queryFn: async () => {
                  // Mock single registered user data since we don't have a single user endpoint
                  const mockUser: RegisteredUser = {
                        id: Number(userId),
                        username: `apiuser${userId}`,
                        email: `apiuser${userId}@example.com`,
                        fullName: `API User ${userId}`,
                        tier: 'premium',
                        isActive: true,
                        isEmailVerified: true,
                        apiCallsUsed: 750,
                        apiCallsLimit: 1000,
                        apiCallsRemaining: 250,
                        hasActiveSubscription: true,
                        subscriptionEndDate: '2024-12-31T23:59:59Z',
                        createdAt: '2024-01-01T00:00:00Z',
                        lastLoginAt: '2024-01-15T08:30:00Z',
                  };
                  return mockUser;
            },
            enabled: !!userId,
      });
};

// Get users approaching API limits
export const useUsersApproachingLimits = (threshold: number = 80) => {
      return useQuery({
            queryKey: ['users-approaching-limits', threshold],
            queryFn: () => usersApi.getUsersApproachingLimits(threshold),
            staleTime: 15 * 60 * 1000, // 15 minutes
      });
};

// Get tier statistics
export const useTierStatistics = () => {
      return useQuery({
            queryKey: ['tier-statistics'],
            queryFn: usersApi.getTierStatistics,
            staleTime: 30 * 60 * 1000, // 30 minutes
      });
};

// Get subscription info for a user
export const useUserSubscription = (userId: number) => {
      return useQuery({
            queryKey: ['user-subscription', userId],
            queryFn: () => usersApi.getSubscriptionInfo(userId),
            enabled: !!userId,
            staleTime: 10 * 60 * 1000, // 10 minutes
      });
};

// Registered user mutations
export const useRegisteredUserMutations = () => {
      const queryClient = useQueryClient();

      const upgradeTier = useMutation({
            mutationFn: ({
                  userId,
                  newTier,
                  subscriptionMonths,
            }: {
                  userId: number;
                  newTier: 'premium' | 'enterprise';
                  subscriptionMonths?: number;
            }) => usersApi.upgradeTier(userId, newTier, subscriptionMonths),
            onSuccess: (_, variables) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['registered-user', variables.userId] });
                  queryClient.invalidateQueries({ queryKey: ['tier-statistics'] });
                  toast.success(`User upgraded to ${variables.newTier} tier successfully`);
            },
            onError: (error: Error) => {
                  toast.error(`Failed to upgrade user: ${error.message}`);
            },
      });

      const downgradeTier = useMutation({
            mutationFn: ({ userId, newTier }: { userId: number; newTier: 'free' | 'premium' }) =>
                  usersApi.downgradeTier(userId, newTier),
            onSuccess: (_, variables) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['registered-user', variables.userId] });
                  queryClient.invalidateQueries({ queryKey: ['tier-statistics'] });
                  toast.success(`User downgraded to ${variables.newTier} tier successfully`);
            },
            onError: (error: Error) => {
                  toast.error(`Failed to downgrade user: ${error.message}`);
            },
      });

      const extendSubscription = useMutation({
            mutationFn: ({ userId, additionalMonths }: { userId: number; additionalMonths: number }) =>
                  usersApi.extendSubscription(userId, additionalMonths),
            onSuccess: (_, variables) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-user', variables.userId] });
                  queryClient.invalidateQueries({ queryKey: ['user-subscription', variables.userId] });
                  toast.success(`Subscription extended by ${variables.additionalMonths} months`);
            },
            onError: (error: Error) => {
                  toast.error(`Failed to extend subscription: ${error.message}`);
            },
      });

      const resetApiUsage = useMutation({
            mutationFn: usersApi.resetMonthlyApiUsage,
            onSuccess: () => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['users-approaching-limits'] });
                  toast.success('Monthly API usage reset successfully');
            },
            onError: (error: Error) => {
                  toast.error(`Failed to reset API usage: ${error.message}`);
            },
      });

      const suspendUser = useMutation({
            mutationFn: async (userId: number) => {
                  // Mock suspend function - replace with actual API call when available
                  console.log('Suspending user:', userId);
                  return { message: 'User suspended successfully' };
            },
            onSuccess: (_, userId) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['registered-user', userId] });
                  toast.success('User suspended successfully');
            },
            onError: (error: Error) => {
                  toast.error(`Failed to suspend user: ${error.message}`);
            },
      });

      const reactivateUser = useMutation({
            mutationFn: async (userId: number) => {
                  // Mock reactivate function - replace with actual API call when available
                  console.log('Reactivating user:', userId);
                  return { message: 'User reactivated successfully' };
            },
            onSuccess: (_, userId) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['registered-user', userId] });
                  toast.success('User reactivated successfully');
            },
            onError: (error: Error) => {
                  toast.error(`Failed to reactivate user: ${error.message}`);
            },
      });

      return {
            upgradeTier,
            downgradeTier,
            extendSubscription,
            resetApiUsage,
            suspendUser,
            reactivateUser,
      };
};

// User analytics and insights
export const useUserAnalytics = () => {
      return useQuery({
            queryKey: ['user-analytics'],
            queryFn: async () => {
                  // Mock analytics data
                  return {
                        totalUsers: 1247,
                        activeUsers: 1156,
                        newUsersThisMonth: 45,
                        tierDistribution: {
                              free: 856,
                              premium: 312,
                              enterprise: 79,
                        },
                        monthlyRevenue: 34567,
                        apiCallsThisMonth: 2847392,
                        apiUsageStats: {
                              totalCallsThisMonth: 2847392,
                              averageCallsPerUser: 188,
                              peakUsageDay: '2024-01-15',
                              topUsers: [
                                    { id: 1, username: 'api_user_premium_1', calls: 9567 },
                                    { id: 2, username: 'enterprise_client', calls: 8234 },
                                    { id: 3, username: 'mobile_app_backend', calls: 7456 },
                              ],
                        },
                        revenueMetrics: {
                              monthlyRecurringRevenue: 34567,
                              averageRevenuePerUser: 11.67,
                              lifetimeValue: 186.45,
                        },
                  };
            },
            staleTime: 5 * 60 * 1000, // 5 minutes
      });
};

// Tier statistics for analytics dashboard
export const useTierStats = (dateRange: string = '30d') => {
      return useQuery({
            queryKey: ['tier-stats', dateRange],
            queryFn: async () => {
                  // Mock tier statistics data
                  return {
                        totalUsers: 1247,
                        totalUsersChange: 8.5,
                        conversionRate: 15.2,
                        conversionRateChange: 2.1,
                        tierDistribution: [
                              {
                                    tier: 'free',
                                    count: 856,
                                    percentage: 68.6,
                                    monthlyGrowth: 5.2,
                                    avgApiUsage: 650,
                              },
                              {
                                    tier: 'premium',
                                    count: 312,
                                    percentage: 25.0,
                                    monthlyGrowth: 12.8,
                                    avgApiUsage: 7500,
                              },
                              {
                                    tier: 'enterprise',
                                    count: 79,
                                    percentage: 6.3,
                                    monthlyGrowth: 18.5,
                                    avgApiUsage: 45000,
                              },
                        ],
                        growthTrends: [
                              { tier: 'free', change: 5.2 },
                              { tier: 'premium', change: 12.8 },
                              { tier: 'enterprise', change: 18.5 },
                        ],
                        apiUsageByTier: [
                              {
                                    tier: 'free',
                                    avgUsage: 650,
                                    usagePercentage: 65,
                              },
                              {
                                    tier: 'premium',
                                    avgUsage: 7500,
                                    usagePercentage: 75,
                              },
                              {
                                    tier: 'enterprise',
                                    avgUsage: 45000,
                                    usagePercentage: 45,
                              },
                        ],
                  };
            },
            staleTime: 15 * 60 * 1000, // 15 minutes
      });
};

// Revenue statistics
export const useRevenueStats = (dateRange: string = '30d') => {
      return useQuery({
            queryKey: ['revenue-stats', dateRange],
            queryFn: async () => {
                  // Mock revenue statistics data
                  return {
                        monthlyRevenue: 34567,
                        revenueChange: 15.3,
                        arpu: 27.72,
                        arpuChange: 3.2,
                        mrr: 34567,
                        churnRate: 3.5,
                        revenueByTier: [
                              {
                                    tier: 'free',
                                    amount: 0,
                                    percentage: 0,
                              },
                              {
                                    tier: 'premium',
                                    amount: 9048,
                                    percentage: 26.2,
                              },
                              {
                                    tier: 'enterprise',
                                    amount: 25519,
                                    percentage: 73.8,
                              },
                        ],
                  };
            },
            staleTime: 15 * 60 * 1000, // 15 minutes
      });
};

// Tier migration patterns
export const useTierMigration = (dateRange: string = '30d') => {
      return useQuery({
            queryKey: ['tier-migration', dateRange],
            queryFn: async () => {
                  // Mock tier migration data
                  return {
                        migrations: [
                              {
                                    fromTier: 'free',
                                    toTier: 'premium',
                                    count: 42,
                                    percentage: 65.6,
                              },
                              {
                                    fromTier: 'premium',
                                    toTier: 'enterprise',
                                    count: 15,
                                    percentage: 23.4,
                              },
                              {
                                    fromTier: 'premium',
                                    toTier: 'free',
                                    count: 7,
                                    percentage: 10.9,
                              },
                        ],
                        totalMigrations: 64,
                  };
            },
            staleTime: 30 * 60 * 1000, // 30 minutes
      });
};

// Get user by ID
export const useGetById = (userId: string) => {
      return useQuery({
            queryKey: ['registered-user', userId],
            queryFn: async () => {
                  // Mock user data - replace with actual API call
                  const mockUser = {
                        id: userId,
                        name: 'John Doe',
                        email: '<EMAIL>',
                        company: 'TechCorp Inc.',
                        phone: '+****************',
                        website: 'https://techcorp.com',
                        tier: 'premium' as const,
                        status: 'active' as const,
                        emailVerified: true,
                        apiCallsUsed: 7250,
                        apiCallsLimit: 10000,
                        monthlySpend: 29.00,
                        avatar: '',
                        createdAt: '2024-01-15T10:30:00Z',
                        updatedAt: '2024-12-15T14:22:00Z',
                        lastLogin: '2024-12-20T09:15:00Z',
                        lastApiCall: '2024-12-22T16:45:00Z',
                        subscriptionStartDate: '2024-01-15T10:30:00Z',
                        subscriptionEndDate: '2025-01-15T10:30:00Z',
                        notes: 'Premium customer with good API usage patterns',
                  };
                  return mockUser;
            },
            enabled: !!userId,
            staleTime: 5 * 60 * 1000, // 5 minutes
      });
};

// Update user
export const useUpdate = () => {
      const queryClient = useQueryClient();

      return useMutation({
            mutationFn: async (userData: any) => {
                  // Mock update - replace with actual API call
                  console.log('Updating user:', userData);
                  return { ...userData, updatedAt: new Date().toISOString() };
            },
            onSuccess: (data) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['registered-user', data.id] });
            },
      });
};

// Delete user
export const useDelete = () => {
      const queryClient = useQueryClient();

      return useMutation({
            mutationFn: async (userId: string) => {
                  // Mock delete - replace with actual API call
                  console.log('Deleting user:', userId);
                  return { message: 'User deleted successfully' };
            },
            onSuccess: () => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
            },
      });
};

// Suspend user
export const useSuspend = () => {
      const queryClient = useQueryClient();

      return useMutation({
            mutationFn: async (userId: string) => {
                  // Mock suspend - replace with actual API call
                  console.log('Suspending user:', userId);
                  return { message: 'User suspended successfully' };
            },
            onSuccess: (_, userId) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['registered-user', userId] });
            },
      });
};

// Reactivate user
export const useReactivate = () => {
      const queryClient = useQueryClient();

      return useMutation({
            mutationFn: async (userId: string) => {
                  // Mock reactivate - replace with actual API call
                  console.log('Reactivating user:', userId);
                  return { message: 'User reactivated successfully' };
            },
            onSuccess: (_, userId) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['registered-user', userId] });
            },
      });
};

// Upgrade tier
export const useUpgradeTier = () => {
      const queryClient = useQueryClient();

      return useMutation({
            mutationFn: async ({ userId, newTier }: { userId: string; newTier: string }) => {
                  // Mock upgrade - replace with actual API call
                  console.log('Upgrading user tier:', userId, newTier);
                  return { message: `User upgraded to ${newTier} tier successfully` };
            },
            onSuccess: (_, { userId }) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['registered-user', userId] });
                  queryClient.invalidateQueries({ queryKey: ['tier-stats'] });
            },
      });
};

// Downgrade tier
export const useDowngradeTier = () => {
      const queryClient = useQueryClient();

      return useMutation({
            mutationFn: async ({ userId, newTier }: { userId: string; newTier: string }) => {
                  // Mock downgrade - replace with actual API call
                  console.log('Downgrading user tier:', userId, newTier);
                  return { message: `User downgraded to ${newTier} tier successfully` };
            },
            onSuccess: (_, { userId }) => {
                  queryClient.invalidateQueries({ queryKey: ['registered-users'] });
                  queryClient.invalidateQueries({ queryKey: ['registered-user', userId] });
                  queryClient.invalidateQueries({ queryKey: ['tier-stats'] });
            },
      });
};

// Usage statistics for a specific user
export const useUsageStats = (userId: string) => {
      return useQuery({
            queryKey: ['usage-stats', userId],
            queryFn: async () => {
                  // Mock usage statistics
                  return {
                        today: 125,
                        thisWeek: 890,
                        thisMonth: 3420,
                        total: 45600,
                  };
            },
            enabled: !!userId,
            staleTime: 5 * 60 * 1000, // 5 minutes
      });
};

// API calls history for a specific user
export const useApiCalls = (userId: string) => {
      return useQuery({
            queryKey: ['api-calls', userId],
            queryFn: async () => {
                  // Mock API calls data
                  return [
                        {
                              id: '1',
                              endpoint: '/api/fixtures',
                              status: 200,
                              responseTime: 145,
                              timestamp: '2024-12-22T16:45:00Z',
                        },
                        {
                              id: '2',
                              endpoint: '/api/teams',
                              status: 200,
                              responseTime: 98,
                              timestamp: '2024-12-22T16:30:00Z',
                        },
                        {
                              id: '3',
                              endpoint: '/api/leagues',
                              status: 429,
                              responseTime: 50,
                              timestamp: '2024-12-22T16:15:00Z',
                        },
                  ];
            },
            enabled: !!userId,
            staleTime: 1 * 60 * 1000, // 1 minute
      });
};

// Export grouped hooks
export const useRegisteredUsers = {
      useList: useRegisteredUsersList,
      useGetById,
      useUpdate,
      useDelete,
      useSuspend,
      useReactivate,
      useUpgradeTier,
      useDowngradeTier,
      useUsageStats,
      useApiCalls,
      useTierStats,
      useRevenueStats,
      useTierMigration,
};
