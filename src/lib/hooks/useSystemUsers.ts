import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authApi } from '@/lib/api/auth';
import { SystemUser } from '@/lib/types/api';
import { toast } from 'sonner';

export interface SystemUserFilters {
      search?: string;
      role?: 'admin' | 'editor' | 'moderator';
      isActive?: boolean;
      page?: number;
      limit?: number;
}

// Get all system users with filtering
export const useSystemUsers = (filters: SystemUserFilters = {}) => {
      return useQuery({
            queryKey: ['system-users', filters],
            queryFn: async () => {
                  // Since we don't have a direct API for listing system users,
                  // we'll use mock data for now that matches the API structure
                  const mockUsers: SystemUser[] = [
                        {
                              id: 1,
                              username: 'admin',
                              email: '<EMAIL>',
                              role: 'admin',
                              fullName: 'System Administrator',
                              isActive: true,
                              createdAt: '2024-01-01T00:00:00Z',
                              updatedAt: '2024-01-15T10:30:00Z',
                              lastLoginAt: '2024-01-15T08:30:00Z',
                        },
                        {
                              id: 2,
                              username: 'editor1',
                              email: '<EMAIL>',
                              role: 'editor',
                              fullName: 'Content Editor',
                              isActive: true,
                              createdAt: '2024-01-02T00:00:00Z',
                              updatedAt: '2024-01-14T15:20:00Z',
                              lastLoginAt: '2024-01-14T14:45:00Z',
                        },
                        {
                              id: 3,
                              username: 'moderator1',
                              email: '<EMAIL>',
                              role: 'moderator',
                              fullName: 'Content Moderator',
                              isActive: true,
                              createdAt: '2024-01-03T00:00:00Z',
                              updatedAt: '2024-01-13T09:15:00Z',
                              lastLoginAt: '2024-01-13T16:20:00Z',
                        },
                        {
                              id: 4,
                              username: 'editor2',
                              email: '<EMAIL>',
                              role: 'editor',
                              fullName: 'Senior Editor',
                              isActive: false,
                              createdAt: '2024-01-05T00:00:00Z',
                              updatedAt: '2024-01-10T11:00:00Z',
                              lastLoginAt: '2024-01-08T13:30:00Z',
                        },
                  ];

                  // Apply filters to mock data
                  let filteredUsers = mockUsers;

                  if (filters.search) {
                        const searchLower = filters.search.toLowerCase();
                        filteredUsers = filteredUsers.filter(
                              user =>
                                    user.username.toLowerCase().includes(searchLower) ||
                                    user.email.toLowerCase().includes(searchLower) ||
                                    user.fullName?.toLowerCase().includes(searchLower)
                        );
                  }

                  if (filters.role) {
                        filteredUsers = filteredUsers.filter(user => user.role === filters.role);
                  }

                  if (filters.isActive !== undefined) {
                        filteredUsers = filteredUsers.filter(user => user.isActive === filters.isActive);
                  }

                  // Simulate pagination
                  const page = filters.page || 1;
                  const limit = filters.limit || 10;
                  const start = (page - 1) * limit;
                  const end = start + limit;
                  const paginatedUsers = filteredUsers.slice(start, end);

                  return {
                        data: paginatedUsers,
                        meta: {
                              total: filteredUsers.length,
                              page,
                              limit,
                              totalPages: Math.ceil(filteredUsers.length / limit),
                        },
                  };
            },
            staleTime: 5 * 60 * 1000, // 5 minutes
      });
};

// Get single system user by ID
export const useSystemUser = (userId: number | string) => {
      return useQuery({
            queryKey: ['system-user', userId],
            queryFn: async () => {
                  // Mock single user data
                  const mockUser: SystemUser = {
                        id: Number(userId),
                        username: `user${userId}`,
                        email: `user${userId}@fecms-sport.com`,
                        role: 'editor',
                        fullName: `User ${userId}`,
                        isActive: true,
                        createdAt: '2024-01-01T00:00:00Z',
                        updatedAt: '2024-01-15T10:30:00Z',
                        lastLoginAt: '2024-01-15T08:30:00Z',
                  };
                  return mockUser;
            },
            enabled: !!userId,
      });
};

// System user mutations
export const useSystemUserMutations = () => {
      const queryClient = useQueryClient();

      const createUser = useMutation({
            mutationFn: authApi.createUser,
            onSuccess: () => {
                  queryClient.invalidateQueries({ queryKey: ['system-users'] });
                  toast.success('System user created successfully');
            },
            onError: (error: Error) => {
                  toast.error(`Failed to create user: ${error.message}`);
            },
      });

      const updateUser = useMutation({
            mutationFn: ({ id, data }: { id: number; data: Partial<SystemUser> }) =>
                  authApi.updateUser(id, data),
            onSuccess: (_, variables) => {
                  queryClient.invalidateQueries({ queryKey: ['system-users'] });
                  queryClient.invalidateQueries({ queryKey: ['system-user', variables.id] });
                  toast.success('System user updated successfully');
            },
            onError: (error: Error) => {
                  toast.error(`Failed to update user: ${error.message}`);
            },
      });

      const deleteUser = useMutation({
            mutationFn: async (userId: number) => {
                  // Mock delete function - replace with actual API call when available
                  console.log('Deleting user:', userId);
                  return { message: 'User deleted successfully' };
            },
            onSuccess: () => {
                  queryClient.invalidateQueries({ queryKey: ['system-users'] });
                  toast.success('System user deleted successfully');
            },
            onError: (error: Error) => {
                  toast.error(`Failed to delete user: ${error.message}`);
            },
      });

      const toggleUserStatus = useMutation({
            mutationFn: async ({ userId, isActive }: { userId: number; isActive: boolean }) => {
                  // Mock toggle function - replace with actual API call when available
                  return authApi.updateUser(userId, { isActive });
            },
            onSuccess: (_, variables) => {
                  queryClient.invalidateQueries({ queryKey: ['system-users'] });
                  queryClient.invalidateQueries({ queryKey: ['system-user', variables.userId] });
                  toast.success(`User ${variables.isActive ? 'activated' : 'deactivated'} successfully`);
            },
            onError: (error: Error) => {
                  toast.error(`Failed to update user status: ${error.message}`);
            },
      });

      return {
            createUser,
            updateUser,
            deleteUser,
            toggleUserStatus,
      };
};

// Get system user statistics
export const useSystemUserStats = () => {
      return useQuery({
            queryKey: ['system-user-stats'],
            queryFn: async () => {
                  // Mock statistics data
                  return {
                        total: 12,
                        active: 10,
                        inactive: 2,
                        byRole: {
                              admin: 2,
                              editor: 6,
                              moderator: 4,
                        },
                        recentLogins: 8,
                        newThisMonth: 2,
                  };
            },
            staleTime: 10 * 60 * 1000, // 10 minutes
      });
};

// Get system user activity logs
export const useSystemUserActivityLogs = (userId: number | string) => {
      return useQuery({
            queryKey: ['system-user-activity', userId],
            queryFn: async () => {
                  // Mock activity logs data
                  const mockLogs = [
                        {
                              id: 1,
                              action: 'User Login',
                              timestamp: '2024-01-15T08:30:00Z',
                              details: 'Successful login from *************',
                              userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        },
                        {
                              id: 2,
                              action: 'Profile Update',
                              timestamp: '2024-01-14T15:20:00Z',
                              details: 'Updated email address',
                              userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        },
                        {
                              id: 3,
                              action: 'Permission Change',
                              timestamp: '2024-01-13T10:45:00Z',
                              details: 'Added moderator permissions',
                              userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        },
                  ];
                  return mockLogs;
            },
            enabled: !!userId,
      });
};

// Individual hooks for specific operations
export const useSystemUsersList = (filters: SystemUserFilters = {}) => {
      return useSystemUsers(filters);
};

export const useSystemUserById = (userId: number | string) => {
      return useSystemUser(userId);
};

export const useSystemUserCreate = () => {
      const { createUser } = useSystemUserMutations();
      return createUser;
};

export const useSystemUserUpdate = () => {
      const { updateUser } = useSystemUserMutations();
      return updateUser;
};

export const useSystemUserDelete = () => {
      const { deleteUser } = useSystemUserMutations();
      return deleteUser;
};

export const useSystemUserToggleStatus = () => {
      const { toggleUserStatus } = useSystemUserMutations();
      return toggleUserStatus;
};

// Grouped export to match registered users pattern
export const systemUsersHooks = {
      useSystemUsers,
      useGetById: useSystemUserById,
      useCreate: useSystemUserCreate,
      useUpdate: useSystemUserUpdate,
      useDelete: useSystemUserDelete,
      useToggleStatus: useSystemUserToggleStatus,
      useStats: useSystemUserStats,
      useActivityLogs: useSystemUserActivityLogs,
};
