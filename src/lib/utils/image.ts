/**
 * Image utility functions for handling CDN URLs and image display
 */

const CDN_DOMAIN = process.env.NEXT_PUBLIC_DOMAIN_CDN_PICTURE || 'http://172.31.213.61';

/**
 * Build image URL using CDN domain
 * @param imagePath - The path returned from API response
 * @returns Full image URL with CDN domain
 */
export function buildImageUrl(imagePath: string | null | undefined): string | null {
      if (!imagePath) return null;

      // If the path already includes a domain, return as is
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
            return imagePath;
      }

      // Remove leading slash if present
      const cleanPath = imagePath.startsWith('/') ? imagePath.slice(1) : imagePath;

      return `${CDN_DOMAIN}/${cleanPath}`;
}

/**
 * Build team logo URL with fallback handling
 * @param logoPath - Team logo path from API
 * @returns Full logo URL or null if no path provided
 */
export function buildTeamLogoUrl(logoPath: string | null | undefined): string | null {
      return buildImageUrl(logoPath);
}

/**
 * Build country flag URL with fallback handling
 * @param flagPath - Country flag path from API
 * @returns Full flag URL or null if no path provided
 */
export function buildCountryFlagUrl(flagPath: string | null | undefined): string | null {
      return buildImageUrl(flagPath);
}

/**
 * Build league logo URL with fallback handling
 * @param logoPath - League logo path from API
 * @returns Full logo URL or null if no path provided
 */
export function buildLeagueLogoUrl(logoPath: string | null | undefined): string | null {
      return buildImageUrl(logoPath);
}

/**
 * Get image proxy URL for client-side image loading
 * @param imagePath - The path returned from API response
 * @returns Proxied image URL through Next.js API route
 */
export function getImageProxyUrl(imagePath: string | null | undefined): string | null {
      if (!imagePath) return null;

      // If the path already includes a domain, extract the path part
      let cleanPath = imagePath;
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
            try {
                  const url = new URL(imagePath);
                  cleanPath = url.pathname;
            } catch {
                  // If URL parsing fails, use the original path
                  cleanPath = imagePath;
            }
      }

      // Remove leading slash if present
      cleanPath = cleanPath.startsWith('/') ? cleanPath.slice(1) : cleanPath;

      return `/api/images/${cleanPath}`;
}

export default {
      buildImageUrl,
      buildTeamLogoUrl,
      buildCountryFlagUrl,
      buildLeagueLogoUrl,
      getImageProxyUrl,
};
