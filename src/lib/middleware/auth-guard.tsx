'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/hooks/useAuth';
import { PageLoading } from '@/components/ui/loading-states';
import { SystemUser } from '@/lib/types/api';

interface AuthGuardProps {
  children: React.ReactNode;
  requiredRole?: SystemUser['role'] | SystemUser['role'][];
  fallbackUrl?: string;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requiredRole,
  fallbackUrl = '/auth/login',
}) => {
  const router = useRouter();
  const { isAuthenticated, user, isLoading } = useAuth();

  useEffect(() => {
    // Wait for auth check to complete
    if (isLoading) return;

    // Redirect to login if not authenticated
    if (!isAuthenticated || !user) {
      router.push(fallbackUrl);
      return;
    }

    // Check role requirements
    if (requiredRole) {
      const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
      if (!allowedRoles.includes(user.role)) {
        // Redirect to unauthorized page or dashboard
        router.push('/dashboard?error=unauthorized');
        return;
      }
    }
  }, [isAuthenticated, user, isLoading, requiredRole, router, fallbackUrl]);

  // Show loading while checking authentication
  if (isLoading) {
    return <PageLoading message="Verifying authentication..." />;
  }

  // Don't render children if not authenticated
  if (!isAuthenticated || !user) {
    return <PageLoading message="Redirecting to login..." />;
  }

  // Check role requirements
  if (requiredRole) {
    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    if (!allowedRoles.includes(user.role)) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600 mb-4">
              You don't have permission to access this page.
            </p>
            <p className="text-sm text-gray-500">
              Required role: {allowedRoles.join(' or ')} | Your role: {user.role}
            </p>
          </div>
        </div>
      );
    }
  }

  return <>{children}</>;
};

// Higher-order component for page-level protection
export const withAuthGuard = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requiredRole?: SystemUser['role'] | SystemUser['role'][];
    fallbackUrl?: string;
  }
) => {
  const WrappedComponent = (props: P) => {
    return (
      <AuthGuard
        requiredRole={options?.requiredRole}
        fallbackUrl={options?.fallbackUrl}
      >
        <Component {...props} />
      </AuthGuard>
    );
  };

  WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Hook for checking permissions
export const usePermissions = () => {
  const { user } = useAuth();

  const hasRole = (role: SystemUser['role'] | SystemUser['role'][]): boolean => {
    if (!user) return false;
    const allowedRoles = Array.isArray(role) ? role : [role];
    return allowedRoles.includes(user.role);
  };

  const isAdmin = (): boolean => hasRole('admin');
  const isEditor = (): boolean => hasRole(['admin', 'editor']);
  const isModerator = (): boolean => hasRole(['admin', 'editor', 'moderator']);

  const canManageUsers = (): boolean => isAdmin();
  const canManageContent = (): boolean => isEditor();
  const canModerate = (): boolean => isModerator();
  const canSync = (): boolean => isAdmin();

  // Generic permission checker function
  const can = (permission: string): boolean => {
    switch (permission) {
      case 'manage-users':
        return canManageUsers();
      case 'manage-content':
        return canManageContent();
      case 'moderate':
        return canModerate();
      case 'sync':
        return canSync();
      default:
        return false;
    }
  };

  return {
    user,
    hasRole,
    isAdmin,
    isEditor,
    isModerator,
    canManageUsers,
    canManageContent,
    canModerate,
    canSync,
    can,
  };
};
