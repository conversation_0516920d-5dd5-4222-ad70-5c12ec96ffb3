'use client';

import { Button } from '@/components/ui/button';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface FixtureNavigationProps {
  variant: 'detail' | 'edit' | 'create';
  fixtureId?: string | number;
  onRefresh?: () => void;
  isLoading?: boolean;
  className?: string;
}

export const FixtureNavigation: React.FC<FixtureNavigationProps> = ({
  variant,
  fixtureId,
  onRefresh,
  isLoading = false,
  className = ''
}) => {
  const router = useRouter();

  const handleBack = () => {
    switch (variant) {
      case 'detail':
        // From detail page -> back to fixtures list
        router.push('/dashboard/fixtures');
        break;
      case 'edit':
        // From edit page -> back to detail page
        if (fixtureId) {
          router.push(`/dashboard/fixtures/${fixtureId}`);
        } else {
          router.back();
        }
        break;
      case 'create':
        // From create page -> back to fixtures list
        router.push('/dashboard/fixtures');
        break;
      default:
        router.back();
    }
  };

  const getBackButtonText = () => {
    switch (variant) {
      case 'detail':
        return 'Back to Fixtures';
      case 'edit':
        return 'Back to Detail';
      case 'create':
        return 'Back to Fixtures';
      default:
        return 'Back';
    }
  };

  return (
    <div className={`flex items-center space-x-4 ${className}`}>
      {/* Back Button */}
      <Button 
        variant="outline" 
        onClick={handleBack}
        disabled={isLoading}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        {getBackButtonText()}
      </Button>

      {/* Refresh Button - Only show for detail page */}
      {variant === 'detail' && onRefresh && (
        <Button 
          variant="outline" 
          onClick={onRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      )}
    </div>
  );
};
