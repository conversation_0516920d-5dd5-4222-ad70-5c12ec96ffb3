'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Edit, Radio, Users, Calendar } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Fixture } from '@/lib/types/api';
import { usePermissions } from '@/lib/middleware/auth-guard';

interface FixtureActionsProps {
  fixture: Fixture;
  onBroadcastLinks?: () => void;
  className?: string;
}

export const FixtureActions: React.FC<FixtureActionsProps> = ({
  fixture,
  onBroadcastLinks,
  className = ''
}) => {
  const router = useRouter();
  const { isEditor } = usePermissions();

  const handleEdit = () => {
    const fixtureId = fixture.externalId || fixture.id;
    router.push(`/dashboard/fixtures/${fixtureId}/edit`);
  };

  const handleViewHomeTeam = () => {
    if (fixture.homeTeamId) {
      window.open(`/dashboard/teams/${fixture.homeTeamId}`, '_blank');
    }
  };

  const handleViewAwayTeam = () => {
    if (fixture.awayTeamId) {
      window.open(`/dashboard/teams/${fixture.awayTeamId}`, '_blank');
    }
  };

  const handleViewLeague = () => {
    if (fixture.leagueId) {
      window.open(`/dashboard/leagues/${fixture.leagueId}`, '_blank');
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Primary Actions */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-gray-700">Actions</h3>
        
        {/* Edit Button - Editor+ only */}
        {isEditor() && (
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={handleEdit}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Fixture
          </Button>
        )}

        {/* Broadcast Links Button */}
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={onBroadcastLinks}
        >
          <Radio className="mr-2 h-4 w-4" />
          Manage Broadcast Links
        </Button>
      </div>

      {/* Related Data Actions */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-gray-700">Related Data</h3>
        
        {/* Home Team */}
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={handleViewHomeTeam}
          disabled={!fixture.homeTeamId}
        >
          <Users className="mr-2 h-4 w-4" />
          View Home Team
        </Button>

        {/* Away Team */}
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={handleViewAwayTeam}
          disabled={!fixture.awayTeamId}
        >
          <Users className="mr-2 h-4 w-4" />
          View Away Team
        </Button>

        {/* League */}
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={handleViewLeague}
          disabled={!fixture.leagueId}
        >
          <Calendar className="mr-2 h-4 w-4" />
          View League
        </Button>
      </div>
    </div>
  );
};
