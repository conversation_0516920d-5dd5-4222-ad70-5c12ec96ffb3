'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Search, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Option {
  value: string;
  label: string;
  logo?: string;
  uniqueKey?: string;
}

interface SearchableSelectFieldProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  options: Option[];
  error?: string;
  disabled?: boolean;
  required?: boolean;
  onSearch?: (query: string) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoading?: boolean;
  searchPlaceholder?: string;
}

export const SearchableSelectField = React.forwardRef<
  HTMLDivElement,
  SearchableSelectFieldProps
>(({
  label,
  placeholder = "Select option",
  value,
  onValueChange,
  options = [],
  error,
  disabled = false,
  required = false,
  onSearch,
  onLoadMore,
  hasMore = false,
  isLoading = false,
  searchPlaceholder = "Search...",
  ...props
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const CDN_URL = process.env.NEXT_PUBLIC_DOMAIN_CDN_PICTURE || 'http://116.203.125.65';

  // Reset search when options change (for load more functionality)
  useEffect(() => {
    // Don't reset search query, just force re-render
  }, [options.length]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Handle search immediately (no debounce for dropdown search)
  useEffect(() => {
    if (onSearch) {
      onSearch(searchQuery);
    }
  }, [searchQuery, onSearch]);

  const selectedOption = options.find(option => option.value === value);

  const handleSelect = (optionValue: string) => {
    onValueChange?.(optionValue);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleLoadMore = () => {
    console.log('🔄 SearchableSelectField Load More:', { hasMore, isLoading, optionsCount: options.length });
    if (onLoadMore && hasMore && !isLoading) {
      onLoadMore();
    }
  };

  return (
    <div className="space-y-2" ref={ref}>
      {label && (
        <label className="text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      <div className="relative" ref={dropdownRef}>
        {/* Trigger Button */}
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className={cn(
            "w-full flex items-center justify-between px-3 py-2 text-left bg-white border rounded-md shadow-sm",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            disabled && "bg-gray-50 text-gray-500 cursor-not-allowed",
            error && "border-red-500 focus:ring-red-500 focus:border-red-500",
            !error && !disabled && "border-gray-300 hover:border-gray-400"
          )}
        >
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            {selectedOption ? (
              <>
                {selectedOption.logo && (
                  <img
                    src={`${CDN_URL}/${selectedOption.logo}`}
                    alt={selectedOption.label}
                    className="w-5 h-5 object-contain rounded flex-shrink-0"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                )}
                <span className="truncate">{selectedOption.label}</span>
              </>
            ) : (
              <span className="text-gray-500">{placeholder}</span>
            )}
          </div>
          <ChevronDown className={cn(
            "w-4 h-4 text-gray-400 transition-transform",
            isOpen && "transform rotate-180"
          )} />
        </button>

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
            {/* Search Input */}
            <div className="p-2 border-b border-gray-200">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Options List */}
            <div className="max-h-60 overflow-y-auto">
              {options.length === 0 && !isLoading ? (
                <div className="px-3 py-2 text-sm text-gray-500 text-center">
                  No options found
                </div>
              ) : (
                <>
                  {options.map((option, index) => (
                    <button
                      key={option.uniqueKey || `${option.value}-${index}`}
                      type="button"
                      onClick={() => handleSelect(option.value)}
                      className={cn(
                        "w-full flex items-center space-x-2 px-3 py-2 text-left text-sm hover:bg-gray-50",
                        value === option.value && "bg-blue-50 text-blue-700"
                      )}
                    >
                      {option.logo && (
                        <img
                          src={`${CDN_URL}/${option.logo}`}
                          alt={option.label}
                          className="w-5 h-5 object-contain rounded flex-shrink-0"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      )}
                      <span className="truncate">{option.label}</span>
                    </button>
                  ))}

                  {/* Load More Button */}
                  {hasMore && (
                    <button
                      type="button"
                      onClick={handleLoadMore}
                      disabled={isLoading}
                      className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 disabled:opacity-50"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span>Loading...</span>
                        </>
                      ) : (
                        <span>Load more...</span>
                      )}
                    </button>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
});

SearchableSelectField.displayName = 'SearchableSelectField';
