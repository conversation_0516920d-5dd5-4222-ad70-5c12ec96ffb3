'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

export interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
      value?: number;
      max?: number;
      showValue?: boolean;
      size?: 'sm' | 'md' | 'lg';
      variant?: 'default' | 'success' | 'warning' | 'error';
}

const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
      ({ className, value = 0, max = 100, showValue = false, size = 'md', variant = 'default', ...props }, ref) => {
            const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

            const sizeClasses = {
                  sm: 'h-2',
                  md: 'h-3',
                  lg: 'h-4',
            };

            const variantClasses = {
                  default: 'bg-blue-600',
                  success: 'bg-green-600',
                  warning: 'bg-yellow-600',
                  error: 'bg-red-600',
            };

            return (
                  <div
                        ref={ref}
                        className={cn(
                              'relative w-full overflow-hidden rounded-full bg-gray-200',
                              sizeClasses[size],
                              className
                        )}
                        {...props}
                  >
                        <div
                              className={cn(
                                    'h-full transition-all duration-300 ease-in-out',
                                    variantClasses[variant]
                              )}
                              style={{ width: `${percentage}%` }}
                        />
                        {showValue && (
                              <div className="absolute inset-0 flex items-center justify-center">
                                    <span className="text-xs font-medium text-white">
                                          {Math.round(percentage)}%
                                    </span>
                              </div>
                        )}
                  </div>
            );
      }
);

Progress.displayName = 'Progress';

export { Progress };
