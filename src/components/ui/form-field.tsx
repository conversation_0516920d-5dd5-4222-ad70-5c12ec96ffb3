'use client';

import { forwardRef } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';

export interface FormFieldProps {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const FormField = forwardRef<HTMLDivElement, FormFieldProps>(
  ({ label, description, error, required, className, children }, ref) => {
    return (
      <div ref={ref} className={cn('space-y-2', className)}>
        {label && (
          <Label className={cn('text-sm font-medium', error && 'text-red-600')}>
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}
        {children}
        {description && !error && (
          <p className="text-sm text-gray-500">{description}</p>
        )}
        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  }
);

FormField.displayName = 'FormField';

// Input Field Component
export interface InputFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
}

export const InputField = forwardRef<HTMLInputElement, InputFieldProps>(
  ({ label, description, error, required, className, ...props }, ref) => {
    return (
      <FormField label={label} description={description} error={error} required={required}>
        <Input
          ref={ref}
          className={cn(error && 'border-red-500 focus:border-red-500', className)}
          {...props}
        />
      </FormField>
    );
  }
);

InputField.displayName = 'InputField';

// Textarea Field Component
export interface TextareaFieldProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
}

export const TextareaField = forwardRef<HTMLTextAreaElement, TextareaFieldProps>(
  ({ label, description, error, required, className, ...props }, ref) => {
    return (
      <FormField label={label} description={description} error={error} required={required}>
        <Textarea
          ref={ref}
          className={cn(error && 'border-red-500 focus:border-red-500', className)}
          {...props}
        />
      </FormField>
    );
  }
);

TextareaField.displayName = 'TextareaField';

// Select Field Component
export interface SelectFieldProps {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  placeholder?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  options: { value: string; label: string; disabled?: boolean; logo?: string }[];
  className?: string;
  disabled?: boolean;
}

export const SelectField = forwardRef<HTMLButtonElement, SelectFieldProps>(
  ({ label, description, error, required, placeholder, value, onValueChange, options, className, disabled }, ref) => {
    const selectedOption = options.find(option => option.value === value);
    const CDN_URL = process.env.NEXT_PUBLIC_DOMAIN_CDN_PICTURE || 'http://**************';

    return (
      <FormField label={label} description={description} error={error} required={required}>
        <Select value={value} onValueChange={onValueChange} disabled={disabled}>
          <SelectTrigger
            ref={ref}
            className={cn(error && 'border-red-500 focus:border-red-500', className)}
          >
            <SelectValue placeholder={placeholder}>
              {selectedOption && (
                <div className="flex items-center space-x-2">
                  {selectedOption.logo && (
                    <img
                      src={`${CDN_URL}/${selectedOption.logo}`}
                      alt={selectedOption.label}
                      className="w-5 h-5 object-contain rounded"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  )}
                  <span>{selectedOption.label}</span>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {options.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                <div className="flex items-center space-x-2">
                  {option.logo && (
                    <img
                      src={`${CDN_URL}/${option.logo}`}
                      alt={option.label}
                      className="w-5 h-5 object-contain rounded"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  )}
                  <span>{option.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </FormField>
    );
  }
);

SelectField.displayName = 'SelectField';

// Checkbox Field Component
export interface CheckboxFieldProps {
  label?: string;
  description?: string;
  error?: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  className?: string;
}

export const CheckboxField = forwardRef<HTMLButtonElement, CheckboxFieldProps>(
  ({ label, description, error, checked, onCheckedChange, className }, ref) => {
    return (
      <FormField description={description} error={error} className={className}>
        <div className="flex items-center space-x-2">
          <Checkbox
            ref={ref}
            checked={checked}
            onCheckedChange={onCheckedChange}
            className={cn(error && 'border-red-500')}
          />
          {label && (
            <Label className={cn('text-sm font-normal cursor-pointer', error && 'text-red-600')}>
              {label}
            </Label>
          )}
        </div>
      </FormField>
    );
  }
);

CheckboxField.displayName = 'CheckboxField';

// Radio Group Field Component
export interface RadioFieldProps {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  value?: string;
  onValueChange?: (value: string) => void;
  options: { value: string; label: string; disabled?: boolean }[];
  orientation?: 'horizontal' | 'vertical';
  className?: string;
}

export const RadioField = forwardRef<HTMLDivElement, RadioFieldProps>(
  ({ label, description, error, required, value, onValueChange, options, orientation = 'vertical', className }, ref) => {
    return (
      <FormField label={label} description={description} error={error} required={required} className={className}>
        <RadioGroup
          ref={ref}
          value={value}
          onValueChange={onValueChange}
          className={cn(
            orientation === 'horizontal' ? 'flex flex-row space-x-4' : 'space-y-2'
          )}
        >
          {options.map((option) => (
            <div key={option.value} className="flex items-center space-x-2">
              <RadioGroupItem
                value={option.value}
                disabled={option.disabled}
                className={cn(error && 'border-red-500')}
              />
              <Label className="text-sm font-normal cursor-pointer">
                {option.label}
              </Label>
            </div>
          ))}
        </RadioGroup>
      </FormField>
    );
  }
);

RadioField.displayName = 'RadioField';

// Form Section Component
export interface FormSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  className,
}) => {
  return (
    <div className={cn('space-y-4', className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          )}
          {description && (
            <p className="text-sm text-gray-600">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
};

// Form Actions Component
export interface FormActionsProps {
  children: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right';
}

export const FormActions: React.FC<FormActionsProps> = ({
  children,
  className,
  align = 'right',
}) => {
  return (
    <div
      className={cn(
        'flex space-x-2 pt-4 border-t',
        align === 'left' && 'justify-start',
        align === 'center' && 'justify-center',
        align === 'right' && 'justify-end',
        className
      )}
    >
      {children}
    </div>
  );
};
