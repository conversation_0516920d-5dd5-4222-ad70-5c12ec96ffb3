'use client';

import * as React from 'react';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

export interface AlertDialogProps {
      open?: boolean;
      onOpenChange?: (open: boolean) => void;
      children: React.ReactNode;
}

export interface AlertDialogContentProps extends React.HTMLAttributes<HTMLDivElement> {
      children: React.ReactNode;
}

export interface AlertDialogHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
      children: React.ReactNode;
}

export interface AlertDialogTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
      children: React.ReactNode;
}

export interface AlertDialogDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
      children: React.ReactNode;
}

export interface AlertDialogFooterProps extends React.HTMLAttributes<HTMLDivElement> {
      children: React.ReactNode;
}

export interface AlertDialogActionProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
      children: React.ReactNode;
      variant?: 'default' | 'destructive';
}

export interface AlertDialogCancelProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
      children: React.ReactNode;
}

export interface AlertDialogTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
      children: React.ReactNode;
      asChild?: boolean;
}

const AlertDialog: React.FC<AlertDialogProps> = ({ open, onOpenChange, children }) => {
      React.useEffect(() => {
            const handleEscape = (e: KeyboardEvent) => {
                  if (e.key === 'Escape' && open) {
                        onOpenChange?.(false);
                  }
            };

            if (open) {
                  document.addEventListener('keydown', handleEscape);
                  document.body.style.overflow = 'hidden';
            }

            return () => {
                  document.removeEventListener('keydown', handleEscape);
                  document.body.style.overflow = 'unset';
            };
      }, [open, onOpenChange]);

      if (!open) return null;

      return createPortal(
            <div className="fixed inset-0 z-50 flex items-center justify-center">
                  {/* Backdrop */}
                  <div
                        className="absolute inset-0 bg-black/50"
                        onClick={() => onOpenChange?.(false)}
                  />
                  {/* Content */}
                  <div className="relative z-10">{children}</div>
            </div>,
            document.body
      );
};

const AlertDialogContent = React.forwardRef<HTMLDivElement, AlertDialogContentProps>(
      ({ className, children, ...props }, ref) => (
            <div
                  ref={ref}
                  className={cn(
                        'bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6',
                        className
                  )}
                  {...props}
            >
                  {children}
            </div>
      )
);
AlertDialogContent.displayName = 'AlertDialogContent';

const AlertDialogHeader = React.forwardRef<HTMLDivElement, AlertDialogHeaderProps>(
      ({ className, children, ...props }, ref) => (
            <div
                  ref={ref}
                  className={cn('flex flex-col space-y-2 text-center sm:text-left mb-4', className)}
                  {...props}
            >
                  {children}
            </div>
      )
);
AlertDialogHeader.displayName = 'AlertDialogHeader';

const AlertDialogTitle = React.forwardRef<HTMLHeadingElement, AlertDialogTitleProps>(
      ({ className, children, ...props }, ref) => (
            <h2
                  ref={ref}
                  className={cn('text-lg font-semibold text-gray-900', className)}
                  {...props}
            >
                  {children}
            </h2>
      )
);
AlertDialogTitle.displayName = 'AlertDialogTitle';

const AlertDialogDescription = React.forwardRef<HTMLParagraphElement, AlertDialogDescriptionProps>(
      ({ className, children, ...props }, ref) => (
            <p
                  ref={ref}
                  className={cn('text-sm text-gray-600', className)}
                  {...props}
            >
                  {children}
            </p>
      )
);
AlertDialogDescription.displayName = 'AlertDialogDescription';

const AlertDialogFooter = React.forwardRef<HTMLDivElement, AlertDialogFooterProps>(
      ({ className, children, ...props }, ref) => (
            <div
                  ref={ref}
                  className={cn('flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-6', className)}
                  {...props}
            >
                  {children}
            </div>
      )
);
AlertDialogFooter.displayName = 'AlertDialogFooter';

const AlertDialogAction = React.forwardRef<HTMLButtonElement, AlertDialogActionProps>(
      ({ className, variant = 'default', children, ...props }, ref) => (
            <Button
                  ref={ref}
                  className={cn(
                        variant === 'destructive' && 'bg-red-600 hover:bg-red-700 text-white',
                        className
                  )}
                  {...props}
            >
                  {children}
            </Button>
      )
);
AlertDialogAction.displayName = 'AlertDialogAction';

const AlertDialogCancel = React.forwardRef<HTMLButtonElement, AlertDialogCancelProps>(
      ({ className, children, ...props }, ref) => (
            <Button
                  ref={ref}
                  variant="outline"
                  className={cn('mt-2 sm:mt-0', className)}
                  {...props}
            >
                  {children}
            </Button>
      )
);
AlertDialogCancel.displayName = 'AlertDialogCancel';

const AlertDialogTrigger = React.forwardRef<HTMLButtonElement, AlertDialogTriggerProps>(
      ({ className, children, asChild = false, ...props }, ref) => {
            if (asChild) {
                  return React.cloneElement(children as React.ReactElement, {
                        ref,
                        ...props,
                  });
            }

            return (
                  <button
                        ref={ref}
                        className={className}
                        {...props}
                  >
                        {children}
                  </button>
            );
      }
);
AlertDialogTrigger.displayName = 'AlertDialogTrigger';

export {
      AlertDialog,
      AlertDialogContent,
      AlertDialogHeader,
      AlertDialogTitle,
      AlertDialogDescription,
      AlertDialogFooter,
      AlertDialogAction,
      AlertDialogCancel,
      AlertDialogTrigger,
};
