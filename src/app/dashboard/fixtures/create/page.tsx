'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  InputField,
  SelectField,
  FormSection,
  FormActions
} from '@/components/ui/form-field';
import { fixturesApi } from '@/lib/api/fixtures';
import { leaguesApi } from '@/lib/api/leagues';
import { teamsApi } from '@/lib/api/teams';
import { ArrowLeft, Save, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { FixtureNavigation } from '@/components/fixtures/FixtureNavigation';

// Status options (same as edit page)
const statusOptions = [
  { value: 'TBD', label: 'Time To Be Defined' },
  { value: 'NS', label: 'Not Started' },
  { value: 'ST', label: 'Scheduled' },
  { value: '1H', label: 'First Half' },
  { value: 'HT', label: 'Halftime' },
  { value: '2H', label: 'Second Half' },
  { value: 'ET', label: 'Extra Time' },
  { value: 'BT', label: 'Break Time' },
  { value: 'P', label: 'Penalty In Progress' },
  { value: 'SUSP', label: 'Match Suspended' },
  { value: 'INT', label: 'Match Interrupted' },
  { value: 'FT', label: 'Match Finished (Regular Time)' },
  { value: 'AET', label: 'Match Finished (After Extra Time)' },
  { value: 'PEN', label: 'Match Finished (After Penalty)' },
  { value: 'PST', label: 'Match Postponed' },
  { value: 'CANC', label: 'Match Cancelled' },
  { value: 'ABD', label: 'Match Abandoned' },
  { value: 'AWD', label: 'Technical Loss' },
  { value: 'WO', label: 'WalkOver' },
  { value: 'LIVE', label: 'In Progress' },
];

interface FixtureFormData {
  homeTeamId: string;
  awayTeamId: string;
  leagueId: string;
  date: string;
  time: string;
  venueName: string;
  venueCity: string;
  round: string;
  status: string;
}

export default function CreateFixturePage() {
  const router = useRouter();
  const [formData, setFormData] = useState<FixtureFormData>({
    homeTeamId: '',
    awayTeamId: '',
    leagueId: '',
    date: '',
    time: '',
    venueName: '',
    venueCity: '',
    round: '',
    status: 'NS', // Default to Not Started
  });

  const [errors, setErrors] = useState<Partial<FixtureFormData>>({});

  // Fetch leagues for dropdown
  const { data: leagues } = useQuery({
    queryKey: ['leagues', 'all'],
    queryFn: () => leaguesApi.getLeagues({ limit: 100 }),
  });

  // Fetch teams for dropdown
  const { data: teams } = useQuery({
    queryKey: ['teams', 'all'],
    queryFn: () => teamsApi.getTeams({ limit: 100 }),
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data: any) => fixturesApi.createFixture(data),
    onSuccess: () => {
      toast.success('Fixture created successfully');
      router.push('/dashboard/fixtures');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create fixture');
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Partial<FixtureFormData> = {};

    if (!formData.homeTeamId) newErrors.homeTeamId = 'Home team is required';
    if (!formData.awayTeamId) newErrors.awayTeamId = 'Away team is required';
    if (!formData.leagueId) newErrors.leagueId = 'League is required';
    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.time) newErrors.time = 'Time is required';

    if (formData.homeTeamId === formData.awayTeamId) {
      newErrors.awayTeamId = 'Away team must be different from home team';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    // Combine date and time
    const dateTime = new Date(`${formData.date}T${formData.time}`);

    // Helper function to get status long description
    const getStatusLong = (status: string): string => {
      const statusMap: Record<string, string> = {
        'TBD': 'Time To Be Defined',
        'NS': 'Not Started',
        'ST': 'Scheduled',
        '1H': 'First Half',
        'HT': 'Halftime',
        '2H': 'Second Half',
        'ET': 'Extra Time',
        'BT': 'Break Time',
        'P': 'Penalty In Progress',
        'SUSP': 'Match Suspended',
        'INT': 'Match Interrupted',
        'FT': 'Match Finished',
        'AET': 'Match Finished After Extra Time',
        'PEN': 'Match Finished After Penalty',
        'PST': 'Match Postponed',
        'CANC': 'Match Cancelled',
        'ABD': 'Match Abandoned',
        'AWD': 'Technical Loss',
        'WO': 'WalkOver',
        'LIVE': 'In Progress',
      };
      return statusMap[status] || status;
    };

    // Prepare data for API - Correct structure with nested data object
    const submitData = {
      homeTeamId: parseInt(formData.homeTeamId),
      awayTeamId: parseInt(formData.awayTeamId),
      leagueId: parseInt(formData.leagueId),
      date: dateTime.toISOString(),
      venueName: formData.venueName || null,
      venueCity: formData.venueCity || null,
      round: formData.round || null,
      referee: null,
      // Match status in nested data object (as per API documentation)
      data: {
        status: formData.status,
        statusLong: getStatusLong(formData.status),
        statusExtra: 0,
        elapsed: null,
        goalsHome: null,
        goalsAway: null,
      }
    };

    createMutation.mutate(submitData);
  };

  const updateFormData = (field: keyof FixtureFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const leagueOptions = leagues?.data?.map(league => ({
    value: league.id.toString(),
    label: league.name,
  })) || [];

  const teamOptions = teams?.data?.map(team => ({
    value: team.id.toString(),
    label: team.name,
  })) || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <FixtureNavigation
          variant="create"
          isLoading={createMutation.isLoading}
        />
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create New Fixture</h1>
          <p className="text-gray-600 mt-1">Add a new football fixture to the system</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Fixture Details
          </CardTitle>
          <CardDescription>
            Fill in the details for the new fixture
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <FormSection title="Teams & Competition" description="Select the teams and league">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <SelectField
                  label="Home Team"
                  placeholder="Select home team"
                  required
                  value={formData.homeTeamId}
                  onValueChange={(value) => updateFormData('homeTeamId', value)}
                  options={teamOptions}
                  error={errors.homeTeamId}
                />

                <SelectField
                  label="Away Team"
                  placeholder="Select away team"
                  required
                  value={formData.awayTeamId}
                  onValueChange={(value) => updateFormData('awayTeamId', value)}
                  options={teamOptions.filter(team => team.value !== formData.homeTeamId)}
                  error={errors.awayTeamId}
                />
              </div>

              <SelectField
                label="League"
                placeholder="Select league"
                required
                value={formData.leagueId}
                onValueChange={(value) => updateFormData('leagueId', value)}
                options={leagueOptions}
                error={errors.leagueId}
              />

              <SelectField
                label="Status"
                placeholder="Select status"
                required
                value={formData.status}
                onValueChange={(value) => updateFormData('status', value)}
                options={statusOptions}
                error={errors.status}
              />
            </FormSection>

            <FormSection title="Schedule" description="Set the date and time">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Date"
                  type="date"
                  required
                  value={formData.date}
                  onChange={(e) => updateFormData('date', e.target.value)}
                  error={errors.date}
                />

                <InputField
                  label="Time"
                  type="time"
                  required
                  value={formData.time}
                  onChange={(e) => updateFormData('time', e.target.value)}
                  error={errors.time}
                />
              </div>
            </FormSection>

            <FormSection title="Venue Information" description="Optional venue details">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Venue Name"
                  placeholder="Stadium name"
                  value={formData.venueName}
                  onChange={(e) => updateFormData('venueName', e.target.value)}
                />

                <InputField
                  label="Venue City"
                  placeholder="City"
                  value={formData.venueCity}
                  onChange={(e) => updateFormData('venueCity', e.target.value)}
                />
              </div>

              <InputField
                label="Round"
                placeholder="e.g., Matchday 1, Quarter-final"
                value={formData.round}
                onChange={(e) => updateFormData('round', e.target.value)}
              />
            </FormSection>

            <FormActions>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={createMutation.isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createMutation.isLoading}
              >
                <Save className="mr-2 h-4 w-4" />
                {createMutation.isLoading ? 'Creating...' : 'Create Fixture'}
              </Button>
            </FormActions>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
