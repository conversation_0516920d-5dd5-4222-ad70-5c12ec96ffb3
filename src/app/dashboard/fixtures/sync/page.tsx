'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  RefreshCw,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Play,
  Database,
  Activity,
  TrendingUp
} from 'lucide-react';
import { toast } from 'sonner';
import { fixturesApi } from '@/lib/api/fixtures';
import { AuthGuard } from '@/lib/middleware/auth-guard';
import { FixtureNavigation } from '@/components/fixtures/FixtureNavigation';

interface SyncOperation {
  id: string;
  type: 'daily' | 'season' | 'manual';
  status: 'running' | 'success' | 'failed' | 'warning';
  startTime: string;
  endTime?: string;
  duration?: string;
  fixturesProcessed: number;
  errors: string[];
  details?: {
    seasonsProcessed?: number[];
    leaguesProcessed?: number;
    totalFixtures?: number;
  };
}

export default function FixturesSyncPage() {
  const queryClient = useQueryClient();
  const [syncHistory, setSyncHistory] = useState<SyncOperation[]>([]);
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // Fetch current sync status
  const { data: syncStatus, isLoading: statusLoading, refetch: refetchStatus } = useQuery({
    queryKey: ['fixtures-sync-status'],
    queryFn: () => fixturesApi.getSyncStatus(),
    refetchInterval: isAutoRefresh ? 5000 : false, // Auto-refresh every 5 seconds
  });

  // Daily sync mutation
  const dailySyncMutation = useMutation({
    mutationFn: () => fixturesApi.triggerDailySync(),
    onSuccess: (result) => {
      toast.success('Daily sync started successfully!');
      addToSyncHistory({
        id: Date.now().toString(),
        type: 'daily',
        status: 'success',
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        duration: result.details?.duration || 'N/A',
        fixturesProcessed: result.fixturesUpserted || 0,
        errors: [],
        details: result.details
      });
      refetchStatus();
    },
    onError: (error: any) => {
      toast.error(`Daily sync failed: ${error.message}`);
      addToSyncHistory({
        id: Date.now().toString(),
        type: 'daily',
        status: 'failed',
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        fixturesProcessed: 0,
        errors: [error.message]
      });
    },
  });

  // Season sync mutation
  const seasonSyncMutation = useMutation({
    mutationFn: () => fixturesApi.triggerSeasonSync(),
    onSuccess: (result) => {
      toast.success('Season sync started successfully!');
      addToSyncHistory({
        id: Date.now().toString(),
        type: 'season',
        status: 'success',
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        duration: result.details?.duration || 'N/A',
        fixturesProcessed: result.fixturesUpserted || 0,
        errors: [],
        details: result.details
      });
      refetchStatus();
    },
    onError: (error: any) => {
      toast.error(`Season sync failed: ${error.message}`);
      addToSyncHistory({
        id: Date.now().toString(),
        type: 'season',
        status: 'failed',
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        fixturesProcessed: 0,
        errors: [error.message]
      });
    },
  });

  const addToSyncHistory = (operation: SyncOperation) => {
    setSyncHistory(prev => [operation, ...prev.slice(0, 9)]); // Keep last 10 operations
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'default',
      failed: 'destructive',
      warning: 'secondary',
      running: 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status}</span>
      </Badge>
    );
  };

  const isAnySyncRunning = dailySyncMutation.isLoading || seasonSyncMutation.isLoading;

  return (
    <AuthGuard requiredRole="admin">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <FixtureNavigation variant="detail" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <RefreshCw className="mr-2 h-6 w-6" />
                Fixtures Sync Management
              </h1>
              <p className="text-gray-600 mt-1">Synchronize fixture data from API Football service</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAutoRefresh(!isAutoRefresh)}
            >
              <Activity className={`h-4 w-4 mr-2 ${isAutoRefresh ? 'text-green-600' : 'text-gray-400'}`} />
              Auto Refresh {isAutoRefresh ? 'ON' : 'OFF'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchStatus()}
              disabled={statusLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${statusLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Sync Status Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-blue-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">Last Sync</p>
                  {statusLoading ? (
                    <Skeleton className="h-4 w-20 mt-1" />
                  ) : (
                    <p className="text-sm font-bold">
                      {syncStatus?.lastSync ? formatDate(syncStatus.lastSync) : 'Never'}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-green-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">Total Fixtures</p>
                  {statusLoading ? (
                    <Skeleton className="h-4 w-16 mt-1" />
                  ) : (
                    <p className="text-lg font-bold">{syncStatus?.fixtures?.toLocaleString() || 0}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">Sync Errors</p>
                  {statusLoading ? (
                    <Skeleton className="h-4 w-8 mt-1" />
                  ) : (
                    <p className="text-lg font-bold text-red-600">
                      {syncStatus?.errors?.length || 0}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">Status</p>
                  <div className="flex items-center mt-1">
                    {isAnySyncRunning ? (
                      <>
                        <RefreshCw className="h-4 w-4 text-blue-600 animate-spin mr-1" />
                        <span className="text-sm font-bold text-blue-600">Syncing...</span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
                        <span className="text-sm font-bold text-green-600">Ready</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Error Display */}
        {syncStatus?.errors && syncStatus.errors.length > 0 && (
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="text-red-800 flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Recent Sync Errors
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {syncStatus.errors.slice(0, 3).map((error, index) => (
                  <div key={index} className="text-sm text-red-700 bg-red-100 p-2 rounded">
                    {typeof error === 'string' ? error : JSON.stringify(error)}
                  </div>
                ))}
                {syncStatus.errors.length > 3 && (
                  <p className="text-sm text-red-600">
                    ... and {syncStatus.errors.length - 3} more errors
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Sync Actions</CardTitle>
            <CardDescription>Trigger manual sync operations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button
                onClick={() => dailySyncMutation.mutate()}
                disabled={isAnySyncRunning}
                className="flex items-center"
              >
                <Calendar className="mr-2 h-4 w-4" />
                {dailySyncMutation.isLoading ? 'Running Daily Sync...' : 'Daily Sync'}
              </Button>

              <Button
                onClick={() => seasonSyncMutation.mutate()}
                disabled={isAnySyncRunning}
                variant="outline"
                className="flex items-center"
              >
                <Database className="mr-2 h-4 w-4" />
                {seasonSyncMutation.isLoading ? 'Running Season Sync...' : 'Season Sync'}
              </Button>

              <Button
                onClick={() => refetchStatus()}
                variant="outline"
                disabled={statusLoading}
                className="flex items-center"
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${statusLoading ? 'animate-spin' : ''}`} />
                Refresh Status
              </Button>
            </div>

            {isAnySyncRunning && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <RefreshCw className="h-4 w-4 text-blue-600 animate-spin mr-2" />
                    <span className="text-blue-800 font-medium">
                      {dailySyncMutation.isLoading ? 'Daily Sync' : 'Season Sync'} in progress...
                    </span>
                  </div>
                  <span className="text-sm text-blue-600">
                    {new Date().toLocaleTimeString()}
                  </span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '45%' }}></div>
                </div>
                <p className="text-xs text-blue-700 mt-2">
                  Please do not close this page while sync is running.
                </p>
              </div>
            )}

            <div className="mt-4 text-xs text-gray-500">
              <p>💡 <strong>Tips:</strong></p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li><strong>Daily Sync:</strong> Updates recent fixtures (last 7 days)</li>
                <li><strong>Season Sync:</strong> Full synchronization of current season data</li>
                <li><strong>Auto Refresh:</strong> Page updates every 5 seconds when enabled</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Sync History */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Sync Operations</CardTitle>
            <CardDescription>History of sync operations and their results</CardDescription>
          </CardHeader>
          <CardContent>
            {syncHistory.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Database className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No sync operations yet. Start your first sync above.</p>
              </div>
            ) : (
              <div className="space-y-3">
                {syncHistory.map((operation) => (
                  <div
                    key={operation.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(operation.status)}
                      <div>
                        <p className="font-medium capitalize">{operation.type} Sync</p>
                        <p className="text-sm text-gray-600">
                          {formatDate(operation.startTime)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {operation.fixturesProcessed} fixtures
                        </p>
                        {operation.duration && (
                          <p className="text-xs text-gray-600">{operation.duration}</p>
                        )}
                      </div>
                      {getStatusBadge(operation.status)}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AuthGuard>
  );
}
