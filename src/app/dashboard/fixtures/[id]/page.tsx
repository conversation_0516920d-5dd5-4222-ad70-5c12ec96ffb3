'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { fixturesApi } from '@/lib/api/fixtures';
import {
  ArrowLeft,
  Edit,
  Calendar,
  MapPin,
  Clock,
  Trophy,
  Users,
  Target,
  Activity,
  RefreshCw,
  Radio,
  Trash2,
  AlertTriangle
} from 'lucide-react';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { FixtureCard } from '@/components/fixtures/FixtureCard';
import { FixtureStats } from '@/components/fixtures/FixtureStats';
import { FixtureTimeline } from '@/components/fixtures/FixtureTimeline';
import { BroadcastLinksModal } from '@/components/fixtures/BroadcastLinksModal';
import { FixtureNavigation } from '@/components/fixtures/FixtureNavigation';
import { FixtureActions } from '@/components/fixtures/FixtureActions';
import { Modal } from '@/components/ui/modal';
import { toast } from 'sonner';

export default function FixtureDetailPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const fixtureId = parseInt(params.id as string);
  const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const { isEditor, isAdmin } = usePermissions();

  // Fetch fixture details
  const { data: fixture, isLoading, error, refetch } = useQuery({
    queryKey: ['fixture', fixtureId],
    queryFn: () => fixturesApi.getFixture(fixtureId),
    enabled: !!fixtureId,
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: () => fixturesApi.deleteFixture(fixtureId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fixtures'] });
      toast.success('Fixture deleted successfully');
      setDeleteModalOpen(false);
      router.push('/dashboard/fixtures');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete fixture');
      setDeleteModalOpen(false);
    },
  });

  // Handler functions
  const handleEdit = () => {
    router.push(`/dashboard/fixtures/${fixtureId}/edit`);
  };

  const handleBroadcastLinks = () => {
    setBroadcastLinksModalOpen(true);
  };

  const handleDelete = () => {
    setDeleteModalOpen(true);
  };

  const confirmDelete = () => {
    deleteMutation.mutate();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '1H':
      case '2H':
      case 'HT':
        return 'bg-green-100 text-green-800';
      case 'FT':
        return 'bg-gray-100 text-gray-800';
      case 'NS':
        return 'bg-blue-100 text-blue-800';
      case 'CANC':
      case 'PST':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusText = (status: string, elapsed?: number) => {
    switch (status) {
      case '1H':
      case '2H':
        return `${elapsed}'`;
      case 'HT':
        return 'Half Time';
      case 'FT':
        return 'Full Time';
      case 'NS':
        return 'Not Started';
      case 'CANC':
        return 'Cancelled';
      case 'PST':
        return 'Postponed';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-10" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-64" />
            <Skeleton className="h-48" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-32" />
            <Skeleton className="h-48" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !fixture) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">Failed to load fixture details</p>
              <Button onClick={() => router.push('/dashboard/fixtures')}>
                Return to Fixtures
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <FixtureNavigation
            variant="detail"
            fixtureId={fixtureId}
            onRefresh={refetch}
            isLoading={isLoading}
          />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {fixture.homeTeamName} vs {fixture.awayTeamName}
            </h1>
            <p className="text-gray-600 mt-1">{fixture.leagueName} • Fixture Details</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          {isEditor() && (
            <>
              <Button variant="outline" onClick={handleBroadcastLinks}>
                <Radio className="h-4 w-4 mr-2" />
                Broadcast Links
              </Button>
              <Button variant="outline" onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </>
          )}

          {isAdmin() && (
            <Button variant="outline" onClick={handleDelete}>
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Fixture Card */}
          <FixtureCard fixture={fixture} />

          {/* Match Timeline */}
          <FixtureTimeline fixture={fixture} />
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Match Statistics */}
          <FixtureStats fixture={fixture} />

          {/* Match Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                Match Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="font-medium">Date</div>
                  <div className="text-sm text-gray-600">
                    {new Date(fixture.date).toLocaleDateString()}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Clock className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="font-medium">Time</div>
                  <div className="text-sm text-gray-600">
                    {new Date(fixture.date).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Trophy className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="font-medium">League</div>
                  <div className="text-sm text-gray-600">{fixture.leagueName}</div>
                </div>
              </div>

              {fixture.venueName && (
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <div>
                    <div className="font-medium">Venue</div>
                    <div className="text-sm text-gray-600">{fixture.venueName}</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <FixtureActions
                fixture={fixture}
                onBroadcastLinks={handleBroadcastLinks}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Broadcast Links Modal */}
      <BroadcastLinksModal
        isOpen={broadcastLinksModalOpen}
        onClose={() => setBroadcastLinksModalOpen(false)}
        fixture={fixture}
      />

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Delete Fixture"
        description="Are you sure you want to delete this fixture? This action cannot be undone."
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200">
            <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-red-800">
                This will permanently delete the fixture:
              </p>
              <p className="text-sm text-red-700 mt-1">
                <strong>{fixture.homeTeamName} vs {fixture.awayTeamName}</strong>
              </p>
              <p className="text-xs text-red-600 mt-1">
                {new Date(fixture.date).toLocaleDateString()} • {fixture.leagueName}
              </p>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setDeleteModalOpen(false)}
              disabled={deleteMutation.isLoading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={deleteMutation.isLoading}
            >
              {deleteMutation.isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Fixture
                </>
              )}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
