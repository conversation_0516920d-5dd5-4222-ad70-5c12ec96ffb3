'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  InputField,
  SelectField,
  FormSection,
  FormActions
} from '@/components/ui/form-field';
import { SearchableSelectField } from '@/components/ui/SearchableSelectField';
import { Skeleton } from '@/components/ui/skeleton';
import { fixturesApi } from '@/lib/api/fixtures';
import { leaguesApi } from '@/lib/api/leagues';
import { teamsApi } from '@/lib/api/teams';
import { ArrowLeft, Save, Calendar } from 'lucide-react';
import { toast } from 'sonner';

interface FixtureFormData {
  homeTeamId: string;
  awayTeamId: string;
  leagueId: string;
  date: string;
  time: string;
  venueName: string;
  venueCity: string;
  round: string;
  status: string;
  goalsHome: string;
  goalsAway: string;
  elapsed: string;
  referee?: string;
  temperature?: string;
  weather?: string;
  attendance?: string;
}

export default function EditFixturePage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const fixtureId = parseInt(params.id as string);

  const [formData, setFormData] = useState<FixtureFormData>({
    homeTeamId: '',
    awayTeamId: '',
    leagueId: '',
    date: '',
    time: '',
    venueName: '',
    venueCity: '',
    round: '',
    status: '',
    goalsHome: '',
    goalsAway: '',
    elapsed: '',
  });

  const [errors, setErrors] = useState<Partial<FixtureFormData>>({});

  // Fetch fixture details
  const { data: fixture, isLoading: fixtureLoading } = useQuery({
    queryKey: ['fixture', fixtureId],
    queryFn: () => fixturesApi.getFixture(fixtureId),
    enabled: !!fixtureId,
  });

  // Search states
  const [leagueSearch, setLeagueSearch] = useState('');
  const [homeTeamSearch, setHomeTeamSearch] = useState('');
  const [homeTeamSearchResults, setHomeTeamSearchResults] = useState<any[]>([]);
  const [awayTeamSearch, setAwayTeamSearch] = useState('');
  const [awayTeamSearchResults, setAwayTeamSearchResults] = useState<any[]>([]);

  // Fetch leagues without search initially
  const { data: leagues, isLoading: leaguesLoading, error: leaguesError } = useQuery({
    queryKey: ['leagues'],
    queryFn: () => leaguesApi.getLeagues({ limit: 100 }),
  });

  // Fetch teams without search initially
  const { data: teams, isLoading: teamsLoading, error: teamsError } = useQuery({
    queryKey: ['teams'],
    queryFn: () => teamsApi.getTeams({ limit: 100 }),
  });

  // Home Team Search with 3s debounce
  useEffect(() => {
    if (!homeTeamSearch.trim()) {
      setHomeTeamSearchResults([]);
      return;
    }

    const timer = setTimeout(async () => {
      try {
        const searchResults = await teamsApi.getTeams({
          limit: 100,
          search: homeTeamSearch
        });

        if (searchResults?.data) {
          setHomeTeamSearchResults(searchResults.data);
        }
      } catch (error) {
        console.error('❌ Home team search error:', error);
        setHomeTeamSearchResults([]);
      }
    }, 3000); // 3 second debounce

    return () => clearTimeout(timer);
  }, [homeTeamSearch]);

  // Away Team Search with 3s debounce
  useEffect(() => {
    if (!awayTeamSearch.trim()) {
      setAwayTeamSearchResults([]);
      return;
    }

    const timer = setTimeout(async () => {
      try {
        const searchResults = await teamsApi.getTeams({
          limit: 100,
          search: awayTeamSearch
        });

        if (searchResults?.data) {
          setAwayTeamSearchResults(searchResults.data);
        }
      } catch (error) {
        console.error('❌ Away team search error:', error);
        setAwayTeamSearchResults([]);
      }
    }, 3000); // 3 second debounce

    return () => clearTimeout(timer);
  }, [awayTeamSearch]);

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: (data: any) => fixturesApi.updateFixture(fixtureId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fixture', fixtureId] });
      queryClient.invalidateQueries({ queryKey: ['fixtures'] });
      toast.success('Fixture updated successfully');
      router.push(`/dashboard/fixtures/${fixtureId}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update fixture');
    },
  });

  // Populate form when fixture data loads
  useEffect(() => {
    if (fixture) {
      const fixtureDate = new Date(fixture.date);




      // Debug status mapping
      console.log('STATUS DEBUG:', {
        fixtureStatus: fixture.status,
        statusType: typeof fixture.status,
        statusOptions: statusOptions.map(s => s.value),
        statusMatch: statusOptions.find(s => s.value === fixture.status)
      });

      setFormData({
        homeTeamId: fixture.homeTeamId?.toString() || '',
        awayTeamId: fixture.awayTeamId?.toString() || '',
        leagueId: fixture.leagueId?.toString() || '',
        date: fixtureDate.toISOString().split('T')[0],
        time: fixtureDate.toTimeString().slice(0, 5),
        // Fix venue mapping - API returns venue.name and venue.city
        venueName: fixture.venue?.name || fixture.venueName || '',
        venueCity: fixture.venue?.city || fixture.venueCity || '',
        round: fixture.round || '',
        status: fixture.status || '',
        goalsHome: fixture.goalsHome?.toString() || '',
        goalsAway: fixture.goalsAway?.toString() || '',
        elapsed: fixture.elapsed?.toString() || '',
        referee: fixture.referee || '',
        temperature: (fixture as any).temperature?.toString() || '',
        weather: (fixture as any).weather || '',
        attendance: (fixture as any).attendance?.toString() || '',
      });
    }
  }, [fixture]);

  // Move this useEffect after teamOptions and leagueOptions are defined

  const validateForm = (): boolean => {
    const newErrors: Partial<FixtureFormData> = {};

    if (!formData.homeTeamId) newErrors.homeTeamId = 'Home team is required';
    if (!formData.awayTeamId) newErrors.awayTeamId = 'Away team is required';
    if (!formData.leagueId) newErrors.leagueId = 'League is required';
    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.time) newErrors.time = 'Time is required';
    if (!formData.status) newErrors.status = 'Status is required';

    if (formData.homeTeamId === formData.awayTeamId) {
      newErrors.awayTeamId = 'Away team must be different from home team';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    // Combine date and time
    const dateTime = new Date(`${formData.date}T${formData.time}`);

    const submitData = {
      homeTeamId: parseInt(formData.homeTeamId),
      awayTeamId: parseInt(formData.awayTeamId),
      leagueId: parseInt(formData.leagueId),
      date: dateTime.toISOString(),
      venueName: formData.venueName || null,
      venueCity: formData.venueCity || null,
      round: formData.round || null,
      status: formData.status,
      goalsHome: formData.goalsHome ? parseInt(formData.goalsHome) : null,
      goalsAway: formData.goalsAway ? parseInt(formData.goalsAway) : null,
      elapsed: formData.elapsed ? parseInt(formData.elapsed) : null,
      referee: formData.referee || null,
      temperature: formData.temperature ? parseInt(formData.temperature) : null,
      weather: formData.weather || null,
      attendance: formData.attendance ? parseInt(formData.attendance) : null,
    };

    updateMutation.mutate(submitData);
  };

  const updateFormData = (field: keyof FixtureFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Search handlers with useCallback to prevent re-renders
  const handleLeagueSearch = useCallback((query: string) => {
    setLeagueSearch(query);
  }, []);

  const handleHomeTeamSearch = useCallback((query: string) => {
    setHomeTeamSearch(query);
  }, []);

  const handleAwayTeamSearch = useCallback((query: string) => {
    setAwayTeamSearch(query);
  }, []);

  // Status options - API Football Official Documentation + Additional API Values
  // Source: https://www.api-football.com/documentation-v3#tag/Fixtures/operation/get-fixtures
  const statusOptions = [
    // Time Status
    { value: 'TBD', label: 'Time To Be Defined' },
    { value: 'NS', label: 'Not Started' },
    { value: 'ST', label: 'Scheduled' }, // Additional API value

    // Match Status - Live
    { value: '1H', label: 'First Half, Kick Off' },
    { value: 'HT', label: 'Halftime' },
    { value: '2H', label: 'Second Half, 2nd Half Started' },
    { value: 'ET', label: 'Extra Time' },
    { value: 'BT', label: 'Break Time (in Extra Time)' },
    { value: 'P', label: 'Penalty In Progress' },
    { value: 'LIVE', label: 'In Progress' },

    // Match Finished
    { value: 'FT', label: 'Match Finished' },
    { value: 'AET', label: 'Match Finished After Extra Time' },
    { value: 'PEN', label: 'Match Finished After Penalty' },

    // Match Suspended
    { value: 'SUSP', label: 'Match Suspended' },
    { value: 'INT', label: 'Match Interrupted' },

    // Match Postponed
    { value: 'PST', label: 'Match Postponed' },

    // Match Cancelled
    { value: 'CANC', label: 'Match Cancelled' },

    // Match Abandoned
    { value: 'ABD', label: 'Match Abandoned' },

    // Technical Loss
    { value: 'AWD', label: 'Technical Loss' },

    // Walk Over
    { value: 'WO', label: 'WalkOver' },
  ];

  // Memoize options to prevent re-renders
  const leagueOptions = useMemo(() =>
    leagues?.data?.map((league, index) => ({
      value: league.externalId.toString(),
      label: `${league.name}${league.season ? ` (${league.season})` : ''}`,
      logo: league.logo,
      season: league.season,
      uniqueKey: `league-${league.id || league.externalId}-${index}`,
    })) || [], [leagues?.data]);

  const teamOptions = useMemo(() =>
    teams?.data?.map((team, index) => ({
      value: team.externalId.toString(),
      label: team.name,
      logo: team.logo,
      uniqueKey: `team-${team.id || team.externalId}-${index}`,
    })) || [], [teams?.data]);

  // Home team options: replace with search results when searching, otherwise use initial teams
  const homeTeamOptions = useMemo(() => {
    // If we have search results, use ONLY search results (replace mode)
    if (homeTeamSearchResults.length > 0) {
      return homeTeamSearchResults.map((team, index) => ({
        value: team.externalId.toString(),
        label: team.name,
        logo: team.logo,
        uniqueKey: `search-home-team-${team.id || team.externalId}-${index}`,
      }));
    }

    // If no search results, use initial teams (default mode)
    return teamOptions;
  }, [teamOptions, homeTeamSearchResults]);

  // Away team options: replace with search results when searching, otherwise use initial teams
  const awayTeamOptions = useMemo(() => {
    // If we have search results, use ONLY search results (replace mode)
    if (awayTeamSearchResults.length > 0) {
      return awayTeamSearchResults.map((team, index) => ({
        value: team.externalId.toString(),
        label: team.name,
        logo: team.logo,
        uniqueKey: `search-away-team-${team.id || team.externalId}-${index}`,
      }));
    }

    // If no search results, use initial teams (default mode)
    return teamOptions;
  }, [teamOptions, awayTeamSearchResults]);



  // Smart preview logic - always try to find from options first, fallback to fixture data
  const getPreviewData = () => {
    if (!fixture) return { league: null, homeTeam: null, awayTeam: null };

    // Try to find from dropdown options first
    const leagueFromOptions = leagueOptions.find(l => l.value === formData.leagueId);
    const homeTeamFromOptions = homeTeamOptions.find(t => t.value === formData.homeTeamId);
    const awayTeamFromOptions = awayTeamOptions.find(t => t.value === formData.awayTeamId);

    return {
      league: leagueFromOptions || {
        value: formData.leagueId,
        label: fixture.leagueName,
        logo: ''
      },
      homeTeam: homeTeamFromOptions || {
        value: formData.homeTeamId,
        label: fixture.homeTeamName,
        logo: fixture.homeTeamLogo
      },
      awayTeam: awayTeamFromOptions || {
        value: formData.awayTeamId,
        label: fixture.awayTeamName,
        logo: fixture.awayTeamLogo
      }
    };
  };

  const { league: selectedLeague, homeTeam: selectedHomeTeam, awayTeam: selectedAwayTeam } = getPreviewData();

  // Force re-render when options change to ensure preview updates
  const [previewKey, setPreviewKey] = useState(0);

  useEffect(() => {
    // Update preview when options or form data changes
    if (homeTeamOptions.length > 0 && awayTeamOptions.length > 0 && leagueOptions.length > 0 && formData.homeTeamId) {
      setPreviewKey(prev => prev + 1);
    }
  }, [homeTeamOptions.length, awayTeamOptions.length, leagueOptions.length, formData.homeTeamId, formData.awayTeamId, formData.leagueId]);

  // Preview component for selected values
  const SelectedValuePreview = ({
    label,
    selectedOption,
    placeholder = "Not selected"
  }: {
    label: string;
    selectedOption?: { value: any; label: string; logo?: string } | null;
    placeholder?: string;
  }) => {
    const CDN_URL = process.env.NEXT_PUBLIC_DOMAIN_CDN_PICTURE || 'http://116.203.125.65';

    return (
      <div className="mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
        <div className="text-sm font-medium text-gray-700 mb-2">{label}</div>
        {selectedOption ? (
          <div className="flex items-center space-x-3">
            {selectedOption.logo && (
              <img
                src={`${CDN_URL}/${selectedOption.logo}`}
                alt={selectedOption.label}
                className="w-8 h-8 object-contain rounded"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
            <span className="text-lg font-semibold text-gray-900">
              {selectedOption.label}
            </span>
          </div>
        ) : (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-gray-400 text-xs">?</span>
            </div>
            <span className="text-gray-500 italic">{placeholder}</span>
          </div>
        )}
      </div>
    );
  };

  // Inline League component (logo + name bên trái, dropdown bên phải)
  const LeagueInlineSelector = () => {
    const CDN_URL = process.env.NEXT_PUBLIC_DOMAIN_CDN_PICTURE || 'http://116.203.125.65';

    return (
      <div className="flex items-center space-x-4">
        {/* Left side: Logo + League name */}
        <div className="flex items-center space-x-3 min-w-0 flex-1">
          {selectedLeague ? (
            <>
              {selectedLeague.logo && (
                <img
                  src={`${CDN_URL}/${selectedLeague.logo}`}
                  alt={selectedLeague.label}
                  className="w-8 h-8 object-contain rounded flex-shrink-0"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              )}
              <span className="text-lg font-semibold text-gray-900 truncate">
                {selectedLeague.label}
              </span>
            </>
          ) : (
            <>
              <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center flex-shrink-0">
                <span className="text-gray-400 text-xs">?</span>
              </div>
              <span className="text-gray-500 italic">No league selected</span>
            </>
          )}
        </div>

        {/* Right side: Dropdown */}
        <div className="flex-shrink-0 w-64">
          <div className="text-sm font-medium text-gray-700 mb-2">League*</div>
          <SearchableSelectField
            key="league-search-stable"
            placeholder={leaguesLoading ? "Loading leagues..." : "Select league"}
            searchPlaceholder="Search leagues..."
            required
            value={formData.leagueId}
            onValueChange={(value) => updateFormData('leagueId', value)}
            options={leagueOptions}
            error={errors.leagueId}
            disabled={leaguesLoading}
            onSearch={handleLeagueSearch}
            isLoading={leaguesLoading}
          />
        </div>
      </div>
    );
  };

  // Show loading state if any required data is loading
  const isDataLoading = fixtureLoading || leaguesLoading || teamsLoading;

  if (isDataLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-20" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <Skeleton className="h-4 w-32" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Skeleton className="h-10" />
                <Skeleton className="h-10" />
              </div>
              <Skeleton className="h-10" />
            </div>
            <div className="space-y-4">
              <Skeleton className="h-4 w-24" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Skeleton className="h-10" />
                <Skeleton className="h-10" />
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-32" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state if any critical data failed to load
  if (!fixture || leaguesError || teamsError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              {!fixture && <p className="text-red-600 mb-4">Fixture not found</p>}
              {!!leaguesError && <p className="text-red-600 mb-4">Failed to load leagues</p>}
              {!!teamsError && <p className="text-red-600 mb-4">Failed to load teams</p>}
              <Button onClick={() => router.push('/dashboard/fixtures')}>
                Return to Fixtures
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Edit Fixture: {fixture.homeTeamName} vs {fixture.awayTeamName}
          </h1>
          <p className="text-gray-600 mt-1">Update fixture details and match information</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Fixture Details
          </CardTitle>
          <CardDescription>
            Update the fixture information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <FormSection title="Teams & Competition" description="Select the teams and league">
              {/* Teams Selection with Preview */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <SelectedValuePreview
                    key={`home-${previewKey}`}
                    label="Selected Home Team"
                    selectedOption={selectedHomeTeam}
                    placeholder="No home team selected"
                  />
                  <SearchableSelectField
                    key="home-team-search-stable"
                    label="Home Team"
                    placeholder={teamsLoading ? "Loading teams..." : "Select home team"}
                    searchPlaceholder="Search teams... (3s delay)"
                    required
                    value={formData.homeTeamId}
                    onValueChange={(value) => updateFormData('homeTeamId', value)}
                    options={homeTeamOptions}
                    error={errors.homeTeamId}
                    disabled={teamsLoading}
                    onSearch={handleHomeTeamSearch}
                    isLoading={teamsLoading}
                  />
                </div>

                <div>
                  <SelectedValuePreview
                    key={`away-${previewKey}`}
                    label="Selected Away Team"
                    selectedOption={selectedAwayTeam}
                    placeholder="No away team selected"
                  />
                  <SearchableSelectField
                    key="away-team-search-stable"
                    label="Away Team"
                    placeholder={teamsLoading ? "Loading teams..." : "Select away team"}
                    searchPlaceholder="Search teams... (3s delay)"
                    required
                    value={formData.awayTeamId}
                    onValueChange={(value) => updateFormData('awayTeamId', value)}
                    options={awayTeamOptions.filter(team => team.value !== formData.homeTeamId)}
                    error={errors.awayTeamId}
                    disabled={teamsLoading}
                    onSearch={handleAwayTeamSearch}
                    isLoading={teamsLoading}
                  />
                </div>
              </div>

              {/* League Selection - Inline Layout */}
              <div>
                <LeagueInlineSelector />
              </div>
            </FormSection>

            <FormSection title="Schedule" description="Set the date and time (local timezone)">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Date *"
                  type="date"
                  required
                  value={formData.date}
                  onChange={(e) => updateFormData('date', e.target.value)}
                  error={errors.date}
                  description="Match date"
                />

                <InputField
                  label="Time *"
                  type="time"
                  required
                  value={formData.time}
                  onChange={(e) => updateFormData('time', e.target.value)}
                  error={errors.time}
                  description={`Local time (${Intl.DateTimeFormat().resolvedOptions().timeZone})`}
                />
              </div>

              <div className="text-sm text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200">
                <p className="flex items-center">
                  <span className="text-blue-600 mr-2">ℹ️</span>
                  <strong>Timezone Info:</strong> Times are displayed in your local timezone ({Intl.DateTimeFormat().resolvedOptions().timeZone}).
                  The asterisk (*) indicates required fields.
                </p>
              </div>
            </FormSection>

            <FormSection title="Match Status" description="Update match status and score">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <SelectField
                  label="Status"
                  placeholder="Select status"
                  required
                  value={formData.status}
                  onValueChange={(value) => updateFormData('status', value)}
                  options={statusOptions}
                  error={errors.status}
                />

                <InputField
                  label="Home Goals"
                  type="number"
                  min="0"
                  value={formData.goalsHome}
                  onChange={(e) => updateFormData('goalsHome', e.target.value)}
                />

                <InputField
                  label="Away Goals"
                  type="number"
                  min="0"
                  value={formData.goalsAway}
                  onChange={(e) => updateFormData('goalsAway', e.target.value)}
                />
              </div>

              <InputField
                label="Elapsed Time (minutes)"
                type="number"
                min="0"
                max="120"
                value={formData.elapsed}
                onChange={(e) => updateFormData('elapsed', e.target.value)}
                description="Minutes played in the match"
              />
            </FormSection>

            <FormSection title="Venue & Match Information" description="Venue details and match context">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Venue Name"
                  placeholder="Stadium name"
                  value={formData.venueName}
                  onChange={(e) => updateFormData('venueName', e.target.value)}
                />

                <InputField
                  label="Venue City"
                  placeholder="City"
                  value={formData.venueCity}
                  onChange={(e) => updateFormData('venueCity', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Round"
                  placeholder="e.g., Matchday 1, Quarter-final"
                  value={formData.round}
                  onChange={(e) => updateFormData('round', e.target.value)}
                />

                <InputField
                  label="Referee"
                  placeholder="Referee name"
                  value={formData.referee || ''}
                  onChange={(e) => updateFormData('referee', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <InputField
                  label="Temperature (°C)"
                  type="number"
                  placeholder="e.g., 22"
                  value={formData.temperature || ''}
                  onChange={(e) => updateFormData('temperature', e.target.value)}
                />

                <InputField
                  label="Weather"
                  placeholder="e.g., Sunny, Rainy"
                  value={formData.weather || ''}
                  onChange={(e) => updateFormData('weather', e.target.value)}
                />

                <InputField
                  label="Attendance"
                  type="number"
                  placeholder="Number of spectators"
                  value={formData.attendance || ''}
                  onChange={(e) => updateFormData('attendance', e.target.value)}
                />
              </div>
            </FormSection>

            <FormActions>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={updateMutation.isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={updateMutation.isLoading}
              >
                <Save className="mr-2 h-4 w-4" />
                {updateMutation.isLoading ? 'Updating...' : 'Update Fixture'}
              </Button>
            </FormActions>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
