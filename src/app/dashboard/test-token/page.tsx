'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthStore } from '@/lib/stores/auth';
import { apiClient } from '@/lib/api/client';
import { authApi } from '@/lib/api/auth';

export default function TestTokenPage() {
  const [logs, setLogs] = useState<string[]>([]);
  const { user, accessToken, refreshToken, isAuthenticated } = useAuthStore();

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const clearLogs = () => setLogs([]);

  const testApiCall = async () => {
    try {
      addLog('🔄 Making API call to /football/fixtures...');
      const response = await apiClient.get('/football/fixtures?limit=1');
      addLog('✅ API call successful');
      addLog(`📊 Response: ${JSON.stringify(response).substring(0, 100)}...`);
    } catch (error: any) {
      addLog(`❌ API call failed: ${error.message}`);
    }
  };

  const testProtectedCall = async () => {
    try {
      addLog('🔄 Making protected API call to profile...');
      const profile = await authApi.getProfile();
      addLog('✅ Protected call successful');
      addLog(`👤 Profile: ${profile.username} (${profile.role})`);
    } catch (error: any) {
      addLog(`❌ Protected call failed: ${error.message}`);
    }
  };

  const testManualRefresh = async () => {
    if (!refreshToken) {
      addLog('❌ No refresh token available');
      return;
    }

    try {
      addLog('🔄 Manual token refresh...');
      const result = await authApi.refreshToken(refreshToken);
      addLog('✅ Manual refresh successful');
      addLog(`🔑 New token: ${result.accessToken.substring(0, 20)}...`);
    } catch (error: any) {
      addLog(`❌ Manual refresh failed: ${error.message}`);
    }
  };

  const simulateExpiredToken = () => {
    addLog('⚠️ Simulating expired token (setting invalid token)...');
    const authStore = useAuthStore.getState();
    if (user && refreshToken) {
      authStore.setAuth(user, 'expired-token-simulation', refreshToken);
      addLog('🔧 Token set to invalid value - next API call should trigger refresh');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Token Refresh Test</h1>
        <p className="text-gray-600 mt-1">Test automatic token refresh functionality</p>
      </div>

      {/* Auth Status */}
      <Card>
        <CardHeader>
          <CardTitle>Authentication Status</CardTitle>
          <CardDescription>Current authentication state</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Authenticated:</span>
              <span className={`ml-2 ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                {isAuthenticated ? '✅ Yes' : '❌ No'}
              </span>
            </div>
            <div>
              <span className="font-medium">User:</span>
              <span className="ml-2">{user?.username || 'None'}</span>
            </div>
            <div>
              <span className="font-medium">Access Token:</span>
              <span className="ml-2 font-mono text-xs">
                {accessToken ? `${accessToken.substring(0, 20)}...` : 'None'}
              </span>
            </div>
            <div>
              <span className="font-medium">Refresh Token:</span>
              <span className="ml-2 font-mono text-xs">
                {refreshToken ? `${refreshToken.substring(0, 20)}...` : 'None'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Actions</CardTitle>
          <CardDescription>Test different scenarios</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button onClick={testApiCall} variant="outline">
              Test Public API
            </Button>
            <Button onClick={testProtectedCall} variant="outline">
              Test Protected API
            </Button>
            <Button onClick={testManualRefresh} variant="outline">
              Manual Refresh
            </Button>
            <Button onClick={simulateExpiredToken} variant="destructive">
              Simulate Expired Token
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Logs */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Test Logs</CardTitle>
            <CardDescription>Real-time test results</CardDescription>
          </div>
          <Button onClick={clearLogs} variant="outline" size="sm">
            Clear Logs
          </Button>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-500 italic">No logs yet. Run a test to see results.</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div>
            <strong>1. Test Public API:</strong> Should work without authentication
          </div>
          <div>
            <strong>2. Test Protected API:</strong> Should work with valid token
          </div>
          <div>
            <strong>3. Manual Refresh:</strong> Manually refresh the access token
          </div>
          <div>
            <strong>4. Simulate Expired Token:</strong> Set invalid token, then test protected API to trigger automatic refresh
          </div>
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <strong>Expected Behavior:</strong> When you simulate expired token and then test protected API, 
            you should see automatic token refresh in the logs, followed by successful API call.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
