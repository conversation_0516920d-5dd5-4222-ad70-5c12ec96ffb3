'use client';

import { useParams, useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { useLeague } from '@/lib/hooks/useLeagues';
import { 
  ArrowLeft, 
  Edit, 
  Trophy, 
  Globe, 
  Calendar,
  Users,
  Clock,
  MapPin,
  Zap
} from 'lucide-react';

export default function LeagueDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { isEditor } = usePermissions();
  const leagueId = parseInt(params.id as string);

  // Fetch league details
  const { league, isLoading, error } = useLeague(leagueId);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-8 w-48" />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-96" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-64" />
            <Skeleton className="h-48" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !league) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>
        
        <Card>
          <CardContent className="flex items-center justify-center h-96">
            <div className="text-center">
              <Trophy className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                League not found
              </h3>
              <p className="text-gray-500">
                The league you're looking for doesn't exist or you don't have permission to view it.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          
          <div className="flex items-center space-x-3">
            {league.logo ? (
              <img 
                src={league.logo} 
                alt={league.name}
                className="w-12 h-12 object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />
            ) : (
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <Trophy className="w-6 h-6 text-gray-400" />
              </div>
            )}
            
            <div>
              <h1 className="text-2xl font-bold tracking-tight">{league.name}</h1>
              <div className="flex items-center space-x-2 text-gray-600">
                <Globe className="w-4 h-4" />
                <span>{league.country}</span>
                {league.type && (
                  <>
                    <span>•</span>
                    <span className="capitalize">{league.type}</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {isEditor() && (
          <Button onClick={() => router.push(`/dashboard/leagues/${leagueId}/edit`)}>
            <Edit className="w-4 h-4 mr-2" />
            Edit League
          </Button>
        )}
      </div>

      {/* Status Badges */}
      <div className="flex items-center space-x-2">
        <Badge variant={league.active ? "default" : "secondary"}>
          {league.active ? 'Active' : 'Inactive'}
        </Badge>
        {league.isHot && (
          <Badge variant="destructive">
            <Zap className="w-3 h-3 mr-1" />
            Hot League
          </Badge>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Trophy className="w-5 h-5" />
                <span>League Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">League Name</label>
                  <p className="text-lg font-medium">{league.name}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Country</label>
                  <div className="flex items-center space-x-2">
                    {league.countryFlag && (
                      <img 
                        src={league.countryFlag} 
                        alt={league.country}
                        className="w-5 h-5"
                      />
                    )}
                    <span className="text-lg font-medium">{league.country}</span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">League Type</label>
                  <p className="text-lg font-medium capitalize">
                    {league.type || 'N/A'}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">External ID</label>
                  <p className="text-lg font-medium">{league.externalId}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Season Information */}
          {league.season_detail && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5" />
                  <span>Season Details</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Season Year</label>
                    <p className="text-lg font-medium">{league.season_detail.year}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-500">Start Date</label>
                    <p className="text-lg font-medium">{league.season_detail.start}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-500">End Date</label>
                    <p className="text-lg font-medium">{league.season_detail.end}</p>
                  </div>
                </div>

                {league.season_detail.current && (
                  <div className="mt-4">
                    <Badge variant="secondary">
                      <Clock className="w-3 h-3 mr-1" />
                      Current Season
                    </Badge>
                  </div>
                )}

                {/* Coverage Information */}
                {league.season_detail.coverage && (
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-500 mb-3">Coverage Details</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${league.season_detail.coverage.fixtures?.events ? 'bg-green-500' : 'bg-red-500'}`}></div>
                        <span>Fixtures</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${league.season_detail.coverage.standings ? 'bg-green-500' : 'bg-red-500'}`}></div>
                        <span>Standings</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${league.season_detail.coverage.players ? 'bg-green-500' : 'bg-red-500'}`}></div>
                        <span>Players</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${league.season_detail.coverage.top_scorers ? 'bg-green-500' : 'bg-red-500'}`}></div>
                        <span>Top Scorers</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Status</span>
                <Badge variant={league.active ? "default" : "secondary"}>
                  {league.active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              
              {league.isHot && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Popularity</span>
                  <Badge variant="destructive">Hot</Badge>
                </div>
              )}

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">League ID</span>
                <span className="text-sm font-medium">#{league.externalId}</span>
              </div>

              {league.season_detail && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Current Season</span>
                  <span className="text-sm font-medium">{league.season_detail.year}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* League Logo */}
          {league.logo && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">League Logo</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <img 
                    src={league.logo} 
                    alt={league.name}
                    className="w-32 h-32 object-contain"
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
