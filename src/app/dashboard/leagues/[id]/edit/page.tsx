'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
      InputField,
      SelectField,
      FormSection,
      FormActions
} from '@/components/ui/form-field';
import { useLeague, useLeagueMutations } from '@/lib/hooks/useLeagues';
import { buildLeagueLogoUrl } from '@/lib/utils/image';
import { ArrowLeft, Save, Trophy } from 'lucide-react';
import { toast } from 'sonner';

interface LeagueFormData {
      name: string;
      country: string;
      type: string;
      active: boolean;
      isHot: boolean;
      logo?: string;
}

export default function EditLeaguePage() {
      const params = useParams();
      const router = useRouter();
      const queryClient = useQueryClient();
      const leagueId = parseInt(params.id as string);

      const [formData, setFormData] = useState<LeagueFormData>({
            name: '',
            country: '',
            type: '',
            active: true,
            isHot: false,
            logo: '',
      });

      const [errors, setErrors] = useState<Partial<LeagueFormData>>({});

      // Fetch league details
      const { league, isLoading: leagueLoading } = useLeague(leagueId);

      // Update mutations
      const { updateLeague, isUpdateLoading } = useLeagueMutations();

      // Populate form when league loads
      useEffect(() => {
            if (league) {
                  setFormData({
                        name: league.name || '',
                        country: league.country || '',
                        type: league.type || '',
                        active: league.active ?? true,
                        isHot: league.isHot ?? false,
                        logo: league.logo || '',
                  });
            }
      }, [league]);

      const updateFormData = (field: keyof LeagueFormData, value: any) => {
            setFormData(prev => ({ ...prev, [field]: value }));
            // Clear error when user starts typing
            if (errors[field]) {
                  setErrors(prev => ({ ...prev, [field]: undefined }));
            }
      };

      const validateForm = (): boolean => {
            const newErrors: Partial<LeagueFormData> = {};

            if (!formData.name.trim()) newErrors.name = 'League name is required';
            if (!formData.country.trim()) newErrors.country = 'Country is required';
            if (!formData.type.trim()) newErrors.type = 'League type is required';

            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
      };

      const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();

            if (!validateForm()) {
                  toast.error('Please fix the form errors');
                  return;
            }

            updateLeague({
                  id: leagueId,
                  data: {
                        name: formData.name.trim(),
                        country: formData.country.trim(),
                        type: formData.type.trim(),
                        active: formData.active,
                        isHot: formData.isHot,
                        ...(formData.logo && { logo: formData.logo.trim() }),
                  }
            }, {
                  onSuccess: () => {
                        queryClient.invalidateQueries({ queryKey: ['leagues', leagueId] });
                        queryClient.invalidateQueries({ queryKey: ['leagues'] });
                        toast.success('League updated successfully');
                        router.push(`/dashboard/leagues/${leagueId}`);
                  },
                  onError: (error: any) => {
                        toast.error(error.message || 'Failed to update league');
                  },
            });
      };

      // Loading state
      if (leagueLoading) {
            return (
                  <div className="space-y-6">
                        <div className="flex items-center space-x-4">
                              <Skeleton className="h-10 w-20" />
                              <Skeleton className="h-8 w-48" />
                        </div>

                        <Card>
                              <CardHeader>
                                    <Skeleton className="h-6 w-32" />
                                    <Skeleton className="h-4 w-64" />
                              </CardHeader>
                              <CardContent className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                          <Skeleton className="h-20" />
                                          <Skeleton className="h-20" />
                                          <Skeleton className="h-20" />
                                          <Skeleton className="h-20" />
                                    </div>
                              </CardContent>
                        </Card>
                  </div>
            );
      }

      if (!league) {
            return (
                  <div className="space-y-6">
                        <div className="flex items-center space-x-4">
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.back()}
                              >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>
                        </div>

                        <Card>
                              <CardContent className="flex items-center justify-center h-96">
                                    <div className="text-center">
                                          <Trophy className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                League not found
                                          </h3>
                                          <p className="text-gray-500">
                                                The league you're trying to edit doesn't exist or you don't have permission to edit it.
                                          </p>
                                    </div>
                              </CardContent>
                        </Card>
                  </div>
            );
      }

      return (
            <div className="space-y-6">
                  {/* Header */}
                  <div className="flex items-center space-x-4">
                        <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.back()}
                        >
                              <ArrowLeft className="w-4 h-4 mr-2" />
                              Back
                        </Button>

                        <div>
                              <h1 className="text-2xl font-bold tracking-tight">Edit League</h1>
                              <p className="text-gray-600">
                                    Update league information and settings
                              </p>
                        </div>
                  </div>

                  {/* Form */}
                  <form onSubmit={handleSubmit}>
                        <Card>
                              <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                          <Trophy className="w-5 h-5" />
                                          <span>League Information</span>
                                    </CardTitle>
                                    <CardDescription>
                                          Edit the league details and configuration settings.
                                    </CardDescription>
                              </CardHeader>
                              <CardContent className="space-y-6">
                                    <FormSection title="Basic Information">
                                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <InputField
                                                      label="League Name"
                                                      placeholder="Enter league name"
                                                      required
                                                      value={formData.name}
                                                      onChange={(e) => updateFormData('name', e.target.value)}
                                                      error={errors.name}
                                                />

                                                <InputField
                                                      label="Country"
                                                      placeholder="Enter country"
                                                      required
                                                      value={formData.country}
                                                      onChange={(e) => updateFormData('country', e.target.value)}
                                                      error={errors.country}
                                                />

                                                <SelectField
                                                      label="League Type"
                                                      placeholder="Select league type"
                                                      required
                                                      value={formData.type}
                                                      onValueChange={(value) => updateFormData('type', value)}
                                                      options={[
                                                            { value: 'league', label: 'League' },
                                                            { value: 'cup', label: 'Cup' },
                                                            { value: 'playoffs', label: 'Playoffs' },
                                                            { value: 'friendly', label: 'Friendly' },
                                                            { value: 'qualification', label: 'Qualification' },
                                                      ]}
                                                      error={errors.type}
                                                />

                                                <InputField
                                                      label="Logo URL"
                                                      placeholder="Enter logo URL (optional)"
                                                      value={formData.logo}
                                                      onChange={(e) => updateFormData('logo', e.target.value)}
                                                />
                                          </div>
                                    </FormSection>

                                    <FormSection title="Settings">
                                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <SelectField
                                                      label="Status"
                                                      value={formData.active.toString()}
                                                      onValueChange={(value) => updateFormData('active', value === 'true')}
                                                      options={[
                                                            { value: 'true', label: 'Active' },
                                                            { value: 'false', label: 'Inactive' },
                                                      ]}
                                                />

                                                <SelectField
                                                      label="Hot League"
                                                      description="Mark as hot/popular league"
                                                      value={formData.isHot.toString()}
                                                      onValueChange={(value) => updateFormData('isHot', value === 'true')}
                                                      options={[
                                                            { value: 'false', label: 'Normal' },
                                                            { value: 'true', label: 'Hot League' },
                                                      ]}
                                                />
                                          </div>
                                    </FormSection>

                                    {/* Logo Preview */}
                                    {formData.logo && (
                                          <FormSection title="Logo Preview">
                                                <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                                                      <img
                                                            src={buildLeagueLogoUrl(formData.logo) || formData.logo}
                                                            alt="Logo preview"
                                                            className="w-16 h-16 object-contain"
                                                            onError={(e) => {
                                                                  const target = e.target as HTMLImageElement;
                                                                  target.src = '';
                                                                  target.style.display = 'none';
                                                            }}
                                                      />
                                                      <div>
                                                            <p className="text-sm font-medium">Logo Preview</p>
                                                            <p className="text-xs text-gray-500">
                                                                  {formData.logo}
                                                            </p>
                                                      </div>
                                                </div>
                                          </FormSection>
                                    )}

                                    <FormActions>
                                          <Button
                                                type="button"
                                                variant="outline"
                                                onClick={() => router.back()}
                                          >
                                                Cancel
                                          </Button>
                                          <Button
                                                type="submit"
                                                disabled={isUpdateLoading}
                                          >
                                                <Save className="w-4 h-4 mr-2" />
                                                {isUpdateLoading ? 'Updating...' : 'Update League'}
                                          </Button>
                                    </FormActions>
                              </CardContent>
                        </Card>
                  </form>
            </div>
      );
}
