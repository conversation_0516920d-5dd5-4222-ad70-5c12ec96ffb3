'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  InputField,
  SelectField,
  FormSection,
  FormActions
} from '@/components/ui/form-field';
import { useLeagueMutations } from '@/lib/hooks/useLeagues';
import { ArrowLeft, Save, Trophy } from 'lucide-react';
import { toast } from 'sonner';

interface LeagueFormData {
  name: string;
  country: string;
  type: string;
  active: boolean;
  isHot: boolean;
  logo?: string;
  externalId?: number;
}

interface FormErrors {
  name?: string;
  country?: string;
  type?: string;
  logo?: string;
  externalId?: string;
}

export default function CreateLeaguePage() {
  const router = useRouter();

  const [formData, setFormData] = useState<LeagueFormData>({
    name: '',
    country: '',
    type: '',
    active: true,
    isHot: false,
    logo: '',
    externalId: undefined,
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Create mutation
  const { createLeague, isCreateLoading } = useLeagueMutations();

  const updateFormData = (field: keyof LeagueFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing (only for string fields in errors)
    if (field === 'name' || field === 'country' || field === 'type' || field === 'logo' || field === 'externalId') {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) newErrors.name = 'League name is required';
    if (!formData.country.trim()) newErrors.country = 'Country is required';
    if (!formData.type.trim()) newErrors.type = 'League type is required';
    
    if (formData.externalId && (formData.externalId < 1 || !Number.isInteger(formData.externalId))) {
      newErrors.externalId = 'External ID must be a positive integer';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    createLeague({
      name: formData.name.trim(),
      country: formData.country.trim(),
      type: formData.type.trim(),
      active: formData.active,
      isHot: formData.isHot,
      ...(formData.logo && { logo: formData.logo.trim() }),
      ...(formData.externalId && { externalId: formData.externalId }),
    }, {
      onSuccess: (newLeague) => {
        toast.success('League created successfully');
        router.push(`/dashboard/leagues/${newLeague.externalId}`);
      },
      onError: (error: any) => {
        toast.error(error.message || 'Failed to create league');
      },
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Create League</h1>
          <p className="text-gray-600">
            Add a new football league to the system
          </p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Trophy className="w-5 h-5" />
              <span>League Information</span>
            </CardTitle>
            <CardDescription>
              Enter the league details and configuration settings.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormSection title="Basic Information">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <InputField
                  label="League Name"
                  placeholder="e.g., Premier League, Champions League"
                  required
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  error={errors.name}
                />

                <InputField
                  label="Country"
                  placeholder="e.g., England, Spain, International"
                  required
                  value={formData.country}
                  onChange={(e) => updateFormData('country', e.target.value)}
                  error={errors.country}
                />

                <SelectField
                  label="League Type"
                  placeholder="Select league type"
                  required
                  value={formData.type}
                  onValueChange={(value) => updateFormData('type', value)}
                  options={[
                    { value: 'league', label: 'League' },
                    { value: 'cup', label: 'Cup' },
                    { value: 'playoffs', label: 'Playoffs' },
                    { value: 'friendly', label: 'Friendly' },
                    { value: 'qualification', label: 'Qualification' },
                  ]}
                  error={errors.type}
                />

                <InputField
                  label="External ID (Optional)"
                  placeholder="Enter external API ID"
                  type="number"
                  value={formData.externalId || ''}
                  onChange={(e) => updateFormData('externalId', 
                    e.target.value ? parseInt(e.target.value) : undefined
                  )}
                  error={errors.externalId}
                />

                <InputField
                  label="Logo URL (Optional)"
                  placeholder="https://example.com/logo.png"
                  value={formData.logo}
                  onChange={(e) => updateFormData('logo', e.target.value)}
                />
              </div>
            </FormSection>

            <FormSection title="Settings">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <SelectField
                  label="Initial Status"
                  value={formData.active.toString()}
                  onValueChange={(value) => updateFormData('active', value === 'true')}
                  options={[
                    { value: 'true', label: 'Active' },
                    { value: 'false', label: 'Inactive' },
                  ]}
                />

                <SelectField
                  label="Hot League"
                  description="Mark as hot/popular league for prominence"
                  value={formData.isHot.toString()}
                  onValueChange={(value) => updateFormData('isHot', value === 'true')}
                  options={[
                    { value: 'false', label: 'Normal League' },
                    { value: 'true', label: 'Hot League' },
                  ]}
                />
              </div>
            </FormSection>

            {/* Logo Preview */}
            {formData.logo && (
              <FormSection title="Logo Preview">
                <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                  <img 
                    src={formData.logo} 
                    alt="Logo preview"
                    className="w-16 h-16 object-contain"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '';
                      target.style.display = 'none';
                      toast.error('Failed to load logo image');
                    }}
                  />
                  <div>
                    <p className="text-sm font-medium">Logo Preview</p>
                    <p className="text-xs text-gray-500 break-all">
                      {formData.logo}
                    </p>
                  </div>
                </div>
              </FormSection>
            )}

            <FormActions>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreateLoading}
              >
                <Save className="w-4 h-4 mr-2" />
                {isCreateLoading ? 'Creating...' : 'Create League'}
              </Button>
            </FormActions>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
