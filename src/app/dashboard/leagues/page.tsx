'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable, Column } from '@/components/ui/data-table';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { useLeagues } from '@/lib/hooks/useLeagues';
import { LeagueFilters } from '@/lib/api/leagues';
import { League } from '@/lib/types/api';
import { 
  Trophy, 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2,
  Globe,
  CalendarDays
} from 'lucide-react';
import Link from 'next/link';

export default function LeaguesPage() {
  const router = useRouter();
  const { isEditor, isAdmin } = usePermissions();
  
  // State for filtering
  const [filters, setFilters] = useState<LeagueFilters>({
    page: 1,
    limit: 20,
  });
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch leagues data
  const { 
    leagues, 
    leaguesMeta, 
    isLoading, 
    error 
  } = useLeagues(filters);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setFilters(prev => ({
      ...prev,
      search: query || undefined,
      page: 1, // Reset to first page
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Handle filters
  const handleCountryFilter = (country: string) => {
    setFilters(prev => ({
      ...prev,
      country: country || undefined,
      page: 1,
    }));
  };

  const handleActiveFilter = (active: boolean | undefined) => {
    setFilters(prev => ({
      ...prev,
      active,
      page: 1,
    }));
  };

  // Table columns
  const columns: Column<League>[] = [
    {
      key: 'logo',
      title: '',
      render: (value: any, row: League) => (
        <div className="w-8 h-8 flex items-center justify-center">
          {row.logo ? (
            <img 
              src={row.logo} 
              alt={row.name}
              className="w-6 h-6 object-contain"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
          ) : (
            <Trophy className="w-5 h-5 text-gray-400" />
          )}
        </div>
      ),
    },
    {
      key: 'name',
      title: 'League Name',
      render: (value: any, row: League) => (
        <div className="space-y-1">
          <div className="font-medium">{row.name}</div>
          {row.type && (
            <div className="text-sm text-gray-500 capitalize">
              {row.type}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'country',
      title: 'Country',
      render: (value: any, row: League) => (
        <div className="flex items-center space-x-2">
          {row.countryFlag && (
            <img 
              src={row.countryFlag} 
              alt={row.country}
              className="w-4 h-4"
            />
          )}
          <span>{row.country}</span>
        </div>
      ),
    },
    {
      key: 'season_detail',
      title: 'Season',
      render: (value: any, row: League) => {
        const seasonDetail = row.season_detail;
        if (!seasonDetail) return <span className="text-gray-400">-</span>;
        
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium">{seasonDetail.year}</div>
            <div className="text-xs text-gray-500">
              {seasonDetail.start} - {seasonDetail.end}
            </div>
            {seasonDetail.current && (
              <Badge variant="secondary" className="text-xs">
                Current
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      key: 'isHot',
      title: 'Status',
      render: (value: any, row: League) => (
        <div className="space-y-1">
          {row.isHot && (
            <Badge variant="destructive" className="text-xs">
              Hot
            </Badge>
          )}
          <Badge 
            variant={row.active ? "default" : "secondary"}
            className="text-xs"
          >
            {row.active ? 'Active' : 'Inactive'}
          </Badge>
        </div>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (value: any, row: League) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/leagues/${row.externalId}`)}
          >
            <Eye className="w-4 h-4" />
          </Button>
          
          {isEditor() && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/dashboard/leagues/${row.externalId}/edit`)}
            >
              <Edit className="w-4 h-4" />
            </Button>
          )}
          
          {isAdmin() && (
            <Button
              variant="ghost"
              size="sm"
              className="text-red-600 hover:text-red-700"
              onClick={() => {
                // TODO: Implement delete functionality
                console.log('Delete league:', row.externalId);
              }}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight">Leagues Management</h1>
          <p className="text-gray-600">
            Manage football leagues, seasons, and configurations
          </p>
        </div>
        
        {isEditor() && (
          <Button onClick={() => router.push('/dashboard/leagues/create')}>
            <Plus className="w-4 h-4 mr-2" />
            Add League
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Leagues</CardTitle>
            <Trophy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {leaguesMeta?.totalItems?.toLocaleString() || 'Loading...'}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all countries
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Leagues</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {leagues?.filter(l => l.active).length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently running seasons
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Countries</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(leagues?.map(l => l.country)).size || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Unique countries represented
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hot Leagues</CardTitle>
            <Trophy className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {leagues?.filter(l => l.isHot).length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Popular leagues
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="w-5 h-5" />
            <span>Filters & Search</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search leagues..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Country Filter */}
            <select
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              onChange={(e) => handleCountryFilter(e.target.value)}
              value={filters.country || ''}
            >
              <option value="">All Countries</option>
              {Array.from(new Set(leagues?.map(l => l.country)))
                .sort()
                .map(country => (
                  <option key={country} value={country}>
                    {country}
                  </option>
                ))}
            </select>

            {/* Active Filter */}
            <select
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              onChange={(e) => handleActiveFilter(
                e.target.value === '' ? undefined : e.target.value === 'true'
              )}
              value={filters.active === undefined ? '' : filters.active.toString()}
            >
              <option value="">All Status</option>
              <option value="true">Active Only</option>
              <option value="false">Inactive Only</option>
            </select>

            {/* Clear Filters */}
            <Button
              variant="outline"
              onClick={() => {
                setSearchQuery('');
                setFilters({ page: 1, limit: 20 });
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Leagues Table */}
      <Card>
        <CardHeader>
          <CardTitle>Leagues List</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={leagues || []}
            loading={isLoading}
            pagination={{
              page: leaguesMeta?.currentPage || 1,
              limit: leaguesMeta?.limit || 20,
              total: leaguesMeta?.totalItems || 0,
              onPageChange: handlePageChange,
              onLimitChange: (limit: number) => {
                setFilters(prev => ({ ...prev, limit, page: 1 }));
              },
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
}
