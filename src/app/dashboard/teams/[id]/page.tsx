'use client';

import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Modal } from '@/components/ui/modal';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { useTeam } from '@/lib/hooks/useTeams';
import { teamsApi } from '@/lib/api/teams';
import { buildTeamLogoUrl, buildCountryFlagUrl } from '@/lib/utils/image';
import { toast } from 'sonner';
import {
      ArrowLeft,
      Edit,
      Users,
      Globe,
      Calendar,
      MapPin,
      Trash2,
      AlertTriangle,
      BarChart3,
      Trophy,
      Building
} from 'lucide-react';

export default function TeamDetailPage() {
      const params = useParams();
      const router = useRouter();
      const { isEditor, isAdmin } = usePermissions();
      const [deleteModalOpen, setDeleteModalOpen] = useState(false);
      const queryClient = useQueryClient();
      const teamId = parseInt(params.id as string);

      // Fetch team details
      const { team, isLoading, error } = useTeam(teamId);

      // Delete mutation
      const deleteMutation = useMutation({
            mutationFn: () => teamsApi.deleteTeam(teamId),
            onSuccess: () => {
                  queryClient.invalidateQueries({ queryKey: ['teams'] });
                  toast.success('Team deleted successfully');
                  setDeleteModalOpen(false);
                  router.push('/dashboard/teams');
            },
            onError: (error: any) => {
                  toast.error(error.message || 'Failed to delete team');
                  setDeleteModalOpen(false);
            },
      });

      // Handlers
      const handleEdit = () => {
            router.push(`/dashboard/teams/${teamId}/edit`);
      };

      const handleDelete = () => {
            setDeleteModalOpen(true);
      };

      const handleViewStatistics = () => {
            router.push(`/dashboard/teams/${teamId}/statistics`);
      };

      const confirmDelete = () => {
            deleteMutation.mutate();
      };

      if (isLoading) {
            return (
                  <div className="space-y-6">
                        <div className="flex items-center space-x-4">
                              <Skeleton className="h-10 w-20" />
                              <Skeleton className="h-8 w-48" />
                        </div>

                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                              <div className="lg:col-span-2 space-y-6">
                                    <Skeleton className="h-96" />
                              </div>
                              <div className="space-y-6">
                                    <Skeleton className="h-64" />
                                    <Skeleton className="h-48" />
                              </div>
                        </div>
                  </div>
            );
      }

      if (error || !team) {
            return (
                  <div className="space-y-6">
                        <div className="flex items-center space-x-4">
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.back()}
                              >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>
                        </div>

                        <Card>
                              <CardContent className="flex items-center justify-center h-96">
                                    <div className="text-center">
                                          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                Team not found
                                          </h3>
                                          <p className="text-gray-500">
                                                The team you're looking for doesn't exist or you don't have permission to view it.
                                          </p>
                                    </div>
                              </CardContent>
                        </Card>
                  </div>
            );
      }

      return (
            <div className="space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.back()}
                              >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>

                              <div className="flex items-center space-x-3">
                                    {buildTeamLogoUrl(team.logo) ? (
                                          <img
                                                src={buildTeamLogoUrl(team.logo) || ''}
                                                alt={team.name}
                                                className="w-12 h-12 object-contain rounded-full"
                                                onError={(e) => {
                                                      const target = e.target as HTMLImageElement;
                                                      target.style.display = 'none';
                                                }}
                                          />
                                    ) : (
                                          <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                                                <Users className="w-6 h-6 text-gray-400" />
                                          </div>
                                    )}

                                    <div>
                                          <h1 className="text-2xl font-bold text-gray-900">{team.name}</h1>
                                          <div className="flex items-center space-x-2 text-gray-600">
                                                {team.country && (
                                                      <>
                                                            <img
                                                                  src={buildCountryFlagUrl(team.country) || ''}
                                                                  alt={`${team.country} flag`}
                                                                  className="w-4 h-3 object-cover"
                                                                  onError={(e) => {
                                                                        const target = e.target as HTMLImageElement;
                                                                        target.style.display = 'none';
                                                                  }}
                                                            />
                                                            <span>{team.country}</span>
                                                      </>
                                                )}
                                                {team.code && (
                                                      <>
                                                            <span>•</span>
                                                            <span className="font-mono text-sm">{team.code}</span>
                                                      </>
                                                )}
                                          </div>
                                    </div>
                              </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center space-x-2">
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleViewStatistics}
                              >
                                    <BarChart3 className="w-4 h-4 mr-2" />
                                    Statistics
                              </Button>

                              {isEditor() && (
                                    <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={handleEdit}
                                    >
                                          <Edit className="w-4 h-4 mr-2" />
                                          Edit
                                    </Button>
                              )}

                              {isAdmin() && (
                                    <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={handleDelete}
                                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                          <Trash2 className="w-4 h-4 mr-2" />
                                          Delete
                                    </Button>
                              )}
                        </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Main Content */}
                        <div className="lg:col-span-2 space-y-6">
                              {/* Team Information */}
                              <Card>
                                    <CardHeader>
                                          <CardTitle className="flex items-center space-x-2">
                                                <Users className="w-5 h-5" />
                                                <span>Team Information</span>
                                          </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div>
                                                      <label className="text-sm font-medium text-gray-500">Team Name</label>
                                                      <p className="text-lg font-medium">{team.name}</p>
                                                </div>

                                                {team.code && (
                                                      <div>
                                                            <label className="text-sm font-medium text-gray-500">Team Code</label>
                                                            <p className="text-lg font-medium font-mono">{team.code}</p>
                                                      </div>
                                                )}

                                                <div>
                                                      <label className="text-sm font-medium text-gray-500">Country</label>
                                                      <div className="flex items-center space-x-2">
                                                            {team.country && (
                                                                  <>
                                                                        <img
                                                                              src={buildCountryFlagUrl(team.country) || ''}
                                                                              alt={`${team.country} flag`}
                                                                              className="w-6 h-4 object-cover"
                                                                              onError={(e) => {
                                                                                    const target = e.target as HTMLImageElement;
                                                                                    target.style.display = 'none';
                                                                              }}
                                                                        />
                                                                        <span className="text-lg font-medium">{team.country}</span>
                                                                  </>
                                                            )}
                                                      </div>
                                                </div>

                                                {team.founded && (
                                                      <div>
                                                            <label className="text-sm font-medium text-gray-500">Founded</label>
                                                            <div className="flex items-center space-x-2">
                                                                  <Calendar className="w-4 h-4 text-gray-400" />
                                                                  <p className="text-lg font-medium">{team.founded}</p>
                                                            </div>
                                                      </div>
                                                )}

                                                <div>
                                                      <label className="text-sm font-medium text-gray-500">Team ID</label>
                                                      <p className="text-lg font-medium">#{team.externalId}</p>
                                                </div>

                                                <div>
                                                      <label className="text-sm font-medium text-gray-500">Internal ID</label>
                                                      <p className="text-lg font-medium">#{team.id}</p>
                                                </div>
                                          </div>
                                    </CardContent>
                              </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                              {/* Team Logo */}
                              {buildTeamLogoUrl(team.logo) && (
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="text-lg">Team Logo</CardTitle>
                                          </CardHeader>
                                          <CardContent>
                                                <div className="flex justify-center">
                                                      <img
                                                            src={buildTeamLogoUrl(team.logo) || ''}
                                                            alt={team.name}
                                                            className="w-32 h-32 object-contain"
                                                      />
                                                </div>
                                          </CardContent>
                                    </Card>
                              )}

                              {/* Quick Stats */}
                              <Card>
                                    <CardHeader>
                                          <CardTitle className="text-lg">Quick Info</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                          <div className="flex items-center justify-between">
                                                <span className="text-sm text-gray-600">Country</span>
                                                <div className="flex items-center space-x-2">
                                                      {team.country && (
                                                            <>
                                                                  <img
                                                                        src={buildCountryFlagUrl(team.country) || ''}
                                                                        alt={`${team.country} flag`}
                                                                        className="w-4 h-3 object-cover"
                                                                        onError={(e) => {
                                                                              const target = e.target as HTMLImageElement;
                                                                              target.style.display = 'none';
                                                                        }}
                                                                  />
                                                                  <span className="text-sm font-medium">{team.country}</span>
                                                            </>
                                                      )}
                                                </div>
                                          </div>

                                          {team.founded && (
                                                <div className="flex items-center justify-between">
                                                      <span className="text-sm text-gray-600">Founded</span>
                                                      <Badge variant="outline" className="font-mono">
                                                            {team.founded}
                                                      </Badge>
                                                </div>
                                          )}

                                          {team.code && (
                                                <div className="flex items-center justify-between">
                                                      <span className="text-sm text-gray-600">Code</span>
                                                      <Badge variant="secondary" className="font-mono">
                                                            {team.code}
                                                      </Badge>
                                                </div>
                                          )}

                                          <div className="flex items-center justify-between">
                                                <span className="text-sm text-gray-600">Team ID</span>
                                                <span className="text-sm font-medium">#{team.externalId}</span>
                                          </div>
                                    </CardContent>
                              </Card>
                        </div>
                  </div>

                  {/* Delete Confirmation Modal */}
                  <Modal
                        isOpen={deleteModalOpen}
                        onClose={() => setDeleteModalOpen(false)}
                        title="Delete Team"
                        description="Are you sure you want to delete this team? This action cannot be undone."
                  >
                        <div className="space-y-4">
                              <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200">
                                    <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0" />
                                    <div>
                                          <p className="text-sm font-medium text-red-800">
                                                This will permanently delete the team:
                                          </p>
                                          <p className="text-sm text-red-700 mt-1">
                                                <strong>{team.name}</strong> ({team.country})
                                          </p>
                                    </div>
                              </div>

                              <div className="flex items-center justify-end space-x-2 pt-4">
                                    <Button
                                          variant="outline"
                                          onClick={() => setDeleteModalOpen(false)}
                                          disabled={deleteMutation.isLoading}
                                    >
                                          Cancel
                                    </Button>
                                    <Button
                                          variant="destructive"
                                          onClick={confirmDelete}
                                          disabled={deleteMutation.isLoading}
                                    >
                                          {deleteMutation.isLoading ? 'Deleting...' : 'Delete Team'}
                                    </Button>
                              </div>
                        </div>
                  </Modal>
            </div>
      );
}
