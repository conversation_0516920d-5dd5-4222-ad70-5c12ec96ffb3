'use client';

import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useTeam, useTeamStatistics } from '@/lib/hooks/useTeams';
import { buildTeamLogoUrl, buildCountryFlagUrl } from '@/lib/utils/image';
import {
      ArrowLeft,
      BarChart3,
      Trophy,
      Target,
      Shield,
      TrendingUp,
      TrendingDown,
      Minus,
      Users,
      Calendar,
      MapPin,
      Eye
} from 'lucide-react';

export default function TeamStatisticsPage() {
      const params = useParams();
      const router = useRouter();
      const teamId = parseInt(params.id as string);

      // Fetch team details and statistics
      const { team, isLoading: teamLoading, error: teamError } = useTeam(teamId);
      const { statistics, isLoading: statsLoading, error: statsError } = useTeamStatistics(teamId);

      const isLoading = teamLoading || statsLoading;
      const error = teamError || statsError;

      const handleViewTeamDetails = () => {
            router.push(`/dashboard/teams/${teamId}`);
      };

      if (isLoading) {
            return (
                  <div className="space-y-6">
                        <div className="flex items-center space-x-4">
                              <Skeleton className="h-10 w-20" />
                              <Skeleton className="h-8 w-64" />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                              {Array.from({ length: 8 }).map((_, i) => (
                                    <Skeleton key={i} className="h-24" />
                              ))}
                        </div>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                              <Skeleton className="h-96" />
                              <Skeleton className="h-96" />
                        </div>
                  </div>
            );
      }

      if (error || !team) {
            return (
                  <div className="space-y-6">
                        <div className="flex items-center space-x-4">
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.back()}
                              >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>
                        </div>

                        <Card>
                              <CardContent className="flex items-center justify-center h-96">
                                    <div className="text-center">
                                          <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                Statistics not available
                                          </h3>
                                          <p className="text-gray-500">
                                                Unable to load team statistics at this time.
                                          </p>
                                    </div>
                              </CardContent>
                        </Card>
                  </div>
            );
      }

      // Mock statistics data (replace with real data when available)
      const mockStats = {
            totalMatches: 28,
            wins: 18,
            draws: 6,
            losses: 4,
            goalsScored: 54,
            goalsConceded: 23,
            cleanSheets: 12,
            winPercentage: 64.3,
            avgGoalsPerMatch: 1.93,
            avgGoalsConcededPerMatch: 0.82,
            homeRecord: { wins: 11, draws: 3, losses: 0 },
            awayRecord: { wins: 7, draws: 3, losses: 4 },
            recentForm: ['W', 'W', 'D', 'W', 'L'], // Last 5 matches
      };

      const stats = statistics || mockStats;

      const getFormIcon = (result: string) => {
            switch (result) {
                  case 'W':
                        return <TrendingUp className="w-4 h-4 text-green-600" />;
                  case 'L':
                        return <TrendingDown className="w-4 h-4 text-red-600" />;
                  case 'D':
                        return <Minus className="w-4 h-4 text-yellow-600" />;
                  default:
                        return null;
            }
      };

      const getFormColor = (result: string) => {
            switch (result) {
                  case 'W':
                        return 'bg-green-100 text-green-800';
                  case 'L':
                        return 'bg-red-100 text-red-800';
                  case 'D':
                        return 'bg-yellow-100 text-yellow-800';
                  default:
                        return 'bg-gray-100 text-gray-800';
            }
      };

      return (
            <div className="space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.back()}
                              >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>

                              <div className="flex items-center space-x-3">
                                    {buildTeamLogoUrl(team.logo) ? (
                                          <img
                                                src={buildTeamLogoUrl(team.logo) || ''}
                                                alt={team.name}
                                                className="w-10 h-10 object-contain rounded-full"
                                                onError={(e) => {
                                                      const target = e.target as HTMLImageElement;
                                                      target.style.display = 'none';
                                                }}
                                          />
                                    ) : (
                                          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                                <Users className="w-5 h-5 text-gray-400" />
                                          </div>
                                    )}

                                    <div>
                                          <h1 className="text-2xl font-bold text-gray-900">{team.name} Statistics</h1>
                                          <div className="flex items-center space-x-2 text-gray-600">
                                                {team.country && (
                                                      <>
                                                            <img
                                                                  src={buildCountryFlagUrl(team.country) || ''}
                                                                  alt={`${team.country} flag`}
                                                                  className="w-4 h-3 object-cover"
                                                                  onError={(e) => {
                                                                        e.currentTarget.style.display = 'none';
                                                                  }}
                                                            />
                                                            <span>{team.country}</span>
                                                      </>
                                                )}
                                                {team.code && (
                                                      <>
                                                            <span>•</span>
                                                            <span className="font-mono text-sm">{team.code}</span>
                                                      </>
                                                )}
                                          </div>
                                    </div>
                              </div>
                        </div>

                        <Button
                              variant="outline"
                              size="sm"
                              onClick={handleViewTeamDetails}
                        >
                              <Eye className="w-4 h-4 mr-2" />
                              View Team
                        </Button>
                  </div>

                  {/* Statistics Overview */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {/* Total Matches */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-gray-900">{stats.totalMatches}</p>
                                                <p className="text-sm text-gray-600">Total Matches</p>
                                          </div>
                                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <Calendar className="w-6 h-6 text-blue-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Wins */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-green-600">{stats.wins}</p>
                                                <p className="text-sm text-gray-600">Wins</p>
                                          </div>
                                          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                                <Trophy className="w-6 h-6 text-green-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Draws */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-yellow-600">{stats.draws}</p>
                                                <p className="text-sm text-gray-600">Draws</p>
                                          </div>
                                          <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                                <Minus className="w-6 h-6 text-yellow-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Losses */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-red-600">{stats.losses}</p>
                                                <p className="text-sm text-gray-600">Losses</p>
                                          </div>
                                          <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                                <TrendingDown className="w-6 h-6 text-red-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Goals Scored */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-blue-600">{stats.goalsScored}</p>
                                                <p className="text-sm text-gray-600">Goals Scored</p>
                                          </div>
                                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <Target className="w-6 h-6 text-blue-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Goals Conceded */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-orange-600">{stats.goalsConceded}</p>
                                                <p className="text-sm text-gray-600">Goals Conceded</p>
                                          </div>
                                          <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                                <Shield className="w-6 h-6 text-orange-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Clean Sheets */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-emerald-600">{stats.cleanSheets}</p>
                                                <p className="text-sm text-gray-600">Clean Sheets</p>
                                          </div>
                                          <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                                                <Shield className="w-6 h-6 text-emerald-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Win Percentage */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-purple-600">{stats.winPercentage}%</p>
                                                <p className="text-sm text-gray-600">Win Rate</p>
                                          </div>
                                          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                                <TrendingUp className="w-6 h-6 text-purple-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Performance Breakdown */}
                        <Card>
                              <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                          <BarChart3 className="w-5 h-5" />
                                          <span>Performance Breakdown</span>
                                    </CardTitle>
                              </CardHeader>
                              <CardContent className="space-y-4">
                                    {/* Overall Record */}
                                    <div>
                                          <div className="flex items-center justify-between mb-2">
                                                <span className="text-sm font-medium text-gray-700">Overall Record</span>
                                                <span className="text-sm text-gray-600">
                                                      {stats.wins}W - {stats.draws}D - {stats.losses}L
                                                </span>
                                          </div>
                                          <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div
                                                      className="bg-green-600 h-2 rounded-l-full"
                                                      style={{ width: `${(stats.wins / stats.totalMatches) * 100}%` }}
                                                ></div>
                                                <div
                                                      className="bg-yellow-600 h-2"
                                                      style={{
                                                            width: `${(stats.draws / stats.totalMatches) * 100}%`,
                                                            marginLeft: `${(stats.wins / stats.totalMatches) * 100}%`
                                                      }}
                                                ></div>
                                          </div>
                                    </div>

                                    {/* Home Record */}
                                    <div>
                                          <div className="flex items-center justify-between mb-2">
                                                <span className="text-sm font-medium text-gray-700">Home Record</span>
                                                <span className="text-sm text-gray-600">
                                                      {stats.homeRecord.wins}W - {stats.homeRecord.draws}D - {stats.homeRecord.losses}L
                                                </span>
                                          </div>
                                          <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div
                                                      className="bg-green-600 h-2 rounded-l-full"
                                                      style={{
                                                            width: `${(stats.homeRecord.wins / (stats.homeRecord.wins + stats.homeRecord.draws + stats.homeRecord.losses)) * 100}%`
                                                      }}
                                                ></div>
                                          </div>
                                    </div>

                                    {/* Away Record */}
                                    <div>
                                          <div className="flex items-center justify-between mb-2">
                                                <span className="text-sm font-medium text-gray-700">Away Record</span>
                                                <span className="text-sm text-gray-600">
                                                      {stats.awayRecord.wins}W - {stats.awayRecord.draws}D - {stats.awayRecord.losses}L
                                                </span>
                                          </div>
                                          <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div
                                                      className="bg-green-600 h-2 rounded-l-full"
                                                      style={{
                                                            width: `${(stats.awayRecord.wins / (stats.awayRecord.wins + stats.awayRecord.draws + stats.awayRecord.losses)) * 100}%`
                                                      }}
                                                ></div>
                                          </div>
                                    </div>

                                    {/* Goal Difference */}
                                    <div className="pt-4 border-t">
                                          <div className="flex items-center justify-between">
                                                <span className="text-sm font-medium text-gray-700">Goal Difference</span>
                                                <span className={`text-lg font-bold ${stats.goalsScored - stats.goalsConceded >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                      {stats.goalsScored - stats.goalsConceded >= 0 ? '+' : ''}{stats.goalsScored - stats.goalsConceded}
                                                </span>
                                          </div>
                                    </div>

                                    {/* Average Goals */}
                                    <div className="grid grid-cols-2 gap-4 pt-2">
                                          <div>
                                                <span className="text-sm text-gray-600">Avg Goals Scored</span>
                                                <p className="text-lg font-bold text-blue-600">{stats.avgGoalsPerMatch}</p>
                                          </div>
                                          <div>
                                                <span className="text-sm text-gray-600">Avg Goals Conceded</span>
                                                <p className="text-lg font-bold text-orange-600">{stats.avgGoalsConcededPerMatch}</p>
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Recent Form & Additional Stats */}
                        <div className="space-y-6">
                              {/* Recent Form */}
                              <Card>
                                    <CardHeader>
                                          <CardTitle className="flex items-center space-x-2">
                                                <TrendingUp className="w-5 h-5" />
                                                <span>Recent Form</span>
                                          </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                          <div className="flex items-center space-x-2">
                                                <span className="text-sm text-gray-600">Last 5 matches:</span>
                                                <div className="flex space-x-1">
                                                      {stats.recentForm.map((result, index) => (
                                                            <div
                                                                  key={index}
                                                                  className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${getFormColor(result)}`}
                                                            >
                                                                  {result}
                                                            </div>
                                                      ))}
                                                </div>
                                          </div>
                                    </CardContent>
                              </Card>

                              {/* Team Information */}
                              <Card>
                                    <CardHeader>
                                          <CardTitle className="flex items-center space-x-2">
                                                <Users className="w-5 h-5" />
                                                <span>Team Info</span>
                                          </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                          <div className="flex items-center justify-between">
                                                <span className="text-sm text-gray-600">Country</span>
                                                <div className="flex items-center space-x-2">
                                                      {team.country && (
                                                            <>
                                                                  <img
                                                                        src={buildCountryFlagUrl(team.country) || ''}
                                                                        alt={`${team.country} flag`}
                                                                        className="w-4 h-3 object-cover"
                                                                        onError={(e) => {
                                                                              e.currentTarget.style.display = 'none';
                                                                        }}
                                                                  />
                                                                  <span className="text-sm font-medium">{team.country}</span>
                                                            </>
                                                      )}
                                                </div>
                                          </div>

                                          {team.founded && (
                                                <div className="flex items-center justify-between">
                                                      <span className="text-sm text-gray-600">Founded</span>
                                                      <Badge variant="outline" className="font-mono">
                                                            {team.founded}
                                                      </Badge>
                                                </div>
                                          )}

                                          {team.code && (
                                                <div className="flex items-center justify-between">
                                                      <span className="text-sm text-gray-600">Team Code</span>
                                                      <Badge variant="secondary" className="font-mono">
                                                            {team.code}
                                                      </Badge>
                                                </div>
                                          )}

                                          <div className="flex items-center justify-between">
                                                <span className="text-sm text-gray-600">Team ID</span>
                                                <span className="text-sm font-medium">#{team.externalId}</span>
                                          </div>
                                    </CardContent>
                              </Card>
                        </div>
                  </div>
            </div>
      );
}
