'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { useTeam } from '@/lib/hooks/useTeams';
import { teamsApi } from '@/lib/api/teams';
import { buildTeamLogoUrl } from '@/lib/utils/image';
import { toast } from 'sonner';
import {
      ArrowLeft,
      Save,
      Users,
      AlertCircle
} from 'lucide-react';

interface TeamFormData {
      name: string;
      code: string;
      country: string;
      founded: string;
      logo: string;
}

export default function EditTeamPage() {
      const params = useParams();
      const router = useRouter();
      const queryClient = useQueryClient();
      const teamId = parseInt(params.id as string);

      // State
      const [formData, setFormData] = useState<TeamFormData>({
            name: '',
            code: '',
            country: '',
            founded: '',
            logo: '',
      });

      // Fetch team details
      const { team, isLoading, error } = useTeam(teamId);

      // Update mutation - placeholder for future implementation
      const updateMutation = useMutation({
            mutationFn: async (data: TeamFormData) => {
                  // This will be implemented when the API endpoint is ready
                  console.log('Update team data:', data);
                  await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
                  return data;
            },
            onSuccess: () => {
                  toast.success('Team updated successfully');
                  queryClient.invalidateQueries({ queryKey: ['team', teamId] });
                  queryClient.invalidateQueries({ queryKey: ['teams'] });
                  router.push(`/dashboard/teams/${teamId}`);
            },
            onError: (error) => {
                  toast.error('Failed to update team');
                  console.error('Update team error:', error);
            },
      });

      // Update form when team data is loaded
      useEffect(() => {
            if (team) {
                  setFormData({
                        name: team.name || '',
                        code: team.code || '',
                        country: team.country || '',
                        founded: team.founded?.toString() || '',
                        logo: team.logo || '',
                  });
            }
      }, [team]);

      const handleInputChange = (field: keyof TeamFormData, value: string) => {
            setFormData(prev => ({ ...prev, [field]: value }));
      };

      const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();

            // Basic validation
            if (!formData.name.trim() || !formData.code.trim() || !formData.country.trim()) {
                  toast.error('Please fill in all required fields');
                  return;
            }

            updateMutation.mutate(formData);
      };

      const handleBack = () => {
            router.push(`/dashboard/teams/${teamId}`);
      };

      if (isLoading) {
            return (
                  <div className="space-y-6">
                        <div className="flex items-center gap-4">
                              <Skeleton className="h-8 w-8" />
                              <Skeleton className="h-8 w-48" />
                        </div>
                        <Card>
                              <CardHeader>
                                    <Skeleton className="h-6 w-32" />
                              </CardHeader>
                              <CardContent className="space-y-4">
                                    <Skeleton className="h-10 w-full" />
                                    <Skeleton className="h-10 w-full" />
                                    <Skeleton className="h-10 w-full" />
                                    <Skeleton className="h-10 w-full" />
                              </CardContent>
                        </Card>
                  </div>
            );
      }

      if (error || !team) {
            return (
                  <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
                        <Users className="h-16 w-16 text-muted-foreground" />
                        <div className="text-center">
                              <h2 className="text-xl font-semibold">Team Not Found</h2>
                              <p className="text-muted-foreground">
                                    The team you're looking for doesn't exist or has been removed.
                              </p>
                        </div>
                        <Button onClick={() => router.push('/dashboard/teams')}>
                              Back to Teams
                        </Button>
                  </div>
            );
      }

      return (
            <div className="space-y-6">
                  {/* Header */}
                  <div className="flex items-center gap-4">
                        <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleBack}
                              className="flex items-center gap-2"
                        >
                              <ArrowLeft className="h-4 w-4" />
                              Back
                        </Button>
                        <div>
                              <h1 className="text-2xl font-bold">Edit Team</h1>
                              <p className="text-muted-foreground">
                                    Update team information and details
                              </p>
                        </div>
                  </div>

                  {/* Form */}
                  <Card>
                        <CardHeader>
                              <CardTitle className="flex items-center gap-2">
                                    <Users className="h-5 w-5" />
                                    Team Information
                              </CardTitle>
                        </CardHeader>
                        <CardContent>
                              <form onSubmit={handleSubmit} className="space-y-6">
                                    {/* Logo Preview */}
                                    <div className="flex items-center gap-4">
                                          <div className="w-16 h-16 border border-border rounded-lg overflow-hidden bg-muted flex items-center justify-center">
                                                {formData.logo ? (
                                                      <img
                                                            src={buildTeamLogoUrl(formData.logo) || ''}
                                                            alt={formData.name}
                                                            className="w-full h-full object-contain"
                                                            onError={(e) => {
                                                                  e.currentTarget.style.display = 'none';
                                                            }}
                                                      />
                                                ) : (
                                                      <Users className="h-8 w-8 text-muted-foreground" />
                                                )}
                                          </div>
                                          <div>
                                                <h3 className="font-medium">{formData.name || 'Team Name'}</h3>
                                                <p className="text-sm text-muted-foreground">{formData.code || 'Team Code'}</p>
                                          </div>
                                    </div>

                                    {/* Form Fields */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                          <div className="space-y-2">
                                                <Label htmlFor="name">Team Name *</Label>
                                                <Input
                                                      id="name"
                                                      value={formData.name}
                                                      onChange={(e) => handleInputChange('name', e.target.value)}
                                                      placeholder="Enter team name"
                                                      required
                                                />
                                          </div>

                                          <div className="space-y-2">
                                                <Label htmlFor="code">Team Code *</Label>
                                                <Input
                                                      id="code"
                                                      value={formData.code}
                                                      onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                                                      placeholder="e.g., MAN, CHE, LIV"
                                                      maxLength={5}
                                                      required
                                                />
                                          </div>

                                          <div className="space-y-2">
                                                <Label htmlFor="country">Country *</Label>
                                                <Input
                                                      id="country"
                                                      value={formData.country}
                                                      onChange={(e) => handleInputChange('country', e.target.value)}
                                                      placeholder="Enter country"
                                                      required
                                                />
                                          </div>

                                          <div className="space-y-2">
                                                <Label htmlFor="founded">Founded Year</Label>
                                                <Input
                                                      id="founded"
                                                      type="number"
                                                      value={formData.founded}
                                                      onChange={(e) => handleInputChange('founded', e.target.value)}
                                                      placeholder="e.g., 1902"
                                                      min="1800"
                                                      max={new Date().getFullYear()}
                                                />
                                          </div>

                                          <div className="space-y-2 md:col-span-2">
                                                <Label htmlFor="logo">Logo URL</Label>
                                                <Input
                                                      id="logo"
                                                      value={formData.logo}
                                                      onChange={(e) => handleInputChange('logo', e.target.value)}
                                                      placeholder="Enter logo URL"
                                                />
                                          </div>
                                    </div>

                                    {/* Development Notice */}
                                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                                          <div className="flex items-start gap-3">
                                                <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
                                                <div>
                                                      <h4 className="font-medium text-amber-800">Development Notice</h4>
                                                      <p className="text-sm text-amber-700 mt-1">
                                                            Team editing functionality is currently under development. The API endpoint for updating teams
                                                            has not been implemented yet. This form is ready for integration once the backend API is available.
                                                      </p>
                                                </div>
                                          </div>
                                    </div>

                                    {/* Form Actions */}
                                    <div className="flex items-center gap-3 pt-4 border-t">
                                          <Button
                                                type="submit"
                                                disabled={updateMutation.isLoading}
                                                className="flex items-center gap-2"
                                          >
                                                <Save className="h-4 w-4" />
                                                {updateMutation.isLoading ? 'Saving...' : 'Save Changes'}
                                          </Button>
                                          <Button
                                                type="button"
                                                variant="outline"
                                                onClick={handleBack}
                                                disabled={updateMutation.isLoading}
                                          >
                                                Cancel
                                          </Button>
                                    </div>
                              </form>
                        </CardContent>
                  </Card>
            </div>
      );
}
