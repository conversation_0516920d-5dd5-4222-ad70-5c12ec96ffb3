'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable, Column } from '@/components/ui/data-table';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { useTeams } from '@/lib/hooks/useTeams';
import { TeamFilters } from '@/lib/api/teams';
import { leaguesApi } from '@/lib/api/leagues';
import { Team } from '@/lib/types/api';
import { buildTeamLogoUrl, buildCountryFlagUrl } from '@/lib/utils/image';
import { toast } from 'sonner';
import {
  Users,
  Search,
  Filter,
  Eye,
  Globe,
  Trophy,
  TrendingUp,
  Calendar,
  MapPin,
  RefreshCw,
  Download
} from 'lucide-react';
import Link from 'next/link';

export default function TeamsPage() {
  const router = useRouter();
  const { isEditor, isAdmin } = usePermissions();

  // State for filtering
  const [filters, setFilters] = useState<TeamFilters>({
    page: 1,
    limit: 20,
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLeague, setSelectedLeague] = useState<string>('');
  const [selectedCountry, setSelectedCountry] = useState<string>('');

  // Fetch teams data
  const {
    teams,
    teamsMeta,
    isLoading,
    error
  } = useTeams(filters);

  // Debug: Log teams data
  console.log('🔍 Teams Page Debug:', {
    teamsCount: teams.length,
    firstTeam: teams[0],
    firstTeamName: teams[0]?.name,
    firstTeamNameType: typeof teams[0]?.name,
    firstTeamNameLength: teams[0]?.name?.length,
    filters,
    teamsMeta,
    error
  });

  // Additional debug for all teams
  if (teams.length > 0) {
    console.log('🔍 All teams names:', teams.slice(0, 3).map(team => ({
      id: team?.id,
      name: team?.name,
      nameType: typeof team?.name,
      nameValid: Boolean(team?.name)
    })));
  }

  // Fetch leagues for filtering
  const { data: leagues } = useQuery({
    queryKey: ['leagues', 'all'],
    queryFn: () => leaguesApi.getLeagues({ limit: 100 }),
  });

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setFilters(prev => ({
      ...prev,
      search: query || undefined,
      page: 1, // Reset to first page
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Handle filters
  const handleLeagueFilter = (leagueId: string) => {
    setSelectedLeague(leagueId);
    setFilters(prev => ({
      ...prev,
      league: leagueId ? parseInt(leagueId) : undefined,
      page: 1,
    }));
  };

  const handleCountryFilter = (country: string) => {
    setSelectedCountry(country);
    setFilters(prev => ({
      ...prev,
      country: country || undefined,
      page: 1,
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery('');
    setSelectedLeague('');
    setSelectedCountry('');
    setFilters({ page: 1, limit: 20 });
  };

  // Define table columns
  const columns: Column<Team>[] = [
    {
      title: 'Team',
      key: 'name',
      render: (value: any, team: Team) => (
        <div className="flex items-center space-x-3">
          <img
            src={buildTeamLogoUrl(team?.logo) || '/images/default-team.png'}
            alt={`${team?.name || 'Team'} logo`}
            className="w-8 h-8 rounded-full object-cover"
            onError={(e) => {
              e.currentTarget.src = '/images/default-team.png';
            }}
          />
          <div>
            <div className="font-medium">{team?.name || 'Unknown Team'}</div>
            {team?.code && (
              <div className="text-sm text-muted-foreground">{team.code}</div>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'Country',
      key: 'country',
      render: (value: any, team: Team) => (
        <div className="flex items-center space-x-2">
          {team && team.country && (
            <>
              <img
                src={buildCountryFlagUrl(team.country) || '/images/default-flag.png'}
                alt={`${team.country} flag`}
                className="w-4 h-3 object-cover"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
              <span>{team.country}</span>
            </>
          )}
        </div>
      ),
    },
    {
      title: 'Founded',
      key: 'founded',
      render: (value: any, team: Team) => (
        <div className="text-center">
          {team?.founded ? (
            <Badge variant="outline" className="font-mono">
              {team.founded}
            </Badge>
          ) : (
            <span className="text-muted-foreground">-</span>
          )}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (value: any, team: Team) => (
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/teams/${team?.externalId}`)}
            disabled={!team?.externalId}
          >
            <Eye className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/teams/${team?.externalId}/statistics`)}
            disabled={!team?.externalId}
          >
            <TrendingUp className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  // Get unique countries for filter
  const countries = Array.from(new Set(teams.filter(team => team?.country).map(team => team.country).filter(Boolean)));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Users className="w-8 h-8 text-blue-600" />
            Teams Management
          </h1>
          <p className="text-muted-foreground">
            Browse and manage football teams from leagues worldwide
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => window.location.reload()}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          {isAdmin() && (
            <Button variant="outline" onClick={() => toast.info('Export feature coming soon')}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Search & Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search teams..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                className="pl-9"
              />
            </div>
            <Button onClick={() => handleSearch(searchQuery)}>
              Search
            </Button>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* League Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">League</label>
              <select
                value={selectedLeague}
                onChange={(e) => handleLeagueFilter(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">All Leagues</option>
                {leagues?.data?.map((league) => (
                  <option key={league.externalId} value={league.externalId}>
                    {league.name} {league.season && `(${league.season})`}
                  </option>
                ))}
              </select>
            </div>

            {/* Country Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Country</label>
              <select
                value={selectedCountry}
                onChange={(e) => handleCountryFilter(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">All Countries</option>
                {countries.map((country) => (
                  <option key={country} value={country}>
                    {country}
                  </option>
                ))}
              </select>
            </div>

            {/* Actions */}
            <div className="flex items-end">
              <Button variant="outline" onClick={clearFilters} className="w-full">
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Teams Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Teams ({teamsMeta?.totalItems || 0})</span>
            {teamsMeta && (
              <Badge variant="outline">
                Page {teamsMeta.currentPage} of {teamsMeta.totalPages}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={teams}
            columns={columns}
            loading={isLoading}
            pagination={{
              page: teamsMeta?.currentPage || 1,
              limit: teamsMeta?.limit || 20,
              total: teamsMeta?.totalItems || 0,
              onPageChange: handlePageChange,
              onLimitChange: (newLimit) => {
                setFilters(prev => ({ ...prev, limit: newLimit, page: 1 }));
              },
            }}
            emptyMessage={error ? `Error loading teams: ${(error as any)?.message || 'Unknown error'}` : "No teams found"}
          />
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-8 h-8 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Total Teams</p>
                <p className="text-2xl font-bold">{teamsMeta?.totalItems || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Globe className="w-8 h-8 text-green-500" />
              <div>
                <p className="text-sm font-medium">Countries</p>
                <p className="text-2xl font-bold">{countries.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Trophy className="w-8 h-8 text-yellow-500" />
              <div>
                <p className="text-sm font-medium">Leagues</p>
                <p className="text-2xl font-bold">{leagues?.data?.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="w-8 h-8 text-purple-500" />
              <div>
                <p className="text-sm font-medium">Current Page</p>
                <p className="text-2xl font-bold">{teamsMeta?.currentPage || 1}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
