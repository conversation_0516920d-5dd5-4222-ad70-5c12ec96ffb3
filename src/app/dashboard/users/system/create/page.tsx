'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Save, User, Mail, Shield, Eye, EyeOff } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { systemUsersHooks } from '@/lib/hooks/useSystemUsers';
import { usePermissions } from '@/lib/middleware/auth-guard';

const createUserSchema = z.object({
      username: z.string().min(3, 'Username must be at least 3 characters'),
      email: z.string().email('Invalid email address'),
      fullName: z.string().min(1, 'Full name is required'),
      password: z.string().min(8, 'Password must be at least 8 characters'),
      confirmPassword: z.string().min(8, 'Please confirm your password'),
      role: z.enum(['admin', 'editor', 'moderator'], {
            required_error: 'Role is required',
      }),
      isActive: z.boolean(),
}).refine((data) => data.password === data.confirmPassword, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
});

type CreateUserFormData = z.infer<typeof createUserSchema>;

export default function CreateSystemUserPage() {
      const router = useRouter();
      const { toast } = useToast();
      const { canManageUsers } = usePermissions();

      const { mutate: createUser, isLoading: isCreating } = systemUsersHooks.useCreate();

      const [showPassword, setShowPassword] = useState(false);
      const [showConfirmPassword, setShowConfirmPassword] = useState(false);

      const form = useForm<CreateUserFormData>({
            resolver: zodResolver(createUserSchema),
            defaultValues: {
                  username: '',
                  email: '',
                  fullName: '',
                  password: '',
                  confirmPassword: '',
                  role: 'editor',
                  isActive: true,
            },
      });

      const onSubmit = (data: CreateUserFormData) => {
            const { confirmPassword, ...userData } = data;
            createUser(userData, {
                  onSuccess: (newUser) => {
                        toast({
                              title: 'User created',
                              description: `System user ${newUser.fullName || newUser.username} has been successfully created.`,
                        });
                        router.push('/dashboard/users/system');
                  },
                  onError: (error: any) => {
                        toast({
                              title: 'Error',
                              description: error?.message || 'Failed to create user.',
                              variant: 'destructive',
                        });
                  },
            });
      };

      const handleCancel = () => {
            router.push('/dashboard/users/system');
      };

      if (!canManageUsers()) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="text-center py-12">
                              <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
                              <p className="text-gray-600 mb-4">You don't have permission to create users.</p>
                              <Button onClick={() => router.push('/dashboard/users/system')}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Users
                              </Button>
                        </div>
                  </div>
            );
      }

      return (
            <div className="container mx-auto p-6 space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                              <Button variant="ghost" size="sm" onClick={handleCancel}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>
                              <div>
                                    <h1 className="text-3xl font-bold text-gray-900">Create System User</h1>
                                    <p className="text-gray-600">Add a new administrator, editor, or moderator account</p>
                              </div>
                        </div>

                        <div className="flex items-center space-x-2">
                              <Button variant="outline" onClick={handleCancel}>
                                    Cancel
                              </Button>
                              <Button
                                    onClick={form.handleSubmit(onSubmit)}
                                    disabled={isCreating}
                              >
                                    <Save className="w-4 h-4 mr-2" />
                                    {isCreating ? 'Creating...' : 'Create User'}
                              </Button>
                        </div>
                  </div>

                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                              {/* Main Form */}
                              <div className="lg:col-span-2 space-y-6">
                                    {/* Basic Information */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <User className="w-5 h-5 mr-2" />
                                                      Basic Information
                                                </CardTitle>
                                                <CardDescription>
                                                      Enter the user's personal information
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="space-y-2">
                                                      <Label htmlFor="fullName">Full Name *</Label>
                                                      <Input
                                                            id="fullName"
                                                            {...form.register('fullName')}
                                                      />
                                                      {form.formState.errors.fullName && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.fullName.message}</p>
                                                      )}
                                                </div>

                                                <div className="space-y-2">
                                                      <Label htmlFor="username">Username *</Label>
                                                      <Input
                                                            id="username"
                                                            {...form.register('username')}
                                                            placeholder="Enter a unique username"
                                                      />
                                                      {form.formState.errors.username && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.username.message}</p>
                                                      )}
                                                </div>

                                                <div className="space-y-2">
                                                      <Label htmlFor="email">Email Address *</Label>
                                                      <Input
                                                            id="email"
                                                            type="email"
                                                            {...form.register('email')}
                                                            placeholder="<EMAIL>"
                                                      />
                                                      {form.formState.errors.email && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.email.message}</p>
                                                      )}
                                                </div>
                                          </CardContent>
                                    </Card>

                                    {/* Password */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <Shield className="w-5 h-5 mr-2" />
                                                      Security
                                                </CardTitle>
                                                <CardDescription>
                                                      Set up the user's login credentials
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="space-y-2">
                                                      <Label htmlFor="password">Password *</Label>
                                                      <div className="relative">
                                                            <Input
                                                                  id="password"
                                                                  type={showPassword ? 'text' : 'password'}
                                                                  {...form.register('password')}
                                                                  placeholder="Enter a strong password"
                                                                  className="pr-10"
                                                            />
                                                            <Button
                                                                  type="button"
                                                                  variant="ghost"
                                                                  size="sm"
                                                                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                                  onClick={() => setShowPassword(!showPassword)}
                                                            >
                                                                  {showPassword ? (
                                                                        <EyeOff className="h-4 w-4 text-gray-400" />
                                                                  ) : (
                                                                        <Eye className="h-4 w-4 text-gray-400" />
                                                                  )}
                                                            </Button>
                                                      </div>
                                                      {form.formState.errors.password && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.password.message}</p>
                                                      )}
                                                </div>

                                                <div className="space-y-2">
                                                      <Label htmlFor="confirmPassword">Confirm Password *</Label>
                                                      <div className="relative">
                                                            <Input
                                                                  id="confirmPassword"
                                                                  type={showConfirmPassword ? 'text' : 'password'}
                                                                  {...form.register('confirmPassword')}
                                                                  placeholder="Confirm the password"
                                                                  className="pr-10"
                                                            />
                                                            <Button
                                                                  type="button"
                                                                  variant="ghost"
                                                                  size="sm"
                                                                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                                            >
                                                                  {showConfirmPassword ? (
                                                                        <EyeOff className="h-4 w-4 text-gray-400" />
                                                                  ) : (
                                                                        <Eye className="h-4 w-4 text-gray-400" />
                                                                  )}
                                                            </Button>
                                                      </div>
                                                      {form.formState.errors.confirmPassword && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.confirmPassword.message}</p>
                                                      )}
                                                </div>

                                                <div className="text-sm text-gray-600 mt-2">
                                                      <p className="font-medium mb-1">Password requirements:</p>
                                                      <ul className="space-y-1 text-xs">
                                                            <li>• At least 8 characters long</li>
                                                            <li>• Should include uppercase and lowercase letters</li>
                                                            <li>• Should include numbers and special characters</li>
                                                      </ul>
                                                </div>
                                          </CardContent>
                                    </Card>

                                    {/* Role & Permissions */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <Shield className="w-5 h-5 mr-2" />
                                                      Role & Permissions
                                                </CardTitle>
                                                <CardDescription>
                                                      Configure the user's role and access level
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="space-y-2">
                                                      <Label htmlFor="role">Role *</Label>
                                                      <Select
                                                            value={form.watch('role')}
                                                            onValueChange={(value) => form.setValue('role', value as any)}
                                                      >
                                                            <SelectTrigger>
                                                                  <SelectValue placeholder="Select a role" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                  <SelectItem value="admin">Administrator</SelectItem>
                                                                  <SelectItem value="editor">Editor</SelectItem>
                                                                  <SelectItem value="moderator">Moderator</SelectItem>
                                                            </SelectContent>
                                                      </Select>
                                                      {form.formState.errors.role && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.role.message}</p>
                                                      )}
                                                </div>

                                                <div className="space-y-4">
                                                      <Separator />
                                                      <div className="space-y-3">
                                                            <h4 className="text-sm font-medium">Role Permissions</h4>
                                                            <div className="text-sm text-gray-600">
                                                                  {form.watch('role') === 'admin' && (
                                                                        <ul className="space-y-1">
                                                                              <li>• Full system access</li>
                                                                              <li>• User management</li>
                                                                              <li>• System configuration</li>
                                                                              <li>• All content operations</li>
                                                                        </ul>
                                                                  )}
                                                                  {form.watch('role') === 'editor' && (
                                                                        <ul className="space-y-1">
                                                                              <li>• Content creation and editing</li>
                                                                              <li>• Media management</li>
                                                                              <li>• Limited user management</li>
                                                                        </ul>
                                                                  )}
                                                                  {form.watch('role') === 'moderator' && (
                                                                        <ul className="space-y-1">
                                                                              <li>• Content review and approval</li>
                                                                              <li>• User content moderation</li>
                                                                              <li>• Basic reporting access</li>
                                                                        </ul>
                                                                  )}
                                                            </div>
                                                      </div>
                                                </div>
                                          </CardContent>
                                    </Card>
                              </div>

                              {/* Sidebar */}
                              <div className="space-y-6">
                                    {/* Account Status */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Account Status</CardTitle>
                                                <CardDescription>
                                                      Configure the initial account status
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="flex items-center justify-between">
                                                      <Label htmlFor="isActive" className="text-sm font-medium">
                                                            Account Active
                                                      </Label>
                                                      <Switch
                                                            id="isActive"
                                                            checked={form.watch('isActive')}
                                                            onCheckedChange={(checked) => form.setValue('isActive', checked)}
                                                      />
                                                </div>
                                                <p className="text-xs text-gray-600">
                                                      Active accounts can log in immediately. Inactive accounts will need to be activated later.
                                                </p>
                                          </CardContent>
                                    </Card>

                                    {/* Instructions */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Instructions</CardTitle>
                                          </CardHeader>
                                          <CardContent className="space-y-3">
                                                <div className="text-sm text-gray-600">
                                                      <h4 className="font-medium mb-2">After creating the user:</h4>
                                                      <ul className="space-y-1 text-xs">
                                                            <li>• The user will receive an email with their login credentials</li>
                                                            <li>• They will be prompted to change their password on first login</li>
                                                            <li>• You can edit their details or deactivate the account anytime</li>
                                                            <li>• Role permissions take effect immediately</li>
                                                      </ul>
                                                </div>
                                          </CardContent>
                                    </Card>

                                    {/* Security Notice */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Security Notice</CardTitle>
                                          </CardHeader>
                                          <CardContent>
                                                <p className="text-xs text-amber-600">
                                                      ⚠️ Only create administrator accounts for trusted personnel. Admin users have full system access including the ability to modify other users and system settings.
                                                </p>
                                          </CardContent>
                                    </Card>
                              </div>
                        </div>
                  </form>
            </div>
      );
}
