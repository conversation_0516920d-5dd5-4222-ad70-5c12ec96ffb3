'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Save, User, Mail, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { systemUsersHooks } from '@/lib/hooks/useSystemUsers';
import { usePermissions } from '@/lib/middleware/auth-guard';

const systemUserSchema = z.object({
      username: z.string().min(3, 'Username must be at least 3 characters'),
      email: z.string().email('Invalid email address'),
      fullName: z.string().min(1, 'Full name is required'),
      role: z.enum(['admin', 'editor', 'moderator'], {
            required_error: 'Role is required',
      }),
      isActive: z.boolean(),
});

type SystemUserFormData = z.infer<typeof systemUserSchema>;

export default function SystemUserEditPage() {
      const params = useParams();
      const router = useRouter();
      const { toast } = useToast();
      const { canManageUsers } = usePermissions();
      const userId = params.id as string;

      const { data: user, isLoading, error } = systemUsersHooks.useGetById(userId);
      const { mutate: updateUser, isLoading: isUpdating } = systemUsersHooks.useUpdate();

      const [isFormDirty, setIsFormDirty] = useState(false);

      const form = useForm<SystemUserFormData>({
            resolver: zodResolver(systemUserSchema),
            defaultValues: {
                  username: '',
                  email: '',
                  fullName: '',
                  role: 'editor',
                  isActive: true,
            },
      });

      // Update form when user data is loaded
      useEffect(() => {
            if (user) {
                  form.reset({
                        username: user.username || '',
                        email: user.email || '',
                        fullName: user.fullName || '',
                        role: user.role || 'editor',
                        isActive: user.isActive,
                  });
            }
      }, [user, form]);

      // Track form changes
      useEffect(() => {
            const subscription = form.watch(() => {
                  setIsFormDirty(true);
            });
            return () => subscription.unsubscribe();
      }, [form]);

      const onSubmit = (data: SystemUserFormData) => {
            updateUser(
                  {
                        id: Number(userId),
                        data,
                  },
                  {
                        onSuccess: () => {
                              toast({
                                    title: 'User updated',
                                    description: 'System user has been successfully updated.',
                              });
                              setIsFormDirty(false);
                              router.push(`/dashboard/users/system/${userId}`);
                        },
                        onError: (error: any) => {
                              toast({
                                    title: 'Error',
                                    description: error?.message || 'Failed to update user.',
                                    variant: 'destructive',
                              });
                        },
                  }
            );
      };

      const handleCancel = () => {
            if (isFormDirty) {
                  if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
                        router.push(`/dashboard/users/system/${userId}`);
                  }
            } else {
                  router.push(`/dashboard/users/system/${userId}`);
            }
      };

      if (!canManageUsers()) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="text-center py-12">
                              <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
                              <p className="text-gray-600 mb-4">You don't have permission to edit users.</p>
                              <Button onClick={() => router.push('/dashboard/users/system')}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Users
                              </Button>
                        </div>
                  </div>
            );
      }

      if (isLoading) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="animate-pulse space-y-6">
                              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                              <div className="space-y-6">
                                    <div className="h-64 bg-gray-200 rounded"></div>
                                    <div className="h-48 bg-gray-200 rounded"></div>
                              </div>
                        </div>
                  </div>
            );
      }

      if (error || !user) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="text-center py-12">
                              <h2 className="text-2xl font-bold text-gray-900 mb-2">User Not Found</h2>
                              <p className="text-gray-600 mb-4">The requested user could not be found.</p>
                              <Button onClick={() => router.push('/dashboard/users/system')}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Users
                              </Button>
                        </div>
                  </div>
            );
      }

      return (
            <div className="container mx-auto p-6 space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                              <Button variant="ghost" size="sm" onClick={handleCancel}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>
                              <div>
                                    <h1 className="text-3xl font-bold text-gray-900">Edit User</h1>
                                    <p className="text-gray-600">{user.email}</p>
                              </div>
                        </div>

                        <div className="flex items-center space-x-2">
                              <Button variant="outline" onClick={handleCancel}>
                                    Cancel
                              </Button>
                              <Button
                                    onClick={form.handleSubmit(onSubmit)}
                                    disabled={isUpdating || !isFormDirty}
                              >
                                    <Save className="w-4 h-4 mr-2" />
                                    {isUpdating ? 'Saving...' : 'Save Changes'}
                              </Button>
                        </div>
                  </div>

                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                              {/* Main Form */}
                              <div className="lg:col-span-2 space-y-6">
                                    {/* Basic Information */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <User className="w-5 h-5 mr-2" />
                                                      Basic Information
                                                </CardTitle>
                                                <CardDescription>
                                                      Update the user's personal information
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="space-y-2">
                                                      <Label htmlFor="fullName">Full Name *</Label>
                                                      <Input
                                                            id="fullName"
                                                            {...form.register('fullName')}
                                                      />
                                                      {form.formState.errors.fullName && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.fullName.message}</p>
                                                      )}
                                                </div>

                                                <div className="space-y-2">
                                                      <Label htmlFor="username">Username *</Label>
                                                      <Input
                                                            id="username"
                                                            {...form.register('username')}
                                                      />
                                                      {form.formState.errors.username && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.username.message}</p>
                                                      )}
                                                </div>

                                                <div className="space-y-2">
                                                      <Label htmlFor="email">Email Address *</Label>
                                                      <Input
                                                            id="email"
                                                            type="email"
                                                            {...form.register('email')}
                                                      />
                                                      {form.formState.errors.email && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.email.message}</p>
                                                      )}
                                                </div>
                                          </CardContent>
                                    </Card>

                                    {/* Role & Permissions */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <Shield className="w-5 h-5 mr-2" />
                                                      Role & Permissions
                                                </CardTitle>
                                                <CardDescription>
                                                      Configure the user's role and access level
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="space-y-2">
                                                      <Label htmlFor="role">Role *</Label>
                                                      <Select
                                                            value={form.watch('role')}
                                                            onValueChange={(value) => form.setValue('role', value as any)}
                                                      >
                                                            <SelectTrigger>
                                                                  <SelectValue placeholder="Select a role" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                  <SelectItem value="admin">Administrator</SelectItem>
                                                                  <SelectItem value="editor">Editor</SelectItem>
                                                                  <SelectItem value="moderator">Moderator</SelectItem>
                                                            </SelectContent>
                                                      </Select>
                                                      {form.formState.errors.role && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.role.message}</p>
                                                      )}
                                                </div>

                                                <div className="space-y-4">
                                                      <Separator />
                                                      <div className="space-y-3">
                                                            <h4 className="text-sm font-medium">Role Permissions</h4>
                                                            <div className="text-sm text-gray-600">
                                                                  {form.watch('role') === 'admin' && (
                                                                        <ul className="space-y-1">
                                                                              <li>• Full system access</li>
                                                                              <li>• User management</li>
                                                                              <li>• System configuration</li>
                                                                              <li>• All content operations</li>
                                                                        </ul>
                                                                  )}
                                                                  {form.watch('role') === 'editor' && (
                                                                        <ul className="space-y-1">
                                                                              <li>• Content creation and editing</li>
                                                                              <li>• Media management</li>
                                                                              <li>• Limited user management</li>
                                                                        </ul>
                                                                  )}
                                                                  {form.watch('role') === 'moderator' && (
                                                                        <ul className="space-y-1">
                                                                              <li>• Content review and approval</li>
                                                                              <li>• User content moderation</li>
                                                                              <li>• Basic reporting access</li>
                                                                        </ul>
                                                                  )}
                                                            </div>
                                                      </div>
                                                </div>
                                          </CardContent>
                                    </Card>
                              </div>

                              {/* Sidebar */}
                              <div className="space-y-6">
                                    {/* Account Status */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Account Status</CardTitle>
                                                <CardDescription>
                                                      Manage the user's account status
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="flex items-center justify-between">
                                                      <Label htmlFor="isActive" className="text-sm font-medium">
                                                            Account Active
                                                      </Label>
                                                      <Switch
                                                            id="isActive"
                                                            checked={form.watch('isActive')}
                                                            onCheckedChange={(checked) => form.setValue('isActive', checked)}
                                                      />
                                                </div>
                                          </CardContent>
                                    </Card>

                                    {/* Account Information */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Account Information</CardTitle>
                                          </CardHeader>
                                          <CardContent className="space-y-3">
                                                <div className="flex justify-between text-sm">
                                                      <span className="text-gray-600">User ID</span>
                                                      <span className="font-medium">{user.id}</span>
                                                </div>
                                                <div className="flex justify-between text-sm">
                                                      <span className="text-gray-600">Created</span>
                                                      <span className="font-medium">
                                                            {new Date(user.createdAt).toLocaleDateString()}
                                                      </span>
                                                </div>
                                                <div className="flex justify-between text-sm">
                                                      <span className="text-gray-600">Last Updated</span>
                                                      <span className="font-medium">
                                                            {new Date(user.updatedAt).toLocaleDateString()}
                                                      </span>
                                                </div>
                                                {user.lastLoginAt && (
                                                      <div className="flex justify-between text-sm">
                                                            <span className="text-gray-600">Last Login</span>
                                                            <span className="font-medium">
                                                                  {new Date(user.lastLoginAt).toLocaleDateString()}
                                                            </span>
                                                      </div>
                                                )}
                                          </CardContent>
                                    </Card>

                                    {/* Security Actions */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Security Actions</CardTitle>
                                          </CardHeader>
                                          <CardContent className="space-y-2">
                                                <Button
                                                      type="button"
                                                      variant="outline"
                                                      size="sm"
                                                      className="w-full justify-start"
                                                      onClick={() => {
                                                            // TODO: Implement password reset
                                                            toast({
                                                                  title: 'Coming Soon',
                                                                  description: 'Password reset functionality will be implemented.',
                                                            });
                                                      }}
                                                >
                                                      Reset Password
                                                </Button>
                                                <Button
                                                      type="button"
                                                      variant="outline"
                                                      size="sm"
                                                      className="w-full justify-start"
                                                      onClick={() => {
                                                            // TODO: Implement force logout
                                                            toast({
                                                                  title: 'Coming Soon',
                                                                  description: 'Force logout functionality will be implemented.',
                                                            });
                                                      }}
                                                >
                                                      Force Logout
                                                </Button>
                                          </CardContent>
                                    </Card>
                              </div>
                        </div>
                  </form>
            </div>
      );
}
