'use client';

import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft, Edit, Trash2, Shield, Key, Clock, Mail, Phone, MapPin, Calendar, Activity } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { useSystemUsers, systemUsersHooks } from '@/lib/hooks/useSystemUsers';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { SystemUser } from '@/lib/types/api';

export default function SystemUserDetailPage() {
      const params = useParams();
      const router = useRouter();
      const { toast } = useToast();
      const { canManageUsers } = usePermissions();
      const userId = params.id as string;

      const { data: user, isLoading, error } = systemUsersHooks.useGetById(userId);
      const { mutate: deleteUser, isLoading: isDeleting } = systemUsersHooks.useDelete();
      const { data: activityLogs = [] } = systemUsersHooks.useActivityLogs(userId);

      const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

      const handleDeleteUser = () => {
            deleteUser(Number(userId), {
                  onSuccess: () => {
                        toast({
                              title: 'User deleted',
                              description: 'System user has been successfully deleted.',
                        });
                        router.push('/dashboard/users/system');
                  },
                  onError: (error: any) => {
                        toast({
                              title: 'Error',
                              description: error?.message || 'Failed to delete user.',
                              variant: 'destructive',
                        });
                  },
            });
      };

      const getRoleBadgeVariant = (role: string) => {
            switch (role) {
                  case 'admin':
                        return 'destructive';
                  case 'editor':
                        return 'default';
                  case 'moderator':
                        return 'secondary';
                  default:
                        return 'outline';
            }
      };

      const getStatusBadgeVariant = (status: string) => {
            switch (status) {
                  case 'active':
                        return 'default';
                  case 'inactive':
                        return 'secondary';
                  case 'suspended':
                        return 'destructive';
                  default:
                        return 'outline';
            }
      };

      if (isLoading) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="animate-pulse space-y-6">
                              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    <div className="lg:col-span-2 space-y-6">
                                          <div className="h-64 bg-gray-200 rounded"></div>
                                          <div className="h-48 bg-gray-200 rounded"></div>
                                    </div>
                                    <div className="space-y-6">
                                          <div className="h-32 bg-gray-200 rounded"></div>
                                          <div className="h-48 bg-gray-200 rounded"></div>
                                    </div>
                              </div>
                        </div>
                  </div>
            );
      }

      if (error || !user) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="text-center py-12">
                              <h2 className="text-2xl font-bold text-gray-900 mb-2">User Not Found</h2>
                              <p className="text-gray-600 mb-4">The requested user could not be found.</p>
                              <Button onClick={() => router.push('/dashboard/users/system')}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Users
                              </Button>
                        </div>
                  </div>
            );
      }

      return (
            <div className="container mx-auto p-6 space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                              <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => router.push('/dashboard/users/system')}
                              >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>
                              <div>
                                    <h1 className="text-3xl font-bold text-gray-900">{user.fullName || user.username}</h1>
                                    <p className="text-gray-600">{user.email}</p>
                              </div>
                        </div>

                        <div className="flex items-center space-x-2">
                              {canManageUsers() && (
                                    <Button
                                          variant="outline"
                                          onClick={() => router.push(`/dashboard/users/system/${userId}/edit`)}
                                    >
                                          <Edit className="w-4 h-4 mr-2" />
                                          Edit
                                    </Button>
                              )}

                              {canManageUsers() && (
                                    <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                                          <AlertDialogTrigger asChild>
                                                <Button variant="destructive">
                                                      <Trash2 className="w-4 h-4 mr-2" />
                                                      Delete
                                                </Button>
                                          </AlertDialogTrigger>
                                          <AlertDialogContent>
                                                <AlertDialogHeader>
                                                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                      <AlertDialogDescription>
                                                            This action cannot be undone. This will permanently delete the user account
                                                            and remove all associated data.
                                                      </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                      <AlertDialogAction
                                                            onClick={handleDeleteUser}
                                                            className="bg-red-600 hover:bg-red-700"
                                                            disabled={isDeleting}
                                                      >
                                                            {isDeleting ? 'Deleting...' : 'Delete User'}
                                                      </AlertDialogAction>
                                                </AlertDialogFooter>
                                          </AlertDialogContent>
                                    </AlertDialog>
                              )}
                        </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Main Content */}
                        <div className="lg:col-span-2">
                              <Tabs defaultValue="overview" className="space-y-6">
                                    <TabsList>
                                          <TabsTrigger value="overview">Overview</TabsTrigger>
                                          <TabsTrigger value="permissions">Permissions</TabsTrigger>
                                          <TabsTrigger value="activity">Activity</TabsTrigger>
                                    </TabsList>

                                    <TabsContent value="overview" className="space-y-6">
                                          {/* Profile Information */}
                                          <Card>
                                                <CardHeader>
                                                      <CardTitle className="flex items-center">
                                                            <Shield className="w-5 h-5 mr-2" />
                                                            Profile Information
                                                      </CardTitle>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                      <div className="flex items-center space-x-4">
                                                            <Avatar className="w-16 h-16">
                                                                  <AvatarFallback>
                                                                        {user.fullName ? user.fullName.charAt(0) : user.username.charAt(0)}
                                                                  </AvatarFallback>
                                                            </Avatar>
                                                            <div className="space-y-2">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Badge variant={getRoleBadgeVariant(user.role)}>
                                                                              {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                                                                        </Badge>
                                                                        <Badge variant={user.isActive ? 'default' : 'secondary'}>
                                                                              {user.isActive ? 'Active' : 'Inactive'}
                                                                        </Badge>
                                                                  </div>
                                                                  <p className="text-sm text-gray-600">
                                                                        ID: {user.id}
                                                                  </p>
                                                            </div>
                                                      </div>

                                                      <Separator />

                                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div className="space-y-3">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Mail className="w-4 h-4 text-gray-500" />
                                                                        <span className="text-sm font-medium">Email</span>
                                                                  </div>
                                                                  <p className="text-sm text-gray-600 ml-6">{user.email}</p>
                                                            </div>

                                                            <div className="space-y-3">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Shield className="w-4 h-4 text-gray-500" />
                                                                        <span className="text-sm font-medium">Username</span>
                                                                  </div>
                                                                  <p className="text-sm text-gray-600 ml-6">{user.username}</p>
                                                            </div>

                                                            <div className="space-y-3">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Calendar className="w-4 h-4 text-gray-500" />
                                                                        <span className="text-sm font-medium">Last Login</span>
                                                                  </div>
                                                                  <p className="text-sm text-gray-600 ml-6">
                                                                        {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : 'Never'}
                                                                  </p>
                                                            </div>
                                                      </div>
                                                </CardContent>
                                          </Card>

                                          {/* Account Details */}
                                          <Card>
                                                <CardHeader>
                                                      <CardTitle className="flex items-center">
                                                            <Key className="w-5 h-5 mr-2" />
                                                            Account Details
                                                      </CardTitle>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div className="space-y-3">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Calendar className="w-4 h-4 text-gray-500" />
                                                                        <span className="text-sm font-medium">Created At</span>
                                                                  </div>
                                                                  <p className="text-sm text-gray-600 ml-6">
                                                                        {new Date(user.createdAt).toLocaleString()}
                                                                  </p>
                                                            </div>

                                                            <div className="space-y-3">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Calendar className="w-4 h-4 text-gray-500" />
                                                                        <span className="text-sm font-medium">Updated At</span>
                                                                  </div>
                                                                  <p className="text-sm text-gray-600 ml-6">
                                                                        {new Date(user.updatedAt).toLocaleString()}
                                                                  </p>
                                                            </div>
                                                      </div>
                                                </CardContent>
                                          </Card>
                                    </TabsContent>

                                    <TabsContent value="permissions" className="space-y-6">
                                          <Card>
                                                <CardHeader>
                                                      <CardTitle>Role Permissions</CardTitle>
                                                      <CardDescription>
                                                            Permissions granted to this user based on their role: {user.role}
                                                      </CardDescription>
                                                </CardHeader>
                                                <CardContent>
                                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div className="flex items-center space-x-2">
                                                                  <Badge variant="outline">
                                                                        {user.role === 'admin' ? 'Full System Access' :
                                                                              user.role === 'editor' ? 'Content Management' :
                                                                                    'Content Moderation'}
                                                                  </Badge>
                                                            </div>
                                                      </div>
                                                </CardContent>
                                          </Card>
                                    </TabsContent>

                                    <TabsContent value="activity" className="space-y-6">
                                          <Card>
                                                <CardHeader>
                                                      <CardTitle className="flex items-center">
                                                            <Activity className="w-5 h-5 mr-2" />
                                                            Recent Activity
                                                      </CardTitle>
                                                      <CardDescription>
                                                            Latest actions performed by this user
                                                      </CardDescription>
                                                </CardHeader>
                                                <CardContent>
                                                      <div className="space-y-4">
                                                            {activityLogs.length > 0 ? (
                                                                  activityLogs.map((log) => (
                                                                        <div key={log.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                                                                              <div className="flex-shrink-0">
                                                                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                                              </div>
                                                                              <div className="flex-1">
                                                                                    <p className="text-sm font-medium">{log.action}</p>
                                                                                    <p className="text-xs text-gray-500">
                                                                                          {new Date(log.timestamp).toLocaleString()}
                                                                                    </p>
                                                                                    <p className="text-xs text-gray-400">{log.details}</p>
                                                                              </div>
                                                                        </div>
                                                                  ))
                                                            ) : (
                                                                  <p className="text-gray-500 italic text-center py-4">
                                                                        No activity logs available
                                                                  </p>
                                                            )}
                                                      </div>
                                                </CardContent>
                                          </Card>
                                    </TabsContent>
                              </Tabs>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                              {/* Quick Stats */}
                              <Card>
                                    <CardHeader>
                                          <CardTitle className="flex items-center">
                                                <Clock className="w-5 h-5 mr-2" />
                                                Quick Stats
                                          </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                          <div className="flex justify-between items-center">
                                                <span className="text-sm text-gray-600">Days Since Created</span>
                                                <span className="font-medium">
                                                      {Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
                                                </span>
                                          </div>
                                          {user.lastLoginAt && (
                                                <div className="flex justify-between items-center">
                                                      <span className="text-sm text-gray-600">Days Since Last Login</span>
                                                      <span className="font-medium">
                                                            {Math.floor((Date.now() - new Date(user.lastLoginAt).getTime()) / (1000 * 60 * 60 * 24))}
                                                      </span>
                                                </div>
                                          )}
                                    </CardContent>
                              </Card>

                              {/* Quick Actions */}
                              {canManageUsers() && (
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Quick Actions</CardTitle>
                                          </CardHeader>
                                          <CardContent className="space-y-2">
                                                <Button
                                                      variant="outline"
                                                      size="sm"
                                                      className="w-full justify-start"
                                                      onClick={() => router.push(`/dashboard/users/system/${userId}/edit`)}
                                                >
                                                      <Edit className="w-4 h-4 mr-2" />
                                                      Edit Profile
                                                </Button>
                                                <Button
                                                      variant="outline"
                                                      size="sm"
                                                      className="w-full justify-start"
                                                      onClick={() => {
                                                            // TODO: Implement reset password
                                                            toast({
                                                                  title: 'Coming Soon',
                                                                  description: 'Password reset functionality will be implemented.',
                                                            });
                                                      }}
                                                >
                                                      <Key className="w-4 h-4 mr-2" />
                                                      Reset Password
                                                </Button>
                                          </CardContent>
                                    </Card>
                              )}
                        </div>
                  </div>
            </div>
      );
}
