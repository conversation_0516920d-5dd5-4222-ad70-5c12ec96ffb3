'use client';

import { useState } from 'react';
import { useSystemUsers, useSystemUserStats, useSystemUserMutations } from '@/lib/hooks/useSystemUsers';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { DataTable } from '@/components/ui/data-table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { UserPlus, Search, MoreHorizontal, Edit, Trash2, UserCheck, UserX, Shield, Users, Clock, Activity } from 'lucide-react';
import { SystemUser } from '@/lib/types/api';
import { Column } from '@/components/ui/data-table';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';

export default function SystemUsersPage() {
      const { canManageUsers } = usePermissions();
      const [filters, setFilters] = useState({
            search: '',
            role: undefined as 'admin' | 'editor' | 'moderator' | undefined,
            isActive: undefined as boolean | undefined,
            page: 1,
            limit: 10,
      });

      const { data: usersData, isLoading, error } = useSystemUsers(filters);
      const { data: stats } = useSystemUserStats();
      const { deleteUser, toggleUserStatus } = useSystemUserMutations();

      const handleSearchChange = (value: string) => {
            setFilters(prev => ({ ...prev, search: value, page: 1 }));
      };

      const handleRoleFilter = (role: string) => {
            setFilters(prev => ({
                  ...prev,
                  role: role === 'all' ? undefined : (role as 'admin' | 'editor' | 'moderator'),
                  page: 1
            }));
      };

      const handleStatusFilter = (status: string) => {
            setFilters(prev => ({
                  ...prev,
                  isActive: status === 'all' ? undefined : status === 'active',
                  page: 1
            }));
      };

      const getRoleColor = (role: string) => {
            switch (role) {
                  case 'admin':
                        return 'bg-red-100 text-red-800 border-red-200';
                  case 'editor':
                        return 'bg-blue-100 text-blue-800 border-blue-200';
                  case 'moderator':
                        return 'bg-green-100 text-green-800 border-green-200';
                  default:
                        return 'bg-gray-100 text-gray-800 border-gray-200';
            }
      };

      const getRoleIcon = (role: string) => {
            switch (role) {
                  case 'admin':
                        return <Shield className="h-3 w-3" />;
                  case 'editor':
                        return <Edit className="h-3 w-3" />;
                  case 'moderator':
                        return <UserCheck className="h-3 w-3" />;
                  default:
                        return <Users className="h-3 w-3" />;
            }
      };

      const columns: Column<SystemUser>[] = [
            {
                  key: 'fullName',
                  title: 'User',
                  render: (value, user) => {
                        return (
                              <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-sm font-medium">
                                          {user.fullName?.charAt(0) || user.username.charAt(0).toUpperCase()}
                                    </div>
                                    <div>
                                          <div className="font-medium">{user.fullName || user.username}</div>
                                          <div className="text-sm text-gray-500">@{user.username}</div>
                                    </div>
                              </div>
                        );
                  },
            },
            {
                  key: 'email',
                  title: 'Email',
                  render: (value) => (
                        <div className="font-mono text-sm">{value}</div>
                  ),
            },
            {
                  key: 'role',
                  title: 'Role',
                  render: (role) => {
                        return (
                              <Badge className={`${getRoleColor(role as string)} flex items-center space-x-1 w-fit`}>
                                    {getRoleIcon(role as string)}
                                    <span className="capitalize">{role}</span>
                              </Badge>
                        );
                  },
            },
            {
                  key: 'isActive',
                  title: 'Status',
                  render: (isActive) => {
                        return (
                              <Badge variant={isActive ? 'default' : 'secondary'} className="flex items-center space-x-1 w-fit">
                                    {isActive ? <UserCheck className="h-3 w-3" /> : <UserX className="h-3 w-3" />}
                                    <span>{isActive ? 'Active' : 'Inactive'}</span>
                              </Badge>
                        );
                  },
            },
            {
                  key: 'lastLoginAt',
                  title: 'Last Login',
                  render: (lastLogin) => {
                        if (!lastLogin) return <span className="text-gray-400">Never</span>;
                        return (
                              <div className="flex items-center space-x-1 text-sm">
                                    <Clock className="h-3 w-3 text-gray-400" />
                                    <span>{formatDistanceToNow(new Date(lastLogin as string), { addSuffix: true })}</span>
                              </div>
                        );
                  },
            },
            {
                  key: 'actions',
                  title: 'Actions',
                  render: (value, user) => {

                        if (!canManageUsers()) {
                              return null;
                        }

                        return (
                              <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                          <Button variant="ghost" className="h-8 w-8 p-0">
                                                <span className="sr-only">Open menu</span>
                                                <MoreHorizontal className="h-4 w-4" />
                                          </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                          <DropdownMenuSeparator />
                                          <DropdownMenuItem asChild>
                                                <Link href={`/dashboard/users/system/${user.id}`}>
                                                      <UserCheck className="mr-2 h-4 w-4" />
                                                      View Details
                                                </Link>
                                          </DropdownMenuItem>
                                          <DropdownMenuItem asChild>
                                                <Link href={`/dashboard/users/system/${user.id}/edit`}>
                                                      <Edit className="mr-2 h-4 w-4" />
                                                      Edit User
                                                </Link>
                                          </DropdownMenuItem>
                                          <DropdownMenuSeparator />
                                          <DropdownMenuItem
                                                onClick={() => toggleUserStatus.mutate({ userId: user.id, isActive: !user.isActive })}
                                          >
                                                {user.isActive ? (
                                                      <>
                                                            <UserX className="mr-2 h-4 w-4" />
                                                            Deactivate
                                                      </>
                                                ) : (
                                                      <>
                                                            <UserCheck className="mr-2 h-4 w-4" />
                                                            Activate
                                                      </>
                                                )}
                                          </DropdownMenuItem>
                                          <AlertDialog>
                                                <AlertDialogTrigger asChild>
                                                      <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                                            <Trash2 className="mr-2 h-4 w-4" />
                                                            Delete User
                                                      </DropdownMenuItem>
                                                </AlertDialogTrigger>
                                                <AlertDialogContent>
                                                      <AlertDialogHeader>
                                                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                  This action cannot be undone. This will permanently delete the user account
                                                                  for <strong>{user.fullName || user.username}</strong> and remove all associated data.
                                                            </AlertDialogDescription>
                                                      </AlertDialogHeader>
                                                      <AlertDialogFooter>
                                                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                            <AlertDialogAction
                                                                  onClick={() => deleteUser.mutate(user.id)}
                                                                  className="bg-red-600 hover:bg-red-700"
                                                            >
                                                                  Delete User
                                                            </AlertDialogAction>
                                                      </AlertDialogFooter>
                                                </AlertDialogContent>
                                          </AlertDialog>
                                    </DropdownMenuContent>
                              </DropdownMenu>
                        );
                  },
            },
      ];

      if (!canManageUsers()) {
            return (
                  <div className="flex items-center justify-center h-96">
                        <div className="text-center">
                              <Shield className="mx-auto h-12 w-12 text-gray-400" />
                              <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
                              <p className="mt-1 text-sm text-gray-500">You don't have permission to view system users.</p>
                        </div>
                  </div>
            );
      }

      return (
            <div className="space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div>
                              <h1 className="text-2xl font-bold text-gray-900">System Users</h1>
                              <p className="text-gray-600">Manage administrator, editor, and moderator accounts</p>
                        </div>
                        <Button asChild>
                              <Link href="/dashboard/users/system/create">
                                    <UserPlus className="mr-2 h-4 w-4" />
                                    Add System User
                              </Link>
                        </Button>
                  </div>

                  {/* Statistics Cards */}
                  {stats && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                              <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                          <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                                          <Users className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                          <div className="text-2xl font-bold">{stats.total}</div>
                                          <p className="text-xs text-muted-foreground">
                                                {stats.active} active, {stats.inactive} inactive
                                          </p>
                                    </CardContent>
                              </Card>
                              <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                          <CardTitle className="text-sm font-medium">Administrators</CardTitle>
                                          <Shield className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                          <div className="text-2xl font-bold">{stats.byRole.admin}</div>
                                          <p className="text-xs text-muted-foreground">Full system access</p>
                                    </CardContent>
                              </Card>
                              <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                          <CardTitle className="text-sm font-medium">Editors</CardTitle>
                                          <Edit className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                          <div className="text-2xl font-bold">{stats.byRole.editor}</div>
                                          <p className="text-xs text-muted-foreground">Content management</p>
                                    </CardContent>
                              </Card>
                              <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                          <CardTitle className="text-sm font-medium">Recent Logins</CardTitle>
                                          <Activity className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                          <div className="text-2xl font-bold">{stats.recentLogins}</div>
                                          <p className="text-xs text-muted-foreground">Last 24 hours</p>
                                    </CardContent>
                              </Card>
                        </div>
                  )}

                  {/* Filters */}
                  <Card>
                        <CardHeader>
                              <CardTitle>Filters</CardTitle>
                              <CardDescription>Search and filter system users</CardDescription>
                        </CardHeader>
                        <CardContent>
                              <div className="flex flex-col md:flex-row gap-4">
                                    <div className="flex-1">
                                          <div className="relative">
                                                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                                <Input
                                                      placeholder="Search by name, username, or email..."
                                                      value={filters.search}
                                                      onChange={(e) => handleSearchChange(e.target.value)}
                                                      className="pl-10"
                                                />
                                          </div>
                                    </div>
                                    <Select value={filters.role} onValueChange={handleRoleFilter}>
                                          <SelectTrigger className="w-full md:w-[180px]">
                                                <SelectValue placeholder="Filter by role" />
                                          </SelectTrigger>
                                          <SelectContent>
                                                <SelectItem value="all">All Roles</SelectItem>
                                                <SelectItem value="admin">Administrator</SelectItem>
                                                <SelectItem value="editor">Editor</SelectItem>
                                                <SelectItem value="moderator">Moderator</SelectItem>
                                          </SelectContent>
                                    </Select>
                                    <Select
                                          value={filters.isActive === undefined ? 'all' : filters.isActive ? 'active' : 'inactive'}
                                          onValueChange={handleStatusFilter}
                                    >
                                          <SelectTrigger className="w-full md:w-[180px]">
                                                <SelectValue placeholder="Filter by status" />
                                          </SelectTrigger>
                                          <SelectContent>
                                                <SelectItem value="all">All Status</SelectItem>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="inactive">Inactive</SelectItem>
                                          </SelectContent>
                                    </Select>
                              </div>
                        </CardContent>
                  </Card>

                  {/* Users Table */}
                  <Card>
                        <CardHeader>
                              <CardTitle>System Users</CardTitle>
                              <CardDescription>
                                    {usersData?.meta && (
                                          `Showing ${usersData.data.length} of ${usersData.meta.total} users`
                                    )}
                              </CardDescription>
                        </CardHeader>
                        <CardContent>
                              <DataTable
                                    columns={columns}
                                    data={usersData?.data || []}
                                    loading={isLoading}
                                    pagination={{
                                          page: filters.page,
                                          limit: filters.limit,
                                          total: usersData?.meta?.total || 0,
                                          onPageChange: (page) => setFilters(prev => ({ ...prev, page })),
                                          onLimitChange: (limit) => setFilters(prev => ({ ...prev, limit, page: 1 })),
                                    }}
                              />
                        </CardContent>
                  </Card>
            </div>
      );
}
