'use client';

import { useState } from 'react';
import { Calendar, Users, TrendingUp, DollarSign, Activity, BarChart3, <PERSON><PERSON>hart, Target, ArrowUpRight, ArrowDownRight, Minus } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { useRegisteredUsers } from '@/lib/hooks/useRegisteredUsers';

export default function UserTiersPage() {
      const [dateRange, setDateRange] = useState('30d');

      const { data: tierStats, isLoading: isLoadingStats } = useRegisteredUsers.useTierStats(dateRange);
      const { data: revenueStats, isLoading: isLoadingRevenue } = useRegisteredUsers.useRevenueStats(dateRange);
      const { data: tierMigration, isLoading: isLoadingMigration } = useRegisteredUsers.useTierMigration(dateRange);

      const tierColors = {
            free: '#6B7280',
            premium: '#3B82F6',
            enterprise: '#10B981',
      };

      const formatCurrency = (amount: number) => {
            return new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'USD',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0,
            }).format(amount);
      };

      const formatPercentage = (value: number) => {
            return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
      };

      const getTrendIcon = (change: number) => {
            if (change > 0) return <ArrowUpRight className="w-4 h-4 text-green-500" />;
            if (change < 0) return <ArrowDownRight className="w-4 h-4 text-red-500" />;
            return <Minus className="w-4 h-4 text-gray-500" />;
      };

      const getTrendColor = (change: number) => {
            if (change > 0) return 'text-green-600';
            if (change < 0) return 'text-red-600';
            return 'text-gray-600';
      };

      if (isLoadingStats || isLoadingRevenue || isLoadingMigration) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="animate-pulse space-y-6">
                              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                    {[1, 2, 3, 4].map((i) => (
                                          <div key={i} className="h-32 bg-gray-200 rounded"></div>
                                    ))}
                              </div>
                              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <div className="h-64 bg-gray-200 rounded"></div>
                                    <div className="h-64 bg-gray-200 rounded"></div>
                              </div>
                        </div>
                  </div>
            );
      }

      return (
            <div className="container mx-auto p-6 space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div>
                              <h1 className="text-3xl font-bold text-gray-900">User Tiers Analytics</h1>
                              <p className="text-gray-600">Monitor subscription tiers, revenue, and user migrations</p>
                        </div>

                        <div className="flex items-center space-x-2">
                              <Select value={dateRange} onValueChange={setDateRange}>
                                    <SelectTrigger className="w-32">
                                          <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                          <SelectItem value="7d">Last 7 days</SelectItem>
                                          <SelectItem value="30d">Last 30 days</SelectItem>
                                          <SelectItem value="90d">Last 90 days</SelectItem>
                                          <SelectItem value="1y">Last year</SelectItem>
                                    </SelectContent>
                              </Select>
                        </div>
                  </div>

                  {/* Overview Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-sm font-medium text-gray-600">Total Users</p>
                                                <p className="text-2xl font-bold">{tierStats?.totalUsers?.toLocaleString() || 0}</p>
                                          </div>
                                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                                <Users className="w-6 h-6 text-blue-600" />
                                          </div>
                                    </div>
                                    <div className="flex items-center mt-2">
                                          {getTrendIcon(tierStats?.totalUsersChange || 0)}
                                          <span className={`text-sm ml-1 ${getTrendColor(tierStats?.totalUsersChange || 0)}`}>
                                                {formatPercentage(tierStats?.totalUsersChange || 0)}
                                          </span>
                                          <span className="text-sm text-gray-500 ml-1">vs last period</span>
                                    </div>
                              </CardContent>
                        </Card>

                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                                                <p className="text-2xl font-bold">{formatCurrency(revenueStats?.monthlyRevenue || 0)}</p>
                                          </div>
                                          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                                <DollarSign className="w-6 h-6 text-green-600" />
                                          </div>
                                    </div>
                                    <div className="flex items-center mt-2">
                                          {getTrendIcon(revenueStats?.revenueChange || 0)}
                                          <span className={`text-sm ml-1 ${getTrendColor(revenueStats?.revenueChange || 0)}`}>
                                                {formatPercentage(revenueStats?.revenueChange || 0)}
                                          </span>
                                          <span className="text-sm text-gray-500 ml-1">vs last period</span>
                                    </div>
                              </CardContent>
                        </Card>

                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-sm font-medium text-gray-600">Avg Revenue per User</p>
                                                <p className="text-2xl font-bold">{formatCurrency(revenueStats?.arpu || 0)}</p>
                                          </div>
                                          <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                                <Target className="w-6 h-6 text-purple-600" />
                                          </div>
                                    </div>
                                    <div className="flex items-center mt-2">
                                          {getTrendIcon(revenueStats?.arpuChange || 0)}
                                          <span className={`text-sm ml-1 ${getTrendColor(revenueStats?.arpuChange || 0)}`}>
                                                {formatPercentage(revenueStats?.arpuChange || 0)}
                                          </span>
                                          <span className="text-sm text-gray-500 ml-1">vs last period</span>
                                    </div>
                              </CardContent>
                        </Card>

                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                                                <p className="text-2xl font-bold">{(tierStats?.conversionRate || 0).toFixed(1)}%</p>
                                          </div>
                                          <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                                                <TrendingUp className="w-6 h-6 text-yellow-600" />
                                          </div>
                                    </div>
                                    <div className="flex items-center mt-2">
                                          {getTrendIcon(tierStats?.conversionRateChange || 0)}
                                          <span className={`text-sm ml-1 ${getTrendColor(tierStats?.conversionRateChange || 0)}`}>
                                                {formatPercentage(tierStats?.conversionRateChange || 0)}
                                          </span>
                                          <span className="text-sm text-gray-500 ml-1">vs last period</span>
                                    </div>
                              </CardContent>
                        </Card>
                  </div>

                  {/* Main Content */}
                  <Tabs defaultValue="overview" className="space-y-6">
                        <TabsList>
                              <TabsTrigger value="overview">Overview</TabsTrigger>
                              <TabsTrigger value="distribution">Distribution</TabsTrigger>
                              <TabsTrigger value="migration">Migration</TabsTrigger>
                              <TabsTrigger value="revenue">Revenue</TabsTrigger>
                        </TabsList>

                        <TabsContent value="overview" className="space-y-6">
                              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    {/* Tier Distribution */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <PieChart className="w-5 h-5 mr-2" />
                                                      Tier Distribution
                                                </CardTitle>
                                                <CardDescription>Current user distribution across tiers</CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                {tierStats?.tierDistribution?.map((tier) => (
                                                      <div key={tier.tier} className="space-y-2">
                                                            <div className="flex items-center justify-between">
                                                                  <div className="flex items-center space-x-2">
                                                                        <div
                                                                              className="w-3 h-3 rounded-full"
                                                                              style={{ backgroundColor: tierColors[tier.tier as keyof typeof tierColors] }}
                                                                        />
                                                                        <span className="text-sm font-medium capitalize">{tier.tier}</span>
                                                                        <Badge variant="outline">{tier.count.toLocaleString()} users</Badge>
                                                                  </div>
                                                                  <span className="text-sm text-gray-600">{tier.percentage.toFixed(1)}%</span>
                                                            </div>
                                                            <Progress value={tier.percentage} className="h-2" />
                                                      </div>
                                                )) || (
                                                            <p className="text-gray-500 italic text-center py-4">No data available</p>
                                                      )}
                                          </CardContent>
                                    </Card>

                                    {/* Growth Trends */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <BarChart3 className="w-5 h-5 mr-2" />
                                                      Growth Trends
                                                </CardTitle>
                                                <CardDescription>User growth by tier over time</CardDescription>
                                          </CardHeader>
                                          <CardContent>
                                                <div className="space-y-4">
                                                      {tierStats?.growthTrends?.map((trend) => (
                                                            <div key={trend.tier} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                                  <div className="flex items-center space-x-3">
                                                                        <div
                                                                              className="w-3 h-3 rounded-full"
                                                                              style={{ backgroundColor: tierColors[trend.tier as keyof typeof tierColors] }}
                                                                        />
                                                                        <span className="font-medium capitalize">{trend.tier}</span>
                                                                  </div>
                                                                  <div className="flex items-center space-x-2">
                                                                        {getTrendIcon(trend.change)}
                                                                        <span className={`font-medium ${getTrendColor(trend.change)}`}>
                                                                              {formatPercentage(trend.change)}
                                                                        </span>
                                                                  </div>
                                                            </div>
                                                      )) || (
                                                                  <p className="text-gray-500 italic text-center py-4">No trend data available</p>
                                                            )}
                                                </div>
                                          </CardContent>
                                    </Card>
                              </div>

                              {/* API Usage by Tier */}
                              <Card>
                                    <CardHeader>
                                          <CardTitle className="flex items-center">
                                                <Activity className="w-5 h-5 mr-2" />
                                                API Usage by Tier
                                          </CardTitle>
                                          <CardDescription>Average API usage across different tiers</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                {tierStats?.apiUsageByTier?.map((usage) => (
                                                      <div key={usage.tier} className="text-center p-4 bg-gray-50 rounded-lg">
                                                            <div
                                                                  className="w-4 h-4 rounded-full mx-auto mb-2"
                                                                  style={{ backgroundColor: tierColors[usage.tier as keyof typeof tierColors] }}
                                                            />
                                                            <p className="text-sm font-medium capitalize">{usage.tier}</p>
                                                            <p className="text-2xl font-bold text-gray-900">{usage.avgUsage.toLocaleString()}</p>
                                                            <p className="text-xs text-gray-500">avg calls/month</p>
                                                            <div className="mt-2">
                                                                  <Progress value={usage.usagePercentage} className="h-1" />
                                                            </div>
                                                      </div>
                                                )) || (
                                                            <p className="text-gray-500 italic text-center py-4 col-span-3">No usage data available</p>
                                                      )}
                                          </div>
                                    </CardContent>
                              </Card>
                        </TabsContent>

                        <TabsContent value="distribution" className="space-y-6">
                              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Tier Breakdown</CardTitle>
                                                <CardDescription>Detailed breakdown of users in each tier</CardDescription>
                                          </CardHeader>
                                          <CardContent>
                                                <div className="space-y-4">
                                                      {tierStats?.tierDistribution?.map((tier) => (
                                                            <div key={tier.tier} className="p-4 border rounded-lg">
                                                                  <div className="flex items-center justify-between mb-2">
                                                                        <h3 className="font-semibold capitalize">{tier.tier} Tier</h3>
                                                                        <Badge variant={tier.tier === 'enterprise' ? 'default' : tier.tier === 'premium' ? 'secondary' : 'outline'}>
                                                                              {tier.count.toLocaleString()} users
                                                                        </Badge>
                                                                  </div>
                                                                  <div className="space-y-2">
                                                                        <div className="flex justify-between text-sm">
                                                                              <span>Percentage of total</span>
                                                                              <span>{tier.percentage.toFixed(1)}%</span>
                                                                        </div>
                                                                        <div className="flex justify-between text-sm">
                                                                              <span>Monthly growth</span>
                                                                              <span className={getTrendColor(tier.monthlyGrowth || 0)}>
                                                                                    {formatPercentage(tier.monthlyGrowth || 0)}
                                                                              </span>
                                                                        </div>
                                                                        <div className="flex justify-between text-sm">
                                                                              <span>Avg API usage</span>
                                                                              <span>{(tier.avgApiUsage || 0).toLocaleString()}</span>
                                                                        </div>
                                                                  </div>
                                                            </div>
                                                      )) || (
                                                                  <p className="text-gray-500 italic text-center py-4">No distribution data available</p>
                                                            )}
                                                </div>
                                          </CardContent>
                                    </Card>

                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Tier Characteristics</CardTitle>
                                                <CardDescription>Key metrics for each subscription tier</CardDescription>
                                          </CardHeader>
                                          <CardContent>
                                                <div className="space-y-4">
                                                      <div className="p-4 border border-green-200 bg-green-50 rounded-lg">
                                                            <h3 className="font-semibold text-green-800">Enterprise</h3>
                                                            <div className="mt-2 space-y-1 text-sm text-green-700">
                                                                  <p>• 100,000 API calls/month</p>
                                                                  <p>• $99/month subscription</p>
                                                                  <p>• 24/7 priority support</p>
                                                                  <p>• Custom integrations</p>
                                                            </div>
                                                      </div>

                                                      <div className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
                                                            <h3 className="font-semibold text-blue-800">Premium</h3>
                                                            <div className="mt-2 space-y-1 text-sm text-blue-700">
                                                                  <p>• 10,000 API calls/month</p>
                                                                  <p>• $29/month subscription</p>
                                                                  <p>• Priority email support</p>
                                                                  <p>• Advanced analytics</p>
                                                            </div>
                                                      </div>

                                                      <div className="p-4 border border-gray-200 bg-gray-50 rounded-lg">
                                                            <h3 className="font-semibold text-gray-800">Free</h3>
                                                            <div className="mt-2 space-y-1 text-sm text-gray-700">
                                                                  <p>• 1,000 API calls/month</p>
                                                                  <p>• Free tier</p>
                                                                  <p>• Community support</p>
                                                                  <p>• Basic documentation</p>
                                                            </div>
                                                      </div>
                                                </div>
                                          </CardContent>
                                    </Card>
                              </div>
                        </TabsContent>

                        <TabsContent value="migration" className="space-y-6">
                              <Card>
                                    <CardHeader>
                                          <CardTitle className="flex items-center">
                                                <TrendingUp className="w-5 h-5 mr-2" />
                                                Tier Migration Patterns
                                          </CardTitle>
                                          <CardDescription>
                                                How users move between subscription tiers
                                          </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                          <div className="space-y-6">
                                                {tierMigration?.migrations?.map((migration) => (
                                                      <div key={`${migration.fromTier}-${migration.toTier}`} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                                            <div className="flex items-center space-x-4">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Badge variant={migration.fromTier === 'enterprise' ? 'default' : migration.fromTier === 'premium' ? 'secondary' : 'outline'}>
                                                                              {migration.fromTier}
                                                                        </Badge>
                                                                        <span>→</span>
                                                                        <Badge variant={migration.toTier === 'enterprise' ? 'default' : migration.toTier === 'premium' ? 'secondary' : 'outline'}>
                                                                              {migration.toTier}
                                                                        </Badge>
                                                                  </div>
                                                                  <span className="text-sm text-gray-600">
                                                                        {migration.count} users migrated
                                                                  </span>
                                                            </div>
                                                            <div className="text-right">
                                                                  <p className="text-sm font-medium">{migration.percentage.toFixed(1)}%</p>
                                                                  <p className="text-xs text-gray-500">of total migrations</p>
                                                            </div>
                                                      </div>
                                                )) || (
                                                            <p className="text-gray-500 italic text-center py-4">No migration data available</p>
                                                      )}
                                          </div>
                                    </CardContent>
                              </Card>
                        </TabsContent>

                        <TabsContent value="revenue" className="space-y-6">
                              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <DollarSign className="w-5 h-5 mr-2" />
                                                      Revenue by Tier
                                                </CardTitle>
                                                <CardDescription>Revenue contribution from each tier</CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                {revenueStats?.revenueByTier?.map((revenue) => (
                                                      <div key={revenue.tier} className="space-y-2">
                                                            <div className="flex items-center justify-between">
                                                                  <div className="flex items-center space-x-2">
                                                                        <div
                                                                              className="w-3 h-3 rounded-full"
                                                                              style={{ backgroundColor: tierColors[revenue.tier as keyof typeof tierColors] }}
                                                                        />
                                                                        <span className="text-sm font-medium capitalize">{revenue.tier}</span>
                                                                        <Badge variant="outline">{formatCurrency(revenue.amount)}</Badge>
                                                                  </div>
                                                                  <span className="text-sm text-gray-600">{revenue.percentage.toFixed(1)}%</span>
                                                            </div>
                                                            <Progress value={revenue.percentage} className="h-2" />
                                                      </div>
                                                )) || (
                                                            <p className="text-gray-500 italic text-center py-4">No revenue data available</p>
                                                      )}
                                          </CardContent>
                                    </Card>

                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Revenue Metrics</CardTitle>
                                                <CardDescription>Key financial performance indicators</CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="grid grid-cols-2 gap-4">
                                                      <div className="text-center p-3 bg-green-50 rounded-lg">
                                                            <p className="text-sm text-green-600 font-medium">MRR</p>
                                                            <p className="text-lg font-bold text-green-800">
                                                                  {formatCurrency(revenueStats?.mrr || 0)}
                                                            </p>
                                                      </div>
                                                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                                                            <p className="text-sm text-blue-600 font-medium">ARR</p>
                                                            <p className="text-lg font-bold text-blue-800">
                                                                  {formatCurrency((revenueStats?.mrr || 0) * 12)}
                                                            </p>
                                                      </div>
                                                      <div className="text-center p-3 bg-purple-50 rounded-lg">
                                                            <p className="text-sm text-purple-600 font-medium">ARPU</p>
                                                            <p className="text-lg font-bold text-purple-800">
                                                                  {formatCurrency(revenueStats?.arpu || 0)}
                                                            </p>
                                                      </div>
                                                      <div className="text-center p-3 bg-yellow-50 rounded-lg">
                                                            <p className="text-sm text-yellow-600 font-medium">Churn Rate</p>
                                                            <p className="text-lg font-bold text-yellow-800">
                                                                  {(revenueStats?.churnRate || 0).toFixed(1)}%
                                                            </p>
                                                      </div>
                                                </div>
                                          </CardContent>
                                    </Card>
                              </div>
                        </TabsContent>
                  </Tabs>
            </div>
      );
}
