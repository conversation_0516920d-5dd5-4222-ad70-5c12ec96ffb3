'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Save, User, Mail, CreditCard, Globe, Phone, Building, TrendingUp, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useRegisteredUsers } from '@/lib/hooks/useRegisteredUsers';
import { usePermissions } from '@/lib/middleware/auth-guard';

const registeredUserSchema = z.object({
      name: z.string().min(1, 'Name is required'),
      email: z.string().email('Invalid email address'),
      company: z.string().optional(),
      phone: z.string().optional(),
      website: z.string().url('Invalid website URL').optional().or(z.literal('')),
      tier: z.enum(['free', 'premium', 'enterprise'], {
            required_error: 'Tier is required',
      }),
      status: z.enum(['active', 'inactive', 'suspended']),
      emailVerified: z.boolean(),
      apiCallsLimit: z.number().min(0, 'API calls limit must be non-negative'),
      notes: z.string().optional(),
});

type RegisteredUserFormData = z.infer<typeof registeredUserSchema>;

export default function RegisteredUserEditPage() {
      const params = useParams();
      const router = useRouter();
      const { toast } = useToast();
      const { can } = usePermissions();
      const userId = params.id as string;

      const { data: user, isLoading, error } = useRegisteredUsers.useGetById(userId);
      const { mutate: updateUser, isLoading: isUpdating } = useRegisteredUsers.useUpdate();
      const { mutate: upgradeTier, isLoading: isUpgrading } = useRegisteredUsers.useUpgradeTier();
      const { mutate: downgradeTier, isLoading: isDowngrading } = useRegisteredUsers.useDowngradeTier();

      const [isFormDirty, setIsFormDirty] = useState(false);
      const [currentTier, setCurrentTier] = useState<string>('');

      const form = useForm<RegisteredUserFormData>({
            resolver: zodResolver(registeredUserSchema),
            defaultValues: {
                  name: '',
                  email: '',
                  company: '',
                  phone: '',
                  website: '',
                  tier: 'free',
                  status: 'active',
                  emailVerified: false,
                  apiCallsLimit: 1000,
                  notes: '',
            },
      });

      // Update form when user data is loaded
      useEffect(() => {
            if (user) {
                  setCurrentTier(user.tier || 'free');
                  form.reset({
                        name: user.name || '',
                        email: user.email || '',
                        company: user.company || '',
                        phone: user.phone || '',
                        website: user.website || '',
                        tier: user.tier || 'free',
                        status: user.status || 'active',
                        emailVerified: user.emailVerified || false,
                        apiCallsLimit: user.apiCallsLimit || 1000,
                        notes: user.notes || '',
                  });
            }
      }, [user, form]);

      // Track form changes
      useEffect(() => {
            const subscription = form.watch(() => {
                  setIsFormDirty(true);
            });
            return () => subscription.unsubscribe();
      }, [form]);

      const onSubmit = (data: RegisteredUserFormData) => {
            const tierChanged = currentTier !== data.tier;

            if (tierChanged) {
                  // Handle tier change separately
                  const isUpgrade = getTierLevel(data.tier) > getTierLevel(currentTier);
                  const tierMutation = isUpgrade ? upgradeTier : downgradeTier;

                  tierMutation(
                        { userId, newTier: data.tier },
                        {
                              onSuccess: () => {
                                    // Update the rest of the user data
                                    updateUser(
                                          { id: userId, ...data },
                                          {
                                                onSuccess: () => {
                                                      toast({
                                                            title: 'User updated',
                                                            description: `User has been successfully updated and tier ${isUpgrade ? 'upgraded' : 'downgraded'} to ${data.tier}.`,
                                                      });
                                                      setIsFormDirty(false);
                                                      router.push(`/dashboard/users/registered/${userId}`);
                                                },
                                                onError: (error) => {
                                                      toast({
                                                            title: 'Partial Success',
                                                            description: `Tier was changed but other updates failed: ${(error as Error)?.message || 'Unknown error'}`,
                                                            variant: 'destructive',
                                                      });
                                                },
                                          }
                                    );
                              },
                              onError: (error) => {
                                    toast({
                                          title: 'Error',
                                          description: (error as Error)?.message || 'Failed to change user tier.',
                                          variant: 'destructive',
                                    });
                              },
                        }
                  );
            } else {
                  // Just update user data
                  updateUser(
                        { id: userId, ...data },
                        {
                              onSuccess: () => {
                                    toast({
                                          title: 'User updated',
                                          description: 'User has been successfully updated.',
                                    });
                                    setIsFormDirty(false);
                                    router.push(`/dashboard/users/registered/${userId}`);
                              },
                              onError: (error) => {
                                    toast({
                                          title: 'Error',
                                          description: (error as Error)?.message || 'Failed to update user.',
                                          variant: 'destructive',
                                    });
                              },
                        }
                  );
            }
      };

      const getTierLevel = (tier: string) => {
            switch (tier) {
                  case 'free':
                        return 1;
                  case 'premium':
                        return 2;
                  case 'enterprise':
                        return 3;
                  default:
                        return 0;
            }
      };

      const getTierBadgeVariant = (tier: string) => {
            switch (tier) {
                  case 'enterprise':
                        return 'default';
                  case 'premium':
                        return 'secondary';
                  case 'free':
                        return 'outline';
                  default:
                        return 'outline';
            }
      };

      const getTierLimits = (tier: string) => {
            switch (tier) {
                  case 'free':
                        return { apiCalls: 1000, features: ['Basic API access', 'Email support'] };
                  case 'premium':
                        return { apiCalls: 10000, features: ['Enhanced API access', 'Priority support', 'Analytics'] };
                  case 'enterprise':
                        return { apiCalls: 100000, features: ['Full API access', '24/7 support', 'Custom integration', 'SLA'] };
                  default:
                        return { apiCalls: 1000, features: [] };
            }
      };

      const handleCancel = () => {
            if (isFormDirty) {
                  if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
                        router.push(`/dashboard/users/registered/${userId}`);
                  }
            } else {
                  router.push(`/dashboard/users/registered/${userId}`);
            }
      };

      if (!can('users:update')) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="text-center py-12">
                              <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
                              <p className="text-gray-600 mb-4">You don't have permission to edit users.</p>
                              <Button onClick={() => router.push('/dashboard/users/registered')}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Users
                              </Button>
                        </div>
                  </div>
            );
      }

      if (isLoading) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="animate-pulse space-y-6">
                              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    <div className="lg:col-span-2 space-y-6">
                                          <div className="h-64 bg-gray-200 rounded"></div>
                                          <div className="h-48 bg-gray-200 rounded"></div>
                                    </div>
                                    <div className="space-y-6">
                                          <div className="h-32 bg-gray-200 rounded"></div>
                                          <div className="h-48 bg-gray-200 rounded"></div>
                                    </div>
                              </div>
                        </div>
                  </div>
            );
      }

      if (error || !user) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="text-center py-12">
                              <h2 className="text-2xl font-bold text-gray-900 mb-2">User Not Found</h2>
                              <p className="text-gray-600 mb-4">The requested user could not be found.</p>
                              <Button onClick={() => router.push('/dashboard/users/registered')}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Users
                              </Button>
                        </div>
                  </div>
            );
      }

      const usagePercentage = user.tier ?
            Math.round((user.apiCallsUsed / user.apiCallsLimit) * 100) : 0;

      const selectedTierLimits = getTierLimits(form.watch('tier'));

      return (
            <div className="container mx-auto p-6 space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                              <Button variant="ghost" size="sm" onClick={handleCancel}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>
                              <div>
                                    <h1 className="text-3xl font-bold text-gray-900">Edit User</h1>
                                    <p className="text-gray-600">{user.email}</p>
                              </div>
                        </div>

                        <div className="flex items-center space-x-2">
                              <Button variant="outline" onClick={handleCancel}>
                                    Cancel
                              </Button>
                              <Button
                                    onClick={form.handleSubmit(onSubmit)}
                                    disabled={isUpdating || isUpgrading || isDowngrading || !isFormDirty}
                              >
                                    <Save className="w-4 h-4 mr-2" />
                                    {isUpdating || isUpgrading || isDowngrading ? 'Saving...' : 'Save Changes'}
                              </Button>
                        </div>
                  </div>

                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                              {/* Main Form */}
                              <div className="lg:col-span-2 space-y-6">
                                    {/* Basic Information */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <User className="w-5 h-5 mr-2" />
                                                      Basic Information
                                                </CardTitle>
                                                <CardDescription>
                                                      Update the user's personal and contact information
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="space-y-2">
                                                      <Label htmlFor="name">Full Name *</Label>
                                                      <Input
                                                            id="name"
                                                            {...form.register('name')}
                                                      />
                                                      {form.formState.errors.name && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                                                      )}
                                                </div>

                                                <div className="space-y-2">
                                                      <Label htmlFor="email">Email Address *</Label>
                                                      <Input
                                                            id="email"
                                                            type="email"
                                                            {...form.register('email')}
                                                      />
                                                      {form.formState.errors.email && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.email.message}</p>
                                                      )}
                                                </div>

                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                      <div className="space-y-2">
                                                            <Label htmlFor="company">Company</Label>
                                                            <Input
                                                                  id="company"
                                                                  {...form.register('company')}
                                                                  placeholder="Company name"
                                                            />
                                                      </div>
                                                      <div className="space-y-2">
                                                            <Label htmlFor="phone">Phone Number</Label>
                                                            <Input
                                                                  id="phone"
                                                                  type="tel"
                                                                  {...form.register('phone')}
                                                                  placeholder="+****************"
                                                            />
                                                      </div>
                                                </div>

                                                <div className="space-y-2">
                                                      <Label htmlFor="website">Website</Label>
                                                      <Input
                                                            id="website"
                                                            type="url"
                                                            {...form.register('website')}
                                                            placeholder="https://example.com"
                                                      />
                                                      {form.formState.errors.website && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.website.message}</p>
                                                      )}
                                                </div>

                                                <div className="space-y-2">
                                                      <Label htmlFor="notes">Notes</Label>
                                                      <Textarea
                                                            id="notes"
                                                            {...form.register('notes')}
                                                            placeholder="Additional notes about this user..."
                                                            rows={3}
                                                      />
                                                </div>
                                          </CardContent>
                                    </Card>

                                    {/* Subscription & Tier */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="flex items-center">
                                                      <CreditCard className="w-5 h-5 mr-2" />
                                                      Subscription & Tier
                                                </CardTitle>
                                                <CardDescription>
                                                      Manage the user's subscription tier and API limits
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="space-y-2">
                                                      <Label htmlFor="tier">Subscription Tier *</Label>
                                                      <Select
                                                            value={form.watch('tier')}
                                                            onValueChange={(value) => form.setValue('tier', value as any)}
                                                      >
                                                            <SelectTrigger>
                                                                  <SelectValue placeholder="Select a tier" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                  <SelectItem value="free">Free</SelectItem>
                                                                  <SelectItem value="premium">Premium</SelectItem>
                                                                  <SelectItem value="enterprise">Enterprise</SelectItem>
                                                            </SelectContent>
                                                      </Select>
                                                      {form.formState.errors.tier && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.tier.message}</p>
                                                      )}
                                                </div>

                                                {currentTier !== form.watch('tier') && (
                                                      <div className="flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                                            <AlertTriangle className="w-4 h-4 text-yellow-500" />
                                                            <p className="text-sm text-yellow-700">
                                                                  Tier will be changed from{' '}
                                                                  <Badge variant={getTierBadgeVariant(currentTier)}>{currentTier}</Badge>
                                                                  {' '}to{' '}
                                                                  <Badge variant={getTierBadgeVariant(form.watch('tier'))}>{form.watch('tier')}</Badge>
                                                            </p>
                                                      </div>
                                                )}

                                                <div className="space-y-2">
                                                      <Label htmlFor="apiCallsLimit">API Calls Limit</Label>
                                                      <Input
                                                            id="apiCallsLimit"
                                                            type="number"
                                                            min="0"
                                                            {...form.register('apiCallsLimit', { valueAsNumber: true })}
                                                      />
                                                      {form.formState.errors.apiCallsLimit && (
                                                            <p className="text-sm text-red-600">{form.formState.errors.apiCallsLimit.message}</p>
                                                      )}
                                                      <p className="text-xs text-gray-500">
                                                            Recommended limit for {form.watch('tier')}: {selectedTierLimits.apiCalls.toLocaleString()}
                                                      </p>
                                                </div>

                                                <div className="space-y-4">
                                                      <Separator />
                                                      <div className="space-y-3">
                                                            <h4 className="text-sm font-medium">Tier Features</h4>
                                                            <div className="text-sm text-gray-600">
                                                                  <ul className="space-y-1">
                                                                        {selectedTierLimits.features.map((feature, index) => (
                                                                              <li key={index}>• {feature}</li>
                                                                        ))}
                                                                  </ul>
                                                            </div>
                                                      </div>
                                                </div>
                                          </CardContent>
                                    </Card>
                              </div>

                              {/* Sidebar */}
                              <div className="space-y-6">
                                    {/* Account Status */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Account Status</CardTitle>
                                                <CardDescription>
                                                      Manage the user's account status
                                                </CardDescription>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="space-y-2">
                                                      <Label htmlFor="status">Status</Label>
                                                      <Select
                                                            value={form.watch('status')}
                                                            onValueChange={(value) => form.setValue('status', value as any)}
                                                      >
                                                            <SelectTrigger>
                                                                  <SelectValue />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                  <SelectItem value="active">Active</SelectItem>
                                                                  <SelectItem value="inactive">Inactive</SelectItem>
                                                                  <SelectItem value="suspended">Suspended</SelectItem>
                                                            </SelectContent>
                                                      </Select>
                                                </div>

                                                <div className="flex items-center justify-between">
                                                      <Label htmlFor="emailVerified" className="text-sm font-medium">
                                                            Email Verified
                                                      </Label>
                                                      <Switch
                                                            id="emailVerified"
                                                            checked={form.watch('emailVerified')}
                                                            onCheckedChange={(checked) => form.setValue('emailVerified', checked)}
                                                      />
                                                </div>
                                          </CardContent>
                                    </Card>

                                    {/* Current Usage */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Current Usage</CardTitle>
                                          </CardHeader>
                                          <CardContent className="space-y-4">
                                                <div className="space-y-2">
                                                      <div className="flex justify-between text-sm">
                                                            <span>API Calls</span>
                                                            <span>{user.apiCallsUsed} / {user.apiCallsLimit}</span>
                                                      </div>
                                                      <Progress value={usagePercentage} className="h-2" />
                                                      <p className="text-xs text-gray-500">{usagePercentage}% used</p>
                                                </div>

                                                <Separator />

                                                <div className="space-y-2">
                                                      <div className="flex justify-between text-sm">
                                                            <span className="text-gray-600">Monthly Spend</span>
                                                            <span className="font-medium">${user.monthlySpend?.toFixed(2) || '0.00'}</span>
                                                      </div>
                                                      <div className="flex justify-between text-sm">
                                                            <span className="text-gray-600">Last API Call</span>
                                                            <span className="font-medium">
                                                                  {user.lastApiCall ? new Date(user.lastApiCall).toLocaleDateString() : 'Never'}
                                                            </span>
                                                      </div>
                                                </div>
                                          </CardContent>
                                    </Card>

                                    {/* Account Information */}
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Account Information</CardTitle>
                                          </CardHeader>
                                          <CardContent className="space-y-3">
                                                <div className="flex justify-between text-sm">
                                                      <span className="text-gray-600">User ID</span>
                                                      <span className="font-medium">{user.id}</span>
                                                </div>
                                                <div className="flex justify-between text-sm">
                                                      <span className="text-gray-600">Created</span>
                                                      <span className="font-medium">
                                                            {new Date(user.createdAt).toLocaleDateString()}
                                                      </span>
                                                </div>
                                                <div className="flex justify-between text-sm">
                                                      <span className="text-gray-600">Last Updated</span>
                                                      <span className="font-medium">
                                                            {new Date(user.updatedAt).toLocaleDateString()}
                                                      </span>
                                                </div>
                                                {user.lastLogin && (
                                                      <div className="flex justify-between text-sm">
                                                            <span className="text-gray-600">Last Login</span>
                                                            <span className="font-medium">
                                                                  {new Date(user.lastLogin).toLocaleDateString()}
                                                            </span>
                                                      </div>
                                                )}
                                          </CardContent>
                                    </Card>
                              </div>
                        </div>
                  </form>
            </div>
      );
}
