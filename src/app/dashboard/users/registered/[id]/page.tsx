'use client';

import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft, Edit, Trash2, Users, CreditCard, Activity, TrendingUp, AlertTriangle, Shield, Calendar, Mail, Phone, Globe, DollarSign } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { useRegisteredUsers } from '@/lib/hooks/useRegisteredUsers';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { RegisteredUser } from '@/lib/types/api';

export default function RegisteredUserDetailPage() {
      const params = useParams();
      const router = useRouter();
      const { toast } = useToast();
      const { canManageUsers } = usePermissions();
      const userId = params.id as string;

      const { data: user, isLoading, error } = useRegisteredUsers.useGetById(userId);
      const { mutate: suspendUser, isLoading: isSuspending } = useRegisteredUsers.useSuspend();
      const { mutate: reactivateUser, isLoading: isReactivating } = useRegisteredUsers.useReactivate();
      const { mutate: deleteUser, isLoading: isDeleting } = useRegisteredUsers.useDelete();
      const { data: usageStats } = useRegisteredUsers.useUsageStats(userId);
      const { data: apiCalls = [] } = useRegisteredUsers.useApiCalls(userId);

      const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
      const [isSuspendDialogOpen, setIsSuspendDialogOpen] = useState(false);

      const handleSuspendUser = () => {
            suspendUser(userId, {
                  onSuccess: () => {
                        toast({
                              title: 'User suspended',
                              description: 'User has been successfully suspended.',
                        });
                        setIsSuspendDialogOpen(false);
                  },
                  onError: (error: any) => {
                        toast({
                              title: 'Error',
                              description: error?.message || 'Failed to suspend user.',
                              variant: 'destructive',
                        });
                  },
            });
      };

      const handleReactivateUser = () => {
            reactivateUser(userId, {
                  onSuccess: () => {
                        toast({
                              title: 'User reactivated',
                              description: 'User has been successfully reactivated.',
                        });
                  },
                  onError: (error: any) => {
                        toast({
                              title: 'Error',
                              description: error?.message || 'Failed to reactivate user.',
                              variant: 'destructive',
                        });
                  },
            });
      };

      const handleDeleteUser = () => {
            deleteUser(userId, {
                  onSuccess: () => {
                        toast({
                              title: 'User deleted',
                              description: 'User has been successfully deleted.',
                        });
                        router.push('/dashboard/users/registered');
                  },
                  onError: (error: any) => {
                        toast({
                              title: 'Error',
                              description: error?.message || 'Failed to delete user.',
                              variant: 'destructive',
                        });
                  },
            });
      };

      const getTierBadgeVariant = (tier: string) => {
            switch (tier) {
                  case 'enterprise':
                        return 'default';
                  case 'premium':
                        return 'secondary';
                  case 'free':
                        return 'outline';
                  default:
                        return 'outline';
            }
      };

      const getStatusBadgeVariant = (status: string) => {
            switch (status) {
                  case 'active':
                        return 'default';
                  case 'inactive':
                        return 'secondary';
                  case 'suspended':
                        return 'destructive';
                  default:
                        return 'outline';
            }
      };

      const getUsageColor = (percentage: number) => {
            if (percentage >= 90) return 'error';
            if (percentage >= 75) return 'warning';
            return 'success';
      };

      if (isLoading) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="animate-pulse space-y-6">
                              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    <div className="lg:col-span-2 space-y-6">
                                          <div className="h-64 bg-gray-200 rounded"></div>
                                          <div className="h-48 bg-gray-200 rounded"></div>
                                    </div>
                                    <div className="space-y-6">
                                          <div className="h-32 bg-gray-200 rounded"></div>
                                          <div className="h-48 bg-gray-200 rounded"></div>
                                    </div>
                              </div>
                        </div>
                  </div>
            );
      }

      if (error || !user) {
            return (
                  <div className="container mx-auto p-6">
                        <div className="text-center py-12">
                              <h2 className="text-2xl font-bold text-gray-900 mb-2">User Not Found</h2>
                              <p className="text-gray-600 mb-4">The requested user could not be found.</p>
                              <Button onClick={() => router.push('/dashboard/users/registered')}>
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Users
                              </Button>
                        </div>
                  </div>
            );
      }

      const usagePercentage = user.tier ?
            Math.round((user.apiCallsUsed / user.apiCallsLimit) * 100) : 0;

      return (
            <div className="container mx-auto p-6 space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                              <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => router.push('/dashboard/users/registered')}
                              >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>
                              <div>
                                    <h1 className="text-3xl font-bold text-gray-900">{user.name}</h1>
                                    <p className="text-gray-600">{user.email}</p>
                              </div>
                        </div>

                        <div className="flex items-center space-x-2">
                              {canManageUsers() && (
                                    <>
                                          {user.status === 'active' ? (
                                                <AlertDialog open={isSuspendDialogOpen} onOpenChange={setIsSuspendDialogOpen}>
                                                      <AlertDialogTrigger asChild>
                                                            <Button variant="outline">
                                                                  <AlertTriangle className="w-4 h-4 mr-2" />
                                                                  Suspend
                                                            </Button>
                                                      </AlertDialogTrigger>
                                                      <AlertDialogContent>
                                                            <AlertDialogHeader>
                                                                  <AlertDialogTitle>Suspend User</AlertDialogTitle>
                                                                  <AlertDialogDescription>
                                                                        This will suspend the user's access to the API. They will not be able to make API calls
                                                                        until reactivated.
                                                                  </AlertDialogDescription>
                                                            </AlertDialogHeader>
                                                            <AlertDialogFooter>
                                                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                                  <AlertDialogAction
                                                                        onClick={handleSuspendUser}
                                                                        disabled={isSuspending}
                                                                  >
                                                                        {isSuspending ? 'Suspending...' : 'Suspend User'}
                                                                  </AlertDialogAction>
                                                            </AlertDialogFooter>
                                                      </AlertDialogContent>
                                                </AlertDialog>
                                          ) : user.status === 'suspended' ? (
                                                <Button
                                                      variant="outline"
                                                      onClick={handleReactivateUser}
                                                      disabled={isReactivating}
                                                >
                                                      <Shield className="w-4 h-4 mr-2" />
                                                      {isReactivating ? 'Reactivating...' : 'Reactivate'}
                                                </Button>
                                          ) : null}

                                          <Button
                                                variant="outline"
                                                onClick={() => router.push(`/dashboard/users/registered/${userId}/edit`)}
                                          >
                                                <Edit className="w-4 h-4 mr-2" />
                                                Edit
                                          </Button>
                                    </>
                              )}

                              {canManageUsers() && (
                                    <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                                          <AlertDialogTrigger asChild>
                                                <Button variant="destructive">
                                                      <Trash2 className="w-4 h-4 mr-2" />
                                                      Delete
                                                </Button>
                                          </AlertDialogTrigger>
                                          <AlertDialogContent>
                                                <AlertDialogHeader>
                                                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                      <AlertDialogDescription>
                                                            This action cannot be undone. This will permanently delete the user account
                                                            and remove all associated data including API keys and usage history.
                                                      </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                      <AlertDialogAction
                                                            onClick={handleDeleteUser}
                                                            className="bg-red-600 hover:bg-red-700"
                                                            disabled={isDeleting}
                                                      >
                                                            {isDeleting ? 'Deleting...' : 'Delete User'}
                                                      </AlertDialogAction>
                                                </AlertDialogFooter>
                                          </AlertDialogContent>
                                    </AlertDialog>
                              )}
                        </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Main Content */}
                        <div className="lg:col-span-2">
                              <Tabs defaultValue="overview" className="space-y-6">
                                    <TabsList>
                                          <TabsTrigger value="overview">Overview</TabsTrigger>
                                          <TabsTrigger value="usage">API Usage</TabsTrigger>
                                          <TabsTrigger value="subscription">Subscription</TabsTrigger>
                                          <TabsTrigger value="activity">Activity</TabsTrigger>
                                    </TabsList>

                                    <TabsContent value="overview" className="space-y-6">
                                          {/* Profile Information */}
                                          <Card>
                                                <CardHeader>
                                                      <CardTitle className="flex items-center">
                                                            <Users className="w-5 h-5 mr-2" />
                                                            Profile Information
                                                      </CardTitle>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                      <div className="flex items-center space-x-4">
                                                            <Avatar className="w-16 h-16">
                                                                  <AvatarImage src={user.avatar} alt={user.name} />
                                                                  <AvatarFallback>
                                                                        {user.name.split(' ').map(n => n.charAt(0)).join('')}
                                                                  </AvatarFallback>
                                                            </Avatar>
                                                            <div className="space-y-2">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Badge variant={getTierBadgeVariant(user.tier)}>
                                                                              {user.tier?.charAt(0).toUpperCase() + user.tier?.slice(1)}
                                                                        </Badge>
                                                                        <Badge variant={getStatusBadgeVariant(user.status)}>
                                                                              {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                                                                        </Badge>
                                                                        {user.emailVerified && (
                                                                              <Badge variant="secondary">Verified</Badge>
                                                                        )}
                                                                  </div>
                                                                  <p className="text-sm text-gray-600">
                                                                        ID: {user.id}
                                                                  </p>
                                                            </div>
                                                      </div>

                                                      <Separator />

                                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div className="space-y-3">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Mail className="w-4 h-4 text-gray-500" />
                                                                        <span className="text-sm font-medium">Email</span>
                                                                  </div>
                                                                  <p className="text-sm text-gray-600 ml-6">{user.email}</p>
                                                            </div>

                                                            {user.company && (
                                                                  <div className="space-y-3">
                                                                        <div className="flex items-center space-x-2">
                                                                              <Globe className="w-4 h-4 text-gray-500" />
                                                                              <span className="text-sm font-medium">Company</span>
                                                                        </div>
                                                                        <p className="text-sm text-gray-600 ml-6">{user.company}</p>
                                                                  </div>
                                                            )}

                                                            {user.phone && (
                                                                  <div className="space-y-3">
                                                                        <div className="flex items-center space-x-2">
                                                                              <Phone className="w-4 h-4 text-gray-500" />
                                                                              <span className="text-sm font-medium">Phone</span>
                                                                        </div>
                                                                        <p className="text-sm text-gray-600 ml-6">{user.phone}</p>
                                                                  </div>
                                                            )}

                                                            <div className="space-y-3">
                                                                  <div className="flex items-center space-x-2">
                                                                        <Calendar className="w-4 h-4 text-gray-500" />
                                                                        <span className="text-sm font-medium">Last Login</span>
                                                                  </div>
                                                                  <p className="text-sm text-gray-600 ml-6">
                                                                        {user.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'Never'}
                                                                  </p>
                                                            </div>
                                                      </div>
                                                </CardContent>
                                          </Card>

                                          {/* API Usage Overview */}
                                          <Card>
                                                <CardHeader>
                                                      <CardTitle className="flex items-center">
                                                            <Activity className="w-5 h-5 mr-2" />
                                                            API Usage Overview
                                                      </CardTitle>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                            <div className="text-center">
                                                                  <p className="text-2xl font-bold text-blue-600">{user.apiCallsUsed?.toLocaleString()}</p>
                                                                  <p className="text-sm text-gray-600">Calls Used</p>
                                                            </div>
                                                            <div className="text-center">
                                                                  <p className="text-2xl font-bold text-green-600">{user.apiCallsLimit?.toLocaleString()}</p>
                                                                  <p className="text-sm text-gray-600">Calls Limit</p>
                                                            </div>
                                                            <div className="text-center">
                                                                  <p className="text-2xl font-bold text-purple-600">{usagePercentage}%</p>
                                                                  <p className="text-sm text-gray-600">Usage</p>
                                                            </div>
                                                      </div>

                                                      <div className="space-y-2">
                                                            <div className="flex justify-between text-sm">
                                                                  <span>API Calls</span>
                                                                  <span>{user.apiCallsUsed} / {user.apiCallsLimit}</span>
                                                            </div>
                                                            <Progress
                                                                  value={usagePercentage}
                                                                  variant={getUsageColor(usagePercentage)}
                                                                  className="h-2"
                                                            />
                                                      </div>

                                                      {usagePercentage >= 90 && (
                                                            <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                                                                  <AlertTriangle className="w-4 h-4 text-red-500" />
                                                                  <p className="text-sm text-red-700">
                                                                        User is approaching their API limit. Consider upgrading their tier.
                                                                  </p>
                                                            </div>
                                                      )}
                                                </CardContent>
                                          </Card>
                                    </TabsContent>

                                    <TabsContent value="usage" className="space-y-6">
                                          <Card>
                                                <CardHeader>
                                                      <CardTitle className="flex items-center">
                                                            <TrendingUp className="w-5 h-5 mr-2" />
                                                            API Usage Statistics
                                                      </CardTitle>
                                                      <CardDescription>
                                                            Detailed breakdown of API usage
                                                      </CardDescription>
                                                </CardHeader>
                                                <CardContent>
                                                      {usageStats ? (
                                                            <div className="space-y-4">
                                                                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                                                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                                                                              <p className="text-xl font-bold text-blue-600">{usageStats.today}</p>
                                                                              <p className="text-sm text-gray-600">Today</p>
                                                                        </div>
                                                                        <div className="text-center p-4 bg-green-50 rounded-lg">
                                                                              <p className="text-xl font-bold text-green-600">{usageStats.thisWeek}</p>
                                                                              <p className="text-sm text-gray-600">This Week</p>
                                                                        </div>
                                                                        <div className="text-center p-4 bg-yellow-50 rounded-lg">
                                                                              <p className="text-xl font-bold text-yellow-600">{usageStats.thisMonth}</p>
                                                                              <p className="text-sm text-gray-600">This Month</p>
                                                                        </div>
                                                                        <div className="text-center p-4 bg-purple-50 rounded-lg">
                                                                              <p className="text-xl font-bold text-purple-600">{usageStats.total}</p>
                                                                              <p className="text-sm text-gray-600">Total</p>
                                                                        </div>
                                                                  </div>
                                                            </div>
                                                      ) : (
                                                            <p className="text-gray-500 italic text-center py-4">
                                                                  No usage statistics available
                                                            </p>
                                                      )}
                                                </CardContent>
                                          </Card>

                                          <Card>
                                                <CardHeader>
                                                      <CardTitle>Recent API Calls</CardTitle>
                                                      <CardDescription>
                                                            Latest API requests made by this user
                                                      </CardDescription>
                                                </CardHeader>
                                                <CardContent>
                                                      <div className="space-y-3">
                                                            {apiCalls.length > 0 ? (
                                                                  apiCalls.slice(0, 10).map((call) => (
                                                                        <div key={call.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                                              <div>
                                                                                    <p className="text-sm font-medium">{call.endpoint}</p>
                                                                                    <p className="text-xs text-gray-500">
                                                                                          {new Date(call.timestamp).toLocaleString()}
                                                                                    </p>
                                                                              </div>
                                                                              <div className="flex items-center space-x-2">
                                                                                    <Badge variant={call.status === 200 ? 'default' : 'destructive'}>
                                                                                          {call.status}
                                                                                    </Badge>
                                                                                    <span className="text-xs text-gray-500">{call.responseTime}ms</span>
                                                                              </div>
                                                                        </div>
                                                                  ))
                                                            ) : (
                                                                  <p className="text-gray-500 italic text-center py-4">
                                                                        No API calls recorded
                                                                  </p>
                                                            )}
                                                      </div>
                                                </CardContent>
                                          </Card>
                                    </TabsContent>

                                    <TabsContent value="subscription" className="space-y-6">
                                          <Card>
                                                <CardHeader>
                                                      <CardTitle className="flex items-center">
                                                            <CreditCard className="w-5 h-5 mr-2" />
                                                            Subscription Details
                                                      </CardTitle>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div className="space-y-3">
                                                                  <div className="flex items-center space-x-2">
                                                                        <DollarSign className="w-4 h-4 text-gray-500" />
                                                                        <span className="text-sm font-medium">Current Tier</span>
                                                                  </div>
                                                                  <p className="text-sm text-gray-600 ml-6">
                                                                        <Badge variant={getTierBadgeVariant(user.tier)}>
                                                                              {user.tier?.charAt(0).toUpperCase() + user.tier?.slice(1)}
                                                                        </Badge>
                                                                  </p>
                                                            </div>

                                                            {user.subscriptionStartDate && (
                                                                  <div className="space-y-3">
                                                                        <div className="flex items-center space-x-2">
                                                                              <Calendar className="w-4 h-4 text-gray-500" />
                                                                              <span className="text-sm font-medium">Subscription Start</span>
                                                                        </div>
                                                                        <p className="text-sm text-gray-600 ml-6">
                                                                              {new Date(user.subscriptionStartDate).toLocaleDateString()}
                                                                        </p>
                                                                  </div>
                                                            )}

                                                            {user.subscriptionEndDate && (
                                                                  <div className="space-y-3">
                                                                        <div className="flex items-center space-x-2">
                                                                              <Calendar className="w-4 h-4 text-gray-500" />
                                                                              <span className="text-sm font-medium">Subscription End</span>
                                                                        </div>
                                                                        <p className="text-sm text-gray-600 ml-6">
                                                                              {new Date(user.subscriptionEndDate).toLocaleDateString()}
                                                                        </p>
                                                                  </div>
                                                            )}

                                                            {user.monthlySpend && (
                                                                  <div className="space-y-3">
                                                                        <div className="flex items-center space-x-2">
                                                                              <DollarSign className="w-4 h-4 text-gray-500" />
                                                                              <span className="text-sm font-medium">Monthly Spend</span>
                                                                        </div>
                                                                        <p className="text-sm text-gray-600 ml-6">
                                                                              ${user.monthlySpend.toFixed(2)}
                                                                        </p>
                                                                  </div>
                                                            )}
                                                      </div>
                                                </CardContent>
                                          </Card>
                                    </TabsContent>

                                    <TabsContent value="activity" className="space-y-6">
                                          <Card>
                                                <CardHeader>
                                                      <CardTitle className="flex items-center">
                                                            <Activity className="w-5 h-5 mr-2" />
                                                            Account Activity
                                                      </CardTitle>
                                                      <CardDescription>
                                                            Recent account-related activities
                                                      </CardDescription>
                                                </CardHeader>
                                                <CardContent>
                                                      <div className="space-y-4">
                                                            <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                                                                  <div className="flex-shrink-0">
                                                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                                  </div>
                                                                  <div className="flex-1">
                                                                        <p className="text-sm font-medium">Account created</p>
                                                                        <p className="text-xs text-gray-500">
                                                                              {new Date(user.createdAt).toLocaleString()}
                                                                        </p>
                                                                  </div>
                                                            </div>

                                                            {user.lastLogin && (
                                                                  <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                                                                        <div className="flex-shrink-0">
                                                                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                                                        </div>
                                                                        <div className="flex-1">
                                                                              <p className="text-sm font-medium">Last login</p>
                                                                              <p className="text-xs text-gray-500">
                                                                                    {new Date(user.lastLogin).toLocaleString()}
                                                                              </p>
                                                                        </div>
                                                                  </div>
                                                            )}

                                                            {user.emailVerified && (
                                                                  <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                                                                        <div className="flex-shrink-0">
                                                                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                                                        </div>
                                                                        <div className="flex-1">
                                                                              <p className="text-sm font-medium">Email verified</p>
                                                                              <p className="text-xs text-gray-500">Email address confirmed</p>
                                                                        </div>
                                                                  </div>
                                                            )}
                                                      </div>
                                                </CardContent>
                                          </Card>
                                    </TabsContent>
                              </Tabs>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                              {/* Quick Stats */}
                              <Card>
                                    <CardHeader>
                                          <CardTitle className="flex items-center">
                                                <TrendingUp className="w-5 h-5 mr-2" />
                                                Quick Stats
                                          </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                          <div className="flex justify-between items-center">
                                                <span className="text-sm text-gray-600">API Calls Today</span>
                                                <span className="font-medium">{usageStats?.today || 0}</span>
                                          </div>
                                          <div className="flex justify-between items-center">
                                                <span className="text-sm text-gray-600">Days as Member</span>
                                                <span className="font-medium">
                                                      {Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
                                                </span>
                                          </div>
                                          {user.lastLogin && (
                                                <div className="flex justify-between items-center">
                                                      <span className="text-sm text-gray-600">Days Since Last Login</span>
                                                      <span className="font-medium">
                                                            {Math.floor((Date.now() - new Date(user.lastLogin).getTime()) / (1000 * 60 * 60 * 24))}
                                                      </span>
                                                </div>
                                          )}
                                    </CardContent>
                              </Card>

                              {/* Quick Actions */}
                              {canManageUsers() && (
                                    <Card>
                                          <CardHeader>
                                                <CardTitle>Quick Actions</CardTitle>
                                          </CardHeader>
                                          <CardContent className="space-y-2">
                                                <Button
                                                      variant="outline"
                                                      size="sm"
                                                      className="w-full justify-start"
                                                      onClick={() => router.push(`/dashboard/users/registered/${userId}/edit`)}
                                                >
                                                      <Edit className="w-4 h-4 mr-2" />
                                                      Edit Profile
                                                </Button>
                                                <Button
                                                      variant="outline"
                                                      size="sm"
                                                      className="w-full justify-start"
                                                      onClick={() => {
                                                            // TODO: Implement tier upgrade
                                                            toast({
                                                                  title: 'Coming Soon',
                                                                  description: 'Tier upgrade functionality will be implemented.',
                                                            });
                                                      }}
                                                >
                                                      <TrendingUp className="w-4 h-4 mr-2" />
                                                      Upgrade Tier
                                                </Button>
                                          </CardContent>
                                    </Card>
                              )}
                        </div>
                  </div>
            </div>
      );
}
