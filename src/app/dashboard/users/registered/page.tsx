'use client';

import { useState } from 'react';
import { usePermissions } from '@/lib/middleware/auth-guard';
import {
      useRegisteredUsersList,
      useUserAnalytics,
      useUsersApproachingLimits,
      useRegisteredUserMutations
} from '@/lib/hooks/useRegisteredUsers';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
      Select,
      SelectContent,
      SelectItem,
      SelectTrigger,
      SelectValue
} from '@/components/ui/select';
import {
      DropdownMenu,
      DropdownMenuContent,
      DropdownMenuItem,
      DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
      AlertDialog,
      AlertDialogAction,
      AlertDialogCancel,
      AlertDialogContent,
      AlertDialogDescription,
      AlertDialogFooter,
      AlertDialogHeader,
      AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { DataTable, Column } from '@/components/ui/data-table';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import {
      Search,
      MoreHorizontal,
      Edit,
      UserX,
      UserCheck,
      TrendingUp,
      Users,
      DollarSign,
      Activity,
      AlertTriangle
} from 'lucide-react';
import { RegisteredUser } from '@/lib/types/api';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';

export default function RegisteredUsersPage() {
      const { canManageUsers } = usePermissions();
      const [filters, setFilters] = useState({
            search: '',
            tier: undefined as 'free' | 'premium' | 'enterprise' | undefined,
            isActive: undefined as boolean | undefined,
            isEmailVerified: undefined as boolean | undefined,
            page: 1,
            limit: 10,
      });

      const { data: usersData, isLoading } = useRegisteredUsersList(filters);
      const { data: analytics } = useUserAnalytics();
      const { data: usersAtRisk } = useUsersApproachingLimits(85);
      const { upgradeTier, downgradeTier, suspendUser, reactivateUser } = useRegisteredUserMutations();

      const [actionDialog, setActionDialog] = useState<{
            isOpen: boolean;
            type: 'suspend' | 'reactivate' | 'upgrade' | 'downgrade' | null;
            user: RegisteredUser | null;
            newTier?: 'free' | 'premium' | 'enterprise';
      }>({
            isOpen: false,
            type: null,
            user: null,
      });

      const handleSearchChange = (value: string) => {
            setFilters(prev => ({ ...prev, search: value, page: 1 }));
      };

      const handleTierFilter = (tier: string) => {
            setFilters(prev => ({
                  ...prev,
                  tier: tier === 'all' ? undefined : tier as 'free' | 'premium' | 'enterprise',
                  page: 1
            }));
      };

      const handleStatusFilter = (status: string) => {
            setFilters(prev => ({
                  ...prev,
                  isActive: status === 'all' ? undefined : status === 'active',
                  page: 1
            }));
      };

      const handleVerificationFilter = (status: string) => {
            setFilters(prev => ({
                  ...prev,
                  isEmailVerified: status === 'all' ? undefined : status === 'verified',
                  page: 1
            }));
      };

      const getTierColor = (tier: string) => {
            switch (tier) {
                  case 'free':
                        return 'bg-gray-100 text-gray-800 border-gray-200';
                  case 'premium':
                        return 'bg-blue-100 text-blue-800 border-blue-200';
                  case 'enterprise':
                        return 'bg-purple-100 text-purple-800 border-purple-200';
                  default:
                        return 'bg-gray-100 text-gray-800 border-gray-200';
            }
      };

      const getApiUsageVariant = (percentage: number) => {
            if (percentage >= 90) return 'error';
            if (percentage >= 75) return 'warning';
            return 'success';
      };

      const handleUserAction = (action: 'suspend' | 'reactivate' | 'upgrade' | 'downgrade', user: RegisteredUser, newTier?: string) => {
            setActionDialog({
                  isOpen: true,
                  type: action,
                  user,
                  newTier: newTier as 'free' | 'premium' | 'enterprise',
            });
      };

      const executeUserAction = async () => {
            if (!actionDialog.user || !actionDialog.type) return;

            try {
                  switch (actionDialog.type) {
                        case 'suspend':
                              await suspendUser.mutateAsync(actionDialog.user.id);
                              break;
                        case 'reactivate':
                              await reactivateUser.mutateAsync(actionDialog.user.id);
                              break;
                        case 'upgrade':
                              if (actionDialog.newTier) {
                                    await upgradeTier.mutateAsync({
                                          userId: actionDialog.user.id,
                                          newTier: actionDialog.newTier as 'premium' | 'enterprise',
                                          subscriptionMonths: 12,
                                    });
                              }
                              break;
                        case 'downgrade':
                              if (actionDialog.newTier) {
                                    await downgradeTier.mutateAsync({
                                          userId: actionDialog.user.id,
                                          newTier: actionDialog.newTier as 'free' | 'premium',
                                    });
                              }
                              break;
                  }
            } catch (error) {
                  console.error('Action failed:', error);
            } finally {
                  setActionDialog({ isOpen: false, type: null, user: null });
            }
      };

      const columns: Column<RegisteredUser>[] = [
            {
                  key: 'username',
                  title: 'User',
                  render: (value, user) => (
                        <div className="flex items-center space-x-3">
                              <Avatar className="h-8 w-8">
                                    <AvatarFallback className="text-xs">
                                          {user.username.slice(0, 2).toUpperCase()}
                                    </AvatarFallback>
                              </Avatar>
                              <div>
                                    <div className="font-medium">{user.username}</div>
                                    <div className="text-sm text-muted-foreground">ID: {user.id}</div>
                              </div>
                        </div>
                  ),
            },
            {
                  key: 'email',
                  title: 'Email & Status',
                  render: (value, user) => (
                        <div className="space-y-1">
                              <div className="font-mono text-sm">{user.email}</div>
                              <div className="flex items-center space-x-1">
                                    {user.isEmailVerified ? (
                                          <Badge variant="outline" className="text-xs text-green-600 border-green-200">
                                                Verified
                                          </Badge>
                                    ) : (
                                          <Badge variant="outline" className="text-xs text-red-600 border-red-200">
                                                Unverified
                                          </Badge>
                                    )}
                                    <Badge
                                          variant="outline"
                                          className={`text-xs ${user.isActive ? 'text-green-600 border-green-200' : 'text-red-600 border-red-200'}`}
                                    >
                                          {user.isActive ? 'Active' : 'Suspended'}
                                    </Badge>
                              </div>
                        </div>
                  ),
            },
            {
                  key: 'tier',
                  title: 'Tier',
                  render: (tier) => (
                        <Badge className={`${getTierColor(tier as string)} capitalize`}>
                              {tier}
                        </Badge>
                  ),
            },
            {
                  key: 'apiUsage',
                  title: 'API Usage',
                  render: (value, user) => {
                        const usage = user.apiCallsUsed || 0;
                        const limit = user.apiCallsLimit;

                        if (!limit) {
                              return (
                                    <div className="text-sm">
                                          <div className="font-medium">{usage.toLocaleString()} calls</div>
                                          <div className="text-muted-foreground">Unlimited</div>
                                    </div>
                              );
                        }

                        const percentage = Math.round((usage / limit) * 100);
                        return (
                              <div className="space-y-1">
                                    <div className="flex justify-between text-xs">
                                          <span>{usage.toLocaleString()}</span>
                                          <span>{limit.toLocaleString()}</span>
                                    </div>
                                    <Progress
                                          value={percentage}
                                          className="h-2"
                                          variant={getApiUsageVariant(percentage)}
                                    />
                                    <div className="text-xs text-muted-foreground">{percentage}% used</div>
                              </div>
                        );
                  },
            },
            {
                  key: 'subscription',
                  title: 'Subscription',
                  render: (value, user) => {
                        if (!user.hasActiveSubscription) {
                              return (
                                    <Badge variant="outline" className="text-gray-600 border-gray-200">
                                          No subscription
                                    </Badge>
                              );
                        }
                        return (
                              <div className="text-sm">
                                    <Badge variant="outline" className="text-green-600 border-green-200 mb-1">
                                          Active
                                    </Badge>
                                    {user.subscriptionEndDate && (
                                          <div className="text-xs text-muted-foreground">
                                                Expires {formatDistanceToNow(new Date(user.subscriptionEndDate), { addSuffix: true })}
                                          </div>
                                    )}
                              </div>
                        );
                  },
            },
            {
                  key: 'lastLoginAt',
                  title: 'Last Login',
                  render: (lastLogin) => lastLogin ? (
                        <div className="text-sm">
                              {formatDistanceToNow(new Date(lastLogin as string), { addSuffix: true })}
                        </div>
                  ) : (
                        <span className="text-muted-foreground text-sm">Never</span>
                  ),
            },
            {
                  key: 'actions',
                  title: 'Actions',
                  render: (value, user) => {
                        if (!canManageUsers) {
                              return (
                                    <Button variant="outline" size="sm" asChild>
                                          <Link href={`/dashboard/users/registered/${user.id}`}>
                                                View
                                          </Link>
                                    </Button>
                              );
                        }

                        return (
                              <div className="flex items-center space-x-2">
                                    <Button variant="outline" size="sm" asChild>
                                          <Link href={`/dashboard/users/registered/${user.id}`}>
                                                <Edit className="h-3 w-3" />
                                          </Link>
                                    </Button>
                                    <DropdownMenu>
                                          <DropdownMenuTrigger asChild>
                                                <Button variant="outline" size="sm">
                                                      <MoreHorizontal className="h-3 w-3" />
                                                </Button>
                                          </DropdownMenuTrigger>
                                          <DropdownMenuContent align="end">
                                                <DropdownMenuItem asChild>
                                                      <Link href={`/dashboard/users/registered/${user.id}`}>
                                                            <Edit className="mr-2 h-4 w-4" />
                                                            View Details
                                                      </Link>
                                                </DropdownMenuItem>

                                                {user.tier === 'free' && (
                                                      <>
                                                            <DropdownMenuItem
                                                                  onClick={() => handleUserAction('upgrade', user, 'premium')}
                                                            >
                                                                  <TrendingUp className="mr-2 h-4 w-4" />
                                                                  Upgrade to Premium
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                  onClick={() => handleUserAction('upgrade', user, 'enterprise')}
                                                            >
                                                                  <TrendingUp className="mr-2 h-4 w-4" />
                                                                  Upgrade to Enterprise
                                                            </DropdownMenuItem>
                                                      </>
                                                )}

                                                {user.tier === 'premium' && (
                                                      <>
                                                            <DropdownMenuItem
                                                                  onClick={() => handleUserAction('upgrade', user, 'enterprise')}
                                                            >
                                                                  <TrendingUp className="mr-2 h-4 w-4" />
                                                                  Upgrade to Enterprise
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                  onClick={() => handleUserAction('downgrade', user, 'free')}
                                                            >
                                                                  <TrendingUp className="mr-2 h-4 w-4" />
                                                                  Downgrade to Free
                                                            </DropdownMenuItem>
                                                      </>
                                                )}

                                                {user.tier === 'enterprise' && (
                                                      <>
                                                            <DropdownMenuItem
                                                                  onClick={() => handleUserAction('downgrade', user, 'premium')}
                                                            >
                                                                  <TrendingUp className="mr-2 h-4 w-4" />
                                                                  Downgrade to Premium
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                  onClick={() => handleUserAction('downgrade', user, 'free')}
                                                            >
                                                                  <TrendingUp className="mr-2 h-4 w-4" />
                                                                  Downgrade to Free
                                                            </DropdownMenuItem>
                                                      </>
                                                )}

                                                {user.isActive ? (
                                                      <DropdownMenuItem
                                                            onClick={() => handleUserAction('suspend', user)}
                                                            className="text-red-600"
                                                      >
                                                            <UserX className="mr-2 h-4 w-4" />
                                                            Suspend User
                                                      </DropdownMenuItem>
                                                ) : (
                                                      <DropdownMenuItem
                                                            onClick={() => handleUserAction('reactivate', user)}
                                                            className="text-green-600"
                                                      >
                                                            <UserCheck className="mr-2 h-4 w-4" />
                                                            Reactivate User
                                                      </DropdownMenuItem>
                                                )}
                                          </DropdownMenuContent>
                                    </DropdownMenu>
                              </div>
                        );
                  },
            },
      ];

      if (!canManageUsers) {
            return (
                  <div className="container mx-auto py-6">
                        <Card>
                              <CardContent className="flex items-center justify-center py-12">
                                    <div className="text-center">
                                          <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                                          <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
                                          <p className="text-muted-foreground">
                                                You don't have permission to manage registered users.
                                          </p>
                                    </div>
                              </CardContent>
                        </Card>
                  </div>
            );
      }

      return (
            <div className="container mx-auto py-6 space-y-6">
                  {/* Header */}
                  <div className="flex justify-between items-center">
                        <div>
                              <h1 className="text-3xl font-bold">Registered Users</h1>
                              <p className="text-muted-foreground">
                                    Manage API consumers and their subscriptions
                              </p>
                        </div>
                  </div>

                  {/* Analytics Cards */}
                  {analytics && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                              <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                          <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                                          <Users className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                          <div className="text-2xl font-bold">{analytics.totalUsers.toLocaleString()}</div>
                                          <p className="text-xs text-muted-foreground">
                                                {analytics.newUsersThisMonth} new this month
                                          </p>
                                    </CardContent>
                              </Card>

                              <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                          <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                                          <Activity className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                          <div className="text-2xl font-bold">{analytics.activeUsers.toLocaleString()}</div>
                                          <p className="text-xs text-muted-foreground">
                                                {Math.round((analytics.activeUsers / analytics.totalUsers) * 100)}% of total
                                          </p>
                                    </CardContent>
                              </Card>

                              <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                          <CardTitle className="text-sm font-medium">API Calls</CardTitle>
                                          <TrendingUp className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                          <div className="text-2xl font-bold">
                                                {analytics.apiUsageStats.totalCallsThisMonth.toLocaleString()}
                                          </div>
                                          <p className="text-xs text-muted-foreground">
                                                Avg {analytics.apiUsageStats.averageCallsPerUser}/user
                                          </p>
                                    </CardContent>
                              </Card>

                              <Card>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                          <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                                    </CardHeader>
                                    <CardContent>
                                          <div className="text-2xl font-bold">
                                                ${analytics.revenueMetrics.monthlyRecurringRevenue.toLocaleString()}
                                          </div>
                                          <p className="text-xs text-muted-foreground">Monthly recurring</p>
                                    </CardContent>
                              </Card>
                        </div>
                  )}

                  {/* Warning for users at risk */}
                  {usersAtRisk && usersAtRisk.length > 0 && (
                        <Card className="border-yellow-200 bg-yellow-50">
                              <CardHeader>
                                    <CardTitle className="flex items-center text-yellow-800">
                                          <AlertTriangle className="mr-2 h-5 w-5" />
                                          Users Approaching API Limits
                                    </CardTitle>
                                    <CardDescription className="text-yellow-700">
                                          {usersAtRisk.length} users are approaching their API usage limits and may need attention.
                                    </CardDescription>
                              </CardHeader>
                              <CardContent>
                                    <div className="flex flex-wrap gap-2">
                                          {usersAtRisk.slice(0, 10).map(user => (
                                                <Badge key={user.id} variant="outline" className="text-yellow-800 border-yellow-300">
                                                      {user.username}
                                                </Badge>
                                          ))}
                                          {usersAtRisk.length > 10 && (
                                                <Badge variant="outline" className="text-yellow-800 border-yellow-300">
                                                      +{usersAtRisk.length - 10} more
                                                </Badge>
                                          )}
                                    </div>
                              </CardContent>
                        </Card>
                  )}

                  {/* Filters */}
                  <Card>
                        <CardHeader>
                              <CardTitle>Filters</CardTitle>
                        </CardHeader>
                        <CardContent>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                    <div className="space-y-2">
                                          <label className="text-sm font-medium">Search</label>
                                          <div className="relative">
                                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                                <Input
                                                      placeholder="Search users..."
                                                      value={filters.search}
                                                      onChange={(e) => handleSearchChange(e.target.value)}
                                                      className="pl-10"
                                                />
                                          </div>
                                    </div>

                                    <div className="space-y-2">
                                          <label className="text-sm font-medium">Tier</label>
                                          <Select value={filters.tier || 'all'} onValueChange={handleTierFilter}>
                                                <SelectTrigger>
                                                      <SelectValue placeholder="All tiers" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                      <SelectItem value="all">All tiers</SelectItem>
                                                      <SelectItem value="free">Free</SelectItem>
                                                      <SelectItem value="premium">Premium</SelectItem>
                                                      <SelectItem value="enterprise">Enterprise</SelectItem>
                                                </SelectContent>
                                          </Select>
                                    </div>

                                    <div className="space-y-2">
                                          <label className="text-sm font-medium">Status</label>
                                          <Select
                                                value={filters.isActive === undefined ? 'all' : filters.isActive ? 'active' : 'suspended'}
                                                onValueChange={handleStatusFilter}
                                          >
                                                <SelectTrigger>
                                                      <SelectValue placeholder="All statuses" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                      <SelectItem value="all">All statuses</SelectItem>
                                                      <SelectItem value="active">Active</SelectItem>
                                                      <SelectItem value="suspended">Suspended</SelectItem>
                                                </SelectContent>
                                          </Select>
                                    </div>

                                    <div className="space-y-2">
                                          <label className="text-sm font-medium">Email Status</label>
                                          <Select
                                                value={filters.isEmailVerified === undefined ? 'all' : filters.isEmailVerified ? 'verified' : 'unverified'}
                                                onValueChange={handleVerificationFilter}
                                          >
                                                <SelectTrigger>
                                                      <SelectValue placeholder="All statuses" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                      <SelectItem value="all">All statuses</SelectItem>
                                                      <SelectItem value="verified">Verified</SelectItem>
                                                      <SelectItem value="unverified">Unverified</SelectItem>
                                                </SelectContent>
                                          </Select>
                                    </div>
                              </div>
                        </CardContent>
                  </Card>

                  {/* Users Table */}
                  <Card>
                        <CardHeader>
                              <CardTitle>Users ({usersData?.meta.totalItems || 0})</CardTitle>
                              <CardDescription>
                                    Manage registered users and their subscriptions
                              </CardDescription>
                        </CardHeader>
                        <CardContent>
                              <DataTable
                                    columns={columns}
                                    data={usersData?.data || []}
                                    loading={isLoading}
                                    pagination={{
                                          page: filters.page,
                                          limit: filters.limit,
                                          total: usersData?.meta.totalItems || 0,
                                          onPageChange: (page) => setFilters(prev => ({ ...prev, page })),
                                          onLimitChange: (limit) => setFilters(prev => ({ ...prev, limit, page: 1 })),
                                    }}
                              />
                        </CardContent>
                  </Card>

                  {/* Action Confirmation Dialog */}
                  <AlertDialog open={actionDialog.isOpen} onOpenChange={(open: boolean) =>
                        !open && setActionDialog({ isOpen: false, type: null, user: null })
                  }>
                        <AlertDialogContent>
                              <AlertDialogHeader>
                                    <AlertDialogTitle>
                                          {actionDialog.type === 'suspend' && 'Suspend User'}
                                          {actionDialog.type === 'reactivate' && 'Reactivate User'}
                                          {actionDialog.type === 'upgrade' && 'Upgrade User Tier'}
                                          {actionDialog.type === 'downgrade' && 'Downgrade User Tier'}
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                          {actionDialog.type === 'suspend' &&
                                                `Are you sure you want to suspend ${actionDialog.user?.username}? This will prevent them from accessing the API.`
                                          }
                                          {actionDialog.type === 'reactivate' &&
                                                `Are you sure you want to reactivate ${actionDialog.user?.username}? They will regain access to the API.`
                                          }
                                          {actionDialog.type === 'upgrade' &&
                                                `Are you sure you want to upgrade ${actionDialog.user?.username} to ${actionDialog.newTier} tier? This will increase their API limits.`
                                          }
                                          {actionDialog.type === 'downgrade' &&
                                                `Are you sure you want to downgrade ${actionDialog.user?.username} to ${actionDialog.newTier} tier? This will reduce their API limits.`
                                          }
                                    </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={executeUserAction}>
                                          Confirm
                                    </AlertDialogAction>
                              </AlertDialogFooter>
                        </AlertDialogContent>
                  </AlertDialog>
            </div>
      );
}