import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

// GET /api/fixtures/sync - Get sync status
export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Proxying sync status request:', `${API_BASE_URL}/football/fixtures/sync/status`);

    const response = await fetch(`${API_BASE_URL}/football/fixtures/sync/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ Sync status API Error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          error: 'Failed to fetch sync status',
          status: response.status,
          message: response.statusText 
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Sync status fetched successfully');

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Sync status proxy error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

// POST /api/fixtures/sync - Trigger sync operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type } = body; // 'daily' or 'season'
    
    let endpoint = '';
    if (type === 'daily') {
      endpoint = `${API_BASE_URL}/football/fixtures/sync/daily`;
    } else if (type === 'season') {
      endpoint = `${API_BASE_URL}/football/fixtures/sync/fixtures`;
    } else {
      return NextResponse.json(
        { error: 'Invalid sync type. Must be "daily" or "season"' },
        { status: 400 }
      );
    }

    console.log('🔄 Proxying sync trigger request:', endpoint);

    const response = await fetch(endpoint, {
      method: 'GET', // API uses GET for triggering sync
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ Sync trigger API Error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          error: `Failed to trigger ${type} sync`,
          status: response.status,
          message: response.statusText 
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`✅ ${type} sync triggered successfully:`, data.message);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Sync trigger proxy error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
