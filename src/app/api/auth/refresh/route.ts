import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🔄 Proxying refresh token request');

    const response = await fetch(`${API_BASE_URL}/system-auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error('❌ Refresh Token API Error:', response.status, response.statusText);
      const errorData = await response.text();
      return NextResponse.json(
        { 
          error: 'Token refresh failed',
          status: response.status,
          message: response.statusText,
          details: errorData
        },
        { status: response.status }
      );
    }

    const refreshData = await response.json();
    console.log('✅ Token refresh successful via proxy');

    return NextResponse.json(refreshData);
  } catch (error: any) {
    console.error('❌ Refresh token proxy error:', error.message);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
