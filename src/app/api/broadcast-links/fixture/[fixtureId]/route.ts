import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { fixtureId: string } }
) {
  try {
    const fixtureId = params.fixtureId;

    // Call real API endpoint
    console.log('🔄 Proxying broadcast links for fixture request:', `${API_BASE_URL}/broadcast-links/fixture/${fixtureId}`);

    const authHeader = request.headers.get('authorization');
    console.log('🔑 Authorization header received:', authHeader ? 'Present' : 'Missing');
    console.log('🔑 Token preview:', authHeader ? authHeader.substring(0, 30) + '...' : 'No token');

    try {
      const response = await fetch(`${API_BASE_URL}/broadcast-links/fixture/${fixtureId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(authHeader && {
            'Authorization': authHeader
          })
        },
      });

      if (!response.ok) {
        console.error('❌ API Error:', response.status, response.statusText);
        return NextResponse.json(
          {
            error: 'Failed to fetch broadcast links for fixture',
            status: response.status,
            message: response.statusText
          },
          { status: response.status }
        );
      }

      const data = await response.json();
      console.log('✅ Broadcast links for fixture fetched successfully:', data.data?.length || 0, 'links');

      return NextResponse.json(data);
    } catch (networkError: any) {
      console.error('❌ Network Error:', networkError);
      return NextResponse.json(
        {
          error: 'Network error occurred',
          message: networkError?.message || 'Unknown network error'
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}
