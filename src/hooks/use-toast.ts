import { toast as sonnerToast } from 'sonner';

export interface ToastProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
}

export const toast = (props: ToastProps) => {
  if (props.variant === 'destructive') {
    sonnerToast.error(props.title || props.description || 'Error occurred');
  } else {
    sonnerToast.success(props.title || props.description || 'Success');
  }
};

export const useToast = () => {
  return {
    toast: (props: ToastProps) => {
      if (props.variant === 'destructive') {
        sonnerToast.error(props.title || props.description || 'Error occurred');
      } else {
        sonnerToast.success(props.title || props.description || 'Success');
      }
    }
  };
};
