import { AggregationFn } from './features/ColumnGrouping';
export declare const aggregationFns: {
    sum: AggregationFn<any>;
    min: AggregationFn<any>;
    max: AggregationFn<any>;
    extent: AggregationFn<any>;
    mean: AggregationFn<any>;
    median: AggregationFn<any>;
    unique: AggregationFn<any>;
    uniqueCount: AggregationFn<any>;
    count: AggregationFn<any>;
};
export type BuiltInAggregationFn = keyof typeof aggregationFns;
