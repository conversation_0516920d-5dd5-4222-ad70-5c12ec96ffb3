{"version": 3, "file": "sortingFns.js", "sources": ["../../src/sortingFns.ts"], "sourcesContent": ["import { SortingFn } from './features/RowSorting'\n\nexport const reSplitAlphaNumeric = /([0-9]+)/gm\n\nconst alphanumeric: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\nconst alphanumericCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\nconst datetime: SortingFn<any> = (rowA, rowB, columnId) => {\n  const a = rowA.getValue<Date>(columnId)\n  const b = rowB.getValue<Date>(columnId)\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nconst basic: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId))\n}\n\n// Utils\n\nfunction compareBasic(a: any, b: any) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction toString(a: any) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr: string, bStr: string) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean)\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift()!\n    const bb = b.shift()!\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0]!)) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1]!)) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\n\n// Exports\n\nexport const sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic,\n}\n\nexport type BuiltInSortingFn = keyof typeof sortingFns\n"], "names": ["reSplitAlphaNumeric", "alphanumeric", "rowA", "rowB", "columnId", "compareAlphanumeric", "toString", "getValue", "toLowerCase", "alphanumericCaseSensitive", "text", "compareBasic", "textCaseSensitive", "datetime", "a", "b", "basic", "isNaN", "Infinity", "String", "aStr", "bStr", "split", "filter", "Boolean", "length", "aa", "shift", "bb", "an", "parseInt", "bn", "combo", "sort", "sortingFns"], "mappings": ";;;;;;;;;;;;AAEO,MAAMA,mBAAmB,GAAG,aAAY;AAE/C,MAAMC,YAA4B,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,KAAK;AAC7D,EAAA,OAAOC,mBAAmB,CACxBC,QAAQ,CAACJ,IAAI,CAACK,QAAQ,CAACH,QAAQ,CAAC,CAAC,CAACI,WAAW,EAAE,EAC/CF,QAAQ,CAACH,IAAI,CAACI,QAAQ,CAACH,QAAQ,CAAC,CAAC,CAACI,WAAW,EAC/C,CAAC,CAAA;AACH,CAAC,CAAA;AAED,MAAMC,yBAAyC,GAAGA,CAACP,IAAI,EAAEC,IAAI,EAAEC,QAAQ,KAAK;EAC1E,OAAOC,mBAAmB,CACxBC,QAAQ,CAACJ,IAAI,CAACK,QAAQ,CAACH,QAAQ,CAAC,CAAC,EACjCE,QAAQ,CAACH,IAAI,CAACI,QAAQ,CAACH,QAAQ,CAAC,CAClC,CAAC,CAAA;AACH,CAAC,CAAA;;AAED;AACA;AACA,MAAMM,IAAoB,GAAGA,CAACR,IAAI,EAAEC,IAAI,EAAEC,QAAQ,KAAK;AACrD,EAAA,OAAOO,YAAY,CACjBL,QAAQ,CAACJ,IAAI,CAACK,QAAQ,CAACH,QAAQ,CAAC,CAAC,CAACI,WAAW,EAAE,EAC/CF,QAAQ,CAACH,IAAI,CAACI,QAAQ,CAACH,QAAQ,CAAC,CAAC,CAACI,WAAW,EAC/C,CAAC,CAAA;AACH,CAAC,CAAA;;AAED;AACA;AACA,MAAMI,iBAAiC,GAAGA,CAACV,IAAI,EAAEC,IAAI,EAAEC,QAAQ,KAAK;EAClE,OAAOO,YAAY,CACjBL,QAAQ,CAACJ,IAAI,CAACK,QAAQ,CAACH,QAAQ,CAAC,CAAC,EACjCE,QAAQ,CAACH,IAAI,CAACI,QAAQ,CAACH,QAAQ,CAAC,CAClC,CAAC,CAAA;AACH,CAAC,CAAA;AAED,MAAMS,QAAwB,GAAGA,CAACX,IAAI,EAAEC,IAAI,EAAEC,QAAQ,KAAK;AACzD,EAAA,MAAMU,CAAC,GAAGZ,IAAI,CAACK,QAAQ,CAAOH,QAAQ,CAAC,CAAA;AACvC,EAAA,MAAMW,CAAC,GAAGZ,IAAI,CAACI,QAAQ,CAAOH,QAAQ,CAAC,CAAA;;AAEvC;AACA;AACA;AACA,EAAA,OAAOU,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AACnC,CAAC,CAAA;AAED,MAAMC,KAAqB,GAAGA,CAACd,IAAI,EAAEC,IAAI,EAAEC,QAAQ,KAAK;AACtD,EAAA,OAAOO,YAAY,CAACT,IAAI,CAACK,QAAQ,CAACH,QAAQ,CAAC,EAAED,IAAI,CAACI,QAAQ,CAACH,QAAQ,CAAC,CAAC,CAAA;AACvE,CAAC,CAAA;;AAED;;AAEA,SAASO,YAAYA,CAACG,CAAM,EAAEC,CAAM,EAAE;AACpC,EAAA,OAAOD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AACrC,CAAA;AAEA,SAAST,QAAQA,CAACQ,CAAM,EAAE;AACxB,EAAA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;AACzB,IAAA,IAAIG,KAAK,CAACH,CAAC,CAAC,IAAIA,CAAC,KAAKI,QAAQ,IAAIJ,CAAC,KAAK,CAACI,QAAQ,EAAE;AACjD,MAAA,OAAO,EAAE,CAAA;AACX,KAAA;IACA,OAAOC,MAAM,CAACL,CAAC,CAAC,CAAA;AAClB,GAAA;AACA,EAAA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;AACzB,IAAA,OAAOA,CAAC,CAAA;AACV,GAAA;AACA,EAAA,OAAO,EAAE,CAAA;AACX,CAAA;;AAEA;AACA;AACA;AACA,SAAST,mBAAmBA,CAACe,IAAY,EAAEC,IAAY,EAAE;AACvD;AACA;AACA,EAAA,MAAMP,CAAC,GAAGM,IAAI,CAACE,KAAK,CAACtB,mBAAmB,CAAC,CAACuB,MAAM,CAACC,OAAO,CAAC,CAAA;AACzD,EAAA,MAAMT,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACtB,mBAAmB,CAAC,CAACuB,MAAM,CAACC,OAAO,CAAC,CAAA;;AAEzD;AACA,EAAA,OAAOV,CAAC,CAACW,MAAM,IAAIV,CAAC,CAACU,MAAM,EAAE;AAC3B,IAAA,MAAMC,EAAE,GAAGZ,CAAC,CAACa,KAAK,EAAG,CAAA;AACrB,IAAA,MAAMC,EAAE,GAAGb,CAAC,CAACY,KAAK,EAAG,CAAA;AAErB,IAAA,MAAME,EAAE,GAAGC,QAAQ,CAACJ,EAAE,EAAE,EAAE,CAAC,CAAA;AAC3B,IAAA,MAAMK,EAAE,GAAGD,QAAQ,CAACF,EAAE,EAAE,EAAE,CAAC,CAAA;IAE3B,MAAMI,KAAK,GAAG,CAACH,EAAE,EAAEE,EAAE,CAAC,CAACE,IAAI,EAAE,CAAA;;AAE7B;AACA,IAAA,IAAIhB,KAAK,CAACe,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE;MACpB,IAAIN,EAAE,GAAGE,EAAE,EAAE;AACX,QAAA,OAAO,CAAC,CAAA;AACV,OAAA;MACA,IAAIA,EAAE,GAAGF,EAAE,EAAE;AACX,QAAA,OAAO,CAAC,CAAC,CAAA;AACX,OAAA;AACA,MAAA,SAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAIT,KAAK,CAACe,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE;MACpB,OAAOf,KAAK,CAACY,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AAC3B,KAAA;;AAEA;IACA,IAAIA,EAAE,GAAGE,EAAE,EAAE;AACX,MAAA,OAAO,CAAC,CAAA;AACV,KAAA;IACA,IAAIA,EAAE,GAAGF,EAAE,EAAE;AACX,MAAA,OAAO,CAAC,CAAC,CAAA;AACX,KAAA;AACF,GAAA;AAEA,EAAA,OAAOf,CAAC,CAACW,MAAM,GAAGV,CAAC,CAACU,MAAM,CAAA;AAC5B,CAAA;;AAEA;;AAEO,MAAMS,UAAU,GAAG;EACxBjC,YAAY;EACZQ,yBAAyB;EACzBC,IAAI;EACJE,iBAAiB;EACjBC,QAAQ;AACRG,EAAAA,KAAAA;AACF;;;;;"}