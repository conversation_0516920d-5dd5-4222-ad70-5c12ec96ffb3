{"version": 3, "file": "ColumnPinning.js", "sources": ["../../../src/features/ColumnPinning.ts"], "sourcesContent": ["import {\n  OnChangeFn,\n  Updater,\n  Table,\n  Column,\n  Row,\n  Cell,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type ColumnPinningPosition = false | 'left' | 'right'\n\nexport interface ColumnPinningState {\n  left?: string[]\n  right?: string[]\n}\n\nexport interface ColumnPinningTableState {\n  columnPinning: ColumnPinningState\n}\n\nexport interface ColumnPinningOptions {\n  /**\n   * Enables/disables column pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablecolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enableColumnPinning?: boolean\n  /**\n   * @deprecated Use `enableColumnPinning` or `enableRowPinning` instead.\n   * Enables/disables all pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnPinning` changes. This overrides the default internal state management, so you will also need to supply `state.columnPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#oncolumnpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/oncolumnpinningchange)\n   */\n  onColumnPinningChange?: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningDefaultOptions {\n  onColumnPinningChange: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningColumnDef {\n  /**\n   * Enables/disables column pinning for this column. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#enablepinning-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningColumn {\n  /**\n   * Returns whether or not the column can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcanpin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the column. (`'left'`, `'right'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getispinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsPinned: () => ColumnPinningPosition\n  /**\n   * Returns the numeric pinned index of the column within a pinned column group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getpinnedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a column to the `'left'` or `'right'`, or unpins the column to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#pin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  pin: (position: ColumnPinningPosition) => void\n}\n\nexport interface ColumnPinningRow<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcentervisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all left pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface ColumnPinningInstance<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getcenterleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getCenterLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether or not any columns are pinned. Optionally specify to only check for pinned columns in either the `left` or `right` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getissomecolumnspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getIsSomeColumnsPinned: (position?: ColumnPinningPosition) => boolean\n  /**\n   * Returns all left pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getleftleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getLeftLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#getrightleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  getRightLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the **columnPinning** state to `initialState.columnPinning`, or `true` can be passed to force a default blank state reset to `{ left: [], right: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#resetcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  resetColumnPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-pinning#setcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-pinning)\n   */\n  setColumnPinning: (updater: Updater<ColumnPinningState>) => void\n}\n\n//\n\nconst getDefaultColumnPinningState = (): ColumnPinningState => ({\n  left: [],\n  right: [],\n})\n\nexport const ColumnPinning: TableFeature = {\n  getInitialState: (state): ColumnPinningTableState => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningDefaultOptions => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.pin = position => {\n      const columnIds = column\n        .getLeafColumns()\n        .map(d => d.id)\n        .filter(Boolean) as string[]\n\n      table.setColumnPinning(old => {\n        if (position === 'right') {\n          return {\n            left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n            right: [\n              ...(old?.right ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n          }\n        }\n\n        if (position === 'left') {\n          return {\n            left: [\n              ...(old?.left ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n            right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n          }\n        }\n\n        return {\n          left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n          right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n        }\n      })\n    }\n\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns()\n\n      return leafColumns.some(\n        d =>\n          (d.columnDef.enablePinning ?? true) &&\n          (table.options.enableColumnPinning ??\n            table.options.enablePinning ??\n            true)\n      )\n    }\n\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id)\n\n      const { left, right } = table.getState().columnPinning\n\n      const isLeft = leafColumnIds.some(d => left?.includes(d))\n      const isRight = leafColumnIds.some(d => right?.includes(d))\n\n      return isLeft ? 'left' : isRight ? 'right' : false\n    }\n\n    column.getPinnedIndex = () => {\n      const position = column.getIsPinned()\n\n      return position\n        ? table.getState().columnPinning?.[position]?.indexOf(column.id) ?? -1\n        : 0\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getCenterVisibleCells = memo(\n      () => [\n        row._getAllVisibleCells(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allCells, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allCells.filter(d => !leftAndRight.includes(d.column.id))\n      },\n      getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells')\n    )\n    row.getLeftVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.left],\n      (allCells, left) => {\n        const cells = (left ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'left' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells')\n    )\n    row.getRightVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.right],\n      (allCells, right) => {\n        const cells = (right ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'right' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells')\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnPinning = updater =>\n      table.options.onColumnPinningChange?.(updater)\n\n    table.resetColumnPinning = defaultState =>\n      table.setColumnPinning(\n        defaultState\n          ? getDefaultColumnPinningState()\n          : table.initialState?.columnPinning ?? getDefaultColumnPinningState()\n      )\n\n    table.getIsSomeColumnsPinned = position => {\n      const pinningState = table.getState().columnPinning\n\n      if (!position) {\n        return Boolean(pinningState.left?.length || pinningState.right?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table.getLeftLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.left],\n      (allColumns, left) => {\n        return (left ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns')\n    )\n\n    table.getRightLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.right],\n      (allColumns, right) => {\n        return (right ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns')\n    )\n\n    table.getCenterLeafColumns = memo(\n      () => [\n        table.getAllLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allColumns.filter(d => !leftAndRight.includes(d.id))\n      },\n      getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns')\n    )\n  },\n}\n"], "names": ["getDefaultColumnPinningState", "left", "right", "ColumnPinning", "getInitialState", "state", "columnPinning", "getDefaultOptions", "table", "onColumnPinningChange", "makeStateUpdater", "createColumn", "column", "pin", "position", "columnIds", "getLeafColumns", "map", "d", "id", "filter", "Boolean", "setColumnPinning", "old", "_old$left3", "_old$right3", "_old$left", "_old$right", "includes", "_old$left2", "_old$right2", "getCanPin", "leafColumns", "some", "_d$columnDef$enablePi", "_ref", "_table$options$enable", "columnDef", "enablePinning", "options", "enableColumnPinning", "getIsPinned", "leafColumnIds", "getState", "isLeft", "isRight", "getPinnedIndex", "_table$getState$colum", "_table$getState$colum2", "indexOf", "createRow", "row", "getCenterVisibleCells", "memo", "_getAllVisibleCells", "allCells", "leftAndRight", "getMemoOptions", "getLeftVisibleCells", "cells", "columnId", "find", "cell", "getRightVisibleCells", "createTable", "updater", "resetColumnPinning", "defaultState", "_table$initialState$c", "_table$initialState", "initialState", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "length", "getLeftLeafColumns", "getAllLeafColumns", "allColumns", "getRightLeafColumns", "getCenterLeafColumns"], "mappings": ";;;;;;;;;;;;;;AAiJA;;AAEA,MAAMA,4BAA4B,GAAGA,OAA2B;AAC9DC,EAAAA,IAAI,EAAE,EAAE;AACRC,EAAAA,KAAK,EAAE,EAAA;AACT,CAAC,CAAC,CAAA;AAEK,MAAMC,aAA2B,GAAG;EACzCC,eAAe,EAAGC,KAAK,IAA8B;IACnD,OAAO;MACLC,aAAa,EAAEN,4BAA4B,EAAE;MAC7C,GAAGK,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfC,KAAmB,IACa;IAChC,OAAO;AACLC,MAAAA,qBAAqB,EAAEC,sBAAgB,CAAC,eAAe,EAAEF,KAAK,CAAA;KAC/D,CAAA;GACF;AAEDG,EAAAA,YAAY,EAAEA,CACZC,MAA6B,EAC7BJ,KAAmB,KACV;AACTI,IAAAA,MAAM,CAACC,GAAG,GAAGC,QAAQ,IAAI;MACvB,MAAMC,SAAS,GAAGH,MAAM,CACrBI,cAAc,EAAE,CAChBC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,CAAC,CACdC,MAAM,CAACC,OAAO,CAAa,CAAA;AAE9Bb,MAAAA,KAAK,CAACc,gBAAgB,CAACC,GAAG,IAAI;QAAA,IAAAC,UAAA,EAAAC,WAAA,CAAA;QAC5B,IAAIX,QAAQ,KAAK,OAAO,EAAE;UAAA,IAAAY,SAAA,EAAAC,UAAA,CAAA;UACxB,OAAO;YACL1B,IAAI,EAAE,CAAAyB,CAAAA,SAAA,GAACH,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEtB,IAAI,KAAAyB,IAAAA,GAAAA,SAAA,GAAI,EAAE,EAAEN,MAAM,CAACF,CAAC,IAAI,EAACH,SAAS,IAAA,IAAA,IAATA,SAAS,CAAEa,QAAQ,CAACV,CAAC,CAAC,CAAC,CAAA;AAC5DhB,YAAAA,KAAK,EAAE,CACL,GAAG,CAAA,CAAAyB,UAAA,GAACJ,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAErB,KAAK,KAAAyB,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAEP,MAAM,CAACF,CAAC,IAAI,EAACH,SAAS,YAATA,SAAS,CAAEa,QAAQ,CAACV,CAAC,CAAC,CAAC,CAAA,EAC1D,GAAGH,SAAS,CAAA;WAEf,CAAA;AACH,SAAA;QAEA,IAAID,QAAQ,KAAK,MAAM,EAAE;UAAA,IAAAe,UAAA,EAAAC,WAAA,CAAA;UACvB,OAAO;AACL7B,YAAAA,IAAI,EAAE,CACJ,GAAG,CAAA,CAAA4B,UAAA,GAACN,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEtB,IAAI,KAAA4B,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAET,MAAM,CAACF,CAAC,IAAI,EAACH,SAAS,YAATA,SAAS,CAAEa,QAAQ,CAACV,CAAC,CAAC,CAAA,CAAC,EACzD,GAAGH,SAAS,CACb;YACDb,KAAK,EAAE,CAAA4B,CAAAA,WAAA,GAACP,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAErB,KAAK,KAAA4B,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAEV,MAAM,CAACF,CAAC,IAAI,EAACH,SAAS,IAATA,IAAAA,IAAAA,SAAS,CAAEa,QAAQ,CAACV,CAAC,CAAC,CAAA,CAAA;WAC9D,CAAA;AACH,SAAA;QAEA,OAAO;UACLjB,IAAI,EAAE,CAAAuB,CAAAA,UAAA,GAACD,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAEtB,IAAI,KAAAuB,IAAAA,GAAAA,UAAA,GAAI,EAAE,EAAEJ,MAAM,CAACF,CAAC,IAAI,EAACH,SAAS,IAAA,IAAA,IAATA,SAAS,CAAEa,QAAQ,CAACV,CAAC,CAAC,CAAC,CAAA;UAC5DhB,KAAK,EAAE,CAAAuB,CAAAA,WAAA,GAACF,GAAG,IAAHA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAErB,KAAK,KAAAuB,IAAAA,GAAAA,WAAA,GAAI,EAAE,EAAEL,MAAM,CAACF,CAAC,IAAI,EAACH,SAAS,IAATA,IAAAA,IAAAA,SAAS,CAAEa,QAAQ,CAACV,CAAC,CAAC,CAAA,CAAA;SAC9D,CAAA;AACH,OAAC,CAAC,CAAA;KACH,CAAA;IAEDN,MAAM,CAACmB,SAAS,GAAG,MAAM;AACvB,MAAA,MAAMC,WAAW,GAAGpB,MAAM,CAACI,cAAc,EAAE,CAAA;AAE3C,MAAA,OAAOgB,WAAW,CAACC,IAAI,CACrBf,CAAC,IAAA;AAAA,QAAA,IAAAgB,qBAAA,EAAAC,IAAA,EAAAC,qBAAA,CAAA;AAAA,QAAA,OACC,CAAAF,CAAAA,qBAAA,GAAChB,CAAC,CAACmB,SAAS,CAACC,aAAa,KAAA,IAAA,GAAAJ,qBAAA,GAAI,IAAI,MAAAC,CAAAA,IAAA,IAAAC,qBAAA,GACjC5B,KAAK,CAAC+B,OAAO,CAACC,mBAAmB,KAAA,IAAA,GAAAJ,qBAAA,GAChC5B,KAAK,CAAC+B,OAAO,CAACD,aAAa,KAAA,IAAA,GAAAH,IAAA,GAC3B,IAAI,CAAC,CAAA;AAAA,OACX,CAAC,CAAA;KACF,CAAA;IAEDvB,MAAM,CAAC6B,WAAW,GAAG,MAAM;AACzB,MAAA,MAAMC,aAAa,GAAG9B,MAAM,CAACI,cAAc,EAAE,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,CAAC,CAAA;MAE5D,MAAM;QAAElB,IAAI;AAAEC,QAAAA,KAAAA;AAAM,OAAC,GAAGM,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAAA;AAEtD,MAAA,MAAMsC,MAAM,GAAGF,aAAa,CAACT,IAAI,CAACf,CAAC,IAAIjB,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAE2B,QAAQ,CAACV,CAAC,CAAC,CAAC,CAAA;AACzD,MAAA,MAAM2B,OAAO,GAAGH,aAAa,CAACT,IAAI,CAACf,CAAC,IAAIhB,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAE0B,QAAQ,CAACV,CAAC,CAAC,CAAC,CAAA;MAE3D,OAAO0B,MAAM,GAAG,MAAM,GAAGC,OAAO,GAAG,OAAO,GAAG,KAAK,CAAA;KACnD,CAAA;IAEDjC,MAAM,CAACkC,cAAc,GAAG,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;AAC5B,MAAA,MAAMlC,QAAQ,GAAGF,MAAM,CAAC6B,WAAW,EAAE,CAAA;AAErC,MAAA,OAAO3B,QAAQ,GAAA,CAAAiC,qBAAA,GAAA,CAAAC,sBAAA,GACXxC,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,KAAA,IAAA,IAAA,CAAA0C,sBAAA,GAA9BA,sBAAA,CAAiClC,QAAQ,CAAC,KAA1CkC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAA4CC,OAAO,CAACrC,MAAM,CAACO,EAAE,CAAC,YAAA4B,qBAAA,GAAI,CAAC,CAAC,GACpE,CAAC,CAAA;KACN,CAAA;GACF;AAEDG,EAAAA,SAAS,EAAEA,CACTC,GAAe,EACf3C,KAAmB,KACV;AACT2C,IAAAA,GAAG,CAACC,qBAAqB,GAAGC,UAAI,CAC9B,MAAM,CACJF,GAAG,CAACG,mBAAmB,EAAE,EACzB9C,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAACL,IAAI,EACnCO,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAACJ,KAAK,CACrC,EACD,CAACqD,QAAQ,EAAEtD,IAAI,EAAEC,KAAK,KAAK;AACzB,MAAA,MAAMsD,YAAsB,GAAG,CAAC,IAAIvD,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,GAAG,IAAIC,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EAAE,CAAA;AAElE,MAAA,OAAOqD,QAAQ,CAACnC,MAAM,CAACF,CAAC,IAAI,CAACsC,YAAY,CAAC5B,QAAQ,CAACV,CAAC,CAACN,MAAM,CAACO,EAAE,CAAC,CAAC,CAAA;KACjE,EACDsC,oBAAc,CAACjD,KAAK,CAAC+B,OAAO,EAAE,WAAW,EAAE,uBAAuB,CACpE,CAAC,CAAA;AACDY,IAAAA,GAAG,CAACO,mBAAmB,GAAGL,UAAI,CAC5B,MAAM,CAACF,GAAG,CAACG,mBAAmB,EAAE,EAAE9C,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAACL,IAAI,CAAC,EACtE,CAACsD,QAAQ,EAAEtD,IAAI,KAAK;AAClB,MAAA,MAAM0D,KAAK,GAAG,CAAC1D,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,EACtBgB,GAAG,CAAC2C,QAAQ,IAAIL,QAAQ,CAACM,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAClD,MAAM,CAACO,EAAE,KAAKyC,QAAQ,CAAE,CAAC,CACpExC,MAAM,CAACC,OAAO,CAAC,CACfJ,GAAG,CAACC,CAAC,KAAK;AAAE,QAAA,GAAGA,CAAC;AAAEJ,QAAAA,QAAQ,EAAE,MAAA;AAAO,OAAC,CAAyB,CAAC,CAAA;AAEjE,MAAA,OAAO6C,KAAK,CAAA;KACb,EACDF,oBAAc,CAACjD,KAAK,CAAC+B,OAAO,EAAE,WAAW,EAAE,qBAAqB,CAClE,CAAC,CAAA;AACDY,IAAAA,GAAG,CAACY,oBAAoB,GAAGV,UAAI,CAC7B,MAAM,CAACF,GAAG,CAACG,mBAAmB,EAAE,EAAE9C,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAACJ,KAAK,CAAC,EACvE,CAACqD,QAAQ,EAAErD,KAAK,KAAK;AACnB,MAAA,MAAMyD,KAAK,GAAG,CAACzD,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EACvBe,GAAG,CAAC2C,QAAQ,IAAIL,QAAQ,CAACM,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAClD,MAAM,CAACO,EAAE,KAAKyC,QAAQ,CAAE,CAAC,CACpExC,MAAM,CAACC,OAAO,CAAC,CACfJ,GAAG,CAACC,CAAC,KAAK;AAAE,QAAA,GAAGA,CAAC;AAAEJ,QAAAA,QAAQ,EAAE,OAAA;AAAQ,OAAC,CAAyB,CAAC,CAAA;AAElE,MAAA,OAAO6C,KAAK,CAAA;KACb,EACDF,oBAAc,CAACjD,KAAK,CAAC+B,OAAO,EAAE,WAAW,EAAE,sBAAsB,CACnE,CAAC,CAAA;GACF;EAEDyB,WAAW,EAA0BxD,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACc,gBAAgB,GAAG2C,OAAO,IAC9BzD,KAAK,CAAC+B,OAAO,CAAC9B,qBAAqB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAnCD,KAAK,CAAC+B,OAAO,CAAC9B,qBAAqB,CAAGwD,OAAO,CAAC,CAAA;IAEhDzD,KAAK,CAAC0D,kBAAkB,GAAGC,YAAY,IAAA;MAAA,IAAAC,qBAAA,EAAAC,mBAAA,CAAA;MAAA,OACrC7D,KAAK,CAACc,gBAAgB,CACpB6C,YAAY,GACRnE,4BAA4B,EAAE,GAAAoE,CAAAA,qBAAA,GAAAC,CAAAA,mBAAA,GAC9B7D,KAAK,CAAC8D,YAAY,KAAlBD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoB/D,aAAa,KAAA8D,IAAAA,GAAAA,qBAAA,GAAIpE,4BAA4B,EACvE,CAAC,CAAA;AAAA,KAAA,CAAA;AAEHQ,IAAAA,KAAK,CAAC+D,sBAAsB,GAAGzD,QAAQ,IAAI;AAAA,MAAA,IAAA0D,qBAAA,CAAA;MACzC,MAAMC,YAAY,GAAGjE,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAAA;MAEnD,IAAI,CAACQ,QAAQ,EAAE;QAAA,IAAA4D,kBAAA,EAAAC,mBAAA,CAAA;QACb,OAAOtD,OAAO,CAAC,CAAAqD,CAAAA,kBAAA,GAAAD,YAAY,CAACxE,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAjByE,kBAAA,CAAmBE,MAAM,MAAAD,CAAAA,mBAAA,GAAIF,YAAY,CAACvE,KAAK,KAAlByE,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAoBC,MAAM,CAAC,CAAA,CAAA;AACzE,OAAA;AACA,MAAA,OAAOvD,OAAO,CAAA,CAAAmD,qBAAA,GAACC,YAAY,CAAC3D,QAAQ,CAAC,KAAtB0D,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAwBI,MAAM,CAAC,CAAA;KAC/C,CAAA;AAEDpE,IAAAA,KAAK,CAACqE,kBAAkB,GAAGxB,UAAI,CAC7B,MAAM,CAAC7C,KAAK,CAACsE,iBAAiB,EAAE,EAAEtE,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAACL,IAAI,CAAC,EACtE,CAAC8E,UAAU,EAAE9E,IAAI,KAAK;AACpB,MAAA,OAAO,CAACA,IAAI,IAAJA,IAAAA,GAAAA,IAAI,GAAI,EAAE,EACfgB,GAAG,CAAC2C,QAAQ,IAAImB,UAAU,CAAClB,IAAI,CAACjD,MAAM,IAAIA,MAAM,CAACO,EAAE,KAAKyC,QAAQ,CAAE,CAAC,CACnExC,MAAM,CAACC,OAAO,CAAC,CAAA;KACnB,EACDoC,oBAAc,CAACjD,KAAK,CAAC+B,OAAO,EAAE,cAAc,EAAE,oBAAoB,CACpE,CAAC,CAAA;AAED/B,IAAAA,KAAK,CAACwE,mBAAmB,GAAG3B,UAAI,CAC9B,MAAM,CAAC7C,KAAK,CAACsE,iBAAiB,EAAE,EAAEtE,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAACJ,KAAK,CAAC,EACvE,CAAC6E,UAAU,EAAE7E,KAAK,KAAK;AACrB,MAAA,OAAO,CAACA,KAAK,IAALA,IAAAA,GAAAA,KAAK,GAAI,EAAE,EAChBe,GAAG,CAAC2C,QAAQ,IAAImB,UAAU,CAAClB,IAAI,CAACjD,MAAM,IAAIA,MAAM,CAACO,EAAE,KAAKyC,QAAQ,CAAE,CAAC,CACnExC,MAAM,CAACC,OAAO,CAAC,CAAA;KACnB,EACDoC,oBAAc,CAACjD,KAAK,CAAC+B,OAAO,EAAE,cAAc,EAAE,qBAAqB,CACrE,CAAC,CAAA;AAED/B,IAAAA,KAAK,CAACyE,oBAAoB,GAAG5B,UAAI,CAC/B,MAAM,CACJ7C,KAAK,CAACsE,iBAAiB,EAAE,EACzBtE,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAACL,IAAI,EACnCO,KAAK,CAACmC,QAAQ,EAAE,CAACrC,aAAa,CAACJ,KAAK,CACrC,EACD,CAAC6E,UAAU,EAAE9E,IAAI,EAAEC,KAAK,KAAK;AAC3B,MAAA,MAAMsD,YAAsB,GAAG,CAAC,IAAIvD,IAAI,IAAA,IAAA,GAAJA,IAAI,GAAI,EAAE,GAAG,IAAIC,KAAK,IAAA,IAAA,GAALA,KAAK,GAAI,EAAE,EAAE,CAAA;AAElE,MAAA,OAAO6E,UAAU,CAAC3D,MAAM,CAACF,CAAC,IAAI,CAACsC,YAAY,CAAC5B,QAAQ,CAACV,CAAC,CAACC,EAAE,CAAC,CAAC,CAAA;KAC5D,EACDsC,oBAAc,CAACjD,KAAK,CAAC+B,OAAO,EAAE,cAAc,EAAE,sBAAsB,CACtE,CAAC,CAAA;AACH,GAAA;AACF;;;;"}