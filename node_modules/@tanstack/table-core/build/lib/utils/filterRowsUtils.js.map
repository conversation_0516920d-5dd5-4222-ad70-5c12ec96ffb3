{"version": 3, "file": "filterRowsUtils.js", "sources": ["../../../src/utils/filterRowsUtils.ts"], "sourcesContent": ["import { createRow } from '../core/row'\nimport { Row, RowModel, Table, RowData } from '../types'\n\nexport function filterRows<TData extends RowData>(\n  rows: Row<TData>[],\n  filterRowImpl: (row: Row<TData>) => any,\n  table: Table<TData>\n) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table)\n  }\n\n  return filterRowModelFromRoot(rows, filterRowImpl, table)\n}\n\nfunction filterRowModelFromLeafs<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => Row<TData>[],\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    const rows: Row<TData>[] = []\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const newRow = createRow(\n        table,\n        row.id,\n        row.original,\n        row.index,\n        row.depth,\n        undefined,\n        row.parentId\n      )\n      newRow.columnFilters = row.columnFilters\n\n      if (row.subRows?.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n        row = newRow\n\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n      } else {\n        row = newRow\n        if (filterRow(row)) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n        }\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n\nfunction filterRowModelFromRoot<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => any,\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  // Filters top level and nested rows\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    // Filter from parents downward first\n\n    const rows: Row<TData>[] = []\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const pass = filterRow(row)\n\n      if (pass) {\n        if (row.subRows?.length && depth < maxDepth) {\n          const newRow = createRow(\n            table,\n            row.id,\n            row.original,\n            row.index,\n            row.depth,\n            undefined,\n            row.parentId\n          )\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n          row = newRow\n        }\n\n        rows.push(row)\n        newFilteredFlatRows.push(row)\n        newFilteredRowsById[row.id] = row\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n"], "names": ["filterRows", "rows", "filterRowImpl", "table", "options", "filterFromLeafRows", "filterRowModelFromLeafs", "filterRowModelFromRoot", "rowsToFilter", "filterRow", "_table$options$maxLea", "newFilteredFlatRows", "newFilteredRowsById", "max<PERSON><PERSON><PERSON>", "maxLeafRowFilterDepth", "recurseFilterRows", "depth", "i", "length", "_row$subRows", "row", "newRow", "createRow", "id", "original", "index", "undefined", "parentId", "columnFilters", "subRows", "push", "flatRows", "rowsById", "_table$options$maxLea2", "pass", "_row$subRows2"], "mappings": ";;;;;;;;;;;;;;AAGO,SAASA,UAAUA,CACxBC,IAAkB,EAClBC,aAAuC,EACvCC,KAAmB,EACnB;AACA,EAAA,IAAIA,KAAK,CAACC,OAAO,CAACC,kBAAkB,EAAE;AACpC,IAAA,OAAOC,uBAAuB,CAACL,IAAI,EAAEC,aAAa,EAAEC,KAAK,CAAC,CAAA;AAC5D,GAAA;AAEA,EAAA,OAAOI,sBAAsB,CAACN,IAAI,EAAEC,aAAa,EAAEC,KAAK,CAAC,CAAA;AAC3D,CAAA;AAEA,SAASG,uBAAuBA,CAC9BE,YAA0B,EAC1BC,SAA4C,EAC5CN,KAAmB,EACF;AAAA,EAAA,IAAAO,qBAAA,CAAA;EACjB,MAAMC,mBAAiC,GAAG,EAAE,CAAA;EAC5C,MAAMC,mBAA+C,GAAG,EAAE,CAAA;AAC1D,EAAA,MAAMC,QAAQ,GAAA,CAAAH,qBAAA,GAAGP,KAAK,CAACC,OAAO,CAACU,qBAAqB,KAAA,IAAA,GAAAJ,qBAAA,GAAI,GAAG,CAAA;AAE3D,EAAA,MAAMK,iBAAiB,GAAG,UAACP,YAA0B,EAAEQ,KAAK,EAAS;AAAA,IAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,MAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,KAAA;IAC9D,MAAMf,IAAkB,GAAG,EAAE,CAAA;;AAE7B;AACA,IAAA,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,YAAY,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;AAAA,MAAA,IAAAE,YAAA,CAAA;AAC5C,MAAA,IAAIC,KAAG,GAAGZ,YAAY,CAACS,CAAC,CAAE,CAAA;MAE1B,MAAMI,MAAM,GAAGC,aAAS,CACtBnB,KAAK,EACLiB,KAAG,CAACG,EAAE,EACNH,KAAG,CAACI,QAAQ,EACZJ,KAAG,CAACK,KAAK,EACTL,KAAG,CAACJ,KAAK,EACTU,SAAS,EACTN,KAAG,CAACO,QACN,CAAC,CAAA;AACDN,MAAAA,MAAM,CAACO,aAAa,GAAGR,KAAG,CAACQ,aAAa,CAAA;AAExC,MAAA,IAAI,CAAAT,YAAA,GAAAC,KAAG,CAACS,OAAO,KAAA,IAAA,IAAXV,YAAA,CAAaD,MAAM,IAAIF,KAAK,GAAGH,QAAQ,EAAE;AAC3CQ,QAAAA,MAAM,CAACQ,OAAO,GAAGd,iBAAiB,CAACK,KAAG,CAACS,OAAO,EAAEb,KAAK,GAAG,CAAC,CAAC,CAAA;AAC1DI,QAAAA,KAAG,GAAGC,MAAM,CAAA;QAEZ,IAAIZ,SAAS,CAACW,KAAG,CAAC,IAAI,CAACC,MAAM,CAACQ,OAAO,CAACX,MAAM,EAAE;AAC5CjB,UAAAA,IAAI,CAAC6B,IAAI,CAACV,KAAG,CAAC,CAAA;AACdR,UAAAA,mBAAmB,CAACQ,KAAG,CAACG,EAAE,CAAC,GAAGH,KAAG,CAAA;AACjCT,UAAAA,mBAAmB,CAACmB,IAAI,CAACV,KAAG,CAAC,CAAA;AAC7B,UAAA,SAAA;AACF,SAAA;QAEA,IAAIX,SAAS,CAACW,KAAG,CAAC,IAAIC,MAAM,CAACQ,OAAO,CAACX,MAAM,EAAE;AAC3CjB,UAAAA,IAAI,CAAC6B,IAAI,CAACV,KAAG,CAAC,CAAA;AACdR,UAAAA,mBAAmB,CAACQ,KAAG,CAACG,EAAE,CAAC,GAAGH,KAAG,CAAA;AACjCT,UAAAA,mBAAmB,CAACmB,IAAI,CAACV,KAAG,CAAC,CAAA;AAC7B,UAAA,SAAA;AACF,SAAA;AACF,OAAC,MAAM;AACLA,QAAAA,KAAG,GAAGC,MAAM,CAAA;AACZ,QAAA,IAAIZ,SAAS,CAACW,KAAG,CAAC,EAAE;AAClBnB,UAAAA,IAAI,CAAC6B,IAAI,CAACV,KAAG,CAAC,CAAA;AACdR,UAAAA,mBAAmB,CAACQ,KAAG,CAACG,EAAE,CAAC,GAAGH,KAAG,CAAA;AACjCT,UAAAA,mBAAmB,CAACmB,IAAI,CAACV,KAAG,CAAC,CAAA;AAC/B,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,OAAOnB,IAAI,CAAA;GACZ,CAAA;EAED,OAAO;AACLA,IAAAA,IAAI,EAAEc,iBAAiB,CAACP,YAAY,CAAC;AACrCuB,IAAAA,QAAQ,EAAEpB,mBAAmB;AAC7BqB,IAAAA,QAAQ,EAAEpB,mBAAAA;GACX,CAAA;AACH,CAAA;AAEA,SAASL,sBAAsBA,CAC7BC,YAA0B,EAC1BC,SAAmC,EACnCN,KAAmB,EACF;AAAA,EAAA,IAAA8B,sBAAA,CAAA;EACjB,MAAMtB,mBAAiC,GAAG,EAAE,CAAA;EAC5C,MAAMC,mBAA+C,GAAG,EAAE,CAAA;AAC1D,EAAA,MAAMC,QAAQ,GAAA,CAAAoB,sBAAA,GAAG9B,KAAK,CAACC,OAAO,CAACU,qBAAqB,KAAA,IAAA,GAAAmB,sBAAA,GAAI,GAAG,CAAA;;AAE3D;AACA,EAAA,MAAMlB,iBAAiB,GAAG,UAACP,YAA0B,EAAEQ,KAAK,EAAS;AAAA,IAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,MAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,KAAA;AAC9D;;IAEA,MAAMf,IAAkB,GAAG,EAAE,CAAA;;AAE7B;AACA,IAAA,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,YAAY,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;AAC5C,MAAA,IAAIG,KAAG,GAAGZ,YAAY,CAACS,CAAC,CAAE,CAAA;AAE1B,MAAA,MAAMiB,IAAI,GAAGzB,SAAS,CAACW,KAAG,CAAC,CAAA;AAE3B,MAAA,IAAIc,IAAI,EAAE;AAAA,QAAA,IAAAC,aAAA,CAAA;AACR,QAAA,IAAI,CAAAA,aAAA,GAAAf,KAAG,CAACS,OAAO,KAAA,IAAA,IAAXM,aAAA,CAAajB,MAAM,IAAIF,KAAK,GAAGH,QAAQ,EAAE;UAC3C,MAAMQ,MAAM,GAAGC,aAAS,CACtBnB,KAAK,EACLiB,KAAG,CAACG,EAAE,EACNH,KAAG,CAACI,QAAQ,EACZJ,KAAG,CAACK,KAAK,EACTL,KAAG,CAACJ,KAAK,EACTU,SAAS,EACTN,KAAG,CAACO,QACN,CAAC,CAAA;AACDN,UAAAA,MAAM,CAACQ,OAAO,GAAGd,iBAAiB,CAACK,KAAG,CAACS,OAAO,EAAEb,KAAK,GAAG,CAAC,CAAC,CAAA;AAC1DI,UAAAA,KAAG,GAAGC,MAAM,CAAA;AACd,SAAA;AAEApB,QAAAA,IAAI,CAAC6B,IAAI,CAACV,KAAG,CAAC,CAAA;AACdT,QAAAA,mBAAmB,CAACmB,IAAI,CAACV,KAAG,CAAC,CAAA;AAC7BR,QAAAA,mBAAmB,CAACQ,KAAG,CAACG,EAAE,CAAC,GAAGH,KAAG,CAAA;AACnC,OAAA;AACF,KAAA;AAEA,IAAA,OAAOnB,IAAI,CAAA;GACZ,CAAA;EAED,OAAO;AACLA,IAAAA,IAAI,EAAEc,iBAAiB,CAACP,YAAY,CAAC;AACrCuB,IAAAA,QAAQ,EAAEpB,mBAAmB;AAC7BqB,IAAAA,QAAQ,EAAEpB,mBAAAA;GACX,CAAA;AACH;;;;"}