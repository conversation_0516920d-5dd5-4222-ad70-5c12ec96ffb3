{"version": 3, "file": "getFilteredRowModel.js", "sources": ["../../../src/utils/getFilteredRowModel.ts"], "sourcesContent": ["import { ResolvedColumnFilter } from '../features/ColumnFiltering'\nimport { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFilteredRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n      ],\n      (rowModel, columnFilters, globalFilter) => {\n        if (\n          !rowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          for (let i = 0; i < rowModel.flatRows.length; i++) {\n            rowModel.flatRows[i]!.columnFilters = {}\n            rowModel.flatRows[i]!.columnFiltersMeta = {}\n          }\n          return rowModel\n        }\n\n        const resolvedColumnFilters: ResolvedColumnFilter<TData>[] = []\n        const resolvedGlobalFilters: ResolvedColumnFilter<TData>[] = []\n\n        ;(columnFilters ?? []).forEach(d => {\n          const column = table.getColumn(d.id)\n\n          if (!column) {\n            return\n          }\n\n          const filterFn = column.getFilterFn()\n\n          if (!filterFn) {\n            if (process.env.NODE_ENV !== 'production') {\n              console.warn(\n                `Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`\n              )\n            }\n            return\n          }\n\n          resolvedColumnFilters.push({\n            id: d.id,\n            filterFn,\n            resolvedValue: filterFn.resolveFilterValue?.(d.value) ?? d.value,\n          })\n        })\n\n        const filterableIds = (columnFilters ?? []).map(d => d.id)\n\n        const globalFilterFn = table.getGlobalFilterFn()\n\n        const globallyFilterableColumns = table\n          .getAllLeafColumns()\n          .filter(column => column.getCanGlobalFilter())\n\n        if (\n          globalFilter &&\n          globalFilterFn &&\n          globallyFilterableColumns.length\n        ) {\n          filterableIds.push('__global__')\n\n          globallyFilterableColumns.forEach(column => {\n            resolvedGlobalFilters.push({\n              id: column.id,\n              filterFn: globalFilterFn,\n              resolvedValue:\n                globalFilterFn.resolveFilterValue?.(globalFilter) ??\n                globalFilter,\n            })\n          })\n        }\n\n        let currentColumnFilter\n        let currentGlobalFilter\n\n        // Flag the prefiltered row model with each filter state\n        for (let j = 0; j < rowModel.flatRows.length; j++) {\n          const row = rowModel.flatRows[j]!\n\n          row.columnFilters = {}\n\n          if (resolvedColumnFilters.length) {\n            for (let i = 0; i < resolvedColumnFilters.length; i++) {\n              currentColumnFilter = resolvedColumnFilters[i]!\n              const id = currentColumnFilter.id\n\n              // Tag the row with the column filter state\n              row.columnFilters[id] = currentColumnFilter.filterFn(\n                row,\n                id,\n                currentColumnFilter.resolvedValue,\n                filterMeta => {\n                  row.columnFiltersMeta[id] = filterMeta\n                }\n              )\n            }\n          }\n\n          if (resolvedGlobalFilters.length) {\n            for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n              currentGlobalFilter = resolvedGlobalFilters[i]!\n              const id = currentGlobalFilter.id\n              // Tag the row with the first truthy global filter state\n              if (\n                currentGlobalFilter.filterFn(\n                  row,\n                  id,\n                  currentGlobalFilter.resolvedValue,\n                  filterMeta => {\n                    row.columnFiltersMeta[id] = filterMeta\n                  }\n                )\n              ) {\n                row.columnFilters.__global__ = true\n                break\n              }\n            }\n\n            if (row.columnFilters.__global__ !== true) {\n              row.columnFilters.__global__ = false\n            }\n          }\n        }\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        // Filter final rows using all of the active filters\n        return filterRows(rowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n"], "names": ["getFilteredRowModel", "table", "memo", "getPreFilteredRowModel", "getState", "columnFilters", "globalFilter", "rowModel", "rows", "length", "i", "flatRows", "columnFiltersMeta", "resolvedColumnFilters", "resolvedGlobalFilters", "for<PERSON>ach", "d", "_filterFn$resolveFilt", "column", "getColumn", "id", "filterFn", "getFilterFn", "process", "env", "NODE_ENV", "console", "warn", "push", "resolvedValue", "resolveFilterValue", "value", "filterableIds", "map", "globalFilterFn", "getGlobalFilterFn", "globallyFilterableColumns", "getAllLeafColumns", "filter", "getCanGlobalFilter", "_globalFilterFn$resol", "currentColumnFilter", "currentGlobalFilter", "j", "row", "filterMeta", "__global__", "filterRowsImpl", "filterRows", "getMemoOptions", "options", "_autoResetPageIndex"], "mappings": ";;;;;;;;;;;;;;;AAKO,SAASA,mBAAmBA,GAER;AACzB,EAAA,OAAOC,KAAK,IACVC,UAAI,CACF,MAAM,CACJD,KAAK,CAACE,sBAAsB,EAAE,EAC9BF,KAAK,CAACG,QAAQ,EAAE,CAACC,aAAa,EAC9BJ,KAAK,CAACG,QAAQ,EAAE,CAACE,YAAY,CAC9B,EACD,CAACC,QAAQ,EAAEF,aAAa,EAAEC,YAAY,KAAK;AACzC,IAAA,IACE,CAACC,QAAQ,CAACC,IAAI,CAACC,MAAM,IACpB,EAACJ,aAAa,IAAA,IAAA,IAAbA,aAAa,CAAEI,MAAM,CAAI,IAAA,CAACH,YAAa,EACzC;AACA,MAAA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACI,QAAQ,CAACF,MAAM,EAAEC,CAAC,EAAE,EAAE;QACjDH,QAAQ,CAACI,QAAQ,CAACD,CAAC,CAAC,CAAEL,aAAa,GAAG,EAAE,CAAA;QACxCE,QAAQ,CAACI,QAAQ,CAACD,CAAC,CAAC,CAAEE,iBAAiB,GAAG,EAAE,CAAA;AAC9C,OAAA;AACA,MAAA,OAAOL,QAAQ,CAAA;AACjB,KAAA;IAEA,MAAMM,qBAAoD,GAAG,EAAE,CAAA;IAC/D,MAAMC,qBAAoD,GAAG,EAAE,CAAA;IAE9D,CAACT,aAAa,WAAbA,aAAa,GAAI,EAAE,EAAEU,OAAO,CAACC,CAAC,IAAI;AAAA,MAAA,IAAAC,qBAAA,CAAA;MAClC,MAAMC,MAAM,GAAGjB,KAAK,CAACkB,SAAS,CAACH,CAAC,CAACI,EAAE,CAAC,CAAA;MAEpC,IAAI,CAACF,MAAM,EAAE;AACX,QAAA,OAAA;AACF,OAAA;AAEA,MAAA,MAAMG,QAAQ,GAAGH,MAAM,CAACI,WAAW,EAAE,CAAA;MAErC,IAAI,CAACD,QAAQ,EAAE;AACb,QAAA,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCC,OAAO,CAACC,IAAI,CACV,CAAA,iEAAA,EAAoET,MAAM,CAACE,EAAE,GAC/E,CAAC,CAAA;AACH,SAAA;AACA,QAAA,OAAA;AACF,OAAA;MAEAP,qBAAqB,CAACe,IAAI,CAAC;QACzBR,EAAE,EAAEJ,CAAC,CAACI,EAAE;QACRC,QAAQ;AACRQ,QAAAA,aAAa,GAAAZ,qBAAA,GAAEI,QAAQ,CAACS,kBAAkB,oBAA3BT,QAAQ,CAACS,kBAAkB,CAAGd,CAAC,CAACe,KAAK,CAAC,YAAAd,qBAAA,GAAID,CAAC,CAACe,KAAAA;AAC7D,OAAC,CAAC,CAAA;AACJ,KAAC,CAAC,CAAA;AAEF,IAAA,MAAMC,aAAa,GAAG,CAAC3B,aAAa,IAAA,IAAA,GAAbA,aAAa,GAAI,EAAE,EAAE4B,GAAG,CAACjB,CAAC,IAAIA,CAAC,CAACI,EAAE,CAAC,CAAA;AAE1D,IAAA,MAAMc,cAAc,GAAGjC,KAAK,CAACkC,iBAAiB,EAAE,CAAA;AAEhD,IAAA,MAAMC,yBAAyB,GAAGnC,KAAK,CACpCoC,iBAAiB,EAAE,CACnBC,MAAM,CAACpB,MAAM,IAAIA,MAAM,CAACqB,kBAAkB,EAAE,CAAC,CAAA;AAEhD,IAAA,IACEjC,YAAY,IACZ4B,cAAc,IACdE,yBAAyB,CAAC3B,MAAM,EAChC;AACAuB,MAAAA,aAAa,CAACJ,IAAI,CAAC,YAAY,CAAC,CAAA;AAEhCQ,MAAAA,yBAAyB,CAACrB,OAAO,CAACG,MAAM,IAAI;AAAA,QAAA,IAAAsB,qBAAA,CAAA;QAC1C1B,qBAAqB,CAACc,IAAI,CAAC;UACzBR,EAAE,EAAEF,MAAM,CAACE,EAAE;AACbC,UAAAA,QAAQ,EAAEa,cAAc;AACxBL,UAAAA,aAAa,EAAAW,CAAAA,qBAAA,GACXN,cAAc,CAACJ,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAjCI,cAAc,CAACJ,kBAAkB,CAAGxB,YAAY,CAAC,KAAA,IAAA,GAAAkC,qBAAA,GACjDlC,YAAAA;AACJ,SAAC,CAAC,CAAA;AACJ,OAAC,CAAC,CAAA;AACJ,KAAA;AAEA,IAAA,IAAImC,mBAAmB,CAAA;AACvB,IAAA,IAAIC,mBAAmB,CAAA;;AAEvB;AACA,IAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,QAAQ,CAACI,QAAQ,CAACF,MAAM,EAAEkC,CAAC,EAAE,EAAE;AACjD,MAAA,MAAMC,GAAG,GAAGrC,QAAQ,CAACI,QAAQ,CAACgC,CAAC,CAAE,CAAA;AAEjCC,MAAAA,GAAG,CAACvC,aAAa,GAAG,EAAE,CAAA;MAEtB,IAAIQ,qBAAqB,CAACJ,MAAM,EAAE;AAChC,QAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,qBAAqB,CAACJ,MAAM,EAAEC,CAAC,EAAE,EAAE;AACrD+B,UAAAA,mBAAmB,GAAG5B,qBAAqB,CAACH,CAAC,CAAE,CAAA;AAC/C,UAAA,MAAMU,EAAE,GAAGqB,mBAAmB,CAACrB,EAAE,CAAA;;AAEjC;AACAwB,UAAAA,GAAG,CAACvC,aAAa,CAACe,EAAE,CAAC,GAAGqB,mBAAmB,CAACpB,QAAQ,CAClDuB,GAAG,EACHxB,EAAE,EACFqB,mBAAmB,CAACZ,aAAa,EACjCgB,UAAU,IAAI;AACZD,YAAAA,GAAG,CAAChC,iBAAiB,CAACQ,EAAE,CAAC,GAAGyB,UAAU,CAAA;AACxC,WACF,CAAC,CAAA;AACH,SAAA;AACF,OAAA;MAEA,IAAI/B,qBAAqB,CAACL,MAAM,EAAE;AAChC,QAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,qBAAqB,CAACL,MAAM,EAAEC,CAAC,EAAE,EAAE;AACrDgC,UAAAA,mBAAmB,GAAG5B,qBAAqB,CAACJ,CAAC,CAAE,CAAA;AAC/C,UAAA,MAAMU,EAAE,GAAGsB,mBAAmB,CAACtB,EAAE,CAAA;AACjC;AACA,UAAA,IACEsB,mBAAmB,CAACrB,QAAQ,CAC1BuB,GAAG,EACHxB,EAAE,EACFsB,mBAAmB,CAACb,aAAa,EACjCgB,UAAU,IAAI;AACZD,YAAAA,GAAG,CAAChC,iBAAiB,CAACQ,EAAE,CAAC,GAAGyB,UAAU,CAAA;AACxC,WACF,CAAC,EACD;AACAD,YAAAA,GAAG,CAACvC,aAAa,CAACyC,UAAU,GAAG,IAAI,CAAA;AACnC,YAAA,MAAA;AACF,WAAA;AACF,SAAA;AAEA,QAAA,IAAIF,GAAG,CAACvC,aAAa,CAACyC,UAAU,KAAK,IAAI,EAAE;AACzCF,UAAAA,GAAG,CAACvC,aAAa,CAACyC,UAAU,GAAG,KAAK,CAAA;AACtC,SAAA;AACF,OAAA;AACF,KAAA;IAEA,MAAMC,cAAc,GAAIH,GAAe,IAAK;AAC1C;AACA,MAAA,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,aAAa,CAACvB,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC7C,IAAIkC,GAAG,CAACvC,aAAa,CAAC2B,aAAa,CAACtB,CAAC,CAAC,CAAE,KAAK,KAAK,EAAE;AAClD,UAAA,OAAO,KAAK,CAAA;AACd,SAAA;AACF,OAAA;AACA,MAAA,OAAO,IAAI,CAAA;KACZ,CAAA;;AAED;IACA,OAAOsC,0BAAU,CAACzC,QAAQ,CAACC,IAAI,EAAEuC,cAAc,EAAE9C,KAAK,CAAC,CAAA;AACzD,GAAC,EACDgD,oBAAc,CAAChD,KAAK,CAACiD,OAAO,EAAE,YAAY,EAAE,qBAAqB,EAAE,MACjEjD,KAAK,CAACkD,mBAAmB,EAC3B,CACF,CAAC,CAAA;AACL;;;;"}